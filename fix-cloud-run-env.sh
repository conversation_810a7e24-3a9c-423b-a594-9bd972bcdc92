#!/bin/bash

# Script para corrigir variáveis de ambiente no Cloud Run - ZapVida
# Uso: ./fix-cloud-run-env.sh

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Configurações
SERVICE_NAME="zapvida"
REGION="europe-west1"

echo "🔧 ZapVida - Corrigindo Variáveis de Ambiente no Cloud Run"
echo "=========================================================="

# Verificar se o gcloud CLI está instalado
if ! command -v gcloud &> /dev/null; then
    error "Google Cloud CLI (gcloud) não está instalado!"
    echo "Instale em: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

log "Google Cloud CLI encontrado"

# Verificar se está logado
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    error "Você não está logado no Google Cloud!"
    echo "Execute: gcloud auth login"
    exit 1
fi

log "Usuário logado no Google Cloud"

# Verificar se o serviço existe
if ! gcloud run services describe "$SERVICE_NAME" --region="$REGION" &>/dev/null; then
    error "Serviço '$SERVICE_NAME' não encontrado na região '$REGION'!"
    exit 1
fi

log "Serviço '$SERVICE_NAME' encontrado na região '$REGION'"

# Mostrar variáveis atuais
log "📋 Variáveis de ambiente atuais:"
gcloud run services describe "$SERVICE_NAME" \
    --region="$REGION" \
    --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)" | while IFS='	' read -r name value; do
    if [ -n "$name" ]; then
        if [ -z "$value" ]; then
            echo "  ❌ $name: (vazio)"
        else
            echo "  ✅ $name: ${value:0:30}..."
        fi
    fi
done

echo ""

# Remover variáveis vazias
log "🧹 Removendo variáveis com valores vazios..."
gcloud run services update "$SERVICE_NAME" \
    --region="$REGION" \
    --remove-env-vars="MAIL_HOST,MAIL_PORT,MAIL_USER,MAIL_PASS,NEXT_PUBLIC_PIRSCH_CODE,NEXT_PUBLIC_PLAUSIBLE_URL,NEXT_PUBLIC_MIXPANEL_TOKEN,MEMED_API_KEY,MEMED_SECRET_KEY"

# Adicionar variáveis críticas
log "🔑 Adicionando variáveis críticas..."
gcloud run services update "$SERVICE_NAME" \
    --region="$REGION" \
    --set-env-vars="NODE_ENV=production" \
    --set-env-vars="NEXT_TELEMETRY_DISABLED=1" \
    --set-env-vars="PORT=8080"

# Atualizar variáveis do Supabase (forçar atualização)
log "🔄 Atualizando variáveis do Supabase..."
gcloud run services update "$SERVICE_NAME" \
    --region="$REGION" \
    --set-env-vars="NEXT_PUBLIC_SUPABASE_URL=https://moupvfqlulvqbzwajkif.supabase.co" \
    --set-env-vars="NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MFeB-phlRJBVc_a2ZeS-yUP6LOLc9C0L4jF6BIqv0i0"

# Atualizar outras variáveis NEXT_PUBLIC_
log "🌐 Atualizando outras variáveis NEXT_PUBLIC_..."
gcloud run services update "$SERVICE_NAME" \
    --region="$REGION" \
    --set-env-vars="NEXT_PUBLIC_SITE_URL=https://zapvida.com" \
    --set-env-vars="NEXT_PUBLIC_S3_ENDPOINT=https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/s3" \
    --set-env-vars="NEXT_PUBLIC_AVATARS_BUCKET_NAME=avatars" \
    --set-env-vars="NEXT_PUBLIC_UPLOADS_BUCKET_NAME=medical_docs" \
    --set-env-vars="NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET=chat_attachments" \
    --set-env-vars="NEXT_PUBLIC_LIVEKIT_URL=dev-gmyd2e9t.livekit.cloud" \
    --set-env-vars="NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=AW-347192758" \
    --set-env-vars="NEXT_PUBLIC_POSTHOG_KEY=phc_BU1u3gA1nCjXQNTQJQBhhOVyaq6oEMQNzZ4HFeLflYO" \
    --set-env-vars="NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com" \
    --set-env-vars="NEXT_PUBLIC_ZAPCHAT_V2_ENABLED=true" \
    --set-env-vars="NEXT_PUBLIC_ZAPCHAT_V2_BETA=true"

# Corrigir valores placeholder
log "🔧 Corrigindo valores placeholder..."
gcloud run services update "$SERVICE_NAME" \
    --region="$REGION" \
    --set-env-vars="LEMONSQUEEZY_API_KEY=sua-chave-real-aqui" \
    --set-env-vars="LEMONSQUEEZY_WEBHOOK_SECRET=seu-webhook-secret-aqui" \
    --set-env-vars="LEMONSQUEEZY_STORE_ID=seu-store-id-aqui"

# Verificar se as variáveis foram aplicadas
log "✅ Verificando se as variáveis foram aplicadas..."
sleep 10

# Mostrar variáveis após correção
log "📋 Variáveis de ambiente após correção:"
gcloud run services describe "$SERVICE_NAME" \
    --region="$REGION" \
    --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)" | while IFS='	' read -r name value; do
    if [ -n "$name" ]; then
        if [ -z "$value" ]; then
            echo "  ❌ $name: (vazio)"
        else
            echo "  ✅ $name: ${value:0:30}..."
        fi
    fi
done

echo ""
log "🎉 Correção das variáveis de ambiente concluída!"
echo ""
echo "📋 Próximos passos:"
echo "1. Aguarde alguns minutos para o serviço reiniciar"
echo "2. Verifique os logs: gcloud run services logs read $SERVICE_NAME --region=$REGION"
echo "3. Teste a aplicação para ver se as variáveis estão sendo lidas"
echo ""
echo "⚠️  IMPORTANTE: As variáveis NEXT_PUBLIC_ podem precisar de um novo build da imagem"
echo "   para funcionarem corretamente no Cloud Run."
echo ""
echo "🔍 Para ver logs em tempo real:"
echo "   gcloud run services logs tail $SERVICE_NAME --region=$REGION"
