import { Decimal } from "@prisma/client/runtime/library";

// Configurações para diferentes ambientes de seed
export const seedConfig = {
  // Configurações de desenvolvimento
  development: {
    // Dados do hospital
    hospital: {
      name: "Hospital São Lucas",
      cnpj: "12345678000199",
      contactEmail: "<EMAIL>",
      contactPhone: "+551133334444",
      address: {
        street: "Avenida Paulista, 1000",
        city: "São Paulo",
        state: "SP",
        zip: "01310-100",
        country: "Brasil",
      },
      settings: {
        timezone: "America/Sao_Paulo",
        allowWalkIn: true,
        requirePreAnesthetic: true,
        defaultAppointmentDuration: 30,
      },
    },

    // Configurações de comissão
    commission: {
      platformFeeRate: new Decimal(10.0), // 10%
      minFeeAmount: new Decimal(5.0),
      maxFeeAmount: new Decimal(50.0),
    },

    // Preços base das consultas
    consultationPrices: {
      cardiology: new Decimal(250.0),
      pediatrics: new Decimal(200.0),
      psychiatry: new Decimal(300.0),
      general: new Decimal(180.0),
    },

    // Multiplicadores de plantão
    onDutyMultipliers: {
      cardiology: new Decimal(1.5),
      pediatrics: new Decimal(1.8),
      psychiatry: new Decimal(1.3),
      general: new Decimal(1.6),
    },

    // Configurações de assinatura
    subscription: {
      basicPlan: {
        name: "ZapVida Sempre",
        price: new Decimal(99.90),
        consultationsIncluded: 2,
        cycle: "MONTHLY",
      },
      premiumPlan: {
        name: "Plano Premium",
        price: new Decimal(199.90),
        consultationsIncluded: 5,
        cycle: "MONTHLY",
      },
    },
  },

  // Configurações de teste
  test: {
    // Dados mínimos para testes unitários
    hospital: {
      name: "Hospital Teste",
      cnpj: "11111111000111",
      contactEmail: "<EMAIL>",
      contactPhone: "+551111111111",
      address: {
        street: "Rua Teste, 123",
        city: "São Paulo",
        state: "SP",
        zip: "00000-000",
        country: "Brasil",
      },
      settings: {
        timezone: "America/Sao_Paulo",
        allowWalkIn: false,
        requirePreAnesthetic: false,
        defaultAppointmentDuration: 30,
      },
    },

    commission: {
      platformFeeRate: new Decimal(5.0),
      minFeeAmount: new Decimal(2.0),
      maxFeeAmount: new Decimal(25.0),
    },

    consultationPrices: {
      general: new Decimal(100.0),
    },

    onDutyMultipliers: {
      general: new Decimal(1.5),
    },

    subscription: {
      basicPlan: {
        name: "Plano Teste",
        price: new Decimal(50.0),
        consultationsIncluded: 1,
        cycle: "MONTHLY",
      },
    },
  },

  // Configurações de produção (dados fictícios para demonstração)
  production: {
    hospital: {
      name: "Hospital ZapVida",
      cnpj: "98765432000198",
      contactEmail: "<EMAIL>",
      contactPhone: "+551144445555",
      address: {
        street: "Rua das Palmeiras, 500",
        city: "São Paulo",
        state: "SP",
        zip: "04567-000",
        country: "Brasil",
      },
      settings: {
        timezone: "America/Sao_Paulo",
        allowWalkIn: true,
        requirePreAnesthetic: true,
        defaultAppointmentDuration: 45,
      },
    },

    commission: {
      platformFeeRate: new Decimal(15.0),
      minFeeAmount: new Decimal(10.0),
      maxFeeAmount: new Decimal(100.0),
    },

    consultationPrices: {
      cardiology: new Decimal(350.0),
      pediatrics: new Decimal(280.0),
      psychiatry: new Decimal(400.0),
      general: new Decimal(250.0),
    },

    onDutyMultipliers: {
      cardiology: new Decimal(2.0),
      pediatrics: new Decimal(2.2),
      psychiatry: new Decimal(1.8),
      general: new Decimal(2.0),
    },

    subscription: {
      basicPlan: {
        name: "Plano Essencial",
        price: new Decimal(149.90),
        consultationsIncluded: 3,
        cycle: "MONTHLY",
      },
      premiumPlan: {
        name: "Plano Completo",
        price: new Decimal(299.90),
        consultationsIncluded: 8,
        cycle: "MONTHLY",
      },
    },
  },
};

// Função para obter configuração baseada no ambiente
export function getSeedConfig(environment: keyof typeof seedConfig = "development") {
  return seedConfig[environment];
}

// Dados padrão para especialidades
export const defaultSpecialties = [
  { name: "Cardiologia", searchCount: 150 },
  { name: "Clínica Geral", searchCount: 200 },
  { name: "Pediatria", searchCount: 120 },
  { name: "Dermatologia", searchCount: 80 },
  { name: "Neurologia", searchCount: 60 },
  { name: "Psiquiatria", searchCount: 90 },
  { name: "Ortopedia", searchCount: 110 },
  { name: "Ginecologia", searchCount: 95 },
  { name: "Oftalmologia", searchCount: 75 },
  { name: "Urologia", searchCount: 65 },
];

// Dados padrão para usuários de teste
export const defaultUsers = {
  admin: {
    email: "<EMAIL>",
    name: "Admin ZapVida",
    role: "ADMIN" as const,
    phone: "+5511987654321",
  },
  doctor: {
    email: "<EMAIL>",
    name: "Dr. João Silva",
    role: "DOCTOR" as const,
    phone: "+5511912345678",
  },
  patient: {
    email: "<EMAIL>",
    name: "Maria Souza",
    role: "PATIENT" as const,
    phone: "+5511998765432",
  },
  secretary: {
    email: "<EMAIL>",
    name: "Maria Secretária",
    role: "SECRETARY" as const,
    phone: "+551133334445",
  },
};

// Função para gerar dados de endereço aleatórios
export function generateRandomAddress() {
  const streets = [
    "Rua das Flores",
    "Avenida Paulista",
    "Rua dos Girassóis",
    "Avenida das Palmeiras",
    "Rua das Acácias",
    "Avenida dos Ipês",
  ];

  const cities = ["São Paulo", "Rio de Janeiro", "Belo Horizonte", "Curitiba", "Porto Alegre"];
  const states = ["SP", "RJ", "MG", "PR", "RS"];

  return {
    street: streets[Math.floor(Math.random() * streets.length)] + ", " + Math.floor(Math.random() * 1000) + 1,
    city: cities[Math.floor(Math.random() * cities.length)],
    state: states[Math.floor(Math.random() * states.length)],
    zip: Math.floor(Math.random() * 90000) + 10000 + "-" + Math.floor(Math.random() * 900) + 100,
    country: "Brasil",
  };
}
