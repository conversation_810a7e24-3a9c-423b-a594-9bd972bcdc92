import { Decimal } from "@prisma/client/runtime/library";
import { v4 as uuidv4 } from "uuid";
import { db } from "../src/client";

// Função auxiliar para logs
function log(message: string) {
	console.log(`[SEED] ${message}`);
}

// Função para simular o hash de senhas (em um cenário real, use bcrypt)
async function hashPassword(password: string) {
	return `hashed_${password}`;
}

async function cleanDatabase() {
	try {
		log("Iniciando limpeza do banco de dados...");

		// First check if there are any records to delete
		const tables = [
			{ name: "consultationUsage", entity: db.consultationUsage },
			{ name: "patientSubscription", entity: db.patientSubscription },
			{ name: "invoice", entity: db.invoice },
			{ name: "transaction", entity: db.transaction },
			{ name: "preAnestheticForm", entity: db.preAnestheticForm },
			{ name: "prescription", entity: db.prescription },
			{ name: "doctorEvaluation", entity: db.doctorEvaluation },
			{ name: "message", entity: db.message },
			{ name: "notification", entity: db.notification },
			{ name: "attachment", entity: db.attachment },
			{ name: "timeSlot", entity: db.timeSlot },
			{ name: "appointment", entity: db.appointment },
			{ name: "scheduleBlock", entity: db.scheduleBlock },
			{ name: "doctorSchedule", entity: db.doctorSchedule },
			{ name: "doctorDocument", entity: db.doctorDocument },
			{ name: "digitalCertificate", entity: db.digitalCertificate },
			{ name: "doctorHospital", entity: db.doctorHospital },
			{ name: "doctor", entity: db.doctor },
			{ name: "patient", entity: db.patient },
			{ name: "specialty", entity: db.specialty },
			{ name: "department", entity: db.department },
			{ name: "hospital", entity: db.hospital },
			{ name: "teamMembership", entity: db.teamMembership },
			{ name: "teamInvitation", entity: db.teamInvitation },
			{ name: "subscription", entity: db.subscription },
			{ name: "team", entity: db.team },
			{ name: "userSession", entity: db.userSession },
			{ name: "userOauthAccount", entity: db.userOauthAccount },
			{ name: "userVerificationToken", entity: db.userVerificationToken },
			{ name: "userOneTimePassword", entity: db.userOneTimePassword },
			{ name: "user", entity: db.user },
		];

		// Delete in correct order to respect foreign key constraints
        for (const table of tables) {
			try {
				log(`Limpando tabela ${table.name}...`);
            // Delegate types form a union; cast to any for batch cleanup in seed context
            await (table.entity as any).deleteMany({});
				log(`Tabela ${table.name} limpa com sucesso.`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				log(`Erro ao limpar tabela ${table.name}: ${errorMessage}`);
				// Continue with other tables even if one fails
			}
		}

		log("Limpeza do banco de dados concluída.");
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		log(`Erro durante limpeza do banco de dados: ${errorMessage}`);
		throw error;
	}
}

async function main() {
	try {
		log("Iniciando seed...");

		// Clean the database first
		await cleanDatabase();

		// 1. Criar Usuários
		log("Criando usuários...");
		const adminUser = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Admin ZapVida",
				role: "ADMIN",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511987654321",
				emailVerified: true,
			},
		});
		log(`Usuário admin criado: ${adminUser.id}`);

		const doctorUser = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Dr. João Silva",
				role: "DOCTOR",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511912345678",
				emailVerified: true,
			},
		});
		log(`Usuário médico criado: ${doctorUser.id}`);

		const patientUser = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Maria Souza",
				role: "PATIENT",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511998765432",
				emailVerified: true,
			},
		});
		log(`Usuário paciente criado: ${patientUser.id}`);

		const patientUser2 = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Carlos Santos",
				role: "PATIENT",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511998765433",
				emailVerified: true,
			},
		});
		log(`Usuário paciente 2 criado: ${patientUser2.id}`);

		// 2. Criar Time para o Hospital
		log("Criando time para o hospital...");
		const hospitalTeam = await db.team.create({
			data: {
				id: uuidv4(),
				name: "Hospital São Lucas",
				teamType: "HOSPITAL",
				metadata: { description: "Hospital de referência em São Paulo" },
				memberships: {
					create: [
						{
							userId: adminUser.id,
							role: "ADMIN",
							isCreator: true,
						},
					],
				},
			},
		});
		log(`Time do hospital criado: ${hospitalTeam.id}`);

		// 3. Criar Hospital
		log("Criando hospital...");
		const hospital = await db.hospital.create({
			data: {
				id: uuidv4(),
				teamId: hospitalTeam.id,
				name: "Hospital São Lucas",
				cnpj: "12345678000199",
				contactEmail: "<EMAIL>",
				contactPhone: "+************",
				address: {
					street: "Avenida Paulista, 1000",
					city: "São Paulo",
					state: "SP",
					zip: "01310-100",
					country: "Brasil",
				},
				logoUrl: "https://storage.example.com/logos/saolucas.png",
				settings: {
					timezone: "America/Sao_Paulo",
					allowWalkIn: true,
					requirePreAnesthetic: true,
					defaultAppointmentDuration: 30,
				},
			},
		});
		log(`Hospital criado: ${hospital.id}`);

		// 4. Criar Departamento
		log("Criando departamento...");
		const department = await db.department.create({
			data: {
				id: uuidv4(),
				hospitalId: hospital.id,
				name: "Cardiologia",
				description: "Departamento de Cardiologia",
				isActive: true,
			},
		});
		log(`Departamento criado: ${department.id}`);

		// 5. Criar Especialidades
		log("Criando especialidades...");
		const cardiology = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Cardiologia",
				searchCount: 150,
			},
		});

		const generalPractice = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Clínica Geral",
				searchCount: 200,
			},
		});

		const pediatrics = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Pediatria",
				searchCount: 120,
			},
		});

		const dermatology = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Dermatologia",
				searchCount: 80,
			},
		});

		const neurology = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Neurologia",
				searchCount: 60,
			},
		});

		const psychiatry = await db.specialty.create({
			data: {
				id: uuidv4(),
				name: "Psiquiatria",
				searchCount: 90,
			},
		});
		log(`Especialidades criadas: ${cardiology.id}, ${generalPractice.id}, ${pediatrics.id}, ${dermatology.id}, ${neurology.id}, ${psychiatry.id}`);

		// 6. Criar Médicos
		log("Criando médicos...");
		const doctor = await db.doctor.create({
			data: {
				id: uuidv4(),
				userId: doctorUser.id,
				crm: "123456",
				crmState: "SP",
				biography: "Cardiologista com 15 anos de experiência, especializado em cardiologia intervencionista e ecocardiografia.",
				consultationPrice: new Decimal(250.0),
				consultationDuration: 30,
				returnPeriod: 15,
				documentStatus: "APPROVED",
				onlineStatus: "ONLINE",
				isAvailableForOnDuty: true,
				onDutyPriceMultiplier: new Decimal(1.5),
				maxConcurrentOnDuty: 3,
				consultTypes: ["VIDEO", "AUDIO", "CHAT"],
				specialties: {
					connect: [{ id: cardiology.id }],
				},
			},
		});

		// Criar mais médicos para testes
		const doctorUser2 = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Dra. Ana Costa",
				role: "DOCTOR",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511912345679",
				emailVerified: true,
			},
		});

		const doctor2 = await db.doctor.create({
			data: {
				id: uuidv4(),
				userId: doctorUser2.id,
				crm: "654321",
				crmState: "SP",
				biography: "Pediatra com 10 anos de experiência, especializada em neonatologia e cuidados intensivos pediátricos.",
				consultationPrice: new Decimal(200.0),
				consultationDuration: 45,
				returnPeriod: 30,
				documentStatus: "APPROVED",
				onlineStatus: "ONLINE",
				isAvailableForOnDuty: true,
				onDutyPriceMultiplier: new Decimal(1.8),
				maxConcurrentOnDuty: 2,
				consultTypes: ["VIDEO", "AUDIO"],
				specialties: {
					connect: [{ id: pediatrics.id }],
				},
			},
		});

		const doctorUser3 = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Dr. Carlos Mendes",
				role: "DOCTOR",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511912345680",
				emailVerified: true,
			},
		});

		const doctor3 = await db.doctor.create({
			data: {
				id: uuidv4(),
				userId: doctorUser3.id,
				crm: "789012",
				crmState: "SP",
				biography: "Psiquiatra com 12 anos de experiência, especializado em transtornos de ansiedade e depressão.",
				consultationPrice: new Decimal(300.0),
				consultationDuration: 60,
				returnPeriod: 7,
				documentStatus: "APPROVED",
				onlineStatus: "OFFLINE",
				isAvailableForOnDuty: false,
				consultTypes: ["VIDEO", "AUDIO"],
				specialties: {
					connect: [{ id: psychiatry.id }],
				},
			},
		});
		log(`Médicos criados: ${doctor.id}, ${doctor2.id}, ${doctor3.id}`);

		// 7. Associar médicos ao hospital
		log("Associando médicos ao hospital...");
		const doctorHospital = await db.doctorHospital.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				hospitalId: hospital.id,
				departmentId: department.id,
				isActive: true,
			},
		});

		const doctorHospital2 = await db.doctorHospital.create({
			data: {
				id: uuidv4(),
				doctorId: doctor2.id,
				hospitalId: hospital.id,
				departmentId: department.id,
				isActive: true,
			},
		});

		const doctorHospital3 = await db.doctorHospital.create({
			data: {
				id: uuidv4(),
				doctorId: doctor3.id,
				hospitalId: hospital.id,
				departmentId: null, // Sem departamento específico
				isActive: true,
			},
		});
		log(`Associações médico-hospital criadas: ${doctorHospital.id}, ${doctorHospital2.id}, ${doctorHospital3.id}`);

		// 8. Criar agenda do médico
		log("Criando agenda do médico...");
		const schedules = await Promise.all([
			db.doctorSchedule.create({
				data: {
					id: uuidv4(),
					doctorId: doctor.id,
					weekDay: 1, // Segunda-feira
					startTime: "08:00",
					endTime: "18:00",
					isEnabled: true,
				},
			}),
			db.doctorSchedule.create({
				data: {
					id: uuidv4(),
					doctorId: doctor.id,
					weekDay: 3, // Quarta-feira
					startTime: "08:00",
					endTime: "18:00",
					isEnabled: true,
				},
			}),
			db.doctorSchedule.create({
				data: {
					id: uuidv4(),
					doctorId: doctor.id,
					weekDay: 5, // Sexta-feira
					startTime: "08:00",
					endTime: "12:00",
					isEnabled: true,
				},
			}),
		]);
		log(`Agenda do médico criada com ${schedules.length} dias configurados`);

		// 9. Criar documento do médico
		log("Criando documento do médico...");
		const doctorDocument = await db.doctorDocument.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				type: "CRM",
				fileName: "crm_123456.pdf",
				fileUrl: "https://storage.example.com/documents/crm_123456.pdf",
				status: "APPROVED",
			},
		});
		log(`Documento do médico criado: ${doctorDocument.id}`);

		// 10. Criar Pacientes
		log("Criando pacientes...");
		const patient = await db.patient.create({
			data: {
				id: uuidv4(),
				userId: patientUser.id,
				cpf: "12345678900",
				birthDate: new Date("1980-05-15"),
				gender: "Feminino",
				maritalStatus: "Casado(a)",
				address: {
					street: "Rua dos Girassóis, 123",
					city: "São Paulo",
					state: "SP",
					zip: "04567-000",
					country: "Brasil",
				},
				allergies: ["Penicilina"],
				chronicConditions: ["Hipertensão"],
				hasActiveSubscription: false,
			},
		});
		log(`Paciente criado: ${patient.id}`);

		const patient2 = await db.patient.create({
			data: {
				id: uuidv4(),
				userId: patientUser2.id,
				cpf: "98765432100",
				birthDate: new Date("1990-08-20"),
				gender: "Masculino",
				maritalStatus: "Solteiro(a)",
				address: {
					street: "Avenida das Flores, 456",
					city: "São Paulo",
					state: "SP",
					zip: "01234-000",
					country: "Brasil",
				},
				allergies: ["Dipirona"],
				chronicConditions: ["Diabetes"],
				hasActiveSubscription: true,
			},
		});
		log(`Paciente 2 criado: ${patient2.id}`);

		// 11. Criar Assinatura Ativa para o Paciente 2
		log("Criando assinatura ativa...");
		const subscription = await db.patientSubscription.create({
			data: {
				id: uuidv4(),
				patientId: patient2.id,
                planId: "zapvida-sempre",
                planName: "ZapVida Sempre",
				planPrice: new Decimal(99.90),
				asaasSubscriptionId: "sub_123456789",
				status: "ACTIVE",
				startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 dias atrás
				endDate: null,
				nextBillingDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 dias no futuro
				// Campos temporariamente removidos até schema ser aplicado
				// currentPeriodStart: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
				// currentPeriodEnd: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
				// cycle: "MONTHLY",
				// gracePeriodDays: 3,
				// retryCount: 0,
				// maxRetryCount: 3,
				// renewalCount: 1,
				// paymentMethod: "CREDIT_CARD",
				// externalCustomerId: "cus_123456789",
				consultationsIncluded: 2,
				consultationsUsed: 1,
				lastResetDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
			},
		});
		log(`Assinatura criada: ${subscription.id}`);

		// 12. Criar documento médico do paciente
		log("Criando documento médico do paciente...");
		const medicalDocument = await db.medicalDocument.create({
			data: {
				id: uuidv4(),
				patientId: patient.id,
				name: "Exames recentes",
				type: "exame",
				fileUrl: "https://storage.example.com/docs/exame_123.pdf",
			},
		});
		log(`Documento médico do paciente criado: ${medicalDocument.id}`);

		// 13. Criar Consulta Agendada (Futura)
		log("Criando consulta futura...");
		const futureAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				patientId: patient.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias no futuro
				consultType: "VIDEO",
				duration: 30,
				status: "SCHEDULED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(250.0),
				paymentStatus: "PENDING",
				symptoms: "Dor no peito ao fazer exercícios",
				chatEnabled: true,
				isOnDuty: false,
			},
		});
		log(`Consulta futura criada: ${futureAppointment.id}`);

		// 14. Criar Consulta de Plantão (Sem médico pré-definido)
		log("Criando consulta de plantão...");
		const plantaoAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: null, // Sem médico pré-definido
				patientId: patient.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 horas no futuro
				consultType: "VIDEO",
				duration: 30,
				status: "WAITING_ON_DUTY",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(375.0), // Preço com multiplicador de plantão
				paymentStatus: "PAID",
				symptoms: "Dor de cabeça intensa",
				chatEnabled: true,
				isOnDuty: true,
				urgencyLevel: "HIGH",
				queuePosition: 1,
			},
		});
		log(`Consulta de plantão criada: ${plantaoAppointment.id}`);

		// 15. Criar Consulta via Assinatura
		log("Criando consulta via assinatura...");
		const subscriptionAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				patientId: patient2.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 dias no futuro
				consultType: "VIDEO",
				duration: 30,
				status: "SCHEDULED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(0.0), // Gratuita via assinatura
				paymentStatus: "PAID",
				symptoms: "Consulta de rotina",
				chatEnabled: true,
				isOnDuty: false,
			},
		});
		log(`Consulta via assinatura criada: ${subscriptionAppointment.id}`);

		// 16. Criar uso de consulta da assinatura
		log("Criando uso de consulta da assinatura...");
		const consultationUsage = await db.consultationUsage.create({
			data: {
				id: uuidv4(),
				patientId: patient2.id,
				subscriptionId: subscription.id,
				appointmentId: subscriptionAppointment.id,
				type: "SUBSCRIPTION",
			},
		});
		log(`Uso de consulta criado: ${consultationUsage.id}`);

		// 17. Criar mensagem para consulta futura
		log("Criando mensagem para consulta futura...");
		const message = await db.message.create({
			data: {
				id: uuidv4(),
				appointmentId: futureAppointment.id,
				senderId: patientUser.id,
				type: "TEXT",
				content: "Olá doutor, estou enviando meus exames prévios.",
			},
		});
		log(`Mensagem criada: ${message.id}`);

		// 18. Criar notificações para consulta futura
		log("Criando notificações para consulta futura...");
		const notifications = await Promise.all([
			db.notification.create({
				data: {
					id: uuidv4(),
					userId: doctorUser.id,
					appointmentId: futureAppointment.id,
					type: "APPOINTMENT_CREATED",
					title: "Nova consulta",
					message: `Consulta agendada com ${patientUser.name}`,
					read: false,
				},
			}),
			db.notification.create({
				data: {
					id: uuidv4(),
					userId: patientUser.id,
					appointmentId: futureAppointment.id,
					type: "APPOINTMENT_CREATED",
					title: "Consulta confirmada",
					message: `Sua consulta com Dr. ${doctorUser.name} foi agendada`,
					read: true,
				},
			}),
		]);
		log(`${notifications.length} notificações criadas`);

		// 19. Criar Consulta Passada (Completada)
		log("Criando consulta passada...");
		const pastAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				patientId: patient.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 dias no passado
				consultType: "VIDEO",
				duration: 30,
				status: "COMPLETED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(250.0),
				paymentStatus: "PAID",
				symptoms: "Pressão alta",
				chatEnabled: true,
				isOnDuty: false,
			},
		});
		log(`Consulta passada criada: ${pastAppointment.id}`);

		// 20. Criar prescrição para consulta passada
		log("Criando prescrição para consulta passada...");
		const prescription = await db.prescription.create({
			data: {
				id: uuidv4(),
				appointmentId: pastAppointment.id,
				content: {
					medications: [
						{
							name: "Losartana",
							dosage: "50mg",
							instructions: "1 comprimido por dia, pela manhã",
							duration: "30 dias",
						},
					],
				},
				pdfUrl: "https://storage.example.com/prescriptions/rx_12345.pdf",
				status: "active",
			},
		});
		log(`Prescrição criada: ${prescription.id}`);

		// 21. Criar avaliação para consulta passada
		log("Criando avaliação para consulta passada...");
		const evaluation = await db.doctorEvaluation.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				patientId: patient.id,
				appointmentId: pastAppointment.id,
				rating: 5,
				comment: "Excelente atendimento, médico muito atencioso",
			},
		});
		log(`Avaliação criada: ${evaluation.id}`);

		// 22. Criar transação para consulta passada
		log("Criando transação para consulta passada...");
		const transaction = await db.transaction.create({
			data: {
				id: uuidv4(),
				appointmentId: pastAppointment.id,
				amount: new Decimal(250.0),
				platformFee: new Decimal(25.0),
				doctorAmount: new Decimal(225.0),
				status: "PAID",
				paymentMethod: "CREDIT_CARD",
				asaasId: "pay_12345678901234567890",
				dueDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
				paidAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
				doctorId: doctor.id,
				hospitalId: hospital.id,
			},
		});
		log(`Transação criada: ${transaction.id}`);

		// 23. Criar transação para plantão
		log("Criando transação para plantão...");
		const plantaoTransaction = await db.transaction.create({
			data: {
				id: uuidv4(),
				appointmentId: plantaoAppointment.id,
				amount: new Decimal(375.0),
				platformFee: new Decimal(37.5),
				doctorAmount: new Decimal(337.5),
				status: "PAID",
				paymentMethod: "PIX",
				asaasId: "pay_plantao_123456789",
				dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000),
				paidAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
				doctorId: null,
				hospitalId: hospital.id,
			},
		});
		log(`Transação de plantão criada: ${plantaoTransaction.id}`);

		// 24. Criar Template de Ficha Pré-Anestésica
		log("Criando template de ficha pré-anestésica...");
		const template = await db.preAnestheticTemplate.create({
			data: {
				id: uuidv4(),
				hospitalId: hospital.id,
				name: "Template Padrão de Avaliação Pré-Anestésica",
				description: "Template para cirurgias gerais",
				fields: {
					clinical_evaluation: {
						history: true,
						physical_examination: true,
						vital_signs: true,
						airways_evaluation: true,
					},
					physical_exam: {
						cardiovascular: true,
						respiratory: true,
						neurological: true,
					},
					lab_tests: {
						blood_count: true,
						coagulation: true,
						biochemistry: true,
					},
					conclusion: {
						asa_classification: true,
						recommendations: true,
					},
				},
				isActive: true,
			},
		});
		log(`Template de ficha pré-anestésica criado: ${template.id}`);

		// 25. Criar Certificado Digital para o Médico
		log("Criando certificado digital para o médico...");
		const certificate = await db.digitalCertificate.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				type: "VIDAAS_A3",
				metadata: {
					issuer: "ICP-Brasil",
					serialNumber: "12345678901234567890",
					providerInfo: "VIDaaS",
				},
				validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 ano no futuro
				isActive: true,
			},
		});
		log(`Certificado digital criado: ${certificate.id}`);

		// 26. Criar configurações de comissão
		log("Criando configurações de comissão...");
		const commissionSettings = await db.commissionSettings.create({
			data: {
				id: uuidv4(),
				platformFeeRate: new Decimal(10.0), // 10%
				minFeeAmount: new Decimal(5.0),
				maxFeeAmount: new Decimal(50.0),
				isActive: true,
			},
		});
		log(`Configurações de comissão criadas: ${commissionSettings.id}`);

		// 27. Criar mais consultas para testes
		log("Criando consultas adicionais para testes...");

		// Consulta com dermatologista
		const dermatologyAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor2.id, // Usando a pediatra para simular dermatologia
				patientId: patient2.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 dias no futuro
				consultType: "VIDEO",
				duration: 45,
				status: "SCHEDULED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(200.0),
				paymentStatus: "PENDING",
				symptoms: "Manchas na pele que coçam",
				chatEnabled: true,
				isOnDuty: false,
			},
		});

		// Consulta psiquiátrica
		const psychiatryAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor3.id,
				patientId: patient.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 dias no futuro
				consultType: "VIDEO",
				duration: 60,
				status: "SCHEDULED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(300.0),
				paymentStatus: "PENDING",
				symptoms: "Ansiedade e insônia",
				chatEnabled: true,
				isOnDuty: false,
			},
		});

		// Consulta cancelada
		const canceledAppointment = await db.appointment.create({
			data: {
				id: uuidv4(),
				doctorId: doctor.id,
				patientId: patient.id,
				hospitalId: hospital.id,
				scheduledAt: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 dia no futuro
				consultType: "AUDIO",
				duration: 30,
				status: "CANCELED",
				appointmentType: "TELEMEDICINE",
				amount: new Decimal(250.0),
				paymentStatus: "REFUNDED",
				symptoms: "Dor no ombro",
				chatEnabled: false,
				isOnDuty: false,
			},
		});

		log(`Consultas adicionais criadas: Dermatologia (${dermatologyAppointment.id}), Psiquiatria (${psychiatryAppointment.id}), Cancelada (${canceledAppointment.id})`);

		// 28. Criar mais pacientes para testes
		log("Criando pacientes adicionais...");

		const patientUser3 = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Fernanda Lima",
				role: "PATIENT",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+5511998765434",
				emailVerified: true,
			},
		});

		const patient3 = await db.patient.create({
			data: {
				id: uuidv4(),
				userId: patientUser3.id,
				cpf: "11122233344",
				birthDate: new Date("1975-03-10"),
				gender: "Feminino",
				maritalStatus: "Divorciado(a)",
				address: {
					street: "Rua das Palmeiras, 789",
					city: "São Paulo",
					state: "SP",
					zip: "05678-000",
					country: "Brasil",
				},
				allergies: ["Iodo", "Látex"],
				chronicConditions: ["Asma", "Rinite"],
				medications: ["Ventolin", "Allegra"],
				hasActiveSubscription: false,
			},
		});

		const patientUser4 = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Roberto Almeida",
				role: "PATIENT",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: false, // Paciente em onboarding
				phone: "+5511998765435",
				emailVerified: false,
			},
		});

		const patient4 = await db.patient.create({
			data: {
				id: uuidv4(),
				userId: patientUser4.id,
				cpf: "55566677788",
				birthDate: new Date("1985-12-25"),
				gender: "Masculino",
				maritalStatus: "Casado(a)",
				address: {
					street: "Avenida dos Ipês, 321",
					city: "São Paulo",
					state: "SP",
					zip: "07890-000",
					country: "Brasil",
				},
				allergies: [],
				chronicConditions: [],
				medications: [],
				hasActiveSubscription: false,
			},
		});

		log(`Pacientes adicionais criados: ${patient3.id}, ${patient4.id}`);

		// 29. Criar usuário secretária
		log("Criando usuário secretária...");
		const secretaryUser = await db.user.create({
			data: {
				id: uuidv4(),
				email: "<EMAIL>",
				name: "Maria Secretária",
				role: "SECRETARY",
				hashedPassword: await hashPassword("password123"),
				onboardingComplete: true,
				phone: "+************",
				emailVerified: true,
			},
		});

		// Adicionar secretária ao time do hospital
		await db.teamMembership.create({
			data: {
				id: uuidv4(),
				teamId: hospitalTeam.id,
				userId: secretaryUser.id,
				role: "SECRETARY",
				isCreator: false,
			},
		});
		log(`Secretária criada e adicionada ao time: ${secretaryUser.id}`);

		log("Seed concluído com sucesso!");
		log("");
		log("📋 Dados criados para teste:");
		log("   • Usuários: Admin, 3 Médicos, 4 Pacientes, 1 Secretária");
		log("   • Hospital: São Lucas com departamento de Cardiologia");
		log("   • Especialidades: Cardiologia, Clínica Geral, Pediatria, Dermatologia, Neurologia, Psiquiatria");
		log("   • Médicos: Dr. João Silva (Cardiologista), Dra. Ana Costa (Pediatra), Dr. Carlos Mendes (Psiquiatra)");
		log("   • Consultas: Agendada, Plantão, Via Assinatura, Passada, Dermatologia, Psiquiatria, Cancelada");
		log("   • Assinatura: Paciente 2 com plano ativo");
		log("   • Transações: Consulta regular e plantão");
		log("   • Documentos: CRM, exames, prescrições, certificados digitais");
		log("   • Cenários: Plantão, Agendamento, Assinatura, Cancelamento, Onboarding");
		log("");
		log("🔗 URLs de teste:");
		log("   • Adminer: http://localhost:8080");
		log("   • pgAdmin: http://localhost:5050");
		log("   • MinIO: http://localhost:9001");
		log("   • MailHog: http://localhost:8025");
		log("");
		log("🧪 Para testar os fluxos:");
		log("   • Plantão: /pay/plantao?urgencyLevel=high");
		log("   • Agendamento: /pay/[doctorId]?date=2024-01-15&time=14:00");
		log("   • Assinatura: /pay/assinatura");
		log("   • Onboarding: /onboarding (<EMAIL>)");
		log("");
		log("👥 Usuários de teste:");
		log("   • Admin: <EMAIL> / password123");
		log("   • Médico Cardiologista: <EMAIL> / password123");
		log("   • Médica Pediatra: <EMAIL> / password123");
		log("   • Médico Psiquiatra: <EMAIL> / password123");
		log("   • Paciente 1: <EMAIL> / password123");
		log("   • Paciente 2 (com assinatura): <EMAIL> / password123");
		log("   • Paciente 3: <EMAIL> / password123");
		log("   • Paciente 4 (onboarding): <EMAIL> / password123");
		log("   • Secretária: <EMAIL> / password123");

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		log(`Erro durante execução do seed: ${errorMessage}`);
		console.error(error);
		throw error;
	}
}

main()
	.catch((e) => {
		console.error("Erro ao executar seed:", e);
		process.exit(1);
	})
	.finally(async () => {
		await db.$disconnect();
	});
