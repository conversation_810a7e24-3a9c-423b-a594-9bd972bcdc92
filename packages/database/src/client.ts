import { PrismaClient } from "@prisma/client";

// Cliente mock para quando o banco não estiver disponível
const createMockClient = () => {
	return {
		$connect: async () => console.log("Cliente mock conectado"),
		$disconnect: async () => console.log("Cliente mock desconectado"),
		$queryRaw: async () => [],
		$executeRaw: async () => 0,
		$transaction: async (fn: any) => fn({}),
		user: {
			findMany: async () => [],
			findUnique: async () => null,
			create: async () => ({}),
			update: async () => ({}),
			delete: async () => ({}),
		},
		teamMembership: {
			findMany: async () => [],
			findUnique: async () => null,
			create: async () => ({}),
			update: async () => ({}),
			delete: async () => ({}),
		},
		// Adicionar outros modelos conforme necessário
	} as any;
};

const prismaClientSingleton = () => {
	// Only create PrismaClient instance on the server, not in the browser
	if (typeof window === 'undefined') {
		// Verificar se as variáveis de ambiente necessárias estão definidas
		if (!process.env.DATABASE_URL) {
			console.error("DATABASE_URL não está definida, usando cliente mock");
			return createMockClient();
		}

		try {
			const client = new PrismaClient({
				log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
				errorFormat: 'pretty',
			});

			// Adicionar tratamento de erro para problemas de conexão
			client.$connect()
				.then(() => {
					console.log("Conectado ao banco de dados com sucesso");
				})
				.catch((error) => {
					console.error("Erro ao conectar ao banco de dados:", error);
				});

			return client;
		} catch (error) {
			console.error("Erro ao criar cliente Prisma:", error);
			return createMockClient();
		}
	}

	// Return a mock or empty client for browser environment
	return createMockClient();
};

declare global {
	var prisma: undefined | ReturnType<typeof prismaClientSingleton>;
}

// biome-ignore lint/suspicious/noRedeclare: <explanation>
let prisma: any;

try {
	prisma = globalThis.prisma ?? prismaClientSingleton();
	if (process.env.NODE_ENV !== "production") globalThis.prisma = prisma;
} catch (error) {
	console.error("Erro ao inicializar cliente Prisma:", error);
	// Criar um cliente mock para evitar crashes
	prisma = createMockClient();
}

export { prisma as db };
