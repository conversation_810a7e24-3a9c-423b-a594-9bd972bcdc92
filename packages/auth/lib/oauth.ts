import { OAuth2RequestError } from "arctic";
import { db } from "database";
import { logger } from "logs";
import { cookies } from "next/headers";
import { createSessionCookie } from "./cookies";
import { createSession, generateSessionToken } from "./sessions";

export function createOauthRedirectHandler(
	providerId: string,
	createAuthorizationTokens: () => {
		state: string;
		codeVerifier?: string;
		url: URL;
	},
) {
	return async () => {
		const cookieStore = await cookies();
		const { url, state, codeVerifier } = createAuthorizationTokens();

		cookieStore.set(`${providerId}_oauth_state`, state, {
			httpOnly: true,
			secure: process.env.NODE_ENV !== "development",
			path: "/",
			maxAge: 60 * 60,
			sameSite: "lax",
		});

		if (codeVerifier) {
			// store code verifier as cookie
			cookieStore.set("code_verifier", codeVerifier, {
				secure: true, // set to false in localhost
				path: "/",
				httpOnly: true,
				maxAge: 60 * 10, // 10 min
			});
		}

		return Response.redirect(url);
	};
}

export function createOauthCallbackHandler(
	providerId: string,
	validateAuthorizationCode: (
		code: string,
		codeVerifier?: string,
	) => Promise<{
		email: string;
		name?: string;
		id: string;
		avatar?: string;
	}>,
) {
	return async (req: Request) => {
		const cookieStore = await cookies();
		const url = new URL(req.url);
		const code = url.searchParams.get("code");
		const state = url.searchParams.get("state");
		const storedState =
			cookieStore.get(`${providerId}_oauth_state`)?.value ?? null;
		const storedCodeVerifier = cookieStore.get("code_verifier")?.value ?? null;

		if (!code || !state || !storedState || state !== storedState) {
			logger.error(
				`Invalid state or code parameters for provider ${providerId}`,
			);

			return new Response(null, {
				status: 400,
			});
		}

		try {
			const oauthUser = await validateAuthorizationCode(
				code,
				storedCodeVerifier ?? undefined,
			);

			const existingUser = await db.user.findFirst({
				where: {
					OR: [
						{
							oauthAccounts: {
								some: {
									providerId,
									providerUserId: oauthUser.id,
								},
							},
						},
						{
							email: oauthUser.email.toLowerCase(),
						},
					],
				},
				select: {
					id: true,
					onboardingComplete: true,
					role: true,
					oauthAccounts: {
						select: {
							providerId: true,
						},
					},
				},
			});

			if (existingUser) {
				if (!existingUser.oauthAccounts.some(
					(account) => account.providerId === providerId,
				)) {
					console.log("Creating new OAuth account record:", {
						providerId,
						providerUserId: oauthUser.id,
						userId: existingUser.id
					});

					try {
						await db.userOauthAccount.create({
							data: {
								providerId,
								providerUserId: oauthUser.id,
								userId: existingUser.id,
							},
						});
						console.log("OAuth account record created successfully");
					} catch (createError) {
						console.error("Error creating OAuth account record:", createError);
						throw createError;
					}
				}

				const sessionToken = generateSessionToken();
				await createSession(sessionToken, existingUser.id);
				cookieStore.set(createSessionCookie(sessionToken));

				// Verificar se o usuário precisa completar o onboarding
				if (!existingUser.onboardingComplete) {
					// Determinar para qual página de onboarding direcionar com base no role
					let onboardingPath = "/onboarding";

					if (existingUser.role === "DOCTOR") {
						onboardingPath = "/onboarding/doctor";
					} else if (existingUser.role === "HOSPITAL") {
						onboardingPath = "/onboarding/hospital";
					} else {
						// Verificar se o usuário já possui registro na tabela Patient
						const patientExists = await db.patient.findUnique({
							where: { userId: existingUser.id },
						});

						if (!patientExists) {
							onboardingPath = "/onboarding/patient";
						}
					}

					return new Response(null, {
						status: 302,
						headers: {
							Location: onboardingPath,
						},
					});
				}

				return new Response(null, {
					status: 302,
					headers: {
						Location: "/app",
					},
				});
			}

			// Verify if there's a pending invitation for this email to preserve role
			const pendingInvitation = await db.teamInvitation.findFirst({
				where: {
					email: oauthUser.email.toLowerCase(),
					expiresAt: {
						gt: new Date(),
					},
				},
			});

			const userRole = pendingInvitation &&
				(pendingInvitation.role === "DOCTOR" || pendingInvitation.role === "ADMIN")
				? pendingInvitation.role
				: "PATIENT";

			console.log(`OAuth signup: Using role ${userRole} for user with email ${oauthUser.email}`);

			// Criar novo usuário
			const newUser = await db.user.create({
				data: {
					email: oauthUser.email.toLowerCase(),
					emailVerified: true,
					name: oauthUser.name,
					avatarUrl: oauthUser.avatar,
					role: userRole,
					onboardingComplete: false,
				},
			});

			await db.userOauthAccount.create({
				data: {
					providerId,
					providerUserId: oauthUser.id,
					userId: newUser.id,
				},
			});

			// Criar um perfil de paciente básico
			try {
				await db.patient.create({
					data: {
						userId: newUser.id,
						cpf: "", // Será preenchido durante o onboarding
						birthDate: new Date(), // Temporário, será atualizado no onboarding
						gender: "PREFER_NOT_TO_SAY",
						address: {}, // Objeto vazio para o campo JSON
						allergies: [],
						chronicConditions: [],
						medications: [],
						familyHistory: [],
					},
				});
				console.log("Perfil de paciente básico criado com sucesso");
			} catch (error) {
				console.error("Erro ao criar perfil de paciente básico:", error);
				// Continuar mesmo com erro, o usuário será redirecionado para onboarding
			}

			const sessionToken = generateSessionToken();
			await createSession(sessionToken, newUser.id);
			cookieStore.set(createSessionCookie(sessionToken));

			// Redirecionar para onboarding de paciente (padrão para novos usuários)
			return new Response(null, {
				status: 302,
				headers: {
					Location: "/onboarding/patient",
				},
			});

		} catch (e) {
			logger.error("Could not handle oAuth request", e);

			if (e instanceof OAuth2RequestError) {
				return new Response(null, {
					status: 400,
				});
			}

			return new Response(null, {
				status: 500,
			});
		}
	};
}
