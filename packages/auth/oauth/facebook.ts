import { Facebook, generateState } from "arctic";
import { getBaseUrl } from "utils";
import {
  createOauthCallbackHandler,
  createOauthRedirectHandler,
} from "../lib/oauth";

// Configuração da autenticação do Facebook com a biblioteca Arctic
export const facebookAuth = new Facebook(
  process.env.FACEBOOK_CLIENT_ID as string,
  process.env.FACEBOOK_CLIENT_SECRET as string,
  new URL("/api/oauth/facebook/callback", getBaseUrl()).toString()
);

const FACEBOOK_PROVIDER_ID = "facebook";

// Interface para os dados do usuário retornados pelo Facebook
type FacebookUser = {
  id: string;
  name: string;
  email: string;
  picture?: {
    data: {
      url: string;
      height?: number;
      width?: number;
      is_silhouette?: boolean;
    };
  };
};

// Função para obter manualmente o token de acesso se a biblioteca Arctic falhar
async function getTokenManually(code: string): Promise<string> {
  const redirectUri = new URL("/api/oauth/facebook/callback", getBaseUrl()).toString();
  const tokenUrl = new URL("https://graph.facebook.com/v17.0/oauth/access_token");

  tokenUrl.searchParams.append("client_id", process.env.FACEBOOK_CLIENT_ID as string);
  tokenUrl.searchParams.append("client_secret", process.env.FACEBOOK_CLIENT_SECRET as string);
  tokenUrl.searchParams.append("redirect_uri", redirectUri);
  tokenUrl.searchParams.append("code", code);

  console.log("Manual token request URL:", tokenUrl.toString());

  const response = await fetch(tokenUrl.toString());
  const data = await response.json();

  if (!data.access_token) {
    console.error("Token error response:", data);
    throw new Error("Failed to obtain access token");
  }

  return data.access_token;
}

// Handler para redirecionamento OAuth
export const facebookRouteHandler = createOauthRedirectHandler(
  FACEBOOK_PROVIDER_ID,
  () => {
    const state = generateState();
    const url = facebookAuth.createAuthorizationURL(state, ["email", "public_profile"]);

    return {
      state,
      url
    };
  }
);

// Handler para callback OAuth
export const facebookCallbackRouteHandler = createOauthCallbackHandler(
  FACEBOOK_PROVIDER_ID,
  async (code) => {
    try {
      // Limpar o código se contiver fragmentos
      if (code.includes('#')) {
        code = code.split('#')[0];
      }

      console.log("Processing OAuth callback with code:", code);

      let accessToken;

      try {
        // Tente primeiro com a biblioteca Arctic
        const tokens = await facebookAuth.validateAuthorizationCode(code);
        accessToken = tokens.accessToken();
        console.log("Access token obtained through Arctic");
      } catch (arcticError) {
        // Se falhar, tente o método manual
        console.error("Arctic token validation failed:", arcticError);
        console.log("Attempting manual token retrieval");
        accessToken = await getTokenManually(code);
        console.log("Access token obtained manually");
      }

      if (!accessToken) {
        throw new Error("Failed to obtain access token");
      }

      const searchParams = new URLSearchParams();
      searchParams.set("access_token", accessToken);
      searchParams.set("fields", ["id", "name", "picture.type(large)", "email"].join(","));

      console.log("Fetching user data from Facebook Graph API");
      const response = await fetch(
        "https://graph.facebook.com/me?" + searchParams.toString()
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Facebook API error:", errorText);
        throw new Error(`Failed to fetch user data: ${errorText}`);
      }

      const responseText = await response.text();

      try {
        const userData = JSON.parse(responseText) as FacebookUser;
        console.log("Facebook user data:", JSON.stringify(userData, null, 2));

        // Verificar se temos os dados mínimos necessários
        if (!userData.id) {
          throw new Error(`Invalid user data format: ${JSON.stringify(userData)}`);
        }

        // Garantir que o email seja definido, mesmo que seja um fallback
        const userEmail = userData.email || `${userData.id}@facebook.com`;
        console.log("User email to be used:", userEmail);

        // Obter a URL do avatar se disponível
        const avatarUrl = userData.picture?.data?.url || null;

        return {
          id: userData.id,
          email: userEmail,
          name: userData.name || '',
          avatar: avatarUrl
        };
      } catch (parseError) {
        console.error("Error parsing Facebook response:", parseError);
        console.error("Raw response:", responseText);
        throw new Error("Failed to parse Facebook user data");
      }
    } catch (error) {
      console.error("Facebook OAuth error:", {
        error,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }
);
