{"admin": {"menu": {"general": "G<PERSON>", "users": "Usuários"}, "users": {"adminRole": "Administrador", "delete": "Excluir", "deleteUser": {"deleted": "Usuário excluído com sucesso!", "deleting": "Excluindo usuário...", "notDeleted": "Não foi possível excluir o usuário. Por favor, tente novamente."}, "emailVerified": {"verified": "E-mail verificado", "waiting": "Aguardando verificação de e-mail"}, "impersonate": "Personificar", "impersonation": {"impersonating": "Personificando como {name}...", "unimpersonating": "Encerrando personifica<PERSON>..."}, "loading": "Carregando usuários...", "resendVerificationMail": {"error": "Não foi possível reenviar o e-mail de verificação. Por favor, tente novamente.", "submitting": "Reenviando e-mail de verificação...", "success": "E-mail de verificação enviado com sucesso.", "title": "Reenviar e-mail de verificação"}, "search": "Buscar por nome ou e-mail...", "title": "Gerenciar usuários"}}, "auth": {"confirmation": {"close": "<PERSON><PERSON><PERSON> esta janela", "message": "Você pode fechar esta janela e continuar na janela anterior.", "title": "Você entrou com sucesso!"}, "continueWithProvider": "Continuar com {provider}", "forgotPassword": {"backToSignin": "Voltar para o login", "email": "E-mail", "hints": {"linkNotSent": {"message": "Não foi possível enviar o link para redefinir sua senha. Por favor, tente novamente mais tarde.", "title": "Link não enviado"}, "linkSent": {"message": "Enviamos um link para seu e-mail. Por favor, verifique sua caixa de entrada.", "title": "Link enviado"}}, "message": "Digite seu e-mail e enviaremos um link para você redefinir sua senha.", "submit": "Enviar link", "title": "Esqueceu sua senha?"}, "invalidToken": {"message": "O link é inválido ou expirou. Por favor, tente fazer login novamente.", "title": "To<PERSON> in<PERSON>lid<PERSON>"}, "login": {"createAnAccount": "Criar uma conta", "dontHaveAnAccount": "Ainda não tem uma conta?", "email": "E-mail", "forgotPassword": "Esque<PERSON>u a senha?", "hints": {"invalidCredentials": "O e-mail ou senha que você digitou são inválidos. Por favor, tente novamente.", "linkNotSent": {"message": "Não foi possível enviar um link mágico para seu e-mail. Por favor, tente novamente mais tarde.", "title": "Link não enviado"}, "linkSent": {"message": "Enviamos um link para seu e-mail. Por favor, verifique sua caixa de entrada.", "title": "Link enviado"}}, "modes": {"magicLink": "<PERSON>", "password": "<PERSON><PERSON>"}, "password": "<PERSON><PERSON>", "sendMagicLink": "Enviar link mágico", "submit": "Entrar", "subtitle": "Digite suas credenciais para acessar sua conta ZapVida.", "title": "Entre na sua conta"}, "setup": {}, "signup": {"alreadyHaveAccount": "Já possui uma conta?", "email": "E-mail", "hints": {"signupFailed": "Não foi possível criar sua conta. Por favor, tente novamente mais tarde.", "verifyEmail": "Enviamos um link para verificar seu e-mail. Por favor, verifique sua caixa de entrada."}, "message": "Ficamos felizes por você querer se juntar à ZapVida. Preencha o formulário abaixo para criar sua conta e ter acesso rápido a consultas médicas.", "name": "Nome", "password": "<PERSON><PERSON>", "passwordHint": "Use pelo menos 8 caracteres, uma letra mai<PERSON>, uma minúscula, um número e um caractere especial.", "signIn": "Entrar", "submit": "C<PERSON><PERSON> conta", "title": "Crie sua conta ZapVida"}, "teamInvitation": {"description": "Você foi convidado(a) para se juntar a uma equipe. Ao entrar ou criar uma conta, você aceitará automaticamente o convite e fará parte da equipe.", "title": "Aceitar convite para equipe"}, "verifyOtp": {"errors": {"otpTooShort": "O código OTP é muito curto."}, "hints": {"verificationFailed": "O código que você digitou é inválido. Por favor, tente novamente."}, "message": "Você recebeu um e-mail! Verifique sua caixa de entrada para o código OTP que enviamos. Você também pode clicar no link do e-mail para verificação automática.", "otp": "Código <PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Verifique seu e-mail"}}, "blog": {"description": "<PERSON><PERSON> as últimas notícias e dicas de saúde da ZapVida.", "title": "Blog ZapVida"}, "changelog": {"description": "Mantenha-se atualizado com as últimas melhorias na plataforma ZapVida.", "title": "Novidades da Plataforma"}, "common": {"confirmation": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "locales": {}, "menu": {"blog": "Blog", "changelog": "Novidades", "dashboard": "<PERSON><PERSON>", "docs": "Documentação", "faq": "D<PERSON>vid<PERSON>e<PERSON>", "login": "Entrar", "pricing": "Planos"}, "tableOfContents": {"title": "Nesta página"}}, "createTeam": {"name": "Nome da equipe", "notifications": {"error": "Não foi possível criar sua equipe. Por favor, tente novamente mais tarde.", "success": "Sua equipe foi criada. Agora você pode convidar membros."}, "submit": "Criar equipe", "title": "Criar uma equipe"}, "dashboard": {"menu": {"admin": "Admin", "aiDemo": "Demonstração IA", "dashboard": "<PERSON><PERSON> Painel ZapVida", "settings": "Configurações"}, "sidebar": {"createTeam": "Criar nova equipe"}, "subtitle": "Acesse suas informações e agende consultas na ZapVida.", "userMenu": {"accountSettings": "Configurações da conta", "colorMode": "<PERSON><PERSON> de <PERSON>", "documentation": "Documentação", "language": "Idioma", "logout": "<PERSON><PERSON>", "unimpersonate": "Encerrar personifica<PERSON>"}, "welcome": "<PERSON><PERSON><PERSON>, {name}!", "roles": {"DOCTOR": "MÉDICO", "PATIENT": "PACIENTE", "ADMIN": "ADMINISTRADOR", "USER": "USUÁRIO", "SECRETARY": "SECRETÁRIO", "HOSPITAL": "HOSPITAL"}}, "faq": {"description": "Tem alguma dúvida sobre a ZapVida ou telemedicina? Nós te ajudamos.", "title": "D<PERSON>vid<PERSON>e<PERSON>"}, "mail": {"common": {"openLinkInBrowser": "Se preferir abrir o link em outro navegador, copie e cole este endereço:", "otp": "Código de uso único", "useLink": "ou use o seguinte link:"}, "emailChange": {"body": "<PERSON><PERSON><PERSON> {name},\nvocê alterou seu e-mail na ZapVida. Por favor, clique no link abaixo para confirmar seu novo endereço de e-mail.", "confirmEmail": "Confirmar e-mail", "subject": "Confirme a alteração do seu e-mail na ZapVida"}, "forgotPassword": {"body": "<PERSON><PERSON><PERSON> {name},\nvocê solicitou a redefinição de senha na ZapVida.\n\nVocê pode inserir manualmente o código de uso único abaixo na aplicação", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "subject": "Redefina sua senha da ZapVida"}, "magicLink": {"body": "<PERSON><PERSON><PERSON> {name},\nvocê solicitou um link de login para a ZapVida.\n\nVocê pode inserir manualmente o código de uso único abaixo na aplicação", "login": "Entrar na ZapVida", "subject": "Seu link de acesso à ZapVida"}, "newUser": {"body": "<PERSON><PERSON><PERSON> {name},\nobrigado por se cadastrar na ZapVida.\n\nPara começar a usar nossa plataforma de telemedicina, por favor, confirme seu endereço de e-mail clicando no link abaixo.", "confirmEmail": "Confirmar e-mail", "subject": "Bem-vindo(a) à ZapVida!"}, "newsletterSignup": {"body": "Obrigado por se inscrever na newsletter da ZapVida. Manteremos você atualizado com as últimas notícias e dicas de saúde.", "subject": "Bem-vindo(a) à nossa newsletter!"}, "teamInvitation": {"body": "Você foi convidado(a) para se juntar à equipe {teamName} na ZapVida. Clique no botão abaixo ou copie e cole o link no seu navegador de preferência para aceitar o convite.", "headline": "Junte-se à equipe {teamName}", "join": "Juntar-se à equipe", "subject": "Você foi convidado(a) para uma equipe na ZapVida"}}, "newsletter": {"email": "E-mail", "hints": {"success": {"message": "Obrigado por assinar nossa newsletter. Manteremos você informado.", "title": "Inscrição realizada"}}, "submit": "Inscrever-se", "subtitle": "Receba dicas de saúde e novidades da ZapVida diretamente no seu e-mail.", "title": "<PERSON>sine nossa Newsletter"}, "onboarding": {"account": {"avatar": "Foto de perfil", "avatarDescription": "Clique no círculo ou arraste uma imagem para cá para carregar sua foto de perfil.", "name": "Nome", "title": "Complete seu perfil ZapVida"}, "back": "Voltar", "complete": "Concluir", "continue": "<PERSON><PERSON><PERSON><PERSON>", "message": "Faltam só alguns passos para você começar a usar a ZapVida.", "notifications": {"accountSetupFailed": "Não foi possível configurar sua conta. Por favor, tente novamente mais tarde."}, "step": "Passo {step} / {total}", "team": {"joinTeam": "Entrar na equipe", "name": "Nome da equipe", "title": "<PERSON><PERSON><PERSON> ou entrar em uma equipe", "joinTeamDescription": "Você está entrando na equipe <strong>{teamName}</strong>."}, "title": "Configure sua conta ZapVida"}, "pricing": {"description": "Escolha o plano ZapVida que melhor atende às suas necessidades de saúde.", "month": "mês", "monthly": "Mensal", "subscribe": "<PERSON><PERSON><PERSON>", "title": "Planos e Preços ZapVida", "year": "ano", "yearly": "<PERSON><PERSON>"}, "settings": {"account": {"avatar": {"description": "Para alterar sua foto de perfil, clique na imagem à direita e selecione um arquivo do seu computador.", "title": "Sua foto de perfil", "notifications": {"updateSuccess": "Sua foto de perfil foi atualizada com sucesso.", "updateFailed": "Não foi possível atualizar sua foto de perfil. Por favor, tente novamente."}}, "changeEmail": {"description": "Digite um novo e-mail e clique em Salvar para atualizá-lo.", "note": "Você precisará verificar seu novo endereço de e-mail antes de poder usá-lo para login.", "title": "Seu e-mail"}, "changeName": {"title": "Seu nome"}, "changePassword": {"description": "Para alterar sua senha, digite a nova senha abaixo e clique em Salvar.", "note": "Use pelo menos 8 caracteres, com letras maiúsculas, minúsculas, números e caracteres especiais.", "title": "<PERSON><PERSON> se<PERSON>a"}, "deleteAccount": {"confirmation": "Tem certeza de que deseja excluir sua conta ZapVida? Esta ação não pode ser desfeita.", "description": "Excluir permanentemente sua conta. Após a exclusão, não será possível recuperá-la. Tenha certeza antes de prosseguir.", "submit": "Excluir conta", "title": "Excluir conta"}, "title": "Conta"}, "billing": {"cancelSubscription": {"notifications": {"error": {"title": "Não foi possível cancelar a assinatura"}, "success": {"title": "Assinatura cancelada com sucesso"}}}, "createCustomerPortal": {"label": "Gerenciar detalhes de cobrança", "notifications": {"error": {"title": "Não foi possível criar uma sessão no portal do cliente. Por favor, tente novamente."}}}, "pauseSubscription": {"label": "Pausar assinatura", "notifications": {"error": {"title": "Não foi possível pausar a assinatura. Por favor, tente novamente."}, "success": {"title": "A assinatura foi pausada."}}}, "resumeSubscription": {"notifications": {"error": {"title": "Não foi possível retomar a assinatura"}, "success": {"title": "Assinatura retomada com sucesso"}}}, "subscription": {"cancel": "Cancelar sua assinatura", "currentPlan": "Seu plano atual", "currentSubscription": "Sua assinatura atual da ZapVida", "endsOn": "Sua assinatura termina em <strong>{nextPaymentDate, date, medium}</strong>", "freePlan": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "month": "mês", "monthly": "Mensal", "nextPayment": "O próximo pagamento será em <strong>{nextPaymentDate, date, medium}</strong>", "pauseSubscription": "Pausar sua assinatura", "resume": "Retomar assinatura", "resumeSubscription": "Retomar sua assinatura", "status": {"active": "Ativa", "canceled": "Cancelada", "expired": "Expirada", "incomplete": "Incompleta", "past_due": "V<PERSON>cid<PERSON>", "paused": "Pausada", "trialing": "Em teste", "unpaid": "Não paga"}, "subscribe": "<PERSON><PERSON><PERSON>", "switchToPlan": "Mudar para este plano", "updateBillingDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> de cobrança", "upgradePlan": "Atualizar seu plano", "year": "ano", "yearly": "<PERSON><PERSON>", "yourSubscription": "Você assina o plano <strong>{plan}</strong> ({price}/{interval})"}, "title": "Cobrança"}, "menu": {"account": {"general": "G<PERSON>", "title": "<PERSON><PERSON>"}, "team": {"billing": "Cobrança", "general": "G<PERSON>", "members": "Me<PERSON><PERSON>", "title": "Equipe"}}, "notifications": {"accountDeleted": "Sua conta foi excluída.", "accountNotDeleted": "Não foi possível excluir sua conta. Por favor, tente novamente mais tarde.", "emailUpdated": "Seu e-mail foi atualizado. Verifique sua caixa de entrada para confirmar.", "nameUpdateFailed": "Não foi possível atualizar seu nome. Por favor, tente novamente mais tarde.", "nameUpdated": "Seu nome foi atualizado.", "passwordNotUpdated": "Não foi possível atualizar sua senha. Por favor, tente novamente.", "passwordUpdated": "<PERSON>a senha foi atualizada.", "teamDeleted": "Sua equipe foi excluída.", "teamNameNotUpdated": "Não foi possível atualizar o nome da equipe. Por favor, tente novamente mais tarde.", "teamNameUpdated": "O nome da equipe foi atualizado.", "teamNotDeleted": "Não foi possível excluir sua equipe. Por favor, tente novamente mais tarde."}, "save": "<PERSON><PERSON>", "sections": {"team": "Equipe"}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configurações da sua conta e equipe na ZapVida.", "team": {"changeName": {"description": "Digite um novo nome e clique em Salvar para atualizá-lo.", "teamUrl": "A URL da sua equipe será <strong>{url}</strong>.", "title": "Nome da equipe"}, "deleteTeam": {"confirmation": "Tem certeza de que deseja excluir sua equipe? Esta ação não pode ser desfeita.", "description": "Excluir permanentemente sua equipe. Após a exclusão, não será possível recuperá-la. Tenha certeza antes de prosseguir.", "submit": "Excluir equipe", "title": "Excluir equipe"}, "members": {"activeMembers": "Membros ativos", "invitations": {"email": "E-mail", "empty": "Você ainda não convidou nenhum membro.", "revoke": "<PERSON><PERSON><PERSON> convite"}, "invite": "Convidar novo membro", "inviteMember": {"email": "E-mail", "notifications": {"error": {"description": "Não foi possível convidar o membro. Por favor, tente novamente mais tarde.", "title": "Erro ao convidar membro"}, "success": {"description": "O membro foi convidado.", "title": "Membro convidado"}}, "role": "Função", "submit": "<PERSON><PERSON><PERSON>", "title": "Convidar novo membro"}, "notifications": {"removeMember": {"error": {"description": "Não foi possível remover o membro da sua equipe. Por favor, tente novamente."}, "loading": {"description": "Removendo membro da equipe..."}, "success": {"description": "O membro foi removido da sua equipe com sucesso."}}, "revokeInvitation": {"error": {"description": "Não foi possível revogar o convite. Por favor, tente novamente mais tarde."}, "loading": {"description": "Revogando convite..."}, "success": {"description": "O convite foi revogado."}}, "updateMembership": {"error": {"description": "Não foi possível atualizar a função do membro. Por favor, tente novamente."}, "loading": {"description": "Atualizando função..."}, "success": {"description": "Função atualizada com sucesso."}}}, "pendingInvitations": "Convites pendentes", "removeMember": "Remover membro", "roles": {"member": "Membro", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "title": "Me<PERSON>ros da Equipe"}, "subtitle": "<PERSON><PERSON><PERSON><PERSON> as configurações e membros da sua equipe na ZapVida", "title": "Equipe"}, "title": "Configurações"}, "zod": {"errors": {"custom": {"email_already_exists": "Este e-mail já está em uso.", "email_not_verified": "Por favor, verifique seu endereço de e-mail primeiro.", "lowercase_character_required": "Use pelo menos uma letra minúscula.", "number_required": "Use pelo menos um número.", "special_character_required": "Use pelo menos um caractere especial ({character}).", "uppercase_character_required": "Use pelo menos uma letra mai<PERSON>."}, "invalid_arguments": "Argumentos de função inválidos", "invalid_date": "Data inválida", "invalid_enum_value": "Valor inválido. E<PERSON>ado {- options}, recebido '{received}'", "invalid_intersection_types": "Resultados de interseção não puderam ser mesclados", "invalid_literal": "Valor literal inválido, esperado {expected}", "invalid_return_type": "Tipo de retorno de função inválido", "invalid_string": {"cuid": "{validation} inv<PERSON><PERSON>o", "datetime": "{validation} in<PERSON><PERSON><PERSON>a", "email": "{validation} inv<PERSON><PERSON>o", "endsWith": "Inválido: deve terminar com \"{endsWith}\"", "regex": "Formato inválido", "startsWith": "Inválido: deve come<PERSON> com \"{startsWith}\"", "url": "{validation} in<PERSON><PERSON><PERSON>a", "uuid": "{validation} inv<PERSON><PERSON>o"}, "invalid_type": "E<PERSON><PERSON> {expected}, recebido {received}", "invalid_type_received_null": "Campo obrigatório", "invalid_type_received_undefined": "Campo obrigatório", "invalid_union": "Entrada inválida", "invalid_union_discriminator": "Valor discriminador inválido. Esperado {- options}", "not_finite": "Número deve ser finito", "not_multiple_of": "Número deve ser múltiplo de {multipleOf}", "too_big": {"array": {"exact": "A lista deve conter exatamente {maximum} item(ns)", "inclusive": "A lista deve conter no máximo {maximum} item(ns)", "not_inclusive": "A lista deve conter menos de {maximum} item(ns)"}, "date": {"exact": "A data deve ser exatamente {- maximum, datetime}", "inclusive": "A data deve ser menor ou igual a {- maximum, datetime}", "not_inclusive": "A data deve ser menor que {- maximum, datetime}"}, "number": {"exact": "Número deve ser exatamente {maximum}", "inclusive": "Número deve ser menor ou igual a {maximum}", "not_inclusive": "Número deve ser menor que {maximum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "O texto deve conter exatamente {maximum} caractere(s)", "inclusive": "O texto deve conter no máximo {maximum} caractere(s)", "not_inclusive": "O texto deve conter menos de {maximum} caractere(s)"}}, "too_small": {"array": {"exact": "A lista deve conter exatamente {minimum} item(ns)", "inclusive": "A lista deve conter pelo menos {minimum} item(ns)", "not_inclusive": "A lista deve conter mais de {minimum} item(ns)"}, "date": {"exact": "A data deve ser exatamente {- minimum, datetime}", "inclusive": "A data deve ser maior ou igual a {- minimum, datetime}", "not_inclusive": "A data deve ser maior que {- minimum, datetime}"}, "number": {"exact": "Número deve ser exatamente {minimum}", "inclusive": "Número must be greater than or equal to {minimum}", "not_inclusive": "Número must be greater than {minimum}"}, "set": {"exact": "Entrada inválida", "inclusive": "Entrada inválida", "not_inclusive": "Entrada inválida"}, "string": {"exact": "O texto deve conter exatamente {minimum} caractere(s)", "inclusive": "O texto deve conter pelo menos {minimum} caractere(s)", "not_inclusive": "O texto deve conter mais de {minimum} caractere(s)"}}, "unrecognized_keys": "Chave(s) não reconhecida(s) no objeto: {- keys}"}, "types": {"array": "lista", "bigint": "inteiro grande", "boolean": "booleano", "date": "data", "float": "decimal", "function": "função", "integer": "inteiro", "map": "mapa", "nan": "não é um número", "never": "nunca", "null": "nulo", "number": "número", "object": "objeto", "promise": "promessa", "set": "conjunto", "string": "texto", "symbol": "sí<PERSON>lo", "undefined": "indefinido", "unknown": "desconhecido", "void": "vazio"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "data e hora", "email": "e-mail", "emoji": "emoji", "ip": "ip", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}}