/**
 * Função centralizada para obter a URL base do site
 * Garante consistência em toda a aplicação
 */
export function getBaseUrl(): string {
  // Prioridade 1: Variável de ambiente configurada
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }

  // Prioridade 2: Fallback para produção
  return 'https://zapvida.com';
}

/**
 * Função para obter a URL base com fallback específico
 */
export function getBaseUrlWithFallback(fallback?: string): string {
  // Prioridade 1: Variável de ambiente configurada
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }

  // Prioridade 2: Fallback fornecido
  if (fallback) {
    return fallback;
  }

  // Prioridade 3: Fallback padrão para produção
  return 'https://zapvida.com';
}

/**
 * Função para verificar se estamos em desenvolvimento
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Função para verificar se estamos em produção
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Função para obter a URL base baseada no ambiente
 */
export function getEnvironmentAwareBaseUrl(): string {
  if (isDevelopment()) {
    // Em desenvolvimento, usar localhost se configurado
    return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  }

  // Em produção, sempre usar zapvida.com
  return process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com';
}
