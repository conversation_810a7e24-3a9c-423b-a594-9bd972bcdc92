import { randomBytes, createCipheriv, createDecipheriv } from 'crypto';

/**
 * Sistema de criptografia para dados sensíveis de cartão
 * Usa AES-256-GCM com chave mestra única e IV aleatório
 */

// Chave mestra - deve ser definida em variável de ambiente
let MASTER_KEY = 'dev-key-32-chars-for-development-';

// Função para inicializar a chave mestra (chamada apenas em runtime)
function initializeMasterKey() {
  if (typeof window !== 'undefined') {
    // Cliente - usar chave de desenvolvimento
    return 'dev-key-32-chars-for-development-';
  }

  // Servidor - validar e usar chave de produção
  const envKey = process.env.CARD_ENCRYPTION_KEY;

  if (process.env.NODE_ENV === 'production') {
    if (!envKey || envKey.length !== 32) {
      throw new Error('CARD_ENCRYPTION_KEY deve ser definida com exatamente 32 caracteres em produção');
    }
    return envKey;
  }

  // Desenvolvimento
  if (!envKey) {
    console.warn('⚠️  AVISO: Usando chave de desenvolvimento para criptografia. Defina CARD_ENCRYPTION_KEY em produção.');
  }

  return envKey || 'dev-key-32-chars-for-development-';
}

// Inicializar a chave apenas quando necessário
let isInitialized = false;
function getMasterKey() {
  if (!isInitialized) {
    MASTER_KEY = initializeMasterKey();
    isInitialized = true;
  }
  return MASTER_KEY;
}

export interface EncryptedData {
  encrypted: string;
  iv: string;
  tag: string;
}

/**
 * Criptografa dados sensíveis usando AES-256-GCM
 */
export function encryptCardData(data: string): EncryptedData {
  try {
    // Gerar IV aleatório de 16 bytes
    const iv = randomBytes(16);

    // Criar cipher AES-256-GCM
    const cipher = createCipheriv('aes-256-gcm', getMasterKey(), iv);
    cipher.setAAD(Buffer.from('card-data', 'utf8'));

    // Criptografar dados
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Obter tag de autenticação
    const tag = cipher.getAuthTag().toString('hex');

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag
    };
  } catch (error) {
    throw new Error(`Erro ao criptografar dados: ${error}`);
  }
}

/**
 * Descriptografa dados sensíveis usando AES-256-GCM
 */
export function decryptCardData(encryptedData: EncryptedData): string {
  try {
    // Converter hex para Buffer
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const encrypted = Buffer.from(encryptedData.encrypted, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');

    // Criar decipher AES-256-GCM
    const decipher = createDecipheriv('aes-256-gcm', getMasterKey(), iv);
    decipher.setAAD(Buffer.from('card-data', 'utf8'));
    decipher.setAuthTag(tag);

    // Descriptografar dados
    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    throw new Error(`Erro ao descriptografar dados: ${error}`);
  }
}

/**
 * Criptografa dados completos de cartão
 */
export function encryptCreditCard(cardData: {
  cardNumber: string;
  cardHolder: string;
  cardExpiry: string;
  cardCvv: string;
}): {
  encryptedCardData: string;
  iv: string;
  tag: string;
} {
  const dataString = JSON.stringify(cardData);
  const encrypted = encryptCardData(dataString);

  return {
    encryptedCardData: encrypted.encrypted,
    iv: encrypted.iv,
    tag: encrypted.tag
  };
}

/**
 * Descriptografa dados completos de cartão
 */
export function decryptCreditCard(encryptedData: {
  encryptedCardData: string;
  iv: string;
  tag: string;
}): {
  cardNumber: string;
  cardHolder: string;
  cardExpiry: string;
  cardCvv: string;
} {
  const decryptedString = decryptCardData({
    encrypted: encryptedData.encryptedCardData,
    iv: encryptedData.iv,
    tag: encryptedData.tag
  });

  return JSON.parse(decryptedString);
}

/**
 * Criptografa apenas o número do cartão (para busca)
 */
export function encryptCardNumber(cardNumber: string): {
  encryptedNumber: string;
  iv: string;
  tag: string;
} {
  const encrypted = encryptCardData(cardNumber);

  return {
    encryptedNumber: encrypted.encrypted,
    iv: encrypted.iv,
    tag: encrypted.tag
  };
}

/**
 * Descriptografa apenas o número do cartão
 */
export function decryptCardNumber(encryptedData: {
  encryptedNumber: string;
  iv: string;
  tag: string;
}): string {
  return decryptCardData({
    encrypted: encryptedData.encryptedNumber,
    iv: encryptedData.iv,
    tag: encryptedData.tag
  });
}

/**
 * Gera hash seguro para busca de cartão (sem criptografia reversível)
 */
export function hashCardNumber(cardNumber: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(cardNumber + getMasterKey()).digest('hex');
}

/**
 * Verifica se dois números de cartão são iguais usando hash
 */
export function compareCardNumbers(cardNumber1: string, cardNumber2: string): boolean {
  return hashCardNumber(cardNumber1) === hashCardNumber(cardNumber2);
}
