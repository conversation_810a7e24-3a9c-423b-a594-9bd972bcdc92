/**
 * Utilitários para validação e identificação de cartões de crédito
 */

export interface CardInfo {
  brand: string;
  type: 'credit' | 'debit' | 'unknown';
  isValid: boolean;
  length: number;
}

/**
 * Padrões de cartões de crédito conhecidos
 */
const CARD_PATTERNS = {
  visa: {
    pattern: /^4[0-9]{12}(?:[0-9]{3})?$/,
    brand: 'Visa',
    lengths: [13, 16]
  },
  mastercard: {
    pattern: /^5[1-5][0-9]{14}$|^2[2-7][0-9]{14}$|^222[1-9][0-9]{12}$|^22[3-9][0-9]{13}$|^2[3-6][0-9]{14}$|^27[0-1][0-9]{13}$|^2720[0-9]{12}$/,
    brand: 'Mastercard',
    lengths: [16]
  },
  amex: {
    pattern: /^3[47][0-9]{13}$/,
    brand: 'American Express',
    lengths: [15]
  },
  elo: {
    pattern: /^(636368|438935|504175|451416|636297|5067|4576|4011)/,
    brand: 'Elo',
    lengths: [16]
  },
  hipercard: {
    pattern: /^(606282|3841)/,
    brand: 'Hipercard',
    lengths: [16]
  },
  discover: {
    pattern: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
    brand: 'Discover',
    lengths: [16]
  },
  jcb: {
    pattern: /^(?:2131|1800|35\d{3})\d{11}$/,
    brand: 'JCB',
    lengths: [16]
  },
  diners: {
    pattern: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,
    brand: 'Diners Club',
    lengths: [14, 16, 19]
  }
};

/**
 * Detecta a bandeira do cartão baseado no número
 */
export function detectCardBrand(cardNumber: string): string {
  const cleanNumber = cardNumber.replace(/\D/g, '');

  for (const [key, card] of Object.entries(CARD_PATTERNS)) {
    if (card.pattern.test(cleanNumber)) {
      return card.brand;
    }
  }

  return 'Unknown';
}

/**
 * Verifica se o número do cartão é válido usando algoritmo de Luhn
 */
export function isValidCardNumber(cardNumber: string): boolean {
  const cleanNumber = cardNumber.replace(/\D/g, '');

  if (cleanNumber.length < 13 || cleanNumber.length > 19) {
    return false;
  }

  let sum = 0;
  let isEven = false;

  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i]);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

/**
 * Obtém informações completas do cartão
 */
export function getCardInfo(cardNumber: string): CardInfo {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  const brand = detectCardBrand(cardNumber);
  const isValid = isValidCardNumber(cardNumber);

  // Determinar tipo baseado na bandeira (simplificado)
  let type: 'credit' | 'debit' | 'unknown' = 'unknown';

  if (brand === 'Visa' || brand === 'Mastercard' || brand === 'American Express') {
    // A maioria dos cartões Visa/Mastercard são de crédito
    // American Express só emite cartões de crédito
    type = 'credit';
  } else if (brand === 'Elo' || brand === 'Hipercard') {
    // Cartões brasileiros podem ser de crédito ou débito
    type = 'credit'; // Assumindo crédito por padrão
  }

  return {
    brand,
    type,
    isValid,
    length: cleanNumber.length
  };
}

/**
 * Formata número do cartão para exibição (mascara)
 */
export function formatCardNumber(cardNumber: string, mask: boolean = true): string {
  if (!mask) {
    return cardNumber;
  }

  const cleanNumber = cardNumber.replace(/\D/g, '');
  const brand = detectCardBrand(cleanNumber);

  // Formatação específica por bandeira
  switch (brand) {
    case 'American Express':
      // Formato: XXXX XXXXXX XXXXX
      return cleanNumber.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');

    case 'Diners Club':
      // Formato: XXXX XXXXXX XXXX
      if (cleanNumber.length === 14) {
        return cleanNumber.replace(/(\d{4})(\d{6})(\d{4})/, '$1 $2 $3');
      }
      // Fallback para outros comprimentos
      break;

    default:
      // Formato padrão: XXXX XXXX XXXX XXXX
      return cleanNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  // Formato padrão para cartões não reconhecidos
  return cleanNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
}

/**
 * Mascara número do cartão para exibição segura
 */
export function maskCardNumber(cardNumber: string, showLastFour: boolean = true): string {
  const cleanNumber = cardNumber.replace(/\D/g, '');

  if (cleanNumber.length < 4) {
    return cardNumber;
  }

  if (showLastFour) {
    const lastFour = cleanNumber.slice(-4);
    const masked = '*'.repeat(cleanNumber.length - 4);
    return masked + lastFour;
  }

  return '*'.repeat(cleanNumber.length);
}

/**
 * Valida data de expiração
 */
export function isValidExpiry(expiry: string): boolean {
  const match = expiry.match(/^(\d{2})\/(\d{2})$/);
  if (!match) return false;

  const month = parseInt(match[1]);
  const year = parseInt(match[2]);

  if (month < 1 || month > 12) return false;

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100;
  const currentMonth = currentDate.getMonth() + 1;

  if (year < currentYear || (year === currentYear && month < currentMonth)) {
    return false;
  }

  return true;
}

/**
 * Valida CVV baseado na bandeira do cartão
 */
export function isValidCVV(cvv: string, cardBrand: string): boolean {
  const cleanCVV = cvv.replace(/\D/g, '');

  switch (cardBrand) {
    case 'American Express':
      return cleanCVV.length === 4;
    default:
      return cleanCVV.length === 3;
  }
}

/**
 * Gera número de cartão de teste válido para desenvolvimento
 */
export function generateTestCardNumber(brand: string = 'visa'): string {
  const testCards = {
    visa: '****************',
    mastercard: '****************',
    amex: '***************',
    elo: '6363680000000001',
    hipercard: '6062820000000001'
  };

  return testCards[brand as keyof typeof testCards] || testCards.visa;
}

/**
 * Gera CVV de teste
 */
export function generateTestCVV(brand: string = 'visa'): string {
  return brand === 'amex' ? '1234' : '123';
}

/**
 * Gera data de expiração de teste (sempre futura)
 */
export function generateTestExpiry(): string {
  const currentDate = new Date();
  const futureYear = currentDate.getFullYear() + 2;
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  const year = String(futureYear).slice(-2);

  return `${month}/${year}`;
}
