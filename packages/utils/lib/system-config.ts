/**
 * Arquivo de configurações do sistema
 * Essas configurações controlam o comportamento e recursos disponíveis da plataforma
 */

/**
 * PAYMENT_MODE
 * Define como o sistema se comporta em relação a pagamentos:
 *
 * - "DIRECT": Pagamento direto ao médico pelo paciente (modelo telemedicina online padrão)
 *   Habilita: preço da consulta, duração, período de retorno e tipos de consulta no perfil médico
 *
 * - "HOSPITAL": Pagamento gerenciado pelo hospital (modelo institucional)
 *   Desativa: configurações de preço e pagamento no perfil médico (valores definidos pelo hospital)
 */
export const PAYMENT_MODE = process.env.NEXT_PUBLIC_PAYMENT_MODE || "DIRECT";

/**
 * Verifica se o modo de pagamento é direto (paciente -> médico)
 */
export function isDirectPaymentEnabled(): boolean {
  return PAYMENT_MODE === "DIRECT";
}

/**
 * Verifica se o modo de pagamento é gerenciado pelo hospital
 */
export function isHospitalPaymentEnabled(): boolean {
  return PAYMENT_MODE === "HOSPITAL";
}
