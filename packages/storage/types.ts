export type CreateBucketHandler = (
	name: string,
	options?: {
		public?: boolean;
	},
) => Promise<void>;

export type GetSignedUploadUrlHandler = (
	path: string,
	options: {
		bucket: string;
	},
) => Promise<string>;

export type GetSignedUrlHander = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
	},
) => Promise<string>;

export type UploadFileHandler = (
	path: string,
	file: Buffer | ArrayBuffer | Uint8Array,
	options: {
		bucket: string;
		contentType?: string;
		metadata?: Record<string, string>;
	},
) => Promise<{ url: string; key: string }>;
