import type {} from "@prisma/client";
import type { inferRouterInputs, inferRouterOutputs } from "@trpc/server";
import * as adminProcedures from "../modules/admin/procedures";
import * as aiProcedures from "../modules/ai/procedures";
import * as appointmentsProcedures from "../modules/appointments/procedures";
import * as authProcedures from "../modules/auth/procedures";
import * as billingProcedures from "../modules/billing/procedures";
import * as doctorsProcedures from "../modules/doctors/procedures";
import * as formsProcedures from "../modules/forms/procedures/templates";
import * as hospitalsProcedures from "../modules/hospital/procedures";
import * as newsletterProcedures from "../modules/newsletter/procedures";
import * as specialtiesProcedures from "../modules/specialties/procedures";
import * as teamProcedures from "../modules/team/procedures";
import * as uploadsProcedures from "../modules/uploads/procedures";
import * as patientsProcedures from "../modules/patient/procedures"
import { checkoutRouter } from "../modules/checkout";
import { onDutyRouter } from "../modules/on-duty";
import { subscriptionsRouter } from "../modules/subscriptions";

import { router } from "./base";

export const apiRouter = router({
	auth: router(authProcedures),
	billing: router(billingProcedures),
	team: router(teamProcedures),
	newsletter: router(newsletterProcedures),
	ai: router(aiProcedures),
	uploads: router(uploadsProcedures),
	admin: router(adminProcedures),
	appointments: router(appointmentsProcedures),
	hospitals: router(hospitalsProcedures),
	doctors: router(doctorsProcedures),
	specialties: router(specialtiesProcedures),
	forms: router(formsProcedures),
	patients: router(patientsProcedures),
	checkout: checkoutRouter,
	onDuty: onDutyRouter,
	subscriptions: subscriptionsRouter
});

export type ApiRouter = typeof apiRouter;
export type ApiInput = inferRouterInputs<ApiRouter>;
export type ApiOutput = inferRouterOutputs<ApiRouter>;
