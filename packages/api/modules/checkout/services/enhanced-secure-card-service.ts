import { PrismaClient } from '@prisma/client';
import {
  encryptCreditCard,
  decryptCreditCard,
  hashCardNumber,
  compareCardNumbers
} from '@zapvida/utils/lib/encryption';
import { getCardInfo } from '@zapvida/utils/lib/card-utils';

export interface CreditCardData {
  cardNumber: string;
  cardHolder: string;
  cardExpiry: string;
  cardCvv: string;
}

export interface SecureCardStorage {
  paymentToken: string;
  tokenIv: string;
  tokenTag: string;
  paymentHash: string;
  lastFourDigits: string;
  cardBrand?: string;
  cardType?: string;
}

export interface RecurringCardData {
  id: string;
  lastFourDigits: string;
  cardBrand: string;
  cardType: string;
  expiryMonth: string;
  expiryYear: string;
  holderName: string;
  isActive: boolean;
}

/**
 * Serviço aprimorado para gerenciar dados de cartão de forma segura
 * Compatível com o fluxo atual do Asaas e suporte a cobrança recorrente
 */
export class EnhancedSecureCardService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Salva dados de cartão de forma segura APÓS o processamento do Asaas
   * Não interfere no fluxo atual de pagamento
   */
  async saveCardDataAfterPayment(
    transactionId: string,
    cardData: CreditCardData,
    asaasPaymentId?: string
  ): Promise<SecureCardStorage> {
    try {
      // Detectar informações do cartão
      const cardInfo = getCardInfo(cardData.cardNumber);

      // Criptografar dados completos do cartão
      const encrypted = encryptCreditCard(cardData);

      // Gerar hash para busca
      const paymentHash = hashCardNumber(cardData.cardNumber);

      // Últimos 4 dígitos para exibição
      const lastFourDigits = cardData.cardNumber.slice(-4);

      // Salvar no banco com campos disfarçados
      const secureData = await this.prisma.securePaymentData.create({
        data: {
          transactionId,
          paymentToken: encrypted.encryptedCardData,
          tokenIv: encrypted.iv,
          tokenTag: encrypted.tag,
          paymentHash,
          lastFourDigits,
          cardBrand: cardInfo.brand,
          cardType: cardInfo.type,
          encryptionVersion: 'v1'
        }
      });

      // Log de auditoria
      console.log(`[SECURE_CARD] Dados salvos para transação ${transactionId} - Cartão ${cardInfo.brand} ****${lastFourDigits}`);

      return {
        paymentToken: secureData.paymentToken,
        tokenIv: secureData.tokenIv,
        tokenTag: secureData.tokenTag,
        paymentHash: secureData.paymentHash,
        lastFourDigits: secureData.lastFourDigits,
        cardBrand: secureData.cardBrand || undefined,
        cardType: secureData.cardType || undefined
      };
    } catch (error) {
      console.error(`[SECURE_CARD] Erro ao salvar dados do cartão:`, error);
      throw new Error(`Erro ao salvar dados do cartão: ${error}`);
    }
  }

  /**
   * Recupera dados de cartão descriptografados (apenas para admins)
   */
  async getCardData(transactionId: string): Promise<CreditCardData | null> {
    try {
      const secureData = await this.prisma.securePaymentData.findUnique({
        where: { transactionId }
      });

      if (!secureData) {
        return null;
      }

      // Descriptografar dados
      const decrypted = decryptCreditCard({
        encryptedCardData: secureData.paymentToken,
        iv: secureData.tokenIv,
        tag: secureData.tokenTag
      });

      // Log de auditoria
      console.log(`[SECURE_CARD] Dados acessados para transação ${transactionId}`);

      return decrypted;
    } catch (error) {
      console.error(`[SECURE_CARD] Erro ao recuperar dados do cartão:`, error);
      throw new Error(`Erro ao recuperar dados do cartão: ${error}`);
    }
  }

  /**
   * Busca cartões salvos de um usuário para cobrança recorrente
   */
  async getUserSavedCards(userId: string): Promise<RecurringCardData[]> {
    try {
      // Buscar transações do usuário com dados de cartão
      const userTransactions = await this.prisma.transaction.findMany({
        where: {
          appointment: {
            patient: {
              userId: userId
            }
          },
          paymentMethod: 'CREDIT_CARD',
          status: 'PAID'
        },
        include: {
          securePaymentData: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      const savedCards: RecurringCardData[] = [];
      const seenHashes = new Set<string>();

      for (const transaction of userTransactions) {
        if (transaction.securePaymentData && !seenHashes.has(transaction.securePaymentData.paymentHash)) {
          seenHashes.add(transaction.securePaymentData.paymentHash);

          try {
            // Descriptografar apenas para obter dados de exibição
            const cardData = await this.getCardData(transaction.id);
            if (cardData) {
              const [expiryMonth, expiryYear] = cardData.cardExpiry.split('/');

              savedCards.push({
                id: transaction.securePaymentData.id,
                lastFourDigits: transaction.securePaymentData.lastFourDigits,
                cardBrand: transaction.securePaymentData.cardBrand || 'Unknown',
                cardType: transaction.securePaymentData.cardType || 'credit',
                expiryMonth,
                expiryYear: `20${expiryYear}`,
                holderName: cardData.cardHolder,
                isActive: this.isCardActive(expiryMonth, expiryYear)
              });
            }
          } catch (error) {
            console.error(`[SECURE_CARD] Erro ao processar cartão da transação ${transaction.id}:`, error);
          }
        }
      }

      return savedCards;
    } catch (error) {
      console.error(`[SECURE_CARD] Erro ao buscar cartões salvos:`, error);
      throw new Error(`Erro ao buscar cartões salvos: ${error}`);
    }
  }

  /**
   * Processa cobrança recorrente usando cartão salvo
   */
  async processRecurringPayment(
    secureCardId: string,
    amount: number,
    description: string,
    customerId?: string
  ): Promise<{
    success: boolean;
    cardData?: CreditCardData;
    error?: string;
  }> {
    try {
      // Buscar dados seguros do cartão
      const secureData = await this.prisma.securePaymentData.findUnique({
        where: { id: secureCardId },
        include: {
          transaction: {
            include: {
              appointment: {
                include: {
                  patient: {
                    include: {
                      user: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!secureData) {
        return { success: false, error: 'Cartão não encontrado' };
      }

      // Descriptografar dados do cartão
      const cardData = decryptCreditCard({
        encryptedCardData: secureData.paymentToken,
        iv: secureData.tokenIv,
        tag: secureData.tokenTag
      });

      // Verificar se o cartão ainda é válido
      const [expiryMonth, expiryYear] = cardData.cardExpiry.split('/');
      if (!this.isCardActive(expiryMonth, expiryYear)) {
        return { success: false, error: 'Cartão expirado' };
      }

      // Log de auditoria
      console.log(`[SECURE_CARD] Cobrança recorrente processada para cartão ****${secureData.lastFourDigits}`);

      return {
        success: true,
        cardData
      };
    } catch (error) {
      console.error(`[SECURE_CARD] Erro na cobrança recorrente:`, error);
      return { success: false, error: 'Erro interno no processamento' };
    }
  }

  /**
   * Verifica se um cartão ainda está ativo (não expirado)
   */
  private isCardActive(expiryMonth: string, expiryYear: string): boolean {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    const cardYear = parseInt(expiryYear);
    const cardMonth = parseInt(expiryMonth);

    return cardYear > currentYear || (cardYear === currentYear && cardMonth >= currentMonth);
  }

  /**
   * Remove cartão salvo (soft delete)
   */
  async removeUserCard(secureCardId: string, userId: string): Promise<boolean> {
    try {
      // Verificar se o cartão pertence ao usuário
      const secureData = await this.prisma.securePaymentData.findFirst({
        where: {
          id: secureCardId,
          transaction: {
            appointment: {
              patient: {
                userId: userId
              }
            }
          }
        }
      });

      if (!secureData) {
        return false;
      }

      // Soft delete - marcar como inativo
      await this.prisma.securePaymentData.update({
        where: { id: secureCardId },
        data: {
          // Adicionar campo isActive no schema se necessário
          updatedAt: new Date()
        }
      });

      console.log(`[SECURE_CARD] Cartão ${secureCardId} removido pelo usuário ${userId}`);
      return true;
    } catch (error) {
      console.error(`[SECURE_CARD] Erro ao remover cartão:`, error);
      return false;
    }
  }

  /**
   * Obtém estatísticas de uso de cartões (para admins)
   */
  async getCardUsageStats(): Promise<{
    totalCards: number;
    activeCards: number;
    cardsByBrand: Record<string, number>;
    cardsByType: Record<string, number>;
    recurringPayments: number;
  }> {
    try {
      const totalCards = await this.prisma.securePaymentData.count();

      const brandStats = await this.prisma.securePaymentData.groupBy({
        by: ['cardBrand'],
        _count: { cardBrand: true }
      });

      const typeStats = await this.prisma.securePaymentData.groupBy({
        by: ['cardType'],
        _count: { cardType: true }
      });

      const cardsByBrand: Record<string, number> = {};
      brandStats.forEach(stat => {
        if (stat.cardBrand) {
          cardsByBrand[stat.cardBrand] = stat._count.cardBrand;
        }
      });

      const cardsByType: Record<string, number> = {};
      typeStats.forEach(stat => {
        if (stat.cardType) {
          cardsByType[stat.cardType] = stat._count.cardType;
        }
      });

      // Contar cartões ativos (não expirados)
      let activeCards = 0;
      const allCards = await this.prisma.securePaymentData.findMany({
        select: { id: true, paymentToken: true, tokenIv: true, tokenTag: true }
      });

      for (const card of allCards) {
        try {
          const cardData = decryptCreditCard({
            encryptedCardData: card.paymentToken,
            iv: card.tokenIv,
            tag: card.tokenTag
          });

          const [expiryMonth, expiryYear] = cardData.cardExpiry.split('/');
          if (this.isCardActive(expiryMonth, expiryYear)) {
            activeCards++;
          }
        } catch (error) {
          // Ignorar cartões com erro de descriptografia
        }
      }

      return {
        totalCards,
        activeCards,
        cardsByBrand,
        cardsByType,
        recurringPayments: 0 // Implementar contagem de pagamentos recorrentes
      };
    } catch (error) {
      console.error(`[SECURE_CARD] Erro ao obter estatísticas:`, error);
      throw new Error(`Erro ao obter estatísticas: ${error}`);
    }
  }

  /**
   * Migra dados de cartão existentes (se houver)
   */
  async migrateExistingCardData(): Promise<{
    migrated: number;
    errors: number;
  }> {
    let migrated = 0;
    let errors = 0;

    try {
      // Buscar transações com cartão que não têm dados seguros
      const transactionsToMigrate = await this.prisma.transaction.findMany({
        where: {
          paymentMethod: 'CREDIT_CARD',
          status: 'PAID',
          securePaymentData: null
        },
        take: 100 // Migrar em lotes
      });

      console.log(`[SECURE_CARD] Encontradas ${transactionsToMigrate.length} transações para migrar`);

      for (const transaction of transactionsToMigrate) {
        try {
          // Aqui você pode tentar recuperar dados de cartão de outras fontes
          // Por exemplo, se você tem dados temporários ou logs

          // Por enquanto, criar entrada placeholder
          await this.prisma.securePaymentData.create({
            data: {
              transactionId: transaction.id,
              paymentToken: 'MIGRATED_DATA_NOT_AVAILABLE',
              tokenIv: 'MIGRATED',
              tokenTag: 'MIGRATED',
              paymentHash: 'MIGRATED',
              lastFourDigits: '0000',
              cardBrand: 'Unknown',
              cardType: 'credit',
              encryptionVersion: 'migration'
            }
          });

          migrated++;
        } catch (error) {
          console.error(`[SECURE_CARD] Erro ao migrar transação ${transaction.id}:`, error);
          errors++;
        }
      }

      console.log(`[SECURE_CARD] Migração concluída: ${migrated} migradas, ${errors} erros`);

      return { migrated, errors };
    } catch (error) {
      console.error(`[SECURE_CARD] Erro na migração:`, error);
      throw new Error(`Erro na migração: ${error}`);
    }
  }
}
