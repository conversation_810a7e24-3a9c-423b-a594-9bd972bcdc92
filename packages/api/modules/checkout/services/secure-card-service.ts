import { PrismaClient } from '@prisma/client';
import {
  encryptCreditCard,
  decryptCreditCard,
  hashCardNumber,
  compareCardNumbers
} from '@zapvida/utils/lib/encryption';

export interface CreditCardData {
  cardNumber: string;
  cardHolder: string;
  cardExpiry: string;
  cardCvv: string;
}

export interface SecureCardStorage {
  paymentToken: string;
  tokenIv: string;
  tokenTag: string;
  paymentHash: string;
  lastFourDigits: string;
  cardBrand?: string;
  cardType?: string;
}

export class SecureCardService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Salva dados de cartão de forma segura
   */
  async saveCardData(
    transactionId: string,
    cardData: CreditCardData,
    cardBrand?: string,
    cardType?: string
  ): Promise<SecureCardStorage> {
    try {
      // Criptografar dados completos do cartão
      const encrypted = encryptCreditCard(cardData);

      // Gerar hash para busca
      const paymentHash = hashCardNumber(cardData.cardNumber);

      // Últimos 4 dígitos para exibição
      const lastFourDigits = cardData.cardNumber.slice(-4);

      // Salvar no banco com campos disfarçados
      const secureData = await this.prisma.securePaymentData.create({
        data: {
          transactionId,
          paymentToken: encrypted.encryptedCardData,
          tokenIv: encrypted.iv,
          tokenTag: encrypted.tag,
          paymentHash,
          lastFourDigits,
          cardBrand,
          cardType,
          encryptionVersion: 'v1'
        }
      });

      return {
        paymentToken: secureData.paymentToken,
        tokenIv: secureData.tokenIv,
        tokenTag: secureData.tokenTag,
        paymentHash: secureData.paymentHash,
        lastFourDigits: secureData.lastFourDigits,
        cardBrand: secureData.cardBrand || undefined,
        cardType: secureData.cardType || undefined
      };
    } catch (error) {
      throw new Error(`Erro ao salvar dados do cartão: ${error}`);
    }
  }

  /**
   * Recupera dados de cartão descriptografados
   */
  async getCardData(transactionId: string): Promise<CreditCardData | null> {
    try {
      const secureData = await this.prisma.securePaymentData.findUnique({
        where: { transactionId }
      });

      if (!secureData) {
        return null;
      }

      // Descriptografar dados
      const decrypted = decryptCreditCard({
        encryptedCardData: secureData.paymentToken,
        iv: secureData.tokenIv,
        tag: secureData.tokenTag
      });

      return decrypted;
    } catch (error) {
      throw new Error(`Erro ao recuperar dados do cartão: ${error}`);
    }
  }

  /**
   * Busca transações por número de cartão (usando hash)
   */
  async findTransactionsByCardNumber(cardNumber: string): Promise<string[]> {
    try {
      const paymentHash = hashCardNumber(cardNumber);

      const transactions = await this.prisma.securePaymentData.findMany({
        where: { paymentHash },
        select: { transactionId: true }
      });

      return transactions.map(t => t.transactionId);
    } catch (error) {
      throw new Error(`Erro ao buscar transações por cartão: ${error}`);
    }
  }

  /**
   * Verifica se um cartão já foi usado
   */
  async isCardPreviouslyUsed(cardNumber: string): Promise<boolean> {
    try {
      const paymentHash = hashCardNumber(cardNumber);

      const count = await this.prisma.securePaymentData.count({
        where: { paymentHash }
      });

      return count > 0;
    } catch (error) {
      throw new Error(`Erro ao verificar uso do cartão: ${error}`);
    }
  }

  /**
   * Atualiza dados de cartão existente
   */
  async updateCardData(
    transactionId: string,
    newCardData: CreditCardData,
    cardBrand?: string,
    cardType?: string
  ): Promise<SecureCardStorage> {
    try {
      // Criptografar novos dados
      const encrypted = encryptCreditCard(newCardData);

      // Gerar novo hash
      const paymentHash = hashCardNumber(newCardData.cardNumber);

      // Últimos 4 dígitos
      const lastFourDigits = newCardData.cardNumber.slice(-4);

      // Atualizar no banco
      const updatedData = await this.prisma.securePaymentData.update({
        where: { transactionId },
        data: {
          paymentToken: encrypted.encryptedCardData,
          tokenIv: encrypted.iv,
          tokenTag: encrypted.tag,
          paymentHash,
          lastFourDigits,
          cardBrand,
          cardType,
          updatedAt: new Date()
        }
      });

      return {
        paymentToken: updatedData.paymentToken,
        tokenIv: updatedData.tokenIv,
        tokenTag: updatedData.tokenTag,
        paymentHash: updatedData.paymentHash,
        lastFourDigits: updatedData.lastFourDigits,
        cardBrand: updatedData.cardBrand || undefined,
        cardType: updatedData.cardType || undefined
      };
    } catch (error) {
      throw new Error(`Erro ao atualizar dados do cartão: ${error}`);
    }
  }

  /**
   * Remove dados de cartão de forma segura
   */
  async deleteCardData(transactionId: string): Promise<void> {
    try {
      await this.prisma.securePaymentData.delete({
        where: { transactionId }
      });
    } catch (error) {
      throw new Error(`Erro ao remover dados do cartão: ${error}`);
    }
  }

  /**
   * Obtém estatísticas de uso de cartões (sem expor dados sensíveis)
   */
  async getCardUsageStats(): Promise<{
    totalCards: number;
    cardsByBrand: Record<string, number>;
    cardsByType: Record<string, number>;
  }> {
    try {
      const totalCards = await this.prisma.securePaymentData.count();

      const brandStats = await this.prisma.securePaymentData.groupBy({
        by: ['cardBrand'],
        _count: { cardBrand: true }
      });

      const typeStats = await this.prisma.securePaymentData.groupBy({
        by: ['cardType'],
        _count: { cardType: true }
      });

      const cardsByBrand: Record<string, number> = {};
      brandStats.forEach(stat => {
        if (stat.cardBrand) {
          cardsByBrand[stat.cardBrand] = stat._count.cardBrand;
        }
      });

      const cardsByType: Record<string, number> = {};
      typeStats.forEach(stat => {
        if (stat.cardType) {
          cardsByType[stat.cardType] = stat._count.cardType;
        }
      });

      return {
        totalCards,
        cardsByBrand,
        cardsByType
      };
    } catch (error) {
      throw new Error(`Erro ao obter estatísticas: ${error}`);
    }
  }

  /**
   * Valida se os dados de cartão são válidos
   */
  validateCardData(cardData: CreditCardData): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validar número do cartão (Luhn algorithm básico)
    if (!this.isValidCardNumber(cardData.cardNumber)) {
      errors.push('Número do cartão inválido');
    }

    // Validar nome do titular
    if (!cardData.cardHolder || cardData.cardHolder.trim().length < 2) {
      errors.push('Nome do titular deve ter pelo menos 2 caracteres');
    }

    // Validar data de expiração
    if (!this.isValidExpiry(cardData.cardExpiry)) {
      errors.push('Data de expiração inválida');
    }

    // Validar CVV
    if (!cardData.cardCvv || cardData.cardCvv.length < 3 || cardData.cardCvv.length > 4) {
      errors.push('CVV deve ter 3 ou 4 dígitos');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Valida número do cartão usando algoritmo de Luhn
   */
  private isValidCardNumber(cardNumber: string): boolean {
    const cleanNumber = cardNumber.replace(/\D/g, '');

    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    let sum = 0;
    let isEven = false;

    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  /**
   * Valida data de expiração
   */
  private isValidExpiry(expiry: string): boolean {
    const match = expiry.match(/^(\d{2})\/(\d{2})$/);
    if (!match) return false;

    const month = parseInt(match[1]);
    const year = parseInt(match[2]);

    if (month < 1 || month > 12) return false;

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return false;
    }

    return true;
  }
}
