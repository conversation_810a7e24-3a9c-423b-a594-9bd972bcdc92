import { PrismaClient } from '@prisma/client';
import { EnhancedSecureCardService } from './enhanced-secure-card-service';

/**
 * Wrapper para integrar o sistema de cartão seguro com o fluxo atual
 * Não interfere no processamento existente, apenas adiciona camada de segurança
 */
export class PaymentIntegrationWrapper {
  private secureCardService: EnhancedSecureCardService;

  constructor(private prisma: PrismaClient) {
    this.secureCardService = new EnhancedSecureCardService(prisma);
  }

  /**
   * Hook para ser chamado APÓS o processamento bem-sucedido do Asaas
   * Salva dados de cartão de forma segura sem interferir no fluxo atual
   */
  async onPaymentSuccess(
    transactionId: string,
    paymentData: {
      paymentMethod: string;
      creditCard?: {
        cardNumber: string;
        cardHolder: string;
        cardExpiry: string;
        cardCvv: string;
      };
    },
    asaasPaymentId?: string
  ): Promise<void> {
    try {
      // Apenas processar se for pagamento com cartão
      if (paymentData.paymentMethod === 'CREDIT_CARD' && paymentData.creditCard) {
        console.log(`[PAYMENT_WRAPPER] Salvando dados seguros para transação ${transactionId}`);

        await this.secureCardService.saveCardDataAfterPayment(
          transactionId,
          paymentData.creditCard,
          asaasPaymentId
        );

        console.log(`[PAYMENT_WRAPPER] Dados seguros salvos com sucesso para transação ${transactionId}`);
      }
    } catch (error) {
      // Log do erro mas não falha o pagamento
      console.error(`[PAYMENT_WRAPPER] Erro ao salvar dados seguros (não crítico):`, error);
    }
  }

  /**
   * Hook para ser chamado quando um pagamento falha
   * Limpa dados temporários se necessário
   */
  async onPaymentFailure(transactionId: string): Promise<void> {
    try {
      // Limpar dados seguros se existirem
      const existingData = await this.prisma.securePaymentData.findUnique({
        where: { transactionId }
      });

      if (existingData) {
        await this.prisma.securePaymentData.delete({
          where: { transactionId }
        });
        console.log(`[PAYMENT_WRAPPER] Dados seguros limpos para transação falhada ${transactionId}`);
      }
    } catch (error) {
      console.error(`[PAYMENT_WRAPPER] Erro ao limpar dados seguros:`, error);
    }
  }

  /**
   * Obtém dados de cartão para cobrança recorrente
   */
  async getRecurringPaymentData(userId: string) {
    return this.secureCardService.getUserSavedCards(userId);
  }

  /**
   * Processa cobrança recorrente
   */
  async processRecurringCharge(
    secureCardId: string,
    amount: number,
    description: string
  ) {
    return this.secureCardService.processRecurringPayment(
      secureCardId,
      amount,
      description
    );
  }

  /**
   * Obtém estatísticas para dashboard admin
   */
  async getAdminStats() {
    return this.secureCardService.getCardUsageStats();
  }
}

/**
 * Função utilitária para integrar facilmente no código existente
 */
export async function integrateSecureCardSystem(
  prisma: PrismaClient,
  transactionId: string,
  paymentData: any,
  asaasPaymentId?: string
): Promise<void> {
  const wrapper = new PaymentIntegrationWrapper(prisma);
  await wrapper.onPaymentSuccess(transactionId, paymentData, asaasPaymentId);
}

/**
 * Função para limpar dados em caso de falha
 */
export async function cleanupFailedPayment(
  prisma: PrismaClient,
  transactionId: string
): Promise<void> {
  const wrapper = new PaymentIntegrationWrapper(prisma);
  await wrapper.onPaymentFailure(transactionId);
}
