import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure, publicProcedure } from "../../../trpc/base";
import { AsaasClient } from "../../asaas/client";
import { ON_DUTY_CONFIG, PARTNER_CONFIG } from '../../../constants/on-duty';

// Define safer scheduledAt handling
const dateSchema = z.union([
  z.string().refine(val => !isNaN(Date.parse(val)), {
    message: "Invalid date string format"
  }),
  z.date()
]).transform(val => new Date(val));

export const processPayment = publicProcedure
.input(
  z.object({
    doctorId: z.string(),
    customerData: z.object({
      name: z.string(),
      email: z.string().email(),
      phone: z.string(),
      cpf: z.string()
    }),
    paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
    creditCard: z.object({
      cardNumber: z.string(),
      cardHolder: z.string(),
      cardExpiry: z.string(),
      cardCvv: z.string(),
      installments: z.number()
    }).optional(),
    scheduledAt: dateSchema.optional(),
    duration: z.number().optional().default(30),
    // Adicionar estes campos para plantão
    isOnDuty: z.union([z.boolean(), z.string()]).optional().default(false),
    urgencyLevel: z.enum(["high", "medium", "low"]).optional(),
    // Novo campo para parceiro
    partner: z.string().optional()
  })
)
  .mutation(async ({ input, ctx }) => {
    try {


      // 1. Create patient/user if needed
      let user;
      try {
        user = await db.user.findUnique({
          where: { email: input.customerData.email },
          include: { patient: true }
        });


        if (!user) {
          user = await db.user.create({
            data: {
              email: input.customerData.email,
              name: input.customerData.name,
              phone: input.customerData.phone,
              emailVerified: false,
              role: "PATIENT",
            },
            include: { patient: true }
          });

        }
      } catch (userError) {
        console.error("Error handling user:", userError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Error creating or finding user'
        });
      }

      // 2. Create patient profile if needed
      let patient = user.patient;
      if (!patient) {
        try {
          patient = await db.patient.create({
            data: {
              userId: user.id,
              cpf: input.customerData.cpf,
              birthDate: new Date(),
              gender: "N",
              address: {},
              allergies: [],
              chronicConditions: [],
            }
          });

        } catch (patientError) {
          console.error("Error creating patient:", patientError);
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Error creating patient profile'
          });
        }
      }

      // 3. Fetch doctor to get price
      let doctor;
      try {
        doctor = await db.doctor.findUnique({
          where: { id: input.doctorId },
          select: { consultationPrice: true }
        });

        if (!doctor) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Doctor not found'
          });
        }

      } catch (doctorError) {
        console.error("Error finding doctor:", doctorError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Error retrieving doctor information'
        });
      }

      // Determine amount based on appointment type
      let amount;
      let scheduledDateTime;
      let partnerSource = null; // Para salvar a origem do parceiro

      // Garantir que os parâmetros de plantão sejam tratados corretamente
      const isOnDutyBool = input.isOnDuty === true || input.isOnDuty === 'true';
      const urgencyLevel = isOnDutyBool ? input.urgencyLevel : undefined;

      // VERIFICAÇÃO DE PARCEIRO TEM PRIORIDADE ABSOLUTA
      // Verificar se é uma consulta de parceiro - mesmo que tenha urgencyLevel
      if (input.partner && input.partner !== "undefined" && input.partner.trim() !== "") {
        // Log para debug


        partnerSource = input.partner.toUpperCase(); // Salvar a origem do parceiro

        // Verificar parceiros conhecidos
        if (partnerSource === 'FARMACIA' && PARTNER_CONFIG.FARMACIA) {
          amount = PARTNER_CONFIG.FARMACIA.price;
          const duration = PARTNER_CONFIG.FARMACIA.duration;
          const waitTime = PARTNER_CONFIG.FARMACIA.waitTime;

          // Calcular o próximo slot disponível para consulta de farmácia
          const now = new Date();
          const nextSlot = await findNextAvailableSlot(
            input.doctorId,
            now,
            duration,
            waitTime
          );

          scheduledDateTime = nextSlot || new Date(Date.now() + 30 * 60 * 1000);

        }
        else if (partnerSource === 'LOOPMAIS' && PARTNER_CONFIG.LOOPMAIS) {
          amount = PARTNER_CONFIG.LOOPMAIS.price;
          const duration = PARTNER_CONFIG.LOOPMAIS.duration;
          const waitTime = PARTNER_CONFIG.LOOPMAIS.waitTime;

          // Calcular o próximo slot disponível para consulta LoopMais
          const now = new Date();
          const nextSlot = await findNextAvailableSlot(
            input.doctorId,
            now,
            duration,
            waitTime
          );

          scheduledDateTime = nextSlot || new Date(Date.now() + 30 * 60 * 1000);

        }
        else {
          // Para parceiros não configurados, usar preço padrão
          amount = PARTNER_CONFIG.DEFAULT_PARTNER_PRICE;
          scheduledDateTime = input.scheduledAt
            ? new Date(input.scheduledAt)
            : new Date(Date.now() + 30 * 60 * 1000);

        }
      }
      // Se não for parceiro, ENTÃO verificamos os outros tipos
      // Se for plantão com nível de urgência definido, usar preço fixo baseado na urgência
      else if (isOnDutyBool && urgencyLevel) {


        // Determinar o próximo slot disponível baseado no nível de urgência
        const now = new Date();
        const waitTime = ON_DUTY_CONFIG[urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG].waitTime;
        const duration = ON_DUTY_CONFIG[urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG].duration;
        amount = ON_DUTY_CONFIG[urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG].price;

        // Calcular o próximo slot disponível
        const nextSlot = await findNextAvailableSlot(
          input.doctorId,
          now,
          duration,
          waitTime
        );

        if (!nextSlot) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Não há horários disponíveis para este tipo de plantão'
          });
        }

        scheduledDateTime = nextSlot;

      } else {
        // Para consultas regulares, usar o preço de consulta do médico
        amount = doctor.consultationPrice || 100;
        scheduledDateTime = input.scheduledAt
          ? new Date(input.scheduledAt)
          : new Date(Date.now() + 48 * 60 * 60 * 1000);

      }

      // 4. Create appointment with proper date handling
      let appointment;
      try {


        // Preparar dados do appointment
        const appointmentData: any = {
          doctorId: input.doctorId,
          patientId: patient.id,
          scheduledAt: scheduledDateTime,
          duration: input.duration,
          status: "SCHEDULED",
          paymentStatus: "PENDING",
          consultType: "CHAT",
          amount: amount,
          appointmentType: "TELEMEDICINE"
        };

        // Se for plantão, adicionar campos específicos
        if (isOnDutyBool && urgencyLevel) {
          // Mapear urgencyLevel para o formato do banco
          const urgencyMap: { [key: string]: string } = {
            'high': 'HIGH',
            'medium': 'MEDIUM',
            'low': 'LOW'
          };

          // Adicionar campos de plantão (quando o schema for atualizado)
          // appointmentData.isOnDuty = true;
          // appointmentData.urgencyLevel = urgencyMap[urgencyLevel];
          // appointmentData.status = "WAITING_ON_DUTY";

          // Por enquanto, usar campos existentes para identificar plantão
          appointmentData.symptoms = `PLANTAO_${urgencyMap[urgencyLevel]}`;
        }

        // Se for parceiro, adicionar informação
        if (partnerSource) {
          // appointmentData.partnerSource = partnerSource;
          // Por enquanto, usar campo existente
          appointmentData.symptoms = appointmentData.symptoms
            ? `${appointmentData.symptoms}_PARTNER_${partnerSource}`
            : `PARTNER_${partnerSource}`;
        }

        appointment = await db.appointment.create({
          data: appointmentData
        });

      } catch (appointmentError) {
        console.error("Error creating appointment:", appointmentError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create appointment'
        });
      }

      // 5. Create transaction - ATUALIZAR PARA INCLUIR partnerSource
      let transaction;
      try {


        // Simplest approach: direct SQL-like value assignment
        // instead of complex object construction
        transaction = await db.transaction.create({
          data: {
            appointmentId: appointment.id,
            amount: amount,
            platformFee: 0,
            doctorAmount: amount,
            status: "PENDING",
            paymentMethod: input.paymentMethod,
            dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
            // Usando o ID original do doutor no formato CUID
            doctorId: input.doctorId,
            // Adicionar o campo de origem do parceiro, se existir
            partnerSource: partnerSource
          }
        });


      } catch (transactionError: any) {
        console.error("!!! CRITICAL ERROR DURING TRANSACTION CREATION !!!");

        // Log the exact error details
        console.error("ERROR MESSAGE:", transactionError?.message || "No message");
        console.error("ERROR NAME:", transactionError?.name || "No name");
        console.error("ERROR CODE:", transactionError?.code || "No code");

        // Add basic error tracing info
        console.error("OCCURRED AT:", new Date().toISOString());
        console.error("WITH APPOINTMENT ID:", appointment.id);
        console.error("WITH DOCTOR ID:", input.doctorId);

        // Rollback appointment
        try {
          await db.appointment.delete({ where: { id: appointment.id } });
        } catch (rollbackError) {
          console.error("FAILED TO ROLLBACK APPOINTMENT:", rollbackError);
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Payment processing failed: Unable to create transaction record'
        });
      }

      // 6. Update appointment with transaction ID
      try {
        await db.appointment.update({
          where: { id: appointment.id },
          data: { transactionId: transaction.id }
        });

      } catch (updateError) {
        console.error("Error updating appointment with transaction ID:", updateError);
        // Continue execution - this is not a critical error
      }

      // 7. Process payment with Asaas
      let paymentResult;
      const asaas = new AsaasClient();


      try {


        // Pass all required data in a flat structure for simplified processing
        paymentResult = await asaas.createPayment({
          customerData: input.customerData,
          paymentMethod: input.paymentMethod,
          creditCard: input.paymentMethod === 'CREDIT_CARD' ? input.creditCard : undefined,
          totalAmount: Number(amount),
          transactionId: transaction.id
        });

        // Enhanced validation and debug logging
        if (!paymentResult) {
          console.error("Payment result is null or undefined");
          throw new Error("Payment gateway returned empty result");
        }

        if (typeof paymentResult !== 'object') {
          console.error("Invalid payment result type:", typeof paymentResult);
          throw new Error("Invalid payment result from Asaas: " + JSON.stringify(paymentResult));
        }



        // Check for API errors returned in the response object
        if (paymentResult.errors || paymentResult.error) {
          const errorMessage =
            paymentResult.errors?.[0]?.description ||
            paymentResult.error?.message ||
            "Unknown payment gateway error";

          console.error("Payment API returned error:", errorMessage);
          throw new Error(errorMessage);
        }
      } catch (paymentError) {
        console.error("Error processing payment with Asaas:",
          paymentError instanceof Error ? {
            message: paymentError.message,
            name: paymentError.name,
            stack: paymentError.stack
          } : paymentError
        );

        // Rollback transaction
        try {
          await db.transaction.delete({ where: { id: transaction.id } });

        } catch (rollbackError) {
          console.error("Failed to rollback transaction:", rollbackError);
        }

        // Rollback appointment if transaction rollback succeeded
        try {
          await db.appointment.delete({ where: { id: appointment.id } });

        } catch (rollbackError) {
          console.error("Failed to rollback appointment:", rollbackError);
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: paymentError instanceof Error
            ? paymentError.message
            : 'Failed to process payment'
        });
      }

      // Check if payment result contains expected properties
      if (!paymentResult.id) {
        console.error("Payment result missing id:", paymentResult);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Payment processed but no ID was returned'
        });
      }

      // 8. Update transaction with Asaas ID
      try {
        if (!paymentResult.id) {
          throw new Error("Payment result missing ID");
        }

        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            asaasId: paymentResult.id,
            status: paymentResult.status === 'CONFIRMED' ? 'PAID' : 'PENDING'
          }
        });

      } catch (updateError) {
        console.error("Error updating transaction with Asaas ID:", updateError);
        // Continue execution - this is not a critical error
      }

      // 9. For PIX payments, get the PIX code
      let pixCode = undefined;
      if (input.paymentMethod === 'PIX') {
        try {
          const pixData = await asaas.getPixQRCode(paymentResult.id);
          pixCode = pixData;

        } catch (pixError) {
          console.error("Error getting PIX code:", pixError);
          // Don't throw here, just return the transaction without PIX code
          // Client can handle this gracefully
        }
      }

      // Prepare response object
      const response = {
        success: true,
        transactionId: transaction.id,
        paymentId: paymentResult.id,
        paymentMethod: input.paymentMethod,
        appointmentId: appointment.id,
        status: paymentResult.status,
        pixCode: pixCode
      };

      // For credit card payments that are confirmed immediately, queue notifications
      // PIX payments will be handled by the webhook when confirmed
      if (input.paymentMethod === 'CREDIT_CARD' && paymentResult.status === 'CONFIRMED') {
        try {


          // Get more details about doctor and patient for notifications
          const doctorDetails = await db.doctor.findUnique({
            where: { id: input.doctorId },
            include: { user: true }
          });

          const patientDetails = await db.patient.findUnique({
            where: { id: patient.id },
            include: { user: true }
          });

          if (doctorDetails && patientDetails) {
            // Import dynamically to avoid circular dependencies
            const { sendAppointmentNotifications } = await import("../../../../../apps/web/actions/checkout/notifications/send-notifications");

            // Queue notifications to run in background
            sendAppointmentNotifications(
              // Appointment data
              {
                id: appointment.id,
                scheduledAt: appointment.scheduledAt,
                type: appointment.appointmentType
              },
              // Patient data
              {
                id: patientDetails.id,
                user: {
                  id: patientDetails.user.id,
                  name: patientDetails.user.name || "",
                  email: patientDetails.user.email,
                  phone: patientDetails.user.phone || undefined
                },
                isNewUser: !user.emailVerified,
                tempPassword: undefined // No temp password for existing users
              },
              // Doctor data
              {
                id: doctorDetails.id,
                user: {
                  id: doctorDetails.user.id,
                  name: doctorDetails.user.name || "Médico",
                  email: doctorDetails.user.email,
                  phone: doctorDetails.user.phone || undefined
                }
              },
              // Options
              {
                sendEmail: true,
                sendWhatsApp: true,
                useDirectLinks: true
              }
            );


          } else {

          }
        } catch (notificationError) {
          // Log error but don't fail the payment process
          console.error("Error queuing notifications:", notificationError);
        }
      }


      return response;
    } catch (error) {
      console.error("Payment processing error:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Failed to process payment'
      });
    }
  });

// Função auxiliar para encontrar o próximo slot disponível
async function findNextAvailableSlot(
  doctorId: string,
  startTime: Date,
  duration: number,
  waitTime: number
): Promise<Date | null> {
  const minimumStartTime = new Date(startTime.getTime() + waitTime * 60000);

  // Buscar appointments existentes
  const existingAppointments = await db.appointment.findMany({
    where: {
      doctorId,
      scheduledAt: {
        gte: minimumStartTime,
      },
      status: {
        notIn: ["CANCELED", "COMPLETED"],
      },
    },
    orderBy: {
      scheduledAt: 'asc'
    }
  });

  // Encontrar o primeiro slot disponível após o tempo mínimo de espera
  let candidateTime = minimumStartTime;

  for (const appointment of existingAppointments) {
    const appointmentEnd = new Date(
      appointment.scheduledAt.getTime() + (appointment.duration * 60000)
    );

    if (candidateTime.getTime() + (duration * 60000) <= appointment.scheduledAt.getTime()) {
      // Encontramos um slot antes deste appointment
      return candidateTime;
    }

    // Mover para depois deste appointment
    candidateTime = new Date(appointmentEnd.getTime());
  }

  // Se não encontrou conflitos, retorna o tempo candidato
  return candidateTime;
}
