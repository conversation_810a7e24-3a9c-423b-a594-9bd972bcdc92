import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";
import { AsaasClient } from "../../asaas/client";
import { ON_DUTY_CONFIG, ON_DUTY_DOCTORS } from '../../../constants/on-duty';
import { getBaseUrl } from '../../../../utils/lib/base-url';

// Schema de entrada para plantão
const plantaoPaymentSchema = z.object({
  customerData: z.object({
    name: z.string().min(1, "Nome é obrigatório"),
    email: z.string().email("Email inválido"),
    phone: z.string().default(""),
    cpf: z.string().default("")
  }),
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: z.object({
    cardNumber: z.string().min(1, "Número do cartão é obrigatório"),
    cardHolder: z.string().min(1, "Nome do portador é obrigatório"),
    cardExpiry: z.string().min(1, "Data de validade é obrigatória"),
    cardCvv: z.string().min(1, "CVV é obrigatório"),
    installments: z.number().min(1).default(1)
  }).optional(),
  urgencyLevel: z.enum(["high", "medium", "low"]),
  symptoms: z.string().nullish().transform(val => val ?? undefined),
  partner: z.string().nullish().transform(val => val ?? undefined) // Para identificar parceiros como farmácia
});

// Função para chamar o webhook de pagamento confirmado
async function triggerPaymentConfirmedWebhook(appointmentId: string, transactionId: string, paymentId: string) {
  try {


    // Validar parâmetros antes de usar JSON.stringify
    if (!appointmentId || !transactionId || !paymentId) {
      console.error("[PLANTAO_PAYMENT] Parâmetros inválidos para webhook:", {
        appointmentId: appointmentId || 'NULL/UNDEFINED',
        transactionId: transactionId || 'NULL/UNDEFINED',
        paymentId: paymentId || 'NULL/UNDEFINED'
      });
      throw new Error("Parâmetros obrigatórios não fornecidos para o webhook");
    }

    const baseUrl = getBaseUrl();
    const webhookUrl = `${baseUrl}/api/webhooks/plantao-payment-confirmed`;

    const payload = {
      appointmentId,
      transactionId,
      paymentId
    };



    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();



    return result;
  } catch (error) {
    console.error("[PLANTAO_PAYMENT] Erro ao chamar webhook de notificação:", error);
    return { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" };
  }
}

export const processPlantaoPayment = publicProcedure
  .input(plantaoPaymentSchema)
  .mutation(async ({ input, ctx }) => {
    try {


      // 1. Obter configuração de preço baseada na urgência
      const urgencyConfig = ON_DUTY_CONFIG[input.urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG];
      const amount = urgencyConfig.price;



      // 2. Criar ou encontrar usuário
      let user;
      try {
        user = await db.user.findUnique({
          where: { email: input.customerData.email },
          include: { patient: true }
        });

        if (!user) {
          user = await db.user.create({
            data: {
              email: input.customerData.email,
              name: input.customerData.name,
              phone: input.customerData.phone,
              emailVerified: false,
              role: "PATIENT",
            },
            include: { patient: true }
          });

        }
      } catch (userError) {
        console.error("[PLANTAO_PAYMENT] Erro ao criar/encontrar usuário:", userError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Erro ao processar dados do usuário'
        });
      }

      // 3. Criar perfil de paciente se necessário
      let patient = user.patient;
      if (!patient) {
        try {
          patient = await db.patient.create({
            data: {
              userId: user.id,
              cpf: input.customerData.cpf,
              birthDate: new Date(),
              gender: "N",
              address: {},
              allergies: [],
              chronicConditions: [],
            }
          });

        } catch (patientError) {
          console.error("[PLANTAO_PAYMENT] Erro ao criar paciente:", patientError);
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Erro ao criar perfil de paciente'
          });
        }
      }

      // 4. Mapear nível de urgência para o banco
      const urgencyMap: { [key: string]: "HIGH" | "MEDIUM" | "LOW" } = {
        'high': 'HIGH',
        'medium': 'MEDIUM',
        'low': 'LOW'
      };

      // 5. Criar appointment de plantão SEM médico atribuído
      let appointment;
      try {
        const appointmentData = {
          patientId: patient.id,
          scheduledAt: new Date(), // Agendado para agora
          duration: urgencyConfig.duration,
          status: "SCHEDULED" as const,
          paymentStatus: "PENDING" as const,
          consultType: "CHAT" as const,
          amount: amount,
          appointmentType: "TELEMEDICINE" as const,
          symptoms: input.symptoms ?? `Atendimento de plantão - ${urgencyConfig.label}`,
          // Campos específicos de plantão
          isOnDuty: true,
          urgencyLevel: urgencyMap[input.urgencyLevel],
          // Adicionar origem do parceiro se existir
          partnerSource: input.partner ?? null
        };

        appointment = await db.appointment.create({
          data: appointmentData
        });


      } catch (appointmentError) {
        console.error("[PLANTAO_PAYMENT] Erro ao criar appointment:", appointmentError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Erro ao criar consulta de plantão'
        });
      }

      // 6. Criar transação
      let transaction;
      try {
        transaction = await db.transaction.create({
          data: {
            appointmentId: appointment.id,
            amount: amount,
            platformFee: 0,
            doctorAmount: amount,
            status: "PENDING",
            paymentMethod: input.paymentMethod,
            dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
            partnerSource: input.partner ?? null
          }
        });


      } catch (transactionError) {
        console.error("[PLANTAO_PAYMENT] Erro ao criar transação:", transactionError);
        // Rollback appointment
        await db.appointment.delete({ where: { id: appointment.id } }).catch(console.error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Erro ao processar transação'
        });
      }

      // 7. Processar pagamento com Asaas
      let paymentResult;
      const asaas = new AsaasClient();

      try {


        // Validar dados antes de enviar para o Asaas
        if (!input.customerData || !input.customerData.name || !input.customerData.email) {
          throw new Error("Dados do cliente são obrigatórios");
        }

        if (!transaction || !transaction.id) {
          throw new Error("ID da transação não encontrado");
        }

        // Preparar dados do cartão de crédito de forma segura
        let creditCardData = undefined;
        if (input.paymentMethod === 'CREDIT_CARD') {
          if (!input.creditCard) {
            throw new Error("Dados do cartão de crédito são obrigatórios para pagamento com cartão");
          }
          creditCardData = {
            cardNumber: input.creditCard.cardNumber || '',
            cardHolder: input.creditCard.cardHolder || '',
            cardExpiry: input.creditCard.cardExpiry || '',
            cardCvv: input.creditCard.cardCvv || '',
            installments: input.creditCard.installments || 1
          };
        }

        paymentResult = await asaas.createPayment({
          customerData: {
            name: input.customerData.name,
            email: input.customerData.email,
            phone: input.customerData.phone || '',
            cpf: input.customerData.cpf || ''
          },
          paymentMethod: input.paymentMethod,
          creditCard: creditCardData,
          totalAmount: Number(amount),
          transactionId: transaction.id
        });

        if (!paymentResult || !paymentResult.id) {
          throw new Error("Payment gateway returned invalid result");
        }


      } catch (paymentError) {
        console.error("[PLANTAO_PAYMENT] Erro no pagamento:", paymentError);

        // Rollback
        await Promise.all([
          db.transaction.delete({ where: { id: transaction.id } }).catch(console.error),
          db.appointment.delete({ where: { id: appointment.id } }).catch(console.error)
        ]);

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: paymentError instanceof Error ? paymentError.message : 'Erro no processamento do pagamento'
        });
      }

      // 8. Atualizar transação com ID do Asaas
      try {
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            asaasId: paymentResult.id,
            status: paymentResult.status === 'CONFIRMED' ? 'PAID' : 'PENDING'
          }
        });

        // Atualizar appointment se o pagamento foi confirmado
        if (paymentResult.status === 'CONFIRMED') {
          await db.appointment.update({
            where: { id: appointment.id },
            data: { paymentStatus: 'PAID' }
          });
        }
      } catch (updateError) {
        console.error("[PLANTAO_PAYMENT] Erro ao atualizar transação:", updateError);
      }

      // 9. Para PIX, obter código QR
      let pixCode = undefined;
      if (input.paymentMethod === 'PIX') {
        try {
          const pixData = await asaas.getPixQRCode(paymentResult.id);
          pixCode = pixData;

        } catch (pixError) {
          console.error("[PLANTAO_PAYMENT] Erro ao gerar PIX:", pixError);
        }
      }

      // 10. Para cartão de crédito com pagamento confirmado, enviar notificações imediatamente
      if (input.paymentMethod === 'CREDIT_CARD' && paymentResult.status === 'CONFIRMED') {
        try {


          // Validar parâmetros antes de chamar o webhook


          // Chamar o webhook para enviar notificações
          const webhookResult = await triggerPaymentConfirmedWebhook(
            appointment.id,
            transaction.id,
            paymentResult.id
          );


        } catch (notificationError) {
          console.error("[PLANTAO_PAYMENT] Erro ao enviar notificações:", notificationError);
          // Não falhar a transação por erro de notificação
        }
      }

      // 11. Preparar resposta
      const response = {
        success: true,
        transactionId: transaction.id,
        paymentId: paymentResult.id,
        appointmentId: appointment.id,
        status: paymentResult.status,
        paymentMethod: input.paymentMethod,
        pixCode: pixCode,
        urgencyLevel: input.urgencyLevel,
        // Incluir informações sobre notificações para pagamentos com cartão
        notificationsTriggered: input.paymentMethod === 'CREDIT_CARD' && paymentResult.status === 'CONFIRMED'
      };



      return response;
    } catch (error) {
      console.error("[PLANTAO_PAYMENT] Erro crítico:", error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Erro desconhecido no processamento'
      });
    }
  });
