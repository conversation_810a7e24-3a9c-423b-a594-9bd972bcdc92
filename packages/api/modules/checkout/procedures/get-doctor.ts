// apps/web/actions/checkout/get-initial-data.ts

import { createApiCaller } from "../../../trpc/caller";




interface CheckoutInitialDataParams {
  doctorId: string;
  date?: string;
  time?: string;
  isOnDuty?: boolean | string; // Aceita tanto boolean quanto string
  urgencyLevel?: 'high' | 'medium' | 'low';
}

export async function getCheckoutInitialData({
  doctorId,
  date,
  time,
  isOnDuty,
  urgencyLevel,
}: CheckoutInitialDataParams) {
  // Converter isOnDuty para boolean
  const isOnDutyBool = isOnDuty === true || isOnDuty === 'true';



  const apiCaller = await createApiCaller();

  try {
    // Buscar dados do médico com tratamento de erros aprimorado
    const doctorData = await apiCaller.doctors.getDoctorById({ doctorId });

    if (!doctorData) {
      throw new Error(`Médico com ID ${doctorId} não encontrado`);
    }

    // Validar campos obrigatórios
    const requiredFields = ['name', 'profileImage', 'specialty', 'crm', 'crmState', 'consultationPrice', 'acceptedPayments'];
    for (const field of requiredFields) {
      if (!doctorData[field]) {
        throw new Error(`Campo obrigatório '${field}' não encontrado nos dados do médico`);
      }
    }

    // Determinar preço com base no nível de urgência para plantão
    let price = doctorData.consultationPrice;

    if (isOnDutyBool && urgencyLevel) {
      if (urgencyLevel === 'high') {
        price = 120.00; // Muito Urgente
      } else if (urgencyLevel === 'medium') {
        price = 100.00; // Urgente
      } else { // low
        price = 80.00; // Pouco Urgente
      }

    } else {

    }

    // Processar data agendada, se fornecida
    let scheduledAt = undefined;
    if (date) {
      if (time) {
        try {
          // Combinar data e hora
          const [hours, minutes] = time.split(':').map(Number);
          scheduledAt = new Date(date);
          scheduledAt.setHours(hours, minutes, 0, 0);
        } catch (error) {
          console.error('[getCheckoutInitialData] Erro ao processar data e hora:', error);
          scheduledAt = new Date(date);
        }
      } else {
        scheduledAt = new Date(date);
      }
    }

    // Retornar todos os dados necessários para o checkout
    return {
      doctor: {
        id: doctorData.id,
        name: doctorData.name,
        profileImage: doctorData.profileImage,
        specialty: doctorData.specialty,
        crm: doctorData.crm,
        crmState: doctorData.crmState,
      },
      price: price,
      scheduledAt: scheduledAt,
      duration: doctorData.consultationDuration || 30,
      isOnline: true,
      serviceFee: 0,
      isOnDuty: isOnDutyBool,
      urgencyLevel: isOnDutyBool ? urgencyLevel : undefined,
      acceptedPayments: doctorData.acceptedPayments,
      successUrl: doctorData.successUrl,
      cancelUrl: doctorData.cancelUrl,
      termsUrl: doctorData.termsUrl
    };
  } catch (error) {
    console.error('[getCheckoutInitialData] Erro:', error);
    throw error;
  }
}
