import { TRPCError } from "@trpc/server";
import { generateOneTimePassword, generateVerificationToken } from "auth";
import { db } from "database";
import { logger } from "logs";
import { sendEmail } from "mail";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";

export const loginWithEmail = publicProcedure
  .input(
    z.object({
      email: z
        .string()
        .email()
        .min(1)
        .max(255)
        .transform((v) => v.trim().toLowerCase()),
      callbackUrl: z.string(),
    }),
  )
  .mutation(async ({ input: { email, callbackUrl }, ctx: { locale } }) => {
    try {
      logger.info(`Attempting login for email: ${email}`);

      // First check for pending invitations
      const invitation = await db.teamInvitation.findFirst({
        where: {
          email,
          expiresAt: {
            gt: new Date(),
          },
        },
        include: {
          team: {
            select: {
              name: true,
            }
          }
        }
      });

      logger.info(`Invitation status for ${email}:`, invitation ?
        `Found invitation with role ${invitation.role} for team ${invitation.team?.name}` :
        "No invitation found");

      // Check if user exists
      const user = await db.user.findUnique({
        where: { email },
      });

      logger.info(`User status for ${email}:`, user ?
        `Found user with ID ${user.id} and role ${user.role}` :
        "No existing user found");

      // Allow login if user exists OR has valid invitation
      if (!user && !invitation) {
        logger.warn(`No user or invitation found for email: ${email}`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Email not found",
        });
      }

      // If user doesn't exist but has invitation, create the account
      // Preserve the role from the invitation if it's DOCTOR or ADMIN
      const finalUser = user || await db.user.create({
        data: {
          email,
          emailVerified: true, // Auto-verify for invited users
          // Set appropriate role based on invitation role
          role: (invitation?.role === "DOCTOR" || invitation?.role === "ADMIN")
            ? invitation.role
            : "USER",
        },
      });

      if (!user && invitation) {
        logger.info(`Created new user with ID ${finalUser.id} and role ${finalUser.role} from invitation`);
      }

      const token = await generateVerificationToken({
        userId: finalUser.id,
      });

      const otp = await generateOneTimePassword({
        userId: finalUser.id,
        type: "LOGIN",
        identifier: email,
      });

      const url = new URL(callbackUrl);
      url.searchParams.set("token", token);

      await sendEmail({
        templateId: "magicLink",
        to: email,
        locale,
        context: {
          url: url.toString(),
          name: finalUser.name ?? finalUser.email,
          otp,
        },
      });

      return { success: true };
    } catch (e) {
      logger.error("Login with email error:", e);
      throw e instanceof TRPCError ? e : new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to process login",
        cause: e,
      });
    }
  });
