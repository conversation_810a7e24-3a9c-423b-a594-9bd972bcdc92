import { TRPCError } from "@trpc/server";
import { generateRandomPassword, hashPassword } from "auth/lib/hashing";
import { db } from "database";
import { logger } from "logs";
import { sendEmail } from "mail";
import { getBaseUrl } from "utils";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";

export const registerDoctor = publicProcedure
	.input(
		z.object({
			name: z.string().min(1, "Nome é obrigatório"),
			email: z.string().email("Email inválido"),
			phone: z.string().min(10, "Telefone inválido"),
			crm: z.string().min(4, "CRM inválido"),
			crmState: z.string().min(2, "Estado de registro inválido"),
		}),
	)
	.mutation(async ({ input, ctx }) => {
		const { name, email, phone, crm, crmState } = input;
		const locale = ctx.locale || "pt";

		try {
			// Check if email already exists
			const existingUser = await db.user.findUnique({
				where: {
					email,
				},
			});

			if (existingUser) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "Email já cadastrado",
				});
			}

			// Check if CRM already exists
			const existingDoctor = await db.doctor.findFirst({
				where: {
					crm,
					crmState,
				},
			});

			if (existingDoctor) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "CRM já cadastrado",
				});
			}

			// Generate a random password for the user
			const randomPassword = generateRandomPassword();

			// Create user with DOCTOR role
			const user = await db.user.create({
				data: {
					name,
					email,
					phone,
					role: "DOCTOR",
					hashedPassword: await hashPassword(randomPassword),
					emailVerified: false, // Email verification will be required
				},
			});

			// Create doctor record
			await db.doctor.create({
				data: {
					userId: user.id,
					crm,
					crmState,
					biography: null,
					consultationPrice: 0, // Default values, to be updated later
					consultationDuration: 30,
					returnPeriod: 0,
					documentStatus: "PENDING", // Doctor needs to be approved
					onlineStatus: "OFFLINE",
					totalRatings: 0,
				},
			});

			// Create verification token
			const verificationToken = await db.userVerificationToken.create({
				data: {
					userId: user.id,
					expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days
				},
			});

			// Send verification email with access instructions
			await sendEmail({
				templateId: "doctorRegistration",
				to: email,
				locale,
				context: {
					name,
					url: `${getBaseUrl()}/auth/verify?token=${verificationToken.id}`,// Include the temporary password in the email
				},
			});

			return {
				success: true,
			};
		} catch (error) {
			logger.error(error);

			if (error instanceof TRPCError) {
				throw error;
			}

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao registrar médico",
			});
		}
	});
