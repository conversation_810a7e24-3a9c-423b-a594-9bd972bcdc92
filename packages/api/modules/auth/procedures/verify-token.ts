import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";
import {
  createSession,
  createSessionCookie,
  generateSessionToken,
} from "auth";
import { logger } from "logs";


export const verifyToken = publicProcedure
  .input(
    z.object({
      token: z.string(),
    }),
  )
  .mutation(async ({ input, ctx: { responseHeaders } }) => {
    try {




      const normalizedToken = input.token;

      // Buscar o token no banco de dados
      const storedToken = await db.userVerificationToken.findUnique({
        where: {
          id: normalizedToken,
        },
      });

    //   logger.info(`Resultado da busca normal: ${JSON.stringify(storedToken)}`);

      // Se não encontrou, tenta uma busca "fuzzy"
      if (!storedToken) {
        logger.info(`Token não encontrado via busca exata, tentando busca alternativa...`);

        // Busca todos os tokens recentes (últimas 72 horas) para comparação
        const recentTokens = await db.userVerificationToken.findMany({
          where: {
            expires: {
              gte: new Date(Date.now() - 72 * 60 * 60 * 1000) // últimas 72 horas
            }
          },
          orderBy: {
            expires: 'desc'
          },
          take: 20 // limitar a busca para performance
        });

        logger.info(`Encontrados ${recentTokens.length} tokens recentes para comparação`);

        // Busca por tokens com caracteres próximos (para possíveis erros de transcrição)
        // Isso é uma técnica fuzzy simples para buscar tokens quase idênticos
        const possibleMatches = recentTokens.filter(token => {
          // Verificação básica de tamanho
          if (Math.abs(token.id.length - normalizedToken.length) > 2) return false;

          // Conta quantos caracteres são diferentes (distância de Levenshtein simplificada)
          let diffCount = 0;
          const shorter = Math.min(token.id.length, normalizedToken.length);

          for (let i = 0; i < shorter; i++) {
            if (token.id[i] !== normalizedToken[i]) diffCount++;
          }

          // Diferença de tamanho também conta como diferenças
          diffCount += Math.abs(token.id.length - normalizedToken.length);

          // Permite até 2 caracteres diferentes (para cobrir problemas comuns)
          return diffCount <= 2;
        });

        if (possibleMatches.length > 0) {
          logger.info(`Encontrados ${possibleMatches.length} possíveis matches: ${JSON.stringify(possibleMatches.map(t => t.id))}`);

          // Usa o primeiro match como token válido
          const fuzzyMatch = possibleMatches[0];

          logger.info(`Usando token encontrado por fuzzy match: ${fuzzyMatch.id}`);

          // Continua o processo com o token encontrado
          const user = await db.user.findUnique({
            where: { id: fuzzyMatch.userId },
            select: {
              id: true,
              email: true,
              emailVerified: true,
            },
          });

          if (!user) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Usuário não encontrado"
            });
          }

          // Gerar token de sessão para o usuário
          const sessionToken = generateSessionToken();
          await createSession(sessionToken, user.id);

          // CORREÇÃO: Definir o cookie de sessão
          if (responseHeaders) {
            responseHeaders.append(
              "Set-Cookie",
              createSessionCookie(sessionToken).serialize()
            );
            logger.info(`Cookie de sessão definido para usuário via fuzzy match: ${user.id}`);
          } else {
            logger.warn(`responseHeaders não disponível, não foi possível definir cookie de sessão para: ${user.id}`);
          }

          // Remover o token de verificação
          await db.userVerificationToken.delete({
            where: { id: fuzzyMatch.id },
          });

          // Atualizar verificação de email se necessário
          if (!user.emailVerified) {
            await db.user.update({
              where: { id: user.id },
              data: { emailVerified: true },
            });
          }

          logger.info(`Autenticação bem-sucedida via fuzzy match para usuário: ${user.id}`);

          return {
            success: true,
            sessionToken,
            userId: user.id,
            note: "Autenticado via correspondência aproximada de token",
            cookieSet: !!responseHeaders
          };
        }

        // Se chegou aqui, realmente não encontrou nenhum token que possa corresponder
        logger.error(`Não foi encontrado nenhum token similar a: "${normalizedToken}"`);

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Token inválido"
        });
      }

      // Verificar se o token expirou
      if (storedToken.expires < new Date()) {
        logger.error(`Token expirado: "${normalizedToken}", expiração: ${storedToken.expires}`);

        // Remover o token expirado
        await db.userVerificationToken.delete({
          where: { id: normalizedToken },
        });

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Token expirado"
        });
      }

      // Buscar o usuário associado ao token
      const user = await db.user.findUnique({
        where: { id: storedToken.userId },
        select: {
          id: true,
          email: true,
          emailVerified: true,
        },
      });

      if (!user) {
        logger.error(`Usuário não encontrado para o token: "${normalizedToken}"`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Usuário não encontrado"
        });
      }

      // Gerar token de sessão para o usuário
      const sessionToken = generateSessionToken();
      await createSession(sessionToken, user.id);

      // CORREÇÃO: Definir o cookie de sessão
      if (responseHeaders) {
        responseHeaders.append(
          "Set-Cookie",
          createSessionCookie(sessionToken).serialize()
        );
        logger.info(`Cookie de sessão definido para usuário: ${user.id}`);
      } else {
        logger.warn(`responseHeaders não disponível, não foi possível definir cookie de sessão para: ${user.id}`);
      }

      logger.info(`Sessão criada com sucesso para o usuário: ${user.id}`);

      // Remover o token de verificação (uso único)
      await db.userVerificationToken.delete({
        where: { id: normalizedToken },
      });

      // Se o email ainda não foi verificado, marcá-lo como verificado
      if (!user.emailVerified) {
        await db.user.update({
          where: { id: user.id },
          data: { emailVerified: true },
        });
      }

      return {
        success: true,
        sessionToken,
        userId: user.id,
        cookieSet: !!responseHeaders
      };
    } catch (error) {
    //   logger.error(`Erro na verificação do token: ${error.message}`, error);


	console.log("error =========================+++>>>>> ", {error});

      if (error instanceof TRPCError) {
        throw error;
      }

    //   throw new TRPCError({
    //     code: "INTERNAL_SERVER_ERROR",
    //     message: error.message,
    //   });
    }
  });
