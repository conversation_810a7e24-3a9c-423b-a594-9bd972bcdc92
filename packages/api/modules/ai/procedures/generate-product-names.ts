import OpenAI from "openai";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const generateProductNames = protectedProcedure
	.input(
		z.object({
			topic: z.string(),
		}),
	)
	.output(z.array(z.string()))
	.query(async ({ input: { topic } }) => {
		const openai = new OpenAI({
			apiKey: process.env.OPENAI_API_KEY as string,
		});


		const response = await openai.chat.completions.create({
			model: "o1-mini",
			messages: [
				{
					role: "PATIENT",
					content: `Crie um texto sobre o ${topic} que ajude a melhorar a pesquisa de mercado.`,
				},
			],
		});

		const ideas = (response.choices[0].message.content ?? "")
			.split("\n")
			.filter((name) => name.length > 0);

		return ideas;
	});
