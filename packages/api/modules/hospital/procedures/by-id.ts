import { db, HospitalSchema, TeamSchema } from "database";
import { z } from "zod";
import { TRPCError, protectedProcedure } from "../../../trpc/base";

export const byId = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.output(HospitalSchema.extend({
		team: TeamSchema,
	}))
	.query(async ({ ctx, input }) => {
		try {
			const hospital = await db.hospital.findUnique({
				where: {
					id: input.id,
				},
				include: {
					team: true,
				},
			});

			if (!hospital) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Hospital not found",
				});
			}

			if (!ctx.abilities.isTeamMember(hospital.teamId)) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "No permission to access this hospital",
				});
			}

			return hospital;
		} catch (error) {
			if (error instanceof TRPCError) {
				throw error;
			}
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to fetch hospital",
			});
		}
	});
