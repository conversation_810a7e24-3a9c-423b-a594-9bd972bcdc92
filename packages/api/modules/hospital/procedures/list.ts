// packages/api/modules/hospital/procedures/list.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const list = protectedProcedure
	.input(z.void())
	.output(
		z.array(
			z.object({
				id: z.string(),
				name: z.string(),
			}),
		),
	)
	.query(async () => {
		// Busca simples de todos os hospitais
		const hospitals = await db.hospital.findMany({
			select: {
				id: true,
				name: true,
			},
			orderBy: {
				name: "asc",
			},
		});

		return hospitals;
	});
