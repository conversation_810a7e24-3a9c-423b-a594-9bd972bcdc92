// packages/api/modules/specialties/procedures/delete.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtyOutputSchema = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().nullable(),
	searchCount: z.number(),
	createdAt: z.date(),
});

export const delete_ = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.output(SpecialtyOutputSchema)
	.mutation(async ({ input: { id } }) => {
		const specialty = await db.specialty.delete({
			where: { id },
			select: {
				id: true,
				name: true,
				description: true,
				searchCount: true,
				createdAt: true,
			},
		});

		return specialty;
	});
