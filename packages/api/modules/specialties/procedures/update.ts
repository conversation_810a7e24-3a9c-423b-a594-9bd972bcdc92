// packages/api/modules/specialties/procedures/update.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtyOutputSchema = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().nullable(),
	searchCount: z.number(),
	createdAt: z.date(),
});

export const update = protectedProcedure
	.input(
		z.object({
			id: z.string(),
			name: z.string().min(1),
			description: z.string().min(1),
		}),
	)
	.output(SpecialtyOutputSchema)
	.mutation(async ({ input: { id, ...data } }) => {
		const specialty = await db.specialty.update({
			where: { id },
			data,
			select: {
				id: true,
				name: true,
				description: true,
				searchCount: true,
				createdAt: true,
			},
		});

		return specialty;
	});
