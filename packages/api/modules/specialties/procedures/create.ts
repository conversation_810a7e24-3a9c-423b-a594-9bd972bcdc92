// packages/api/modules/specialties/procedures/create.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtyOutputSchema = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().nullable(),
	searchCount: z.number(),
	createdAt: z.date(),
});

export const create = protectedProcedure
	.input(
		z.object({
			name: z.string().min(1),
			description: z.string().min(1),
		}),
	)
	.output(SpecialtyOutputSchema)
	.mutation(async ({ input }) => {
		const specialty = await db.specialty.create({
			data: {
				name: input.name,
				description: input.description || "",
				searchCount: 0,
			},
			select: {
				id: true,
				name: true,
				description: true,
				searchCount: true,
				createdAt: true,
			},
		});

		return specialty;
	});
