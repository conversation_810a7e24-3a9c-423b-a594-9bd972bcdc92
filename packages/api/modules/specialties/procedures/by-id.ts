import { TRPCError } from "@trpc/server";
// packages/api/modules/specialties/procedures/by-id.ts
import { SpecialtySchema, db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const byId = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.output(SpecialtySchema)
	.query(async ({ input: { id } }) => {
		const specialty = await db.specialty.findUnique({
			where: { id },
		});

		if (!specialty) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Specialty not found",
			});
		}

		return specialty;
	});
