// packages/api/modules/specialties/procedures/list.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtyOutputSchema = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().nullable(),
	searchCount: z.number(),
	createdAt: z.date(),
});

export const list = protectedProcedure
	.input(
		z
			.object({
				searchTerm: z.string().optional(),
			})
			.optional(),
	)
	.output(z.array(SpecialtyOutputSchema))
	.query(async ({ input }) => {
		const searchTerm = input?.searchTerm?.trim().toLowerCase();

		const where = searchTerm
			? {
					name: {
						contains: searchTerm,
						mode: "insensitive",
					},
				}
			: {};

		const specialties = await db.specialty.findMany({
			where,
			orderBy: {
				name: "asc",
			},
			select: {
				id: true,
				name: true,
				description: true,
				searchCount: true,
				createdAt: true,
			},
		});

		return specialties;
	});
