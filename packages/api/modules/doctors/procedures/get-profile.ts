import { db } from "database";
import { protectedProcedure } from "../../../trpc/base";
import { serializeObject } from "../../../utils/decimal-serializer";

export const getProfile = protectedProcedure.query(async ({ ctx }) => {
  const { user } = ctx;

  const doctor = await db.doctor.findFirst({
    where: {
      userId: user.id,
    },
    include: {
      hospitals: {
        include: {
          hospital: true,
        },
      },
     specialties: true,
      user: {
        select: {
          name: true,
          email: true,
          avatarUrl: true,
        },
      },
    },
  });

  if (!doctor) {
    throw new Error("Doctor profile not found");
  }

  return serializeObject(doctor);
})
