import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const CreateScheduleBlockInputSchema = z.object({
  doctorId: z.string(),
  startTime: z.date(),
  endTime: z.date(),
  reason: z.string().optional(),
  type: z.enum(["VACATION", "HOLIDAY", "LUNCH", "PERSONAL", "OTHER"]),
});

const ScheduleBlockOutputSchema = z.object({
  id: z.string(),
  doctorId: z.string(),
  startTime: z.date(),
  endTime: z.date(),
  reason: z.string().nullable(),
  description: z.string().nullable(),
  type: z.enum(["VACATION", "HOLIDAY", "LUNCH", "PERSONAL", "OTHER"]),
});

export const createScheduleBlock = protectedProcedure
  .input(CreateScheduleBlockInputSchema)
  .output(ScheduleBlockOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;
      const { doctorId, startTime, endTime, reason, type } = input;

      // Verificar permissões
      if (
        user?.role !== "ADMIN" &&
        user?.role !== "DOCTOR" &&
        user?.role !== "HOSPITAL"
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para criar bloqueios de agenda",
        });
      }

      // Se for médico, verificar se está criando seu próprio bloqueio
      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: {
            userId: user.id,
          },
        });

        if (!doctor || doctor.id !== doctorId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Você só pode criar bloqueios para sua própria agenda",
          });
        }
      }

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: doctorId,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      // Verificar se há consultas agendadas no período
      const appointments = await db.appointment.findMany({
        where: {
          doctorId,
          scheduledAt: {
            gte: startTime,
            lte: endTime,
          },
          status: {
            notIn: ["CANCELED", "COMPLETED"],
          },
        },
      });

      if (appointments.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Existem consultas agendadas neste período",
        });
      }

      // Criar o bloqueio
      const block = await db.scheduleBlock.create({
        data: {
          doctorId,
          startTime,
          endTime,
          reason: reason || null,
          description: null,
          type,
        },
      });

      // Atualizar os time slots para o médico
      await updateTimeSlotsForDoctor(doctorId);

      return block;
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao criar bloqueio de agenda",
        cause: error,
      });
    }
  });

// Função auxiliar para atualizar os time slots do médico
async function updateTimeSlotsForDoctor(doctorId: string) {
  try {
    // Obter horários do médico e informações em uma única transação
    const [schedules, blocks, doctor] = await Promise.all([
      db.doctorSchedule.findMany({
        where: { doctorId, isEnabled: true, isBreak: false },
      }),
      db.scheduleBlock.findMany({
        where: {
          doctorId,
          endTime: { gte: new Date() },
        },
      }),
      db.doctor.findUnique({
        where: { id: doctorId },
        select: { consultationDuration: true },
      }),
    ]);

    if (!doctor) {
      throw new Error("Doctor not found");
    }

    const duration = doctor.consultationDuration || 30;
    const today = new Date();
    const fourWeeksLater = new Date();
    fourWeeksLater.setDate(today.getDate() + 28);

    // Limpar slots futuros não reservados em uma única operação
    await db.timeSlot.deleteMany({
      where: {
        doctorId,
        startTime: { gte: today },
        appointmentId: null,
      },
    });

    // Preparar todos os slots para inserção em lote
    const slotsToCreate = [];

    for (let d = new Date(today); d <= fourWeeksLater; d.setDate(d.getDate() + 1)) {
      const weekDay = d.getDay();
      const daySchedules = schedules.filter(s => s.weekDay === weekDay);

      for (const daySchedule of daySchedules) {
        const [startHour, startMinute] = daySchedule.startTime.split(':').map(Number);
        const [endHour, endMinute] = daySchedule.endTime.split(':').map(Number);
        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;

        for (let minutes = startMinutes; minutes < endMinutes; minutes += duration) {
          const slotDate = new Date(d);
          slotDate.setHours(Math.floor(minutes / 60), minutes % 60, 0, 0);

          const slotEndDate = new Date(slotDate);
          slotEndDate.setMinutes(slotEndDate.getMinutes() + duration);

          const isBlocked = blocks.some(block =>
            slotDate >= block.startTime && slotEndDate <= block.endTime
          );

          if (!isBlocked) {
            slotsToCreate.push({
              doctorId,
              startTime: slotDate,
              endTime: slotEndDate,
              isAvailable: true,
            });
          }
        }
      }
    }

    // Criar todos os slots em uma única operação em lote
    if (slotsToCreate.length > 0) {
      await db.timeSlot.createMany({
        data: slotsToCreate,
        skipDuplicates: true,
      });
    }
  } catch (error) {
    console.error("Erro ao atualizar time slots:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Erro ao atualizar time slots do médico",
      cause: error,
    });
  }
}
