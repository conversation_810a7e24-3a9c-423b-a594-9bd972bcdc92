// packages/api/modules/doctors/procedures/get-specialties.ts
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtyOutputSchema = z.object({
	id: z.string(),
	name: z.string(),
});

export const getSpecialties = protectedProcedure
	.input(z.void())
	.output(z.array(SpecialtyOutputSchema))
	.query(async () => {
		const specialties = await db.specialty.findMany({
			orderBy: {
				name: "asc",
			},
			select: {
				id: true,
				name: true,
			},
		});

		return specialties;
	});
