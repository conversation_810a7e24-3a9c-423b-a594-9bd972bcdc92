// packages/api/modules/doctors/procedures/create.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { sendEmail } from "mail";
import { generateVerificationToken } from "auth/lib/tokens";

const DoctorInputSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	phone: z.string().min(10, "Telefone inválido"),
	crm: z.string().min(4, "CRM inválido"),
	crmState: z.string().min(2, "Estado de registro inválido"),
	specialtyIds: z
		.array(z.string())
		.min(1, "Selecione pelo menos uma especialidade"),
	hospitalId: z.string().min(1, "Hospital é obrigatório"),
	consultationDuration: z.number().min(15).default(30),
	consultationPrice: z.number().min(0).default(0),
	biography: z.string().optional(),
});

const DoctorOutputSchema = z.object({
	id: z.string(),
	user: z.object({
		id: z.string(),
		name: z.string().nullable(),
		email: z.string(),
		emailVerified: z.boolean(),
		phone: z.string().nullable(),
		image: z.string().nullable(),
	}),
	crm: z.string(),
	crmState: z.string(),
	specialty: z.string(),
	consultationPrice: z.number(),
	consultationDuration: z.number(),
	biography: z.string().nullable(),
	rating: z.number().nullable(),
	totalRatings: z.number(),
	appointments: z.array(z.any()),
});

export const create = protectedProcedure
	.input(DoctorInputSchema)
	.output(DoctorOutputSchema)
	.mutation(async ({ input, ctx }) => {
		try {
			console.log("[Doctor Create] Starting doctor creation with input:", {
				...input,
				email: input.email?.toLowerCase() // log sanitized email
			});

			const { user, abilities } = ctx;

			// Verify permissions
			if (user?.role !== "ADMIN" && user?.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Sem permissão para criar médicos",
				});
			}

			// Get hospital ID
			let hospitalId = input.hospitalId;

			if (user?.role === "HOSPITAL" && !hospitalId) {
				const userHospital = await db.hospital.findFirst({
					where: {
						teamId: ctx.teamMemberships?.[0]?.teamId,
					},
				});

				if (!userHospital) {
					throw new TRPCError({
						code: "BAD_REQUEST",
						message: "Hospital não encontrado",
					});
				}

				hospitalId = userHospital.id;
			}

			// Check if email is already in use
			const existingUser = await db.user.findUnique({
				where: { email: input.email.toLowerCase() },
			});

			// Flag para identificar novos usuários
			const isNewUser = !existingUser;

			// Create or get user ID
			const userId = existingUser
				? existingUser.id
				: (
						await db.user.create({
							data: {
								name: input.name,
								email: input.email.toLowerCase(),
								phone: input.phone,
								role: "DOCTOR",
								emailVerified: true,
							},
						})
					).id;

			// Check if CRM is already registered
			const existingDoctor = await db.doctor.findFirst({
				where: {
					crm: input.crm,
					crmState: input.crmState,
				},
			});

			if (existingDoctor) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "CRM já cadastrado no sistema",
				});
			}

			// Create doctor
			const doctor = await db.doctor.create({
				data: {
					userId,
					crm: input.crm,
					crmState: input.crmState,
					consultationPrice: input.consultationPrice,
					consultationDuration: input.consultationDuration,
					biography: input.biography || null,
					specialties: {
						connect: input.specialtyIds.map((id) => ({ id })),
					},
					hospitals: {
						create: {
							hospitalId: input.hospitalId,
							isActive: true,
						},
					},
					rating: null,
					totalRatings: 0,
				},
				include: {
					user: true,
					specialties: true,
					appointments: true,
					hospitals: true,
				},
			});

			// Enviar email de boas-vindas para novos médicos
			if (isNewUser) {
				try {
					// Gerar token de verificação para o novo usuário
					const verificationToken = await generateVerificationToken({ userId: userId });

					console.log(`[Doctor Create] Sending welcome email to ${input.email} with token ${verificationToken}`);

					// Enviar email de boas-vindas com link de verificação
					await sendEmail({
						to: input.email.toLowerCase(),
						templateId: "newPatient", // Usar o mesmo template dos pacientes por enquanto
						context: {
							name: input.name,
							url: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify?token=${verificationToken}`,
							otp: "",
						},
						locale: "pt",
					});

					console.log(`[Doctor Create] Welcome email sent to ${input.email}`);
				} catch (emailError) {
					// Apenas logar o erro, não interromper o fluxo
					console.error("[Doctor Create] Error sending welcome email:", emailError);
				}
			}

			return {
				id: doctor.id,
				user: {
					id: doctor.user.id,
					name: doctor.user.name,
					email: doctor.user.email,
					emailVerified: doctor.user.emailVerified,
					phone: doctor.user.phone,
					image: doctor.user.avatarUrl,
				},
				crm: doctor.crm,
				crmState: doctor.crmState,
				specialty: doctor.specialties.map((s) => s.name).join(", ") || "",
				consultationPrice: Number(doctor.consultationPrice),
				consultationDuration: doctor.consultationDuration,
				biography: doctor.biography,
				rating: doctor.rating,
				totalRatings: doctor.totalRatings,
				appointments: doctor.appointments,
			};
		} catch (error) {
			console.error("[Doctor Create] Error creating doctor:", error);

			if (error instanceof TRPCError) throw error;
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao criar médico",
				cause: error,
			});
		}
	});
