// packages/api/modules/doctors/procedures/update.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const DoctorUpdateInputSchema = z.object({
	id: z.string(),
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	phone: z.string().min(10, "Telefone inválido"),
	crm: z.string().min(4, "CRM inválido"),
	crmState: z.string().min(2, "Estado de registro inválido"),
	specialtyIds: z
		.array(z.string())
		.min(1, "Selecione pelo menos uma especialidade"),
	hospitalId: z.string().min(1, "Hospital é obrigatório"),
	consultationDuration: z.number().min(15).default(30),
	consultationPrice: z.number().min(0).default(0),
	biography: z.string().optional(),
});

const DoctorOutputSchema = z.object({
	id: z.string(),
	user: z.object({
		id: z.string(),
		name: z.string().nullable(),
		email: z.string(),
		emailVerified: z.boolean(),
		phone: z.string().nullable(),
		image: z.string().nullable(),
	}),
	crm: z.string(),
	crmState: z.string(),
	specialty: z.string(),
	consultationPrice: z.number(),
	consultationDuration: z.number(),
	biography: z.string().nullable(),
	rating: z.number().nullable(),
	totalRatings: z.number(),
	appointments: z.array(z.any()),
});

export const update = protectedProcedure
	.input(DoctorUpdateInputSchema)
	.output(DoctorOutputSchema)
	.mutation(async ({ input, ctx }) => {
		try {
			const { user, abilities } = ctx;

			// Verificar permissões
			if (user?.role !== "ADMIN" && user?.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Sem permissão para atualizar médicos",
				});
			}

			// Se for hospital, verificar se o médico pertence ao hospital
			if (user?.role === "HOSPITAL") {
				const userHospital = await db.hospital.findFirst({
					where: {
						teamId: ctx.teamMemberships?.[0]?.teamId,
					},
				});

				if (!userHospital) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Hospital não encontrado",
					});
				}

				const doctorBelongsToHospital = await db.doctorHospital.findFirst({
					where: {
						doctorId: input.id,
						hospitalId: userHospital.id,
					},
				});

				if (!doctorBelongsToHospital) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Médico não pertence a este hospital",
					});
				}
			}

			// Buscar médico para atualização
			// Verificar se já existe associação com o hospital
			const existingHospital = await db.doctorHospital.findFirst({
				where: {
					doctorId: input.id,
					hospitalId: input.hospitalId,
				},
			});

			if (!existingHospital) {
				// Remover associações existentes (opcional, dependendo da sua lógica de negócio)
				await db.doctorHospital.deleteMany({
					where: {
						doctorId: input.id,
					},
				});

				// Criar nova associação com o hospital
				await db.doctorHospital.create({
					data: {
						doctorId: input.id,
						hospitalId: input.hospitalId,
						isActive: true,
					},
				});
			}
			const currentHospitalRelation = existingDoctor?.hospitals.find(
				(h) => h.hospitalId === input.hospitalId,
			);

			if (!existingDoctor) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Médico não encontrado",
				});
			}

			// Verificar se o CRM está sendo alterado e se já existe
			if (
				existingDoctor?.crm !== input.crm ||
				existingDoctor?.crmState !== input.crmState
			) {
				const duplicateCrm = await db.doctor.findFirst({
					where: {
						crm: input.crm,
						crmState: input.crmState,
						id: { not: input.id },
					},
				});

				if (duplicateCrm) {
					throw new TRPCError({
						code: "CONFLICT",
						message: "CRM já cadastrado para outro médico",
					});
				}
			}

			// Atualizar usuário
			await db.user.update({
				where: { id: existingDoctor.userId },
				data: {
					name: input.name,
					phone: input.phone,
					email: input.email,
				},
			});

			const updatedDoctor = await db.$transaction(async (tx) => {
				// Atualizar usuário
				await tx.user.update({
					where: { id: existingDoctor.userId },
					data: {
						name: input.name,
						phone: input.phone,
						email: input.email,
					},
				});

				// Se o hospital mudou, atualizar relações
				if (!currentHospitalRelation) {
					// Remover associações existentes se solicitado pelo client
					// Delete all existing hospital associations since we're adding a new one
					await tx.doctorHospital.deleteMany({
						where: { doctorId: input.id },
					});

					// Criar nova associação
					await tx.doctorHospital.create({
						data: {
							doctorId: input.id,
							hospitalId: input.hospitalId,
							isActive: true,
						},
					});
				}

				// Atualizar médico
				return await tx.doctor.update({
					where: { id: input.id },
					data: {
						crm: input.crm,
						crmState: input.crmState,
						consultationPrice: input.consultationPrice,
						consultationDuration: input.consultationDuration,
						biography: input.biography || null,
						specialties: {
							disconnect: existingDoctor.specialties.map((s) => ({ id: s.id })),
							connect: input.specialtyIds.map((id) => ({ id })),
						},
					},
					include: {
						user: true,
						specialties: true,
						appointments: true,
						hospitals: true,
					},
				});
			});

			return {
				id: updatedDoctor.id,
				user: {
					id: updatedDoctor.user.id,
					name: updatedDoctor.user.name,
					email: updatedDoctor.user.email,
					emailVerified: updatedDoctor.user.emailVerified,
					phone: updatedDoctor.user.phone,
					image: updatedDoctor.user.avatarUrl,
				},
				crm: updatedDoctor.crm,
				crmState: updatedDoctor.crmState,
				specialty:
					updatedDoctor.specialties.map((s) => s.name).join(", ") || "",
				consultationPrice: Number(updatedDoctor.consultationPrice),
				consultationDuration: updatedDoctor.consultationDuration,
				biography: updatedDoctor.biography,
				rating: updatedDoctor.rating,
				totalRatings: updatedDoctor.totalRatings,
				appointments: updatedDoctor.appointments,
			};
		} catch (error) {
			if (error instanceof TRPCError) throw error;
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao atualizar médico",
				cause: error,
			});
		}
	});
