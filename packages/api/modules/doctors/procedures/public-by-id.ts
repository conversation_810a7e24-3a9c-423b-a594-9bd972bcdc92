import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";
import { cacheService } from "../../../utils/cache";

export const getPublicById = publicProcedure
  .input(
    z.object({
      id: z.string().min(1, "ID do médico é obrigatório"),
    })
  )
  .query(async ({ input }) => {
    try {
      const { id } = input;
      console.log("[PUBLIC_BY_ID] Buscando médico com ID:", id);
      
      const cacheKey = `doctor:public:${id}`;
      const cachedDoctor = await cacheService.get(cacheKey);
      
      if (cachedDoctor) {
        console.log("[PUBLIC_BY_ID] Médico encontrado no cache");
        return cachedDoctor;
      }
      
      console.log("[PUBLIC_BY_ID] Cache miss, buscando no banco");

      // Verificar se o DB está acessível
      console.log("[PUBLIC_BY_ID] Verificando acesso ao DB");
      try {
        const totalDoctors = await db.doctor.count();
        console.log("[PUBLIC_BY_ID] Total de médicos no DB:", totalDoctors);

        // Listar alguns IDs de médicos existentes para diagnóstico
        if (totalDoctors > 0) {
          const sampleDoctors = await db.doctor.findMany({
            take: 5,
            select: {
              id: true,
              user: {
                select: {
                  name: true
                }
              }
            }
          });

          console.log("[PUBLIC_BY_ID] Exemplos de médicos disponíveis:");
          sampleDoctors.forEach(doc => {
            console.log(`  - ID: ${doc.id} | Nome: ${doc.user?.name}`);
          });

          // Tentar buscar diretamente com diferentes formatos de ID
          console.log("[PUBLIC_BY_ID] Tentando buscar com formatos alternativos do ID");

          // Tentar achar o médico pelo ID original
          console.log(`[PUBLIC_BY_ID] Tentando ID original: ${id}`);
          const doctorOriginal = await db.doctor.findUnique({
            where: { id },
            select: { id: true }
          });
          console.log(`[PUBLIC_BY_ID] Resultado com ID original:`, doctorOriginal ? "Encontrado" : "Não encontrado");
        }
      } catch (dbError) {
        console.error("[PUBLIC_BY_ID] Erro ao acessar DB:", dbError);
      }

      console.log("[PUBLIC_BY_ID] Tentando buscar médico no DB...");
      const doctor = await db.doctor.findUnique({
        where: { id },
        select: {
          id: true,
          userId: true,
          crm: true,
          crmState: true,
          biography: true,
          consultationPrice: true,
          consultationDuration: true,
          returnPeriod: true,
          memedToken: true,
          asaasId: true,
          rating: true,
          totalRatings: true,
          onlineStatus: true,
          documentStatus: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              emailVerified: true,
              phone: true,
              avatarUrl: true,
            },
          },
          specialties: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          doctorSchedules: {
            select: {
              id: true,
              weekDay: true,
              startTime: true,
              endTime: true,
              isEnabled: true,
            },
            where: {
              isEnabled: true,
            },
          },
          evaluations: {
            select: {
              id: true,
              rating: true,
              comment: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });

      console.log("[PUBLIC_BY_ID] Médico encontrado:", doctor ? "SIM" : "NÃO");
      if (doctor) {
        console.log("[PUBLIC_BY_ID] ID do médico encontrado:", doctor.id);
        console.log("[PUBLIC_BY_ID] Nome do médico:", doctor.user?.name);
      } else {
        console.log("[PUBLIC_BY_ID] Tentando buscar qualquer médico para confirmar que o DB está funcionando...");
        const anyDoctor = await db.doctor.findFirst({
          take: 1,
          select: { id: true }
        });
        console.log("[PUBLIC_BY_ID] Algum médico encontrado?", anyDoctor ? "SIM" : "NÃO");
        if (anyDoctor) {
          console.log("[PUBLIC_BY_ID] ID de um médico existente:", anyDoctor.id);
        }
      }

      if (!doctor) {
        console.log("[PUBLIC_BY_ID] Médico não encontrado, lançando erro NOT_FOUND");
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      console.log("[PUBLIC_BY_ID] Criando objeto seguro para retorno");

      const doctorSchedules = doctor.doctorSchedules || [];
      const evaluations = doctor.evaluations || [];

      const safeDoctor = {
        id: doctor.id,
        userId: doctor.userId,
        createdAt: doctor.createdAt?.toISOString(),
        updatedAt: doctor.updatedAt?.toISOString(),
        user: doctor.user ? {
          id: doctor.user.id,
          name: doctor.user.name || '',
          email: doctor.user.email || '',
          avatarUrl: doctor.user.avatarUrl || null,
          emailVerified: doctor.user.emailVerified,
          phone: doctor.user.phone,
        } : null,
        crm: doctor.crm || '',
        crmState: doctor.crmState || '',
        biography: doctor.biography || '',
        consultationPrice: doctor.consultationPrice ? Number(doctor.consultationPrice) : 0,
        consultationDuration: doctor.consultationDuration || 30,
        returnPeriod: doctor.returnPeriod || 0,
        memedToken: doctor.memedToken,
        returnEnabled: 'returnEnabled' in doctor ? doctor.returnEnabled : false,
        asaasId: doctor.asaasId,
        rating: doctor.rating || 0,
        totalRatings: doctor.totalRatings || 0,
        onlineStatus: doctor.onlineStatus || 'OFFLINE',
        documentStatus: doctor.documentStatus || 'PENDING',
        consultTypes: ['VIDEO', 'AUDIO', 'CHAT'],
        specialties: doctor.specialties || [],
        doctorSchedules: doctorSchedules.map(schedule => ({
          id: schedule.id,
          week_day: schedule.weekDay,
          weekDay: schedule.weekDay,
          start_time: schedule.startTime,
          startTime: schedule.startTime,
          end_time: schedule.endTime,
          endTime: schedule.endTime,
          is_enabled: schedule.isEnabled,
          isEnabled: schedule.isEnabled,
        })),

        evaluations: evaluations.map(evaluation => ({
          id: evaluation.id,
          rating: evaluation.rating,
          comment: evaluation.comment || '',
          createdAt: evaluation.createdAt.toISOString(),
        })),
      };

      console.log("[PUBLIC_BY_ID] Objeto final seguro criado");
      
      await cacheService.set(cacheKey, safeDoctor, 600);
      console.log("[PUBLIC_BY_ID] Dados salvos no cache");
      
      console.log("[PUBLIC_BY_ID] Retornando dados do médico");
      return safeDoctor;
    } catch (error) {
      console.error("[PUBLIC_BY_ID] Erro:", error);

      if (error instanceof TRPCError) {
        console.error("[PUBLIC_BY_ID] Erro TRPC:", error.code, error.message);
        throw error;
      }

      console.error("[PUBLIC_BY_ID] Erro desconhecido, lançando INTERNAL_SERVER_ERROR");
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar médico",
        cause: error,
      });
    }
  });
