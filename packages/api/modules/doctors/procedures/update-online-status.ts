import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const UpdateOnlineStatusSchema = z.object({
  isOnline: z.boolean(),
});

export const updateOnlineStatus = protectedProcedure
  .input(UpdateOnlineStatusSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar se o usuário é um médico
      if (user.role !== "DOCTOR") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Apenas médicos podem atualizar seu status online",
        });
      }

      const doctor = await db.doctor.findUnique({
        where: {
          userId: user.id,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Perfil de médico não encontrado",
        });
      }

      // Atualizar o status online do médico
      const updatedDoctor = await db.doctor.update({
        where: { id: doctor.id },
        data: {
          onlineStatus: input.isOnline ? "ONLINE" : "OFFLINE",
        },
      });

      return {
        success: true,
        onlineStatus: updatedDoctor.onlineStatus,
      };
    } catch (error) {
      console.error("Erro ao atualizar status online:", error);
      throw error;
    }
  });
