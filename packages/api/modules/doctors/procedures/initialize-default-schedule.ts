import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const InitializeScheduleInputSchema = z.object({
  doctorId: z.string(),
});

const ScheduleOutputSchema = z.array(
  z.object({
    id: z.string(),
    doctorId: z.string(),
    weekDay: z.number(),
    startTime: z.string(),
    endTime: z.string(),
    isEnabled: z.boolean(),
    isBreak: z.boolean(),
  })
);

export const initializeDefaultScheduleProcedure = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
    })
  )
  .output(
    z.object({
      success: z.boolean(),
      message: z.string(),
      scheduleCount: z.number(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    const { doctorId } = input;
    const { user } = ctx;

    try {
      console.log(`Iniciando criação de horários padrão para médico: ${doctorId}`);

      // Verificar se médico existe
      const doctor = await db.doctor.findUnique({
        where: { id: doctorId },
        select: { id: true, userId: true },
      });

      if (!doctor) {
        console.error(`Médico não encontrado ao inicializar horários padrão: ${doctorId}`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      // Verificar permissões - apenas o próprio médico ou administrador pode criar horários
      if (user.id !== doctor.userId && user.role !== "ADMIN") {
        console.error(`Usuário ${user.id} sem permissão para criar horários para médico ${doctorId}`);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para criar horários para este médico",
        });
      }

      // Verificar se já existem horários
      const existingSchedulesCount = await db.doctorSchedule.count({
        where: { doctorId },
      });

      console.log(`Horários existentes para o médico ${doctorId}: ${existingSchedulesCount}`);

      // Se já existem horários, não precisamos criar novos
      if (existingSchedulesCount > 0) {
        console.log(`Médico ${doctorId} já possui horários configurados, apenas atualizando time slots`);
        await updateTimeSlotsForDoctor(doctorId);
        return { 
          success: true, 
          message: "Horários já existem, time slots atualizados",
          scheduleCount: existingSchedulesCount
        };
      }

      // Criar horários padrão
      const defaultSchedules = createDefaultSchedules(doctorId);
      
      if (defaultSchedules.length === 0) {
        console.error(`Não foi possível criar horários padrão para médico ${doctorId}`);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao criar horários padrão",
        });
      }

      console.log(`Criando ${defaultSchedules.length} horários padrão para médico ${doctorId}`);

      try {
        // Criar horários em uma transação
        const createdSchedules = await db.doctorSchedule.createMany({
          data: defaultSchedules,
        });

        console.log(`Horários padrão criados para médico ${doctorId}: ${createdSchedules.count}`);

        // Atualizar time slots para refletir os novos horários
        await updateTimeSlotsForDoctor(doctorId);

        return { 
          success: true, 
          message: "Horários padrão criados com sucesso",
          scheduleCount: createdSchedules.count 
        };
      } catch (createError: any) {
        console.error(`Erro ao criar horários padrão para médico ${doctorId}:`, createError);
        
        // Verificar se é um problema de duplicação
        if (createError.code === 'P2002') {
          // Tentar recuperar criando horários individualmente
          console.log(`Tentando criar horários individualmente para médico ${doctorId}...`);
          let createdCount = 0;
          
          for (const schedule of defaultSchedules) {
            try {
              await db.doctorSchedule.create({
                data: schedule,
              });
              createdCount++;
            } catch (individualError) {
              console.warn(`Não foi possível criar horário individual para médico ${doctorId} (dia ${schedule.weekDay}):`, individualError);
              // Continuar para o próximo horário
            }
          }
          
          if (createdCount > 0) {
            console.log(`${createdCount} horários criados individualmente para médico ${doctorId}`);
            // Atualizar time slots para refletir os novos horários
            await updateTimeSlotsForDoctor(doctorId);
            
            return { 
              success: true, 
              message: "Alguns horários padrão foram criados com sucesso",
              scheduleCount: createdCount 
            };
          }
        }
        
        // Se não conseguiu recuperar, lança o erro
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao criar horários padrão",
          cause: createError,
        });
      }
    } catch (error) {
      console.error("Erro ao inicializar horários padrão:", error);
      
      // Se for um erro TRPCError, apenas propaga
      if (error instanceof TRPCError) {
        throw error;
      }
      
      // Caso contrário, cria um novo TRPCError
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao inicializar horários padrão",
        cause: error,
      });
    }
  });

// Função para criar os horários padrão para um médico
function createDefaultSchedules(doctorId: string) {
  try {
    console.log(`Gerando configuração de horários padrão para médico ${doctorId}`);
    const defaultSchedules = [];

    // Horários para dias úteis (1 = Segunda a 5 = Sexta)
    for (let weekDay = 1; weekDay <= 5; weekDay++) {
      // Horário da manhã (9h às 12h)
      defaultSchedules.push({
        doctorId,
        weekDay,
        startTime: "09:00",
        endTime: "12:00",
        isEnabled: true,
        isBreak: false,
      });

      // Intervalo de almoço (12h às 14h)
      defaultSchedules.push({
        doctorId,
        weekDay,
        startTime: "12:00",
        endTime: "14:00",
        isEnabled: true,
        isBreak: true,
      });

      // Horário da tarde (14h às 18h)
      defaultSchedules.push({
        doctorId,
        weekDay,
        startTime: "14:00",
        endTime: "18:00",
        isEnabled: true,
        isBreak: false,
      });
    }

    // Horários para sábados (6 = Sábado) - apenas pela manhã
    defaultSchedules.push({
      doctorId,
      weekDay: 6,
      startTime: "09:00",
      endTime: "13:00",
      isEnabled: true,
      isBreak: false,
    });

    // Domingos e feriados não há atendimento (não adicionamos horários)

    console.log(`${defaultSchedules.length} horários padrão gerados para médico ${doctorId}`);
    return defaultSchedules;
  } catch (error) {
    console.error(`Erro ao gerar horários padrão para médico ${doctorId}:`, error);
    return [];
  }
}

// Função auxiliar para atualizar os time slots do médico
async function updateTimeSlotsForDoctor(doctorId: string) {
  try {
    console.log(`Inicializando time slots para médico: ${doctorId}`);

    // Obter horários do médico e informações em uma única transação
    const [schedules, blocks, doctor] = await Promise.all([
      db.doctorSchedule.findMany({
        where: { doctorId, isEnabled: true, isBreak: false },
      }),
      db.scheduleBlock.findMany({
        where: {
          doctorId,
          endTime: { gte: new Date() },
        },
      }),
      db.doctor.findUnique({
        where: { id: doctorId },
        select: { consultationDuration: true },
      }),
    ]);

    if (!doctor) {
      console.error(`Médico não encontrado na inicialização de time slots: ${doctorId}`);
      throw new Error("Doctor not found");
    }

    // Garantir que a duração da consulta seja válida
    const duration = doctor.consultationDuration && doctor.consultationDuration > 0 
      ? doctor.consultationDuration 
      : 30;
    
    console.log(`Duração da consulta para inicialização: ${duration} minutos`);
    
    // Usar um período mais curto para inicialização (2 semanas)
    const today = new Date();
    const twoWeeksLater = new Date();
    twoWeeksLater.setDate(today.getDate() + 14);
    
    console.log(`Período de geração de slots na inicialização: ${today.toISOString()} até ${twoWeeksLater.toISOString()}`);

    // Limpar slots futuros não reservados em uma única operação
    const deleteResult = await db.timeSlot.deleteMany({
      where: {
        doctorId,
        startTime: { gte: today },
        appointmentId: null,
      },
    });
    
    console.log(`Slots removidos durante a inicialização: ${deleteResult.count}`);

    // Verificar se existem horários configurados
    if (schedules.length === 0) {
      console.log(`Nenhum horário configurado para o médico ${doctorId}, não é possível gerar time slots`);
      return;
    }

    const slotsToCreate = [];
    let slotCount = 0;
    const maxSlotsPerBatch = 500; // Limitar o número de slots por lote

    for (let d = new Date(today); d <= twoWeeksLater; d.setDate(d.getDate() + 1)) {
      const weekDay = d.getDay();
      const daySchedules = schedules.filter(s => s.weekDay === weekDay);

      for (const daySchedule of daySchedules) {
        try {
          const [startHour, startMinute] = daySchedule.startTime.split(':').map(Number);
          const [endHour, endMinute] = daySchedule.endTime.split(':').map(Number);
          
          // Verificar se os valores de hora são válidos
          if (isNaN(startHour) || isNaN(startMinute) || isNaN(endHour) || isNaN(endMinute)) {
            console.warn(`Horário inválido na inicialização para médico ${doctorId}: ${daySchedule.startTime} - ${daySchedule.endTime}`);
            continue;
          }
          
          const startMinutes = startHour * 60 + startMinute;
          const endMinutes = endHour * 60 + endMinute;

          // Verificar se o horário de início é anterior ao de término
          if (startMinutes >= endMinutes) {
            console.warn(`Horário inválido na inicialização para médico ${doctorId}: início (${startMinutes} min) >= término (${endMinutes} min)`);
            continue;
          }

          for (let minutes = startMinutes; minutes < endMinutes; minutes += duration) {
            if (slotCount >= maxSlotsPerBatch) {
              console.log(`Limite de ${maxSlotsPerBatch} slots atingido na inicialização, criando lote atual`);
              break;
            }
            
            const slotDate = new Date(d);
            slotDate.setHours(Math.floor(minutes / 60), minutes % 60, 0, 0);

            const slotEndDate = new Date(slotDate);
            slotEndDate.setMinutes(slotEndDate.getMinutes() + duration);

            const isBlocked = blocks.some(block =>
              slotDate >= block.startTime && slotEndDate <= block.endTime
            );

            if (!isBlocked) {
              slotsToCreate.push({
                doctorId,
                startTime: slotDate,
                endTime: slotEndDate,
                isAvailable: true,
              });
              slotCount++;
            }
          }
        } catch (scheduleError) {
          console.error(`Erro ao processar horário na inicialização para médico ${doctorId}:`, scheduleError);
          // Continuar com o próximo horário
          continue;
        }
      }
    }

    console.log(`Total de slots a serem criados na inicialização: ${slotsToCreate.length}`);

    if (slotsToCreate.length > 0) {
      try {
        const createResult = await db.timeSlot.createMany({
          data: slotsToCreate,
          skipDuplicates: true,
        });
        console.log(`Slots criados com sucesso na inicialização: ${createResult.count}`);
      } catch (createError: any) {
        console.error(`Erro ao criar slots na inicialização para médico ${doctorId}:`, createError);
        
        // Tentar criar em lotes menores se falhar
        if (slotsToCreate.length > 50) {
          console.log(`Tentando criar slots em lotes menores durante a inicialização...`);
          const batchSize = 50;
          for (let i = 0; i < slotsToCreate.length; i += batchSize) {
            const batch = slotsToCreate.slice(i, i + batchSize);
            try {
              await db.timeSlot.createMany({
                data: batch,
                skipDuplicates: true,
              });
              console.log(`Lote ${i/batchSize + 1} criado com sucesso na inicialização (${batch.length} slots)`);
            } catch (batchError) {
              console.error(`Erro ao criar lote ${i/batchSize + 1} na inicialização:`, batchError);
              // Continuar com o próximo lote
            }
          }
        } else {
          throw createError;
        }
      }
    }
  } catch (error) {
    console.error("Erro ao inicializar time slots:", error);
    // Não propagar o erro para não interromper o fluxo principal
    // de criação de horários padrão
  }
}
