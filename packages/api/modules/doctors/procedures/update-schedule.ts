// packages/api/modules/doctors/procedures/update-schedule.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const ScheduleItemSchema = z.object({
  weekDay: z.number().min(0).max(6),
  startTime: z.string(),
  endTime: z.string(),
  isEnabled: z.boolean().default(true),
  isBreak: z.boolean().default(false),
});

const ScheduleInputSchema = z.object({
  doctorId: z.string(),
  schedule: z.array(ScheduleItemSchema),
  skipTimeSlotUpdate: z.boolean().default(false), 
});

const ScheduleOutputSchema = z.array(
  z.object({
    id: z.string(),
    doctorId: z.string(),
    weekDay: z.number(),
    startTime: z.string(),
    endTime: z.string(),
    isEnabled: z.boolean(),
  })
);

export const updateSchedule = protectedProcedure
  .input(ScheduleInputSchema)
  .output(ScheduleOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar permissões
      if (
        user?.role !== "ADMIN" &&
        user?.role !== "DOCTOR" &&
        user?.role !== "HOSPITAL"
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para atualizar horários do médico",
        });
      }

      // Se for médico, verificar se está atualizando seu próprio horário
      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: {
            userId: user.id,
          },
        });

        if (!doctor || doctor.id !== input.doctorId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Você só pode atualizar seu próprio horário",
          });
        }
      }

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: input.doctorId,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      // Remover horários existentes
      await db.doctorSchedule.deleteMany({
        where: {
          doctorId: input.doctorId,
        },
      });

      // Criar novos horários
      const schedules = await Promise.all(
        input.schedule.map(async (scheduleItem) => {
          return db.doctorSchedule.create({
            data: {
              doctorId: input.doctorId,
              weekDay: scheduleItem.weekDay,
              startTime: scheduleItem.startTime,
              endTime: scheduleItem.endTime,
              isEnabled: scheduleItem.isEnabled,
              isBreak: scheduleItem.isBreak,
            },
          });
        })
      );

      // Atualizar os time slots para o médico baseado nos novos horários (opcional)
      if (!input.skipTimeSlotUpdate) {
        try {
          await updateTimeSlotsForDoctor(input.doctorId);
        } catch (timeSlotError) {
          console.error("Erro ao atualizar time slots, mas os horários foram atualizados:", timeSlotError);
          // Não falhar a atualização dos horários se a atualização de time slots falhar
        }
      }

      return schedules;
    } catch (error) {
      console.error("Erro detalhado ao atualizar horários do médico:", error);
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao atualizar horários do médico",
        cause: error,
      });
    }
  });

// Função auxiliar para atualizar os time slots do médico
async function updateTimeSlotsForDoctor(doctorId: string) {
  try {
    console.log(`[updateTimeSlotsForDoctor] Iniciando atualização de slots para médico: ${doctorId}`);
    
    // Obter horários do médico e informações em uma única transação
    const [schedules, blocks, doctor] = await Promise.all([
      db.doctorSchedule.findMany({
        where: { doctorId, isEnabled: true },
      }),
      db.scheduleBlock.findMany({
        where: {
          doctorId,
          endTime: { gte: new Date() },
        },
      }),
      db.doctor.findUnique({
        where: { id: doctorId },
        select: { consultationDuration: true },
      }),
    ]);

    console.log(`[updateTimeSlotsForDoctor] Dados obtidos: ${schedules.length} horários, ${blocks.length} bloqueios`);

    if (!doctor) {
      console.error(`[updateTimeSlotsForDoctor] Médico não encontrado: ${doctorId}`);
      throw new Error("Doctor not found");
    }

    const duration = doctor.consultationDuration || 30;
    const today = new Date();
    const fourWeeksLater = new Date();
    fourWeeksLater.setDate(today.getDate() + 28);

    console.log(`[updateTimeSlotsForDoctor] Removendo slots existentes para médico: ${doctorId}`);
    
    // Limpar slots futuros não reservados em uma única operação
    const deletedSlots = await db.timeSlot.deleteMany({
      where: {
        doctorId,
        startTime: { gte: today },
        appointmentId: null,
      },
    });
    
    console.log(`[updateTimeSlotsForDoctor] ${deletedSlots.count} slots removidos`);

    // Preparar todos os slots para inserção em lote
    const slotsToCreate = [];

    for (let d = new Date(today); d <= fourWeeksLater; d.setDate(d.getDate() + 1)) {
      const weekDay = d.getDay();
      const daySchedules = schedules.filter(s => s.weekDay === weekDay && !s.isBreak);

      for (const daySchedule of daySchedules) {
        if (daySchedule?.isEnabled) {
          try {
            const [startHour, startMinute] = daySchedule.startTime.split(':').map(Number);
            const [endHour, endMinute] = daySchedule.endTime.split(':').map(Number);
            const startMinutes = startHour * 60 + startMinute;
            const endMinutes = endHour * 60 + endMinute;

            for (let minutes = startMinutes; minutes < endMinutes; minutes += duration) {
              const slotDate = new Date(d);
              slotDate.setHours(Math.floor(minutes / 60), minutes % 60, 0, 0);

              const slotEndDate = new Date(slotDate);
              slotEndDate.setMinutes(slotEndDate.getMinutes() + duration);

              const isBlocked = blocks.some(block =>
                slotDate >= block.startTime && slotEndDate <= block.endTime
              );

              if (!isBlocked) {
                slotsToCreate.push({
                  doctorId,
                  startTime: slotDate,
                  endTime: slotEndDate,
                  isAvailable: true,
                });
              }
            }
          } catch (slotError) {
            console.error(`[updateTimeSlotsForDoctor] Erro ao processar horário: ${JSON.stringify(daySchedule)}`, slotError);
          }
        }
      }
    }

    console.log(`[updateTimeSlotsForDoctor] ${slotsToCreate.length} novos slots preparados para criação`);

    // Criar todos os slots em uma única operação em lote
    if (slotsToCreate.length > 0) {
      try {
        await db.timeSlot.createMany({
          data: slotsToCreate,
          skipDuplicates: true,
        });
        console.log(`[updateTimeSlotsForDoctor] Slots criados com sucesso para médico: ${doctorId}`);
      } catch (createError) {
        console.error(`[updateTimeSlotsForDoctor] Erro ao criar slots em lote:`, createError);
        throw createError;
      }
    } else {
      console.log(`[updateTimeSlotsForDoctor] Nenhum slot para criar para médico: ${doctorId}`);
    }
  } catch (error) {
    console.error("Erro ao atualizar time slots:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Erro ao atualizar time slots do médico",
      cause: error,
    });
  }
}
