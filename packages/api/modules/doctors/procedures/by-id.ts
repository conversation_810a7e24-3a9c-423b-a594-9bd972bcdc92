import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { serializeObject } from "../../../utils/decimal-serializer";

export const getById = protectedProcedure
  .input(
    z.object({
      id: z.string().min(1, "ID do médico é obrigatório"),
    })
  )
  .query(async ({ input }) => {
    try {
      const { id } = input;

      const doctor = await db.doctor.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              emailVerified: true,
              phone: true,
              avatarUrl: true,
            },
          },
          specialties: true,
          doctorSchedules: true,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      return serializeObject(doctor);
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching doctor:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar detalhes do médico",
        cause: error,
      });
    }
  });
