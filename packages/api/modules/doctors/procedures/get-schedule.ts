// packages/api/modules/doctors/procedures/get-schedule.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const DoctorScheduleOutputSchema = z.array(
  z.object({
    id: z.string(),
    doctorId: z.string(),
    weekDay: z.number(),
    startTime: z.string(),
    endTime: z.string(),
    isEnabled: z.boolean(),
    isBreak: z.boolean().default(false),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  })
);

export const getSchedule = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
    })
  )
  .output(DoctorScheduleOutputSchema)
  .query(async ({ input, ctx }) => {
    try {
      // Verificar autenticação
      const { user } = ctx;
      if (!user) {
        console.error("[getSchedule] Usu<PERSON><PERSON> não autenticado");
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Usuário não autenticado",
        });
      }

      console.log(`[getSchedule] Usuário ${user.id} (${user.role}) buscando horários para o médico: ${input.doctorId}`);

      // Validar se o doctorId é válido
      if (!input.doctorId || input.doctorId === "") {
        console.log("[getSchedule] ID do médico vazio ou inválido, retornando array vazio");
        return [];
      }

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: input.doctorId,
        },
        select: {
          id: true,
          userId: true,
        }
      });

      if (!doctor) {
        console.warn(`[getSchedule] Médico não encontrado: ${input.doctorId}`);
        return []; // Retornar array vazio em vez de erro
      }

      // Verificar permissões (apenas para log, não bloquear)
      if (user.role === "DOCTOR" && doctor.userId !== user.id) {
        console.warn(`[getSchedule] Médico ${user.id} tentando acessar horários de outro médico ${input.doctorId}`);
      }

      console.log(`[getSchedule] Buscando horários para o médico: ${input.doctorId}`);

      const schedules = await db.doctorSchedule.findMany({
        where: {
          doctorId: input.doctorId,
        },
        orderBy: {
          weekDay: "asc",
        },
      });

      // Garantir que todos os campos estejam presentes
      const safeSchedules = schedules.map(schedule => ({
        ...schedule,
        isBreak: schedule.isBreak ?? false, // Garantir que isBreak tenha um valor padrão
      }));

      console.log(`[getSchedule] Encontrados ${safeSchedules.length} horários para o médico ${input.doctorId}`);
      return safeSchedules;
    } catch (error) {
      console.error("[getSchedule] Erro detalhado ao buscar horários do médico:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar horários do médico",
        cause: error,
      });
    }
  });
