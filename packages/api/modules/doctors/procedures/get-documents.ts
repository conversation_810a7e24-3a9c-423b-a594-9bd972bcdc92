import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "database";
import { protectedProcedure } from "../../../trpc/base";

export const getDocuments = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
    })
  )
  .query(async ({ ctx, input }) => {
    try {
      const { doctorId } = input;

      return await db.doctorDocument.findMany({
        where: {
          doctorId,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar documentos do médico",
        cause: error,
      });
    }
  });
