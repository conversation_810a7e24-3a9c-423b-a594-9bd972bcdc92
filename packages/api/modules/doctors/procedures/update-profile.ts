import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { serializeObject } from "../../../utils/decimal-serializer";

// Schema mais flexível para atualização do próprio perfil pelo médico
const DoctorProfileUpdateSchema = z.object({
  id: z.string(),
  crm: z.string().min(4, "CRM inválido"),
  crmState: z.string().min(2, "Estado de registro inválido"),
  biography: z.string().optional(),
  specialtyIds: z.array(z.string()).min(1, "Selecione pelo menos uma especialidade"),
  consultationPrice: z.number().min(0).default(0),
  consultationDuration: z.number().min(15).default(30),
  returnPeriod: z.number().min(0).default(0),
  consultTypes: z.array(z.string()).optional(),
  bankAccount: z.object({
    bank: z.string().optional(),
    agency: z.string().optional(),
    account: z.string().optional(),
    accountType: z.enum(["CHECKING", "SAVINGS"]).optional(),
    document: z.string().optional()
  }).optional().nullable(),
  hospitalIds: z.array(z.string()).optional(),
  schedulingEnabled: z.boolean().optional(),
  schedules: z
    .array(
      z.object({
        dayOfWeek: z.number().min(0).max(6),
        startTime: z.string(),
        endTime: z.string(),
      })
    )
    .optional(),
});

export const updateProfile = protectedProcedure
  .input(DoctorProfileUpdateSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar se o usuário é um médico
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          specialties: true,
          hospitals: true,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Perfil de médico não encontrado",
        });
      }

      // Verificar se o ID fornecido corresponde ao médico atual
      if (doctor.id !== input.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Você só pode atualizar seu próprio perfil",
        });
      }

      // Verificar se o CRM está sendo alterado e se já existe
      if (
        doctor.crm !== input.crm ||
        doctor.crmState !== input.crmState
      ) {
        const duplicateCrm = await db.doctor.findFirst({
          where: {
            crm: input.crm,
            crmState: input.crmState,
            id: { not: input.id },
          },
        });

        if (duplicateCrm) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "CRM já cadastrado para outro médico",
          });
        }
      }

      // Atualizar médico
      const updatedDoctor = await db.doctor.update({
        where: { id: input.id },
        data: {
          crm: input.crm,
          crmState: input.crmState,
          consultationPrice: input.consultationPrice,
          consultationDuration: input.consultationDuration,
          returnPeriod: input.returnPeriod,
          consultTypes: input.consultTypes as any,
          biography: input.biography || null,
          bankAccount: input.bankAccount,
          specialties: {
            disconnect: doctor.specialties.map((s) => ({ id: s.id })),
            connect: input.specialtyIds.map((id) => ({ id })),
          },
        },
        include: {
          user: true,
          specialties: true,
        },
      });

      // Update hospital associations if provided
      if (input.hospitalIds) {
        // First, remove all current hospital associations
        await db.doctorHospital.deleteMany({
          where: {
            doctorId: input.id,
          },
        });

        // Then create new associations
        for (const hospitalId of input.hospitalIds) {
          await db.doctorHospital.create({
            data: {
              doctorId: input.id,
              hospitalId,
              isActive: true,
            },
          });
        }
      }

      // Update schedules if provided
      if (input.schedules) {
        // First, delete existing schedules
        await db.doctorSchedule.deleteMany({
          where: {
            doctorId: input.id,
          },
        });

        // Then create new schedules
        for (const schedule of input.schedules) {
          await db.doctorSchedule.create({
            data: {
              doctorId: input.id,
              weekDay: schedule.dayOfWeek,
              startTime: schedule.startTime,
              endTime: schedule.endTime,
            },
          });
        }
      }

      return serializeObject(updatedDoctor);
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao atualizar perfil do médico",
        cause: error,
      });
    }
  });
