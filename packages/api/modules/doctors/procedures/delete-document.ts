import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "database";
import { protectedProcedure } from "../../../trpc/base";

export const deleteDocument = protectedProcedure
  .input(
    z.object({
      id: z.string(),
    })
  )
  .mutation(async ({ ctx, input }) => {
    try {
      const { id } = input;

      // Only allow deletion of pending documents
      const document = await db.doctorDocument.findUnique({
        where: {
          id,
        },
      });

      if (!document || document.status !== 'PENDING') {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Documento não encontrado ou não pode ser excluído",
        });
      }

      // Delete the document from the database
      return await db.doctorDocument.delete({
        where: {
          id,
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao excluir documento",
        cause: error,
      });
    }
  });
