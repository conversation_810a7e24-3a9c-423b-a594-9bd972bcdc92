import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada
const ListDoctorsInputSchema = z.object({
  query: z.string().optional(),
  specialtyId: z.string().optional(),
  onlineOnly: z.boolean().optional(),
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(100).default(20),
}).optional();

export const publicList = publicProcedure
  .input(ListDoctorsInputSchema)
  .query(async ({ input = {} }) => {
    try {
      const {
        query = "",
        specialtyId,
        onlineOnly,
        page = 1,
        perPage = 20
      } = input;

      console.log("[PUBLIC_LIST] Buscando médicos com filtros:", {
        query, specialtyId, onlineOnly, page, perPage
      });

      // Calcular o offset para paginação
      const skip = (page - 1) * perPage;

      // Construir o where clause
      const whereClause: any = {
        documentStatus: "APPROVED",
      };

      // Adicionar filtro de especialidade se fornecido
      if (specialtyId) {
        whereClause.specialties = {
          some: {
            id: specialtyId,
          },
        };
      }

      // Adicionar filtro de status online
      if (onlineOnly) {
        whereClause.onlineStatus = "ONLINE";
      }

      // Adicionar filtro de busca por texto
      if (query) {
        whereClause.OR = [
          {
            user: {
              name: {
                contains: query,
                mode: "insensitive",
              },
            },
          },
          {
            specialties: {
              some: {
                name: {
                  contains: query,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            biography: {
              contains: query,
              mode: "insensitive",
            },
          },
        ];
      }

      console.log("[PUBLIC_LIST] Where clause:", JSON.stringify(whereClause));

      // Buscar total de médicos para paginação
      const total = await db.doctor.count({
        where: whereClause,
      });

      // Buscar o total de médicos online
      const onlineCount = await db.doctor.count({
        where: {
          ...whereClause,
          onlineStatus: "ONLINE",
        },
      });

      // Buscar médicos
      const doctors = await db.doctor.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatarUrl: true,
            },
          },
          specialties: {
            select: {
              id: true,
              name: true,
            },
          },
          evaluations: {
            select: {
              id: true,
              rating: true,
            },
          },
        },
        skip,
        take: perPage,
        orderBy: [
          {
            onlineStatus: "asc",
          },
          {
            rating: "desc",
          },
        ],
      });

      console.log(`[PUBLIC_LIST] Encontrados ${doctors.length} médicos`);

      // Formatar os dados dos médicos
      const formattedDoctors = doctors.map((doctor) => {
        // Calcular a média de avaliações
        const ratings = doctor.evaluations.map(e => e.rating);
        const averageRating = ratings.length > 0
          ? ratings.reduce((a, b) => a + b, 0) / ratings.length
          : null;

        return {
          id: doctor.id,
          user: {
            id: doctor.user.id,
            name: doctor.user.name || '',
            email: doctor.user.email || '',
            avatarUrl: doctor.user.avatarUrl || null,
          },
          crm: doctor.crm || '',
          crmState: doctor.crmState || '',
          biography: doctor.biography || '',
          consultationPrice: doctor.consultationPrice ? Number(doctor.consultationPrice) : 0,
          consultationDuration: doctor.consultationDuration || 30,
          onlineStatus: doctor.onlineStatus || 'OFFLINE',
          rating: averageRating || 0,
          totalRatings: doctor.evaluations.length,
          specialties: doctor.specialties || [],
        };
      });

      // Buscar todas as especialidades para o filtro
      const specialties = await db.specialty.findMany({
        select: {
          id: true,
          name: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      console.log(`[PUBLIC_LIST] Retornando ${formattedDoctors.length} médicos formatados`);

      return {
        doctors: formattedDoctors,
        pagination: {
          total,
          page,
          perPage,
          pageCount: Math.ceil(total / perPage),
        },
        specialties,
        onlineCount,
      };
    } catch (error) {
      console.error("[PUBLIC_LIST] Erro:", error);

      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao listar médicos",
        cause: error,
      });
    }
  });
