import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const DeleteScheduleInputSchema = z.object({
  id: z.string(),
});

export const deleteSchedule = protectedProcedure
  .input(DeleteScheduleInputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;
      const { id } = input;

      // Buscar o horário para verificar permissões
      const schedule = await db.doctorSchedule.findUnique({
        where: { id },
        include: {
          doctor: {
            select: {
              userId: true,
            },
          },
        },
      });

      if (!schedule) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Hor<PERSON><PERSON> não encontrado",
        });
      }

      // Verificar permissões
      if (
        user?.role !== "ADMIN" &&
        user?.role !== "HOSPITAL" &&
        (user?.role !== "DOCTOR" || user.id !== schedule.doctor.userId)
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para excluir este horário",
        });
      }

      // Excluir o horário
      await db.doctorSchedule.delete({
        where: { id },
      });

      // Atualizar os time slots para o médico
      await updateTimeSlotsForDoctor(schedule.doctorId);

      return { success: true };
    } catch (error) {
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao excluir horário do médico",
        cause: error,
      });
    }
  });

// Função auxiliar para atualizar os time slots do médico
async function updateTimeSlotsForDoctor(doctorId: string) {
  try {
    // Obter horários do médico e informações em uma única transação
    const [schedules, blocks, doctor] = await Promise.all([
      db.doctorSchedule.findMany({
        where: { doctorId, isEnabled: true },
      }),
      db.scheduleBlock.findMany({
        where: {
          doctorId,
          endTime: { gte: new Date() },
        },
      }),
      db.doctor.findUnique({
        where: { id: doctorId },
        select: { consultationDuration: true },
      }),
    ]);

    if (!doctor) {
      throw new Error("Doctor not found");
    }

    const duration = doctor.consultationDuration || 30;
    const today = new Date();
    const fourWeeksLater = new Date();
    fourWeeksLater.setDate(today.getDate() + 28);

    // Limpar slots futuros não reservados em uma única operação
    await db.timeSlot.deleteMany({
      where: {
        doctorId,
        startTime: { gte: today },
        appointmentId: null,
      },
    });

    // Preparar todos os slots para inserção em lote
    const slotsToCreate = [];

    for (let d = new Date(today); d <= fourWeeksLater; d.setDate(d.getDate() + 1)) {
      const weekDay = d.getDay();
      const daySchedules = schedules.filter(s => s.weekDay === weekDay);

      for (const daySchedule of daySchedules) {
        const [startHour, startMinute] = daySchedule.startTime.split(':').map(Number);
        const [endHour, endMinute] = daySchedule.endTime.split(':').map(Number);
        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;

        for (let minutes = startMinutes; minutes < endMinutes; minutes += duration) {
          const slotDate = new Date(d);
          slotDate.setHours(Math.floor(minutes / 60), minutes % 60, 0, 0);

          const slotEndDate = new Date(slotDate);
          slotEndDate.setMinutes(slotEndDate.getMinutes() + duration);

          const isBlocked = blocks.some(block =>
            slotDate >= block.startTime && slotEndDate <= block.endTime
          );

          if (!isBlocked) {
            slotsToCreate.push({
              doctorId,
              startTime: slotDate,
              endTime: slotEndDate,
              isAvailable: true,
            });
          }
        }
      }
    }

    // Criar todos os slots em uma única operação em lote
    if (slotsToCreate.length > 0) {
      await db.timeSlot.createMany({
        data: slotsToCreate,
        skipDuplicates: true,
      });
    }
  } catch (error) {
    console.error("Erro ao atualizar time slots:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Erro ao atualizar time slots do médico",
      cause: error,
    });
  }
}
