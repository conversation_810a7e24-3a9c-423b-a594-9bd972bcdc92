import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { adminProcedure } from "../../../trpc/base";

export const approve = adminProcedure
  .input(
    z.object({
      id: z.string(),
    })
  )
  .mutation(async ({ input }) => {
    try {
      const doctor = await db.doctor.findUnique({
        where: { id: input.id },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      const updatedDoctor = await db.doctor.update({
        where: { id: input.id },
        data: {
          documentStatus: "APPROVED",
        },
      });

      return updatedDoctor;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao aprovar médico",
        cause: error,
      });
    }
  });
