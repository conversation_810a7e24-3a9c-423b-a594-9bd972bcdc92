// packages/api/modules/doctors/procedures/list.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { serializeObject } from "../../../utils/decimal-serializer";

// Schema para validar os dados de entrada
const ListDoctorsInputSchema = z.object({
  searchTerm: z.string().optional(),
  hospitalId: z.string().optional(),
  documentStatus: z.enum(["PENDING", "APPROVED", "REJECTED"]).optional(),
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(100).default(20),
}).optional();

// Schema para a saída
const DoctorOutputSchema = z.object({
  id: z.string(),
  user: z.object({
    name: z.string().nullable(),
    email: z.string().nullable(),
    image: z.string().nullable(),
    phone: z.string().nullable(),
  }),
  crm: z.string(),
  crmState: z.string(),
  specialty: z.string().nullable(),
  consultationPrice: z.number(),
  consultationDuration: z.number(),
  biography: z.string().nullable(),
  documentStatus: z.string(),
  rating: z.number().nullable(),
  totalRatings: z.number(),
  appointments: z.array(z.any()),
});

export const list = protectedProcedure
  .input(ListDoctorsInputSchema)
  .output(z.array(DoctorOutputSchema))
  .query(async ({ input, ctx }) => {
    try {
      console.log("Executing doctors.list procedure with input:", input);

      // Construir os filtros da query
      const where: any = {};

      // Filtro por termo de busca
      if (input?.searchTerm) {
        where.OR = [
          {
            user: {
              name: {
                contains: input.searchTerm,
                mode: "insensitive",
              },
            },
          },
          {
            user: {
              email: {
                contains: input.searchTerm,
                mode: "insensitive",
              },
            },
          },
          {
            crm: {
              contains: input.searchTerm,
            },
          },
          {
            specialties: {
              some: {
                name: {
                  contains: input.searchTerm,
                  mode: "insensitive",
                },
              },
            },
          },
        ];
      }

      // Filtro por hospital
      if (input?.hospitalId) {
        where.hospitals = {
          some: {
            hospitalId: input.hospitalId,
            isActive: true,
          },
        };
      }

      // Filtro por status do documento
      if (input?.documentStatus) {
        where.documentStatus = input.documentStatus;
      }

      // Calcular paginação
      const skip = input?.page ? (input.page - 1) * (input.perPage || 20) : undefined;
      const take = input?.perPage || undefined;

      // Executar a query com a abordagem que funcionou
      const doctors = await db.doctor.findMany({
        where,
        include: {
          user: {
            select: {
              name: true,
              email: true,
              avatarUrl: true,
              phone: true,
            },
          },
          specialties: {
            select: {
              name: true,
            },
            take: 3, // Pegar até 3 especialidades
          },
          _count: {
            select: {
              appointments: {
                where: {
                  status: "COMPLETED",
                },
              },
            },
          },
        },
        orderBy: {
          user: {
            name: "asc",
          },
        },
        skip,
        take,
      });

      console.log(`Found ${doctors.length} doctors`);

      // Serialize para lidar com campos Decimal
      const serializedDoctors = serializeObject(doctors);

      // Formatar os dados para o formato esperado
      return serializedDoctors.map((doctor) => ({
        id: doctor.id,
        user: {
          name: doctor.user?.name ?? "Médico sem nome",
          email: doctor.user?.email ?? "",
          image: doctor.user?.avatarUrl ?? null,
          phone: doctor.user?.phone ?? null,
        },
        crm: doctor.crm,
        crmState: doctor.crmState,
        specialty: doctor.specialties.length > 0
          ? doctor.specialties.map((s: any) => s.name).join(", ")
          : null,
        consultationPrice: Number(doctor.consultationPrice),
        consultationDuration: doctor.consultationDuration,
        biography: doctor.biography ?? null,
        documentStatus: doctor.documentStatus,
        rating: doctor.rating ?? null,
        totalRatings: doctor.totalRatings,
        appointments: Array(doctor._count.appointments).fill(null),
      }));
    } catch (error) {
      console.error("Error in doctors.list procedure:", error);

      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao listar médicos",
        cause: error,
      });
    }
  });
