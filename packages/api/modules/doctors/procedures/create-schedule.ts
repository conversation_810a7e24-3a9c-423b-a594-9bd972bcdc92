import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const CreateScheduleInputSchema = z.object({
  doctorId: z.string(),
  weekDay: z.number().min(0).max(6),
  startTime: z.string(),
  endTime: z.string(),
  isEnabled: z.boolean().default(true),
  isBreak: z.boolean().default(false),
  skipTimeSlotUpdate: z.boolean().default(false), 
});

const ScheduleOutputSchema = z.object({
  id: z.string(),
  doctorId: z.string(),
  weekDay: z.number(),
  startTime: z.string(),
  endTime: z.string(),
  isEnabled: z.boolean(),
  isBreak: z.boolean(),
});

export const createSchedule = protectedProcedure
  .input(CreateScheduleInputSchema)
  .output(ScheduleOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      console.log("Recebendo requisição de criação de horário:", input ? JSON.stringify(input, null, 2) : "input is null or undefined");
      
      if (!input) {
        console.error("Erro: Payload de entrada nulo ou inválido");
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Dados do horário não fornecidos",
        });
      }
      
      const { user } = ctx;
      const { doctorId, weekDay, startTime, endTime, isEnabled, isBreak, skipTimeSlotUpdate } = input;
      
      // Verificações adicionais de validação
      if (!doctorId) {
        console.error("Erro: doctorId não fornecido");
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "ID do médico é obrigatório",
        });
      }
      
      if (weekDay === undefined || weekDay < 0 || weekDay > 6) {
        console.error(`Erro: weekDay inválido: ${weekDay}`);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Dia da semana inválido (deve ser de 0 a 6)",
        });
      }
      
      if (!startTime || !endTime) {
        console.error(`Erro: horários inválidos - início: ${startTime}, fim: ${endTime}`);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Horários de início e fim são obrigatórios",
        });
      }

      // Validate time format
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
        console.error(`Erro: formato de horário inválido. Início: ${startTime}, Fim: ${endTime}`);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Formato de horário inválido. Use HH:MM",
        });
      }

      // Validate startTime < endTime
      if (startTime >= endTime) {
        console.error(`Erro: horário de início maior ou igual ao fim. Início: ${startTime}, Fim: ${endTime}`);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Horário de início deve ser anterior ao horário de término",
        });
      }

      if (
        user?.role !== "ADMIN" &&
        user?.role !== "DOCTOR" &&
        user?.role !== "HOSPITAL"
      ) {
        console.error(`Usuário sem permissão. Role: ${user?.role}`);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para criar horários do médico",
        });
      }

      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: {
            userId: user.id,
          },
        });

        if (!doctor || doctor.id !== doctorId) {
          console.error(`Médico tentando criar horário para outro médico. UserId: ${user.id}, DoctorId solicitado: ${doctorId}`);
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Você só pode criar seu próprio horário",
          });
        }
      }

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: doctorId,
        },
      });

      if (!doctor) {
        console.error(`Médico não encontrado. ID: ${doctorId}`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      // Verificar se já existe um horário configurado para o mesmo dia e período
      const existingSchedule = await db.doctorSchedule.findFirst({
        where: {
          doctorId,
          weekDay,
          OR: [
            {
              // Verificar sobreposição: novo horário começa dentro de um existente
              startTime: { lte: endTime },
              endTime: { gte: startTime }
            }
          ]
        },
      });

      if (existingSchedule) {
        console.log(`Horário existente encontrado para mesmo período. ID: ${existingSchedule.id}`);
        console.log(`Detalhes do horário existente: ${JSON.stringify(existingSchedule)}`);
        console.log(`Detalhes do novo horário: ${JSON.stringify(input)}`);
        
        // Atualizar o existente em vez de criar um novo
        const updatedSchedule = await db.doctorSchedule.update({
          where: { id: existingSchedule.id },
          data: {
            startTime,
            endTime,
            isEnabled,
            isBreak,
          },
        });
        
        console.log(`Horário existente atualizado: ${updatedSchedule.id}`);

        if (!skipTimeSlotUpdate) {
          try {
            await updateTimeSlotsForDoctor(doctorId);
          } catch (timeSlotError) {
            console.error("Erro ao atualizar time slots, mas o horário foi atualizado:", timeSlotError);
          }
        }

        return updatedSchedule;
      }

      // Criar novo horário
      console.log(`Criando novo horário para médico ${doctorId} - Dia ${weekDay}, ${startTime} às ${endTime}`);
      const schedule = await db.doctorSchedule.create({
        data: {
          doctorId,
          weekDay,
          startTime,
          endTime,
          isEnabled,
          isBreak,
        },
      });
      
      console.log(`Horário criado com sucesso: ${schedule.id}`);

      if (!skipTimeSlotUpdate) {
        try {
          console.log(`Atualizando time slots para o médico ${doctorId}`);
          await updateTimeSlotsForDoctor(doctorId);
        } catch (timeSlotError) {
          console.error("Erro ao atualizar time slots, mas o horário foi criado:", timeSlotError);
        }
      }

      return schedule;
    } catch (error) {
      console.error("Erro detalhado ao criar horário do médico:", error);
      
      // Verificar se já é um TRPCError
      if (error instanceof TRPCError) {
        throw error;
      }
      
      if (error instanceof Error) {
        // Tratar erros de validação do Prisma
        if (error.name === 'PrismaClientValidationError') {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Dados inválidos para criar horário",
            cause: error
          });
        }
        
        // Tratar erros de violação de chave única
        if (error.name === 'PrismaClientKnownRequestError' && (error as any).code === 'P2002') {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Já existe um horário com essas configurações",
            cause: error
          });
        }
      }
      
      // Outros erros
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao criar horário do médico",
        cause: error,
      });
    }
  });

async function updateTimeSlotsForDoctor(doctorId: string) {
  try {
    console.log(`Iniciando atualização de time slots para médico: ${doctorId}`);
    
    const [schedules, blocks, doctor] = await Promise.all([
      db.doctorSchedule.findMany({
        where: { doctorId, isEnabled: true, isBreak: false },
      }),
      db.scheduleBlock.findMany({
        where: {
          doctorId,
          endTime: { gte: new Date() },
        },
      }),
      db.doctor.findUnique({
        where: { id: doctorId },
        select: { consultationDuration: true },
      }),
    ]);

    if (!doctor) {
      console.error(`Médico não encontrado: ${doctorId}`);
      throw new Error("Doctor not found");
    }

    // Garantir que a duração da consulta seja válida (mínimo 15 minutos)
    const duration = doctor.consultationDuration && doctor.consultationDuration > 0 
      ? doctor.consultationDuration 
      : 30;
    
    console.log(`Duração da consulta para o médico ${doctorId}: ${duration} minutos`);
    
    // Limitar a geração de slots para 2 semanas no futuro para evitar timeout
    const today = new Date();
    const twoWeeksLater = new Date();
    twoWeeksLater.setDate(today.getDate() + 14);

    console.log(`Período de geração de slots: ${today.toISOString()} até ${twoWeeksLater.toISOString()}`);
    
    // Remover apenas os slots vazios (sem agendamentos)
    const deleteResult = await db.timeSlot.deleteMany({
      where: {
        doctorId,
        startTime: { gte: today },
        appointmentId: null,
      },
    });
    
    console.log(`Slots antigos removidos: ${deleteResult.count}`);

    if (schedules.length === 0) {
      console.log(`Nenhum horário configurado para o médico ${doctorId}, pulando geração de slots`);
      return;
    }

    const slotsToCreate = [];
    let slotCount = 0;
    const maxSlotsPerBatch = 500; // Limitar o número de slots por lote para evitar timeout

    for (let d = new Date(today); d <= twoWeeksLater; d.setDate(d.getDate() + 1)) {
      const weekDay = d.getDay();
      const daySchedules = schedules.filter(s => s.weekDay === weekDay);

      for (const daySchedule of daySchedules) {
        try {
          const [startHour, startMinute] = daySchedule.startTime.split(':').map(Number);
          const [endHour, endMinute] = daySchedule.endTime.split(':').map(Number);
          
          // Verificar se os valores de hora são válidos
          if (isNaN(startHour) || isNaN(startMinute) || isNaN(endHour) || isNaN(endMinute)) {
            console.warn(`Horário inválido para médico ${doctorId}: ${daySchedule.startTime} - ${daySchedule.endTime}`);
            continue;
          }
          
          const startMinutes = startHour * 60 + startMinute;
          const endMinutes = endHour * 60 + endMinute;

          // Verificar se o horário de início é anterior ao de término
          if (startMinutes >= endMinutes) {
            console.warn(`Horário inválido para médico ${doctorId}: início (${startMinutes} min) >= término (${endMinutes} min)`);
            continue;
          }

          for (let minutes = startMinutes; minutes < endMinutes; minutes += duration) {
            if (slotCount >= maxSlotsPerBatch) {
              console.log(`Limite de ${maxSlotsPerBatch} slots atingido, criando lote atual`);
              break;
            }
            
            const slotDate = new Date(d);
            slotDate.setHours(Math.floor(minutes / 60), minutes % 60, 0, 0);

            const slotEndDate = new Date(slotDate);
            slotEndDate.setMinutes(slotEndDate.getMinutes() + duration);

            const isBlocked = blocks.some(block =>
              slotDate >= block.startTime && slotEndDate <= block.endTime
            );

            if (!isBlocked) {
              slotsToCreate.push({
                doctorId,
                startTime: slotDate,
                endTime: slotEndDate,
                isAvailable: true,
              });
              slotCount++;
            }
          }
        } catch (scheduleError) {
          console.error(`Erro ao processar horário para médico ${doctorId}:`, scheduleError);
          // Continuar com o próximo horário em vez de falhar completamente
          continue;
        }
      }
    }

    console.log(`Total de slots a serem criados: ${slotsToCreate.length}`);

    if (slotsToCreate.length > 0) {
      try {
        const createResult = await db.timeSlot.createMany({
          data: slotsToCreate,
          skipDuplicates: true,
        });
        console.log(`Slots criados com sucesso: ${createResult.count}`);
      } catch (createError: any) {
        console.error(`Erro ao criar slots para médico ${doctorId}:`, createError);
        
        // Tentar criar em lotes menores se falhar
        if (slotsToCreate.length > 50) {
          console.log(`Tentando criar slots em lotes menores...`);
          const batchSize = 50;
          for (let i = 0; i < slotsToCreate.length; i += batchSize) {
            const batch = slotsToCreate.slice(i, i + batchSize);
            try {
              await db.timeSlot.createMany({
                data: batch,
                skipDuplicates: true,
              });
              console.log(`Lote ${i/batchSize + 1} criado com sucesso (${batch.length} slots)`);
            } catch (batchError) {
              console.error(`Erro ao criar lote ${i/batchSize + 1}:`, batchError);
              // Continuar com o próximo lote
            }
          }
        } else {
          throw createError;
        }
      }
    }
  } catch (error) {
    console.error("Erro ao atualizar time slots:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Erro ao atualizar time slots do médico",
      cause: error,
    });
  }
}
