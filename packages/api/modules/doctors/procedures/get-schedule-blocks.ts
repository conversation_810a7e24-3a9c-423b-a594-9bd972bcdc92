import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const ScheduleBlockOutputSchema = z.array(
  z.object({
    id: z.string(),
    doctorId: z.string(),
    startTime: z.date(),
    endTime: z.date(),
    reason: z.string().nullable(),
    description: z.string().nullable(),
    type: z.enum(["VACATION", "HOLIDAY", "LUNCH", "PERSONAL", "OTHER"]),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  })
);

export const getScheduleBlocks = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
    })
  )
  .output(ScheduleBlockOutputSchema)
  .query(async ({ input, ctx }) => {
    try {
      // Verificar autenticação
      const { user } = ctx;
      if (!user) {
        console.error("[getScheduleBlocks] Usuário não autenticado");
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Usuário não autenticado",
        });
      }

      console.log(`[getScheduleBlocks] Usuário ${user.id} (${user.role}) buscando bloqueios para o médico: ${input.doctorId}`);

      // Validar se o doctorId é válido
      if (!input.doctorId || input.doctorId === "") {
        console.log("[getScheduleBlocks] ID do médico vazio ou inválido, retornando array vazio");
        return [];
      }
      
      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: input.doctorId,
        },
        select: {
          id: true,
          userId: true,
        }
      });

      if (!doctor) {
        console.warn(`[getScheduleBlocks] Médico não encontrado: ${input.doctorId}`);
        return []; // Retornar array vazio em vez de erro
      }

      // Verificar permissões (apenas para log, não bloquear)
      if (user.role === "DOCTOR" && doctor.userId !== user.id) {
        console.warn(`[getScheduleBlocks] Médico ${user.id} tentando acessar bloqueios de outro médico ${input.doctorId}`);
      }
      
      console.log(`[getScheduleBlocks] Buscando bloqueios para o médico: ${input.doctorId}`);
      
      const blocks = await db.scheduleBlock.findMany({
        where: {
          doctorId: input.doctorId,
        },
        orderBy: {
          startTime: "asc",
        },
      });

      // Garantir que todos os campos estejam presentes e com valores válidos
      const safeBlocks = blocks.map(block => ({
        ...block,
        reason: block.reason ?? null,
        description: block.description ?? null,
      }));

      console.log(`[getScheduleBlocks] Encontrados ${safeBlocks.length} bloqueios para o médico ${input.doctorId}`);
      return safeBlocks;
    } catch (error) {
      console.error("[getScheduleBlocks] Erro detalhado ao buscar bloqueios de agenda do médico:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar bloqueios de agenda do médico",
        cause: error,
      });
    }
  });
