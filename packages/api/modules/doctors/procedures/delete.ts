// packages/api/modules/doctors/procedures/delete.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const delete_ = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.mutation(async ({ input, ctx }) => {
		try {
			const { user, abilities } = ctx;

			// Verificar permissões
			if (user?.role !== "ADMIN" && user?.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Sem permissão para excluir médicos",
				});
			}

			// Se for hospital, verificar se o médico pertence ao hospital
			if (user?.role === "HOSPITAL") {
				const userHospital = await db.hospital.findFirst({
					where: {
						teamId: ctx.teamMemberships?.[0]?.teamId,
					},
				});

				if (!userHospital) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Hospital não encontrado",
					});
				}

				const doctorBelongsToHospital = await db.doctorHospital.findFirst({
					where: {
						doctorId: input.id,
						hospitalId: userHospital.id,
					},
				});

				if (!doctorBelongsToHospital) {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Médico não pertence a este hospital",
					});
				}

				// Se o médico pertence a este hospital mas também a outros hospitais,
				// apenas remover a vinculação com este hospital
				const otherHospitals = await db.doctorHospital.count({
					where: {
						doctorId: input.id,
						hospitalId: { not: userHospital.id },
					},
				});

				if (otherHospitals > 0) {
					await db.doctorHospital.deleteMany({
						where: {
							doctorId: input.id,
							hospitalId: userHospital.id,
						},
					});

					return { success: true };
				}
			}

			// Verificar se o médico tem agendamentos futuros
			const futureAppointments = await db.appointment.count({
				where: {
					doctorId: input.id,
					scheduledAt: {
						gt: new Date(),
					},
				},
			});

			if (futureAppointments > 0) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Não é possível excluir médico com agendamentos futuros",
				});
			}

			// Excluir médico
			await db.doctor.delete({
				where: { id: input.id },
			});

			return { success: true };
		} catch (error) {
			if (error instanceof TRPCError) throw error;
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao excluir médico",
				cause: error,
			});
		}
	});
