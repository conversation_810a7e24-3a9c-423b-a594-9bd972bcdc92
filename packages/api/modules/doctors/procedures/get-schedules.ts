import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Definir um schema mais completo e seguro
const DoctorScheduleOutputSchema = z.array(
  z.object({
    id: z.string(),
    doctorId: z.string(),
    weekDay: z.number(),
    startTime: z.string(),
    endTime: z.string(),
    isEnabled: z.boolean(),
    isBreak: z.boolean().default(false), // Garantir valor padrão
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  })
);

export const getSchedules = protectedProcedure
  .input(
    z.object({
      doctorId: z.string().min(1, 'ID do médico é obrigatório')
    })
  )
  .output(DoctorScheduleOutputSchema)
  .query(async ({ input, ctx }) => {
    try {
      // Verificar se o payload é válido
      if (!input || typeof input !== 'object') {
        console.error('[getSchedules] Payload inválido:', input);
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Payload inválido'
        });
      }

      // Verificar autenticação
      const { user } = ctx;
      if (!user) {
        console.error('[getSchedules] Usuário não autenticado');
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Usuário não autenticado',
        });
      }

      console.log(`[getSchedules] Usuário ${user.id} (${user.role}) buscando horários para o médico: ${input.doctorId}`);

      // Validar se o doctorId é válido
      if (!input.doctorId || input.doctorId === "") {
        console.log("[getSchedules] ID do médico vazio ou inválido, retornando array vazio");
        return [];
      }

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: {
          id: input.doctorId,
        },
        select: {
          id: true,
          userId: true,
        }
      });

      if (!doctor) {
        console.warn(`[getSchedules] Médico não encontrado: ${input.doctorId}`);
        return []; // Retornar array vazio em vez de erro
      }

      // Verificar permissões (apenas para log, não bloquear)
      if (user.role === "DOCTOR" && doctor.userId !== user.id) {
        console.warn(`[getSchedules] Médico ${user.id} tentando acessar horários de outro médico ${input.doctorId}`);
      }

      console.log(`[getSchedules] Buscando horários para o médico: ${input.doctorId}`);

      const schedules = await db.doctorSchedule.findMany({
        where: {
          doctorId: input.doctorId,
        },
        orderBy: [
          { weekDay: "asc" },
          { startTime: "asc" },
        ],
      });

      // Garantir que todos os campos estejam presentes
      const safeSchedules = schedules.map(schedule => ({
        ...schedule,
        isBreak: schedule.isBreak ?? false, // Garantir que isBreak tenha um valor padrão
      }));

      console.log(`[getSchedules] Encontrados ${safeSchedules.length} horários para o médico ${input.doctorId}`);
      return safeSchedules;
    } catch (error) {
      console.error("[getSchedules] Erro detalhado ao buscar horários do médico:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar horários do médico",
        cause: error,
      });
    }
  });
