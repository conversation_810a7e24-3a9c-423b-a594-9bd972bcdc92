// packages/api/modules/doctors/procedures/get-current-doctor.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { protectedProcedure } from "../../../trpc/base";
import { serializeObject } from "../../../utils/decimal-serializer";

export const getCurrentDoctor = protectedProcedure
  .query(async ({ ctx }) => {
    try {
      const { user } = ctx;

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Usu<PERSON>rio não autenticado",
        });
      }

      console.log("[getCurrentDoctor] Searching doctor for user:", user.id);

      if (user.role !== "DOCTOR") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "<PERSON>u<PERSON>rio não é um médico",
        });
      }

      // Buscar o médico pelo userId
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatarUrl: true,
              phone: true,
            },
          },
          specialties: true,
        },
      });

      if (!doctor) {
        console.error(`[getCurrentDoctor] No doctor profile found for user ${user.id}`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Perfil de médico não encontrado",
        });
      }

      console.log(`[getCurrentDoctor] Found doctor profile ${doctor.id} for user ${user.id}`);

      // Garantir que os dados do usuário estejam presentes
      if (!doctor.user) {
        console.error(`[getCurrentDoctor] User data not found for doctor ${doctor.id}`);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Dados do usuário não encontrados",
        });
      }

      // Retornar o objeto sem serialização para manter os dados do usuário
      return doctor;
    } catch (error) {
      console.error("[getCurrentDoctor] Error:", error);
      if (error instanceof TRPCError) throw error;
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar perfil do médico",
        cause: error,
      });
    }
  });
