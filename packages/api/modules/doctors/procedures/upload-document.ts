import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "database";
import { getSignedUploadUrl, getSignedUrl } from "storage";
import { protectedProcedure } from "../../../trpc/base";

export const uploadDocument = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
      type: z.enum(["CRM", "SPECIALTY", "ID", "COLLEGE_DEGREE", "RESIDENCY", "OTHER"]),
      fileName: z.string(),
      fileType: z.string(),
    })
  )
  .mutation(async ({ ctx, input }) => {
    try {
      const { doctorId, type, fileName, fileType } = input;

      // Generate a unique path for the file in S3
      const fileKey = `doctors/${doctorId}/documents/${Date.now()}-${fileName}`;

      // Get a signed upload URL from S3
      const uploadUrl = await getSignedUploadUrl(fileKey, {
        bucket: process.env.NEXT_PUBLIC_UPLOADS_BUCKET_NAME || 'medical_docs',
      });

      // Get a signed URL for viewing the file
      const fileUrl = await getSignedUrl(fileKey, {
        bucket: process.env.NEXT_PUBLIC_UPLOADS_BUCKET_NAME || 'medical_docs',
        expiresIn: 60 * 60 * 24 * 7, // 7 days
      });

      // Create a record in the database
      const document = await db.doctorDocument.create({
        data: {
          doctorId,
          type,
          fileName,
          fileUrl,
          status: 'PENDING'
        },
      });

      return {
        document,
        uploadUrl
      };
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao fazer upload do documento",
        cause: error,
      });
    }
  });
