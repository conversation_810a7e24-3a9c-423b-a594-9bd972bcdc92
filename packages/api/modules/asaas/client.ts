import { createHmac } from "crypto";

export type AsaasConfig = {
  baseUrl: string;
  apiKey: string; // sempre com prefixo $
};

/**
 * Lê e normaliza as variáveis do Asaas.
 * - `ASAAS_API_URL` padrão para sandbox
 * - `ASAAS_API_KEY` é sempre retornada com prefixo '$'
 * - Se `strict=true`, lança erro quando sem chave
 */
export function getAsaasConfig(options: { strict?: boolean } = {}): AsaasConfig {
  const { strict = true } = options;

  const baseUrl = process.env.ASAAS_API_URL || "https://sandbox.asaas.com/api/v3";
  const rawKey = process.env.ASAAS_API_KEY || "";
  const apiKey = rawKey.startsWith("$") ? rawKey : `$${rawKey}`;

  if (strict && (!apiKey || apiKey === "$" || apiKey === "$undefined" || apiKey === "$null")) {
    throw new Error("ASAAS_API_KEY não configurada");
  }

  return { baseUrl, apiKey };
}

export function buildAsaasHeaders(config?: Partial<AsaasConfig>): Record<string, string> {
  const { apiKey } = config ?? getAsaasConfig();
  return {
    access_token: apiKey!,
    "Content-Type": "application/json",
  };
}

/**
 * Cliente consolidado do Asaas com todos os métodos necessários.
 * Substitui tanto AsaasClient quanto AsaasClientSimple.
 */
export class AsaasClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    const { baseUrl, apiKey } = getAsaasConfig();
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  // Método para criar pagamentos (do AsaasClient original)
  async createPayment(data: {
    customerData: {
      name: string;
      email: string;
      cpf: string;
      phone?: string;
    };
    paymentMethod: "CREDIT_CARD" | "PIX" | "BOLETO";
    creditCard?: {
      cardNumber: string;
      cardHolder: string;
      cardExpiry: string;
      cardCvv: string;
      installments: number;
    };
    totalAmount: number;
    transactionId: string;
  }) {
    // Validação inicial dos dados de entrada
    if (!data.customerData) {
      throw new Error("customerData é obrigatório");
    }
    if (!data.customerData.name) {
      throw new Error("Nome do cliente é obrigatório");
    }
    if (!data.customerData.email) {
      throw new Error("Email do cliente é obrigatório");
    }
    if (!data.totalAmount || data.totalAmount <= 0) {
      throw new Error("Valor total deve ser maior que zero");
    }
    if (!data.transactionId) {
      throw new Error("ID da transação é obrigatório");
    }

    try {
      // 1. Get or create customer
      const customer = await this.getOrCreateCustomer(data.customerData);

      // 2. Create payment data object
      const paymentData: Record<string, any> = {
        customer: customer.id,
        billingType: data.paymentMethod,
        value: data.totalAmount,
        dueDate: this.getFormattedDate(new Date(Date.now() + 24 * 60 * 60 * 1000)),
        description: `Consulta no Zapvida (${data.transactionId})`,
        externalReference: data.transactionId
      };

      // 3. Add credit card data for card payments
      if (data.paymentMethod === "CREDIT_CARD" && data.creditCard) {
        // Validate credit card data
        if (!data.creditCard.cardNumber || !data.creditCard.cardHolder || !data.creditCard.cardExpiry || !data.creditCard.cardCvv) {
          throw new Error("Dados do cartão de crédito incompletos");
        }

        // Extract month/year from expiry date
        const expiryParts = data.creditCard.cardExpiry.split('/');
        const expiryMonth = expiryParts[0]?.trim() || '12';
        const expiryYear = expiryParts[1]?.trim() || '2030';

        // Format year to include century if needed
        const formattedYear = expiryYear.length === 2 ? `20${expiryYear}` : expiryYear;

        paymentData.creditCard = {
          holderName: data.creditCard.cardHolder.trim(),
          number: data.creditCard.cardNumber.replace(/\D/g, ''),
          expiryMonth: expiryMonth.padStart(2, '0'),
          expiryYear: formattedYear,
          ccv: data.creditCard.cardCvv.trim()
        };

        paymentData.creditCardHolderInfo = {
          name: data.customerData.name.trim(),
          email: data.customerData.email.trim(),
          cpfCnpj: data.customerData.cpf.replace(/\D/g, ''),
          postalCode: "01311000", // Default for Asaas requirements
          addressNumber: "123", // Default for Asaas requirements
          phone: data.customerData.phone?.replace(/\D/g, '') || ""
        };

        // Add installments if needed
        if (data.creditCard.installments > 1) {
          paymentData.installmentCount = data.creditCard.installments;
          paymentData.installmentValue = Math.round((data.totalAmount / data.creditCard.installments) * 100) / 100;
        }
      }

      // 4. Call Asaas API
      const response = await this.makeApiRequest("POST", "/payments", paymentData);
      return response;
    } catch (error) {
      console.error("[ASAAS ERROR] Payment creation failed:", error);
      throw error;
    }
  }

  // Get or create a customer
  private async getOrCreateCustomer(customerData: {
    name: string;
    email: string;
    cpf: string;
    phone?: string;
  }) {
    try {
      // First try to find existing customer
      try {
        const existingCustomer = await this.findCustomerByEmail(customerData.email);
        if (existingCustomer) {
          return existingCustomer;
        }
      } catch (error) {
        // No existing customer found, will create new one
      }

      // Create new customer
      const newCustomer = await this.createCustomer(customerData);
      return newCustomer;
    } catch (error) {
      console.error("[ASAAS ERROR] Error in getOrCreateCustomer:", error);
      throw new Error("Failed to get or create customer");
    }
  }

  // Find customer by email
  async findCustomerByEmail(email: string) {
    try {
      const customers = await this.makeApiRequest("GET", `/customers?email=${encodeURIComponent(email)}`);

      // Return first customer if found
      if (customers.data && customers.data.length > 0) {
        return customers.data[0];
      }

      return null;
    } catch (error) {
      console.error("[ASAAS ERROR] Error finding customer by email:", error);
      throw new Error(`Failed to find customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Create a new customer
  async createCustomer(data: {
    name: string;
    email: string;
    cpf: string;
    phone?: string;
  }) {
    try {
      const customerData = {
        name: data.name,
        email: data.email,
        cpfCnpj: data.cpf.replace(/\D/g, ""),
        mobilePhone: data.phone?.replace(/\D/g, "") || undefined,
        notificationDisabled: true
      };

      const response = await this.makeApiRequest("POST", "/customers", customerData);
      return response;
    } catch (error) {
      console.error("[ASAAS ERROR] Error creating customer:", error);
      throw new Error(`Failed to create customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Generate PIX QR code
  async getPixQRCode(paymentId: string) {
    try {
      if (!paymentId) {
        throw new Error("Payment ID is required for PIX code generation");
      }

      const response = await this.makeApiRequest("GET", `/payments/${paymentId}/pixQrCode`);

      return {
        encodedImage: response.encodedImage,
        payload: response.payload
      };
    } catch (error) {
      console.error("[ASAAS ERROR] Error generating PIX QR code:", error);
      throw new Error(`Failed to generate PIX code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get payment status (do AsaasClientSimple)
  async getPaymentStatus(paymentId: string): Promise<{ id: string; status: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/${paymentId}`, {
        method: "GET",
        headers: buildAsaasHeaders({ apiKey: this.apiKey }),
      });

      const responseText = await response.text();

      if (!response.ok) {
        throw new Error(`Erro ao verificar status do pagamento: ${response.status}`);
      }

      try {
        const data = JSON.parse(responseText);
        return { id: data.id, status: data.status };
      } catch {
        throw new Error("Erro ao processar resposta da Asaas");
      }
    } catch (error) {
      throw error as Error;
    }
  }

  // Core API request method
  private async makeApiRequest(method: "GET" | "POST", endpoint: string, data?: any) {
    try {
      // Prepare URL - ensure no double slashes between base URL and endpoint
      const baseUrl = this.baseUrl.endsWith("/") ? this.baseUrl.slice(0, -1) : this.baseUrl;
      const path = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      const url = `${baseUrl}${path}`;

      // Prepare headers
      const headers: Record<string, string> = buildAsaasHeaders({ apiKey: this.apiKey });

      // Prepare request options
      const options: RequestInit = {
        method,
        headers
      };

      // Add body for POST requests
      if (method === "POST") {
        if (data !== null && data !== undefined) {
          try {
            options.body = JSON.stringify(data);
          } catch (error) {
            console.error("[ASAAS ERROR] Error serializing request body:", error);
            throw new Error("Failed to serialize request data");
          }
        } else {
          options.body = JSON.stringify({});
        }
      }

      // Make the request
      const response = await fetch(url, options);
      const responseText = await response.text();

      // Parse response
      let responseData;
      try {
        responseData = responseText ? JSON.parse(responseText) : null;
      } catch (error) {
        console.error("[ASAAS ERROR] Error parsing response:", error);
        throw new Error(`Failed to parse response: ${responseText ? responseText.substring(0, 100) : 'Empty response'}`);
      }

      // Handle error responses
      if (!response.ok) {
        const errorMessage = responseData?.errors?.[0]?.description ||
                            responseData?.message ||
                            `Request failed with status ${response.status}`;

        console.error("[ASAAS ERROR] API error response:", {
          status: response.status,
          message: errorMessage,
          data: responseData
        });

        throw new Error(errorMessage);
      }

      return responseData;
    } catch (error) {
      console.error(`[ASAAS ERROR] API request failed: ${method} ${endpoint}`, error);
      throw error;
    }
  }

  // Format date for API
  private getFormattedDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}

// Validate webhook signatures
export function validateAsaasSignature(
  signature: string | null,
  payload: unknown
): boolean {
  try {
    console.log("[ASAAS_SIGNATURE_VALIDATION] Iniciando validação:", {
      hasSignature: !!signature,
      signatureType: typeof signature,
      signatureLength: signature?.length || 0,
      hasWebhookToken: !!process.env.ASAAS_WEBHOOK_TOKEN,
      webhookTokenLength: process.env.ASAAS_WEBHOOK_TOKEN?.length || 0
    });

    if (!signature) {
      console.warn("[ASAAS_SIGNATURE_VALIDATION] Assinatura não encontrada nos headers");
      return false;
    }

    if (!process.env.ASAAS_WEBHOOK_TOKEN) {
      console.error("[ASAAS_SIGNATURE_VALIDATION] ASAAS_WEBHOOK_TOKEN não configurado");
      return false;
    }

    // Limpar a assinatura (remover prefixo se existir)
    let signatureHash = signature;
    if (signature.startsWith("sha1=")) {
      signatureHash = signature.replace("sha1=", "");
    }

    console.log("[ASAAS_SIGNATURE_VALIDATION] Assinatura processada:", {
      original: signature,
      cleaned: signatureHash,
      webhookToken: `${process.env.ASAAS_WEBHOOK_TOKEN.substring(0, 10)}...`
    });

    // Criar HMAC com o token do webhook
    const hmac = createHmac("sha1", process.env.ASAAS_WEBHOOK_TOKEN);

    // Preparar payload para validação
    let payloadString: string;
    if (payload === null || payload === undefined) {
      payloadString = "";
    } else {
      try {
        payloadString = JSON.stringify(payload);
      } catch (stringifyError) {
        console.error("[ASAAS_SIGNATURE_VALIDATION] Erro ao serializar payload:", stringifyError);
        payloadString = "";
      }
    }

    console.log("[ASAAS_SIGNATURE_VALIDATION] Payload para validação:", {
      payloadLength: payloadString.length,
      payloadStart: payloadString.substring(0, 100) + "..."
    });

    // Calcular hash esperado
    const expectedHash = hmac.update(payloadString).digest("hex");

    console.log("[ASAAS_SIGNATURE_VALIDATION] Hash calculado:", {
      received: signatureHash,
      expected: expectedHash,
      match: signatureHash === expectedHash
    });

    const isValid = signatureHash === expectedHash;

    if (!isValid) {
      console.warn("[ASAAS_SIGNATURE_VALIDATION] Assinatura inválida:", {
        received: signatureHash,
        expected: expectedHash,
        payloadLength: payloadString.length
      });
    } else {
      console.log("[ASAAS_SIGNATURE_VALIDATION] Assinatura válida ✅");
    }

    return isValid;
  } catch (error) {
    console.error("[ASAAS_SIGNATURE_VALIDATION] Erro na validação:", error);
    return false;
  }
}


