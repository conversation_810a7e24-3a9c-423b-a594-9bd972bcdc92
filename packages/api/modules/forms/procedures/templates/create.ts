import { TRPCError } from "@trpc/server";
import { db } from "database";
// packages/api/modules/forms/procedures/templates/create.ts
import { z } from "zod";
import { protectedProcedure } from "../../../../trpc/base";

// Schema para definição da estrutura de campos do template
const FieldSchema = z.object({
	id: z.string(),
	type: z.enum(["text", "number", "select", "checkbox", "radio", "textarea"]),
	label: z.string(),
	required: z.boolean().default(false),
	options: z.array(z.string()).optional(),
	placeholder: z.string().optional(),
	defaultValue: z.union([z.string(), z.number(), z.boolean()]).optional(),
	category: z.string().optional(),
});

// Schema para cada seção do formulário pré-anestésico
const SectionSchema = z.object({
	id: z.string(),
	title: z.string(),
	description: z.string().optional(),
	fields: z.array(FieldSchema),
});

export const create = protectedProcedure
	.input(
		z.object({
			hospitalId: z.string(),
			name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
			description: z.string().optional(),
			sections: z.array(SectionSchema),
		}),
	)
	.mutation(async ({ input, ctx }) => {
		try {
			// Verifica se o usuário tem permissão para criar templates no hospital
			if (ctx.user.role !== "ADMIN" && ctx.user.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Você não tem permissão para criar templates neste hospital",
				});
			}

			// Verifica se o hospital existe
			const hospital = await db.hospital.findUnique({
				where: { id: input.hospitalId },
			});

			if (!hospital) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Hospital não encontrado",
				});
			}

			// Estrutura JSON dos campos do template
			const fields = {
				version: 1,
				sections: input.sections,
			};

			// Criar o template
			const template = await db.preAnestheticTemplate.create({
				data: {
					hospitalId: input.hospitalId,
					name: input.name,
					description: input.description || "",
					fields: fields,
					isActive: true,
				},
			});

			return template;
		} catch (error) {
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao criar template",
				cause: error,
			});
		}
	});
