import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../../trpc/base";

export const list = protectedProcedure
	.input(
		z.object({
			hospitalId: z.string(),
			isActive: z.boolean().optional(),
			searchTerm: z.string().optional(),
		}),
	)
	.query(async ({ input, ctx }) => {
		try {
			// Verifica se o usuário tem permissão para acessar os templates do hospital
			if (ctx.user.role !== "ADMIN" && ctx.user.role !== "HOSPITAL") {
				// Se for médico, verificar associação com o hospital
				if (ctx.user.role === "DOCTOR" && ctx.user.doctor) {
					const doctorHospital = await db.doctorHospital.findFirst({
						where: {
							doctorId: ctx.user.doctor.id,
							hospitalId: input.hospitalId,
							isActive: true,
						},
					});

					if (!doctorHospital) {
						throw new TRPCError({
							code: "FORBIDDEN",
							message: "Você não tem permissão para acessar estes templates",
						});
					}
				} else {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Você não tem permissão para acessar estes templates",
					});
				}
			}

			// Busca os templates
			const templates = await db.preAnestheticTemplate.findMany({
				where: {
					hospitalId: input.hospitalId,
					isActive: input.isActive,
					...(input.searchTerm
						? {
								OR: [
									{ name: { contains: input.searchTerm, mode: "insensitive" } },
									{
										description: {
											contains: input.searchTerm,
											mode: "insensitive",
										},
									},
								],
							}
						: {}),
				},
				orderBy: {
					name: "asc",
				},
			});

			return templates;
		} catch (error) {
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao buscar templates",
				cause: error,
			});
		}
	});
