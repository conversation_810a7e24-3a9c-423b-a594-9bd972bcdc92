import { TRPCError } from "@trpc/server";
import { db } from "database";
// packages/api/modules/forms/procedures/templates/delete.ts
import { z } from "zod";
import { protectedProcedure } from "../../../../trpc/base";

export const delete_ = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.mutation(async ({ input, ctx }) => {
		try {
			// Buscar o template para verificar permissões
			const template = await db.preAnestheticTemplate.findUnique({
				where: { id: input.id },
			});

			if (!template) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Template não encontrado",
				});
			}

			// Verificar permissões
			if (ctx.user.role !== "ADMIN" && ctx.user.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Você não tem permissão para excluir este template",
				});
			}

			// Verificar se o template está sendo usado em algum formulário
			const formsUsingTemplate = await db.preAnestheticForm.count({
				where: { templateId: input.id },
			});

			if (formsUsingTemplate > 0) {
				// Em vez de excluir, apenas desativa o template
				await db.preAnestheticTemplate.update({
					where: { id: input.id },
					data: { isActive: false },
				});

				return {
					success: true,
					message: "Template desativado pois já está em uso em formulários",
				};
			}

			// Excluir o template se não estiver em uso
			await db.preAnestheticTemplate.delete({
				where: { id: input.id },
			});

			return {
				success: true,
				message: "Template excluído com sucesso",
			};
		} catch (error) {
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao excluir template",
				cause: error,
			});
		}
	});
