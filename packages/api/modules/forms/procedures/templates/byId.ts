import { TRPCError } from "@trpc/server";
import { db } from "database";
// packages/api/modules/forms/procedures/templates/byId.ts
import { z } from "zod";
import { protectedProcedure } from "../../../../trpc/base";

export const byId = protectedProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.query(async ({ input, ctx }) => {
		try {
			const template = await db.preAnestheticTemplate.findUnique({
				where: { id: input.id },
				include: { hospital: true },
			});

			if (!template) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Template não encontrado",
				});
			}

			// Verificar permissões
			if (ctx.user.role !== "ADMIN" && ctx.user.role !== "HOSPITAL") {
				// Se for médico, verificar associação com o hospital
				if (ctx.user.role === "DOCTOR" && ctx.user.doctor) {
					const doctorHospital = await db.doctorHospital.findFirst({
						where: {
							doctorId: ctx.user.doctor.id,
							hospitalId: template.hospitalId,
							isActive: true,
						},
					});

					if (!doctorHospital) {
						throw new TRPCError({
							code: "FORBIDDEN",
							message: "Você não tem permissão para acessar este template",
						});
					}
				} else {
					throw new TRPCError({
						code: "FORBIDDEN",
						message: "Você não tem permissão para acessar este template",
					});
				}
			}

			return template;
		} catch (error) {
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao buscar template",
				cause: error,
			});
		}
	});
