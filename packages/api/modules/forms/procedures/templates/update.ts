import { TRPCError } from "@trpc/server";
import { db } from "database";
// packages/api/modules/forms/procedures/templates/update.ts
import { z } from "zod";
import { protectedProcedure } from "../../../../trpc/base";

// Reutilizando os schemas de create.ts
const FieldSchema = z.object({
	id: z.string(),
	type: z.enum(["text", "number", "select", "checkbox", "radio", "textarea"]),
	label: z.string(),
	required: z.boolean().default(false),
	options: z.array(z.string()).optional(),
	placeholder: z.string().optional(),
	defaultValue: z.union([z.string(), z.number(), z.boolean()]).optional(),
	category: z.string().optional(),
});

const SectionSchema = z.object({
	id: z.string(),
	title: z.string(),
	description: z.string().optional(),
	fields: z.array(FieldSchema),
});

export const update = protectedProcedure
	.input(
		z.object({
			id: z.string(),
			name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
			description: z.string().optional(),
			isActive: z.boolean().optional(),
			sections: z.array(SectionSchema),
		}),
	)
	.mutation(async ({ input, ctx }) => {
		try {
			// Buscar o template para verificar permissões
			const template = await db.preAnestheticTemplate.findUnique({
				where: { id: input.id },
				include: { hospital: true },
			});

			if (!template) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Template não encontrado",
				});
			}

			// Verificar permissões
			if (ctx.user.role !== "ADMIN" && ctx.user.role !== "HOSPITAL") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Você não tem permissão para atualizar este template",
				});
			}

			// Estrutura JSON dos campos do template
			const fields = {
				version: (template.fields as any).version + 1,
				sections: input.sections,
			};

			// Atualizar o template
			const updatedTemplate = await db.preAnestheticTemplate.update({
				where: { id: input.id },
				data: {
					name: input.name,
					description: input.description,
					isActive:
						input.isActive !== undefined ? input.isActive : template.isActive,
					fields: fields,
				},
			});

			return updatedTemplate;
		} catch (error) {
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao atualizar template",
				cause: error,
			});
		}
	});
