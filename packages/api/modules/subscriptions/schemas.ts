import { z } from "zod";

export const createSubscriptionSchema = z.object({
  planId: z.string().min(1, "Plan ID é obrigatório"),
  billingType: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  customerData: z.object({
    name: z.string().min(1, "Nome é obrigatório"),
    email: z.string().email("Email inválido"),
    cpf: z.string().min(11, "CPF inválido"),
    phone: z.string().min(10, "Telefone inválido"),
  }),
  creditCard: z.object({
    holderName: z.string().min(1, "Nome do portador é obrigatório"),
    number: z.string().min(13, "Número do cartão inválido"),
    expiryMonth: z.string().length(2, "Mês de expiração deve ter 2 dígitos"),
    expiryYear: z.string().length(4, "Ano de expiração deve ter 4 dígitos"),
    ccv: z.string().min(3, "CCV deve ter pelo menos 3 dígitos"),
  }).optional(),
  creditCardHolderInfo: z.object({
    name: z.string().min(1, "Nome é obrigatório"),
    email: z.string().email("Email inválido"),
    cpfCnpj: z.string().min(11, "CPF/CNPJ inválido"),
    postalCode: z.string().min(8, "CEP inválido"),
    addressNumber: z.string().min(1, "Número do endereço é obrigatório"),
    addressComplement: z.string().optional(),
    phone: z.string().min(10, "Telefone inválido"),
    mobilePhone: z.string().optional(),
  }).optional(),
  remoteIp: z.string().optional(),
});

export const cancelSubscriptionSchema = z.object({
  subscriptionId: z.string().min(1, "ID da assinatura é obrigatório"),
});

export const getSubscriptionSchema = z.object({
  subscriptionId: z.string().min(1, "ID da assinatura é obrigatório"),
});

export const getSubscriptionUsageSchema = z.object({
  subscriptionId: z.string().min(1, "ID da assinatura é obrigatório"),
});

export const resetMonthlyUsageSchema = z.object({
  subscriptionId: z.string().min(1, "ID da assinatura é obrigatório"),
});

export const useConsultationSchema = z.object({
  subscriptionId: z.string().min(1, "ID da assinatura é obrigatório"),
  appointmentId: z.string().min(1, "ID do agendamento é obrigatório"),
});

// Plan definitions
export const SUBSCRIPTION_PLANS = {
  "zapvida-sempre": {
    id: "zapvida-sempre",
    name: "ZapVida Sempre - 2 Consultas",
    price: 49.00,
    consultationsIncluded: 2,
    description: "2 consultas médicas por mês",
    features: [
      "2 consultas médicas por mês",
      "Atendimento por vídeo ou chat",
      "Acesso a especialistas",
      "Receitas digitais",
      "Histórico médico"
    ]
  }
} as const;

export type PlanId = keyof typeof SUBSCRIPTION_PLANS;

export const planIdSchema = z.enum(["zapvida-sempre"]);
