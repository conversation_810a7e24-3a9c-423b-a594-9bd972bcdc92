import { protectedProcedure } from "../../../trpc/base";
import { TRPCError } from "@trpc/server";
import { cancelSubscriptionSchema } from "../schemas";
import { AsaasSubscriptionClient } from "../asaas-subscription";
import { db } from "database";

export const cancelSubscription = protectedProcedure
  .input(cancelSubscriptionSchema)
  .mutation(async ({ input, ctx }) => {
    const { user } = ctx;

    try {
      // Encontrar assinatura do usuário
      const subscription = await db.patientSubscription.findFirst({
        where: { id: input.subscriptionId, patientId: user.id, status: "ACTIVE" }
      });

      if (!subscription) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Assinatura não encontrada ou já cancelada" });
      }

      // Cancelar no Asaas
      const asaas = new AsaasSubscriptionClient();
      try {
        await asaas.cancelSubscription(subscription.asaasSubscriptionId);
      } catch (e: any) {
        console.error("[CANCEL_SUBSCRIPTION] Erro ao cancelar no Asaas:", e);
        // Continua e tenta refletir estado localmente para não travar usuário
      }

      // Atualizar banco
      const updated = await db.patientSubscription.update({
        where: { id: subscription.id },
        data: { status: "CANCELED", endDate: new Date() }
      });

      return {
        subscriptionId: updated.id,
        status: updated.status,
        canceledAt: updated.endDate,
        message: "Assinatura cancelada com sucesso"
      };
    } catch (err) {
      if (err instanceof TRPCError) throw err;
      console.error("[CANCEL_SUBSCRIPTION] Erro:", err);
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Erro ao cancelar assinatura" });
    }
  });
