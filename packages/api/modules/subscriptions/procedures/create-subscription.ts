import { protectedProcedure } from "../../../trpc/base";
import { TRPCError } from "@trpc/server";
import { createSubscriptionSchema, SUBSCRIPTION_PLANS } from "../schemas";
import { AsaasSubscriptionClient } from "../asaas-subscription";
import { db } from "database";
import { sendEmail } from "mail";

export const createSubscription = protectedProcedure
  .input(createSubscriptionSchema)
  .mutation(async ({ input, ctx }) => {
    const { user } = ctx;

    try {
      console.log("[CREATE_SUBSCRIPTION] Starting subscription creation for user:", user.id);

      // Validar o plano
      const plan = SUBSCRIPTION_PLANS[input.planId as keyof typeof SUBSCRIPTION_PLANS];
      if (!plan) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Plano inválido",
        });
      }

      // Validar dados do cliente
      if (!input.customerData) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Dados do cliente são obrigatórios",
        });
      }

      // Buscar paciente do usuário
      let patient = await db.patient.findFirst({ where: { userId: user.id } });

      if (!patient) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Paciente não encontrado",
        });
      }

      // Verificar se o paciente já tem uma assinatura ativa
      const existingSubscription = await db.patientSubscription.findFirst({
        where: {
          patientId: patient.id,
          status: "ACTIVE"
        }
      });

      if (existingSubscription) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Você já possui uma assinatura ativa",
        });
      }

      // Já temos o paciente acima

      // Inicializar cliente Asaas
      const asaasClient = new AsaasSubscriptionClient();

      // Preparar dados do cliente para o Asaas
      const customerData = {
        name: input.customerData.name,
        email: input.customerData.email,
        cpfCnpj: input.customerData.cpf.replace(/\D/g, ''),
        phone: input.customerData.phone?.replace(/\D/g, '') || undefined,
        mobilePhone: input.customerData.phone?.replace(/\D/g, '') || undefined,
        notificationDisabled: false
      };

      console.log("[CREATE_SUBSCRIPTION] Creating Asaas customer");

      // Criar cliente no Asaas
      let asaasCustomer;
      try {
        asaasCustomer = await asaasClient.createCustomer(customerData);
        console.log("[CREATE_SUBSCRIPTION] Asaas customer created:", asaasCustomer.id);
      } catch (error: any) {
        console.error("[CREATE_SUBSCRIPTION] Error creating Asaas customer:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao criar cliente no sistema de pagamentos",
        });
      }

      // Preparar dados da assinatura
      const subscriptionData = {
        customer: asaasCustomer.id,
        billingType: input.billingType,
        value: plan.price,
        nextDueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Amanhã
        cycle: "MONTHLY" as const,
        description: `Assinatura ${plan.name} - Zapvida`,
        ...(input.creditCard && {
          creditCard: {
            holderName: input.creditCard.holderName,
            number: input.creditCard.number.replace(/\D/g, ''),
            expiryMonth: input.creditCard.expiryMonth,
            expiryYear: input.creditCard.expiryYear,
            ccv: input.creditCard.ccv,
          }
        }),
        ...(input.creditCardHolderInfo && {
          creditCardHolderInfo: {
            name: input.creditCardHolderInfo.name,
            email: input.creditCardHolderInfo.email,
            cpfCnpj: input.creditCardHolderInfo.cpfCnpj.replace(/\D/g, ''),
            postalCode: input.creditCardHolderInfo.postalCode,
            addressNumber: input.creditCardHolderInfo.addressNumber,
            addressComplement: input.creditCardHolderInfo.addressComplement,
            phone: input.creditCardHolderInfo.phone.replace(/\D/g, ''),
            mobilePhone: input.creditCardHolderInfo.mobilePhone?.replace(/\D/g, ''),
          }
        }),
        ...(input.remoteIp && { remoteIp: input.remoteIp })
      };

      console.log("[CREATE_SUBSCRIPTION] Creating Asaas subscription with data:", {
        customer: subscriptionData.customer,
        billingType: subscriptionData.billingType,
        value: subscriptionData.value,
        cycle: subscriptionData.cycle,
        hasCreditCard: Boolean(subscriptionData.creditCard)
      });

      // Criar assinatura no Asaas
      let asaasSubscription;
      let firstPaymentId: string | null = null;
      let pixCode: { encodedImage: string; payload: string } | null = null;
      try {
        asaasSubscription = await asaasClient.createSubscription(subscriptionData);
        console.log("[CREATE_SUBSCRIPTION] Asaas subscription created:", asaasSubscription.id);
        // PIX recorrente 100% via API: gerar QR Code do primeiro pagamento da assinatura
        if (input.billingType === 'PIX') {
          try {
            const payments = await asaasClient.listSubscriptionPayments({ subscriptionId: asaasSubscription.id, status: 'PENDING', limit: 1 });
            const payment = payments?.data?.[0];
            if (payment?.id) {
              firstPaymentId = payment.id;
              pixCode = await asaasClient.getPixQRCode(payment.id);
            }
          } catch (pixErr) {
            console.error('[CREATE_SUBSCRIPTION] Erro ao obter PIX da assinatura:', pixErr);
          }
        }
      } catch (error: any) {
        console.error("[CREATE_SUBSCRIPTION] Error creating Asaas subscription:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao criar assinatura no sistema de pagamentos: " + (error.message || "Erro desconhecido"),
        });
      }

      // Determinar status baseado na resposta do Asaas
      const subscriptionStatus = asaasSubscription.status === "ACTIVE" ? "ACTIVE" : "ACTIVE"; // Defaulting to ACTIVE for now

      // Salvar assinatura no banco de dados
      const subscription = await db.patientSubscription.create({
        data: {
          patientId: patient.id,
          planId: input.planId,
          planName: plan.name,
          planPrice: plan.price,
          asaasSubscriptionId: asaasSubscription.id,
          status: subscriptionStatus,
          nextBillingDate: new Date(asaasSubscription.nextDueDate),
          consultationsIncluded: plan.consultationsIncluded,
          consultationsUsed: 0,
          lastResetDate: new Date(),
        }
      });

      // Atualizar status do paciente
      await db.patient.update({
        where: { id: patient.id },
        data: { hasActiveSubscription: subscription.status === "ACTIVE" }
      });

      console.log("[CREATE_SUBSCRIPTION] Subscription created successfully:", subscription.id);

      // Enviar notificações para o paciente confirmando assinatura
      try {
        // Buscar dados do usuário para nome/telefone
        const userRecord = await db.user.findUnique({ where: { id: user.id } });

        if (userRecord) {
          // Email
          await sendEmail({
            to: userRecord.email,
            templateId: "subscriptionCreated" as any,
            context: {
              recipientName: userRecord.name || "Paciente",
              planName: plan.name,
              planPrice: plan.price,
              nextBillingDate: subscription.nextBillingDate,
              manageUrl: `${process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com"}/app`,
            } as any,
            locale: "pt" as any,
          });

          // Importante: envio de WhatsApp é responsabilidade da camada web/app
        }
      } catch (notifErr) {
        console.error("[CREATE_SUBSCRIPTION] Notificações de assinatura falharam:", notifErr);
      }

      return {
        subscription: {
          id: subscription.id,
          planId: subscription.planId,
          planName: subscription.planName,
          planPrice: subscription.planPrice,
          status: subscription.status,
          nextBillingDate: subscription.nextBillingDate,
          consultationsIncluded: subscription.consultationsIncluded,
          consultationsUsed: subscription.consultationsUsed,
        },
        asaasSubscription: {
          id: asaasSubscription.id,
          status: asaasSubscription.status,
          // paymentLink intencionalmente não usado quando billingType=PIX para evitar redirecionamento
        },
        paymentId: firstPaymentId,
        pixCode,
        plan,
        message: "Assinatura criada com sucesso!"
      };

    } catch (error: any) {
      console.error("[CREATE_SUBSCRIPTION] Error:", error);

      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro interno do servidor ao criar assinatura",
      });
    }
  });
