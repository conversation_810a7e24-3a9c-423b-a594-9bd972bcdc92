import { protectedProcedure } from "../../../trpc/base";
import { TRPCError } from "@trpc/server";
import { getSubscriptionSchema } from "../schemas";

export const getSubscription = protectedProcedure
  .input(getSubscriptionSchema)
  .query(async ({ input, ctx }) => {
    const { user } = ctx;

    // Por enquanto, apenas retorna dados mockados
    // TODO: Implementar busca real no banco de dados após migração
    return {
      id: input.subscriptionId,
      planId: "zapvida-sempre",
      planName: "ZapVida Sempre - 2 Consultas",
      planPrice: 49.00,
      status: "ACTIVE",
      consultationsIncluded: 2,
      consultationsUsed: 0,
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
      message: "Dados mockados - implementação em desenvolvimento"
    };
  });
