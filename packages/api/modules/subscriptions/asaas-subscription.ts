import { getAsaasConfig, buildAsaasHeaders } from "../asaas/client";

export interface CreateSubscriptionData {
  customer: string;
  billingType: "CREDIT_CARD" | "PIX" | "BOLETO";
  value: number;
  nextDueDate: string;
  cycle: "WEEKLY" | "BIWEEKLY" | "MONTHLY" | "QUARTERLY" | "SEMIANNUALLY" | "YEARLY";
  description?: string;
  creditCard?: {
    holderName: string;
    number: string;
    expiryMonth: string;
    expiryYear: string;
    ccv: string;
  };
  creditCardHolderInfo?: {
    name: string;
    email: string;
    cpfCnpj: string;
    postalCode: string;
    addressNumber: string;
    addressComplement?: string;
    phone: string;
    mobilePhone?: string;
  };
  remoteIp?: string;
}

export interface SubscriptionResponse {
  object: string;
  id: string;
  dateCreated: string;
  customer: string;
  paymentLink?: string;
  billingType: string;
  value: number;
  nextDueDate: string;
  cycle: string;
  description?: string;
  status: "ACTIVE" | "CANCELED" | "OVERDUE" | "EXPIRED";
  discount?: {
    value: number;
    dueDateLimitDays: number;
  };
  interest?: {
    value: number;
  };
  fine?: {
    value: number;
  };
  deleted: boolean;
}

export class AsaasSubscriptionClient {
  private baseUrl: string;
  private apiKey: string | undefined;

  constructor() {
    // Do not throw at import/build time. Load non-strict and validate only when used.
    const { baseUrl, apiKey } = getAsaasConfig({ strict: false });
    this.baseUrl = baseUrl;
    this.apiKey = apiKey?.trim();

    console.log("[ASAAS_SUBSCRIPTION_CLIENT] Initializing", {
      baseUrl: this.baseUrl,
      keyConfigured: Boolean(this.apiKey && this.apiKey !== "$"),
    });
  }

  private ensureApiKey() {
    if (!this.apiKey || this.apiKey === "$" || this.apiKey === "$undefined" || this.apiKey === "$null") {
      throw new Error("ASAAS_API_KEY não configurada corretamente");
    }
  }

  private async makeRequest(endpoint: string, method: string, data?: any) {
    this.ensureApiKey();
    console.log(`[ASAAS_SUBSCRIPTION] ${method} ${endpoint}`);

    try {
      const headers: Record<string, string> = buildAsaasHeaders({ apiKey: this.apiKey! });

      const hasBody = typeof data === "object" && data !== null;
      if (!hasBody && (method === "POST" || method === "PUT" || method === "PATCH")) {
        console.warn("[ASAAS_SUBSCRIPTION] Missing body for", { endpoint, method });
      }

      const options: RequestInit = {
        method,
        headers,
        ...(hasBody ? { body: JSON.stringify(data) } : {}),
      };

      const baseUrl = this.baseUrl.endsWith("/") ? this.baseUrl.slice(0, -1) : this.baseUrl;
      const path = endpoint.startsWith("/") ? endpoint : `/${endpoint}`;
      const url = `${baseUrl}${path}`;

      const response = await fetch(url, options);
      const responseText = await response.text();

      if (responseText.length > 0) {
        try {
          const responseData = JSON.parse(responseText);

          if (!response.ok) {
            const message =
              responseData.errors?.[0]?.description ||
              responseData.message ||
              `Error ${response.status}`;
            const err: any = new Error(message);
            err.status = response.status;
            err.provider = 'asaas';
            err.endpoint = endpoint;
            err.method = method;
            err.body = data ? { ...data, creditCard: data?.creditCard ? { ...data.creditCard, number: '****' } : undefined } : undefined;
            err.response = responseData;
            console.error("[ASAAS_SUBSCRIPTION_ERROR] Request failed:", {
              status: response.status,
              message,
              endpoint,
              method,
            });
            throw err;
          }

          return responseData;
        } catch (parseError: any) {
          console.error(
            "[ASAAS_SUBSCRIPTION_ERROR] Failed to parse JSON response:",
            parseError.message
          );
          throw new Error(`Failed to parse API response: ${parseError.message}`);
        }
      } else {
        console.error("[ASAAS_SUBSCRIPTION_ERROR] Empty response");
        throw new Error("Empty response from API");
      }
    } catch (error: any) {
      console.error("[ASAAS_SUBSCRIPTION_ERROR]", error);
      throw error;
    }
  }

  async createCustomer(data: {
    name: string;
    email: string;
    cpfCnpj: string;
    phone?: string;
    mobilePhone?: string;
    notificationDisabled?: boolean;
  }) {
    console.log("[ASAAS_SUBSCRIPTION] Creating customer", {
      name: data.name,
      email: data.email,
      cpfCnpj: data.cpfCnpj ? "***" : undefined,
    });

    return await this.makeRequest("/customers", "POST", data);
  }

  async createSubscription(data: CreateSubscriptionData): Promise<SubscriptionResponse> {
    console.log("[ASAAS_SUBSCRIPTION] Creating subscription", {
      customer: data.customer,
      billingType: data.billingType,
      value: data.value,
      cycle: data.cycle,
      hasCreditCard: Boolean(data.creditCard),
    });

    return await this.makeRequest("/subscriptions", "POST", data);
  }

  async getSubscription(subscriptionId: string): Promise<SubscriptionResponse> {
    console.log("[ASAAS_SUBSCRIPTION] Getting subscription:", subscriptionId);

    return await this.makeRequest(`/subscriptions/${subscriptionId}`, "GET");
  }

  async cancelSubscription(subscriptionId: string): Promise<SubscriptionResponse> {
    console.log("[ASAAS_SUBSCRIPTION] Canceling subscription:", subscriptionId);

    return await this.makeRequest(`/subscriptions/${subscriptionId}`, "DELETE");
  }

  async listSubscriptions(params?: {
    customer?: string;
    status?: string;
    offset?: number;
    limit?: number;
  }): Promise<{ object: string; hasMore: boolean; totalCount: number; limit: number; offset: number; data: SubscriptionResponse[] }> {
    console.log("[ASAAS_SUBSCRIPTION] Listing subscriptions", params);

    const queryParams = new URLSearchParams();
    if (params?.customer) queryParams.append("customer", params.customer);
    if (params?.status) queryParams.append("status", params.status);
    if (params?.offset) queryParams.append("offset", params.offset.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());

    const endpoint = `/subscriptions${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
    return await this.makeRequest(endpoint, "GET");
  }

  async updateSubscription(subscriptionId: string, data: Partial<CreateSubscriptionData>): Promise<SubscriptionResponse> {
    console.log("[ASAAS_SUBSCRIPTION] Updating subscription:", subscriptionId);

    return await this.makeRequest(`/subscriptions/${subscriptionId}`, "POST", data);
  }

  /**
   * Lista pagamentos (charges) associados a uma assinatura
   */
  async listSubscriptionPayments(params: {
    subscriptionId: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ object: string; hasMore: boolean; totalCount: number; limit: number; offset: number; data: Array<{ id: string; status: string; value: number; dueDate: string; billingType: string }> }> {
    const q = new URLSearchParams();
    q.append("subscription", params.subscriptionId);
    if (params.status) q.append("status", params.status);
    if (typeof params.limit === "number") q.append("limit", String(params.limit));
    if (typeof params.offset === "number") q.append("offset", String(params.offset));

    const endpoint = `/payments?${q.toString()}`;
    return await this.makeRequest(endpoint, "GET");
  }

  /**
   * Obtém o QR Code PIX para um pagamento específico
   */
  async getPixQRCode(paymentId: string): Promise<{ encodedImage: string; payload: string }> {
    if (!paymentId) {
      throw new Error("paymentId é obrigatório para gerar QR Code PIX");
    }
    return await this.makeRequest(`/payments/${paymentId}/pixQrCode`, "GET");
  }
}
