import { z } from "zod";
import { AppointmentStatus, ConsultType, AppointmentType, PaymentStatus, UrgencyLevel } from "@prisma/client";

// Schema base de consulta
export const AppointmentBaseSchema = z.object({
  id: z.string(),
  doctorId: z.string(),
  patientId: z.string(),
  hospitalId: z.string().nullable(),
  scheduledAt: z.date(),
  consultType: z.nativeEnum(ConsultType),
  duration: z.number(),
  status: z.nativeEnum(AppointmentStatus),
  symptoms: z.string().nullable(),
  appointmentType: z.nativeEnum(AppointmentType),
  amount: z.number().nullable(),
  paymentStatus: z.nativeEnum(PaymentStatus),
  createdAt: z.date(),
  updatedAt: z.date(),
  // Campos específicos para plantão
  isOnDuty: z.boolean(),
  urgencyLevel: z.nativeEnum(UrgencyLevel).nullable(),
  acceptedAt: z.date().nullable(),
  acceptedByDoctorId: z.string().nullable(),
});

// Schema para criação de consulta
export const AppointmentCreateSchema = z.object({
  doctorId: z.string().min(1, "ID do médico é obrigatório"),
  patientId: z.string().min(1, "ID do paciente é obrigatório"),
  scheduledAt: z.string().or(z.date()).refine(value => !isNaN(new Date(value).getTime()), {
    message: "Data inválida",
  }),
  consultType: z.nativeEnum(ConsultType),
  duration: z.number().min(15, "Duração mínima de 15 minutos"),
  hospitalId: z.string().optional(),
  symptoms: z.string().optional(),
  appointmentType: z.nativeEnum(AppointmentType).default(AppointmentType.TELEMEDICINE),
  timeSlotId: z.string().optional(),
});

// Schema para consulta com relações
export const AppointmentWithRelationsSchema = AppointmentBaseSchema.extend({
  doctor: z.object({
    id: z.string(),
    user: z.object({
      id: z.string(),
      name: z.string().nullable(),
      email: z.string(),
      avatarUrl: z.string().nullable(),
    }),
    specialties: z.array(z.object({
      id: z.string(),
      name: z.string(),
    })),
  }),
  patient: z.object({
    id: z.string(),
    user: z.object({
      id: z.string(),
      name: z.string().nullable(),
      email: z.string(),
      avatarUrl: z.string().nullable(),
    }),
  }),
  hospital: z
    .object({
      id: z.string(),
      name: z.string(),
      logoUrl: z.string().nullable(),
    })
    .nullable(),
});
