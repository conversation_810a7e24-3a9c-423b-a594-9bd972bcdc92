// packages/api/modules/appointments/procedures/list.ts
import { AppointmentStatus } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema simplificado apenas com paginação e status opcional
const listSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(50).default(9),
  status: z.nativeEnum(AppointmentStatus).optional(),
  chatOnly: z.boolean().optional().default(false), // Add flag for chat-optimized queries
}).optional().default({
  page: 1,
  perPage: 9,
  chatOnly: false
});

export const list = protectedProcedure
  .input(listSchema)
  .query(async ({ ctx, input }) => {
    if (!ctx.user) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Usu<PERSON>rio não autenticado",
      });
    }

    console.log("[Appointment List] Starting with params:", input, "for user:", ctx.user.id, "role:", ctx.user.role);

    try {
      // For chat-optimized queries, use a different approach
      if (input.chatOnly) {
        return await getChatOptimizedAppointments(ctx, input);
      }

      const where: any = {};

      // Aplicar filtro baseado no papel do usuário
      if (ctx.user.role === "DOCTOR") {
        const doctor = await db.doctor.findUnique({
          where: { userId: ctx.user.id },
          select: { id: true },
        });
        if (!doctor) {
          console.log("[Appointment List] No doctor record found for user:", ctx.user.id);
          return { appointments: [], pagination: { total: 0, pages: 0, page: input.page, perPage: input.perPage } };
        }

        // Para médicos, incluir tanto consultas agendadas quanto plantões aceitos
        where.OR = [
          { doctorId: doctor.id }, // Consultas regulares agendadas
          { acceptedByDoctorId: doctor.id }, // Plantões aceitos pelo médico
        ];
        console.log("[Appointment List] Filtering by doctorId and acceptedByDoctorId:", doctor.id);
      } else if (ctx.user.role === "PATIENT") {
        const patient = await db.patient.findUnique({
          where: { userId: ctx.user.id },
          select: { id: true },
        });
        if (!patient) {
          console.log("[Appointment List] No patient record found for user:", ctx.user.id);
          return { appointments: [], pagination: { total: 0, pages: 0, page: input.page, perPage: input.perPage } };
        }
        where.patientId = patient.id;
        console.log("[Appointment List] Filtering by patientId:", patient.id);
      }

      // Aplicar filtro de status se fornecido
      if (input.status) {
        where.status = input.status;
        console.log("[Appointment List] Filtering by status:", input.status);
      }

      // Calcular paginação
      const skip = (input.page - 1) * input.perPage;
      const take = input.perPage;

      // 1. Buscar total e APENAS os dados básicos dos appointments
      const [total, baseAppointments] = await Promise.all([
        db.appointment.count({ where }),
        db.appointment.findMany({
          where,
          skip,
          take,
          orderBy: [
            // Priorizar plantões aceitos primeiro
            { isOnDuty: "desc" },
            // Dentro dos plantões, ordenar por urgência (HIGH > MEDIUM > LOW)
            { urgencyLevel: "desc" },
            // Depois por ordem de chegada (primeiro a chegar, primeiro a ser atendido)
            { createdAt: "asc" },
            // Por último, consultas regulares por data agendada
            { scheduledAt: "desc" }
          ],
          select: { // Selecionar apenas o necessário, incluindo chaves estrangeiras
            id: true,
            doctorId: true,
            patientId: true,
            hospitalId: true,
            scheduledAt: true,
            consultType: true,
            duration: true,
            status: true,
            appointmentType: true,
            amount: true,
            paymentStatus: true,
            symptoms: true,
            createdAt: true,
            updatedAt: true,
            // Campos específicos para plantão
            isOnDuty: true,
            urgencyLevel: true,
            acceptedAt: true,
            acceptedByDoctorId: true,
          },
        })
      ]);

      console.log(`[Appointment List] Found total appointments matching filters: ${total}`);
      console.log(`[Appointment List] Fetched ${baseAppointments.length} base appointments for page ${input.page}`);

      if (baseAppointments.length === 0) {
        return {
          appointments: [],
          pagination: { total, pages: Math.ceil(total / input.perPage), page: input.page, perPage: input.perPage },
        };
      }

      // 2. Coletar IDs únicos (filtrando nulls)
      const doctorIds = [...new Set(baseAppointments.map(a => a.doctorId).filter(id => id !== null))] as string[];
      const patientIds = [...new Set(baseAppointments.map(a => a.patientId).filter(id => id !== null))] as string[];
      const hospitalIds = [...new Set(baseAppointments.map(a => a.hospitalId).filter(id => id !== null))] as string[];


      // 3. Buscar dados relacionados separadamente
      const [doctorsData, patientsData, hospitalsData] = await Promise.all([
        db.doctor.findMany({
          where: { id: { in: doctorIds } },
          select: { id: true, userId: true, user: { select: { id: true, name: true, email: true, avatarUrl: true } } },
        }),
        db.patient.findMany({
          where: { id: { in: patientIds } },
          select: { id: true, userId: true, user: { select: { id: true, name: true, email: true, avatarUrl: true } } },
        }),
        db.hospital.findMany({
          where: { id: { in: hospitalIds } },
          select: { id: true, name: true, logoUrl: true },
        }),
      ]);

      // 4. Criar mapas para acesso rápido O(1)
      const doctorsMap = new Map(doctorsData.map(d => [d.id, d]));
      const patientsMap = new Map(patientsData.map(p => [p.id, p]));
      const hospitalsMap = new Map(hospitalsData.map(h => [h.id, h]));

      // Dados padrão para fallback
      const unknownUser = { id: "unknown", name: "Desconhecido", email: null, avatarUrl: null };
      const unknownDoctor = { id: "unknown", user: unknownUser };
      const unknownPatient = { id: "unknown", user: unknownUser };
      const unknownHospital = { id: "unknown", name: "Hospital Desconhecido", logoUrl: null };


      // 5. Montar o resultado final
      const appointments = baseAppointments.map(appointment => {
        const doctor = appointment.doctorId ? (doctorsMap.get(appointment.doctorId) || { ...unknownDoctor, id: appointment.doctorId }) : unknownDoctor;
        const patient = appointment.patientId ? (patientsMap.get(appointment.patientId) || { ...unknownPatient, id: appointment.patientId }) : unknownPatient;
        const hospital = appointment.hospitalId ? (hospitalsMap.get(appointment.hospitalId) || { ...unknownHospital, id: appointment.hospitalId }) : unknownHospital;

        // Garantir que user exista dentro de doctor/patient, mesmo que seja o fallback
        const safeDoctor = { ...doctor, user: doctor.user || unknownUser };
        const safePatient = { ...patient, user: patient.user || unknownUser };


        if (appointment.doctorId && !doctorsMap.has(appointment.doctorId)) {
             console.warn(`[Appointment List] Doctor record not found for doctorId ${appointment.doctorId} in appointment ${appointment.id}`);
        }
        if (appointment.patientId && !patientsMap.has(appointment.patientId)) {
             console.warn(`[Appointment List] Patient record not found for patientId ${appointment.patientId} in appointment ${appointment.id}`);
        }

        return {
          ...appointment,
          amount: appointment.amount ? Number(appointment.amount) : null,
          doctor: safeDoctor,
          patient: safePatient,
          hospital: hospital, // hospitalMap já retorna o objeto correto ou o fallback direto
          // Incluir campos específicos do plantão
          isOnDuty: appointment.isOnDuty,
          urgencyLevel: appointment.urgencyLevel,
          acceptedAt: appointment.acceptedAt,
          acceptedByDoctorId: appointment.acceptedByDoctorId,
        };
      });

      return {
        appointments,
        pagination: {
          total,
          pages: Math.ceil(total / input.perPage),
          page: input.page,
          perPage: input.perPage,
        },
      };
    } catch (error) {
      console.error("Erro detalhado ao listar consultas:", error); // Log mais detalhado

      if (error instanceof TRPCError) {
        throw error;
      }

      // Tenta preservar a causa original do erro para melhor depuração
      const message = error instanceof Error ? error.message : "Erro ao buscar consultas";
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: `Erro interno no servidor: ${message}`,
        cause: error,
      });
    }
  });

// Optimized function for chat appointments with single query
async function getChatOptimizedAppointments(ctx: any, input: any) {
  console.log("[Chat Optimized] Starting chat-optimized query for user:", ctx.user.id);

  const where: any = {};

  if (ctx.user.role === "DOCTOR") {
    const doctor = await db.doctor.findUnique({
      where: { userId: ctx.user.id },
      select: { id: true },
    });
    if (!doctor) {
      return { appointments: [], pagination: { total: 0, pages: 0, page: input.page, perPage: input.perPage } };
    }

    where.OR = [
      { doctorId: doctor.id },
      { acceptedByDoctorId: doctor.id },
    ];
  } else if (ctx.user.role === "PATIENT") {
    const patient = await db.patient.findUnique({
      where: { userId: ctx.user.id },
      select: { id: true },
    });
    if (!patient) {
      return { appointments: [], pagination: { total: 0, pages: 0, page: input.page, perPage: input.perPage } };
    }

    where.patientId = patient.id;
  }

  // Add chat-enabled filter and status filter
  where.chatEnabled = true;
  where.status = {
    in: ["SCHEDULED", "IN_PROGRESS", "COMPLETED"]
  };

  const skip = (input.page - 1) * input.perPage;
  const take = input.perPage;

  // Single optimized query with all relations
  const [total, appointments] = await Promise.all([
    db.appointment.count({ where }),
    db.appointment.findMany({
      where,
      skip,
      take,
      orderBy: [
        { isOnDuty: "desc" },
        { urgencyLevel: "desc" },
        { updatedAt: "desc" }
      ],
      select: {
        id: true,
        scheduledAt: true,
        status: true,
        updatedAt: true,
        isOnDuty: true,
        urgencyLevel: true,
        acceptedAt: true,
        acceptedByDoctorId: true,
        patient: {
          select: {
            user: {
              select: {
                id: true,
                name: true,
                avatarUrl: true
              }
            }
          }
        },
        doctor: {
          select: {
            user: {
              select: {
                id: true,
                name: true,
                avatarUrl: true
              }
            }
          }
        }
      }
    })
  ]);

  console.log(`[Chat Optimized] Found ${appointments.length} appointments in single query`);

  return {
    appointments,
    pagination: {
      total,
      pages: Math.ceil(total / input.perPage),
      page: input.page,
      perPage: input.perPage,
    },
  };
}
