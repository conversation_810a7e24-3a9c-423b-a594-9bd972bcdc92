// packages/api/modules/appointments/procedures/create.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { AppointmentStatus, ConsultType, AppointmentType } from "@prisma/client";
import { sendEmail } from "mail";
import { generateVerificationToken } from "auth/lib/tokens";

// Schema de entrada para criação de consulta
const AppointmentInputSchema = z.object({
  doctorId: z.string().min(1, "ID do médico é obrigatório"),
  patientId: z.string().min(1, "ID do paciente é obrigatório"),
  scheduledAt: z.string().or(z.date()).refine(value => !isNaN(new Date(value).getTime()), {
    message: "Data inválida",
  }),
  consultType: z.nativeEnum(ConsultType),
  duration: z.number().min(15, "Duração mínima de 15 minutos"),
  hospitalId: z.string().optional(),
  symptoms: z.string().optional(),
  appointmentType: z.nativeEnum(AppointmentType).default(AppointmentType.TELEMEDICINE),
  timeSlotId: z.string().optional(),
});

// Schema de saída
const AppointmentOutputSchema = z.object({
  id: z.string(),
  doctorId: z.string(),
  patientId: z.string(),
  hospitalId: z.string().nullable(),
  scheduledAt: z.date(),
  consultType: z.nativeEnum(ConsultType),
  duration: z.number(),
  status: z.nativeEnum(AppointmentStatus),
  appointmentType: z.nativeEnum(AppointmentType),
  amount: z.number().nullable(),
  createdAt: z.date(),
});

export const create = protectedProcedure
  .input(AppointmentInputSchema)
  .output(AppointmentOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Usuário não autenticado",
        });
      }

      console.log("[Appointment Create] Starting with input:", {
        doctorId: input.doctorId,
        patientId: input.patientId,
        scheduledAt: input.scheduledAt,
        consultType: input.consultType,
        duration: input.duration,
      });

      // Verificar se o médico existe e obter seus dados completos
      const doctor = await db.doctor.findUnique({
        where: { id: input.doctorId },
        include: {
          user: true,
          specialties: true,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Médico não encontrado",
        });
      }

      console.log("[Appointment Create] Doctor found:", {
        id: doctor.id,
        userId: doctor.userId,
        userName: doctor.user?.name,
        specialties: doctor.specialties.map(s => s.name).join(', ')
      });

      // Verificar se o paciente existe e obter seus dados completos
      const patient = await db.patient.findUnique({
        where: { id: input.patientId },
        include: {
          user: true,
        },
      });

      if (!patient) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Paciente não encontrado",
        });
      }

      console.log("[Appointment Create] Patient found:", {
        id: patient.id,
        userId: patient.userId,
        userName: patient.user?.name,
      });

      // Converter a data agendada
      const scheduledAt = new Date(input.scheduledAt);

      // Verificar se a data é futura
      if (scheduledAt < new Date()) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "A data agendada deve ser futura",
        });
      }

      // Verificar se o horário está disponível
      const existingAppointment = await db.appointment.findFirst({
        where: {
          doctorId: input.doctorId,
          scheduledAt: {
            gte: new Date(scheduledAt.getTime() - 1000 * 60 * 10), // 10 minutos antes
            lte: new Date(scheduledAt.getTime() + 1000 * 60 * input.duration + 1000 * 60 * 10), // 10 minutos depois do fim
          },
          status: {
            notIn: ["CANCELED", "COMPLETED"],
          },
        },
      });

      if (existingAppointment) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Horário indisponível. O médico já possui uma consulta agendada para este horário.",
        });
      }

      // Reservar o time slot se fornecido
      if (input.timeSlotId) {
        await db.timeSlot.update({
          where: { id: input.timeSlotId },
          data: {
            isAvailable: false,
            appointmentId: null // Será atualizado após criar a consulta
          },
        });
      }

      // Valor da consulta
      const amount = doctor.consultationPrice ? parseFloat(doctor.consultationPrice.toString()) : 0;

      console.log("[Appointment Create] Prepared data, creating appointment...");

      // Criar a consulta
      const appointment = await db.appointment.create({
        data: {
          doctorId: input.doctorId,
          patientId: input.patientId,
          hospitalId: input.hospitalId,
          scheduledAt,
          consultType: input.consultType,
          duration: input.duration || doctor.consultationDuration || 30,
          status: AppointmentStatus.SCHEDULED,
          appointmentType: input.appointmentType,
          symptoms: input.symptoms,
          amount,
          paymentStatus: "PENDING",
          // Criar transação pendente
          transaction: {
            create: {
              amount,
              platformFee: amount * 0.10, // 10% de fee
              doctorAmount: amount * 0.90, // 90% para o médico
              status: "PENDING",
              paymentMethod: "CREDIT_CARD",
              asaasId: "", // Será preenchido após integração com Asaas
              dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3), // Vencimento em 3 dias
            },
          },
        },
      });

      console.log("[Appointment Create] Appointment created:", appointment.id);

      // Atualizar o time slot para vincular à consulta criada
      if (input.timeSlotId) {
        await db.timeSlot.update({
          where: { id: input.timeSlotId },
          data: { appointmentId: appointment.id },
        });
      }

      // Criar notificações para o médico e o paciente
      await db.notification.create({
        data: {
          userId: doctor.userId,
          appointmentId: appointment.id,
          type: "APPOINTMENT_CREATED",
          title: "Nova Consulta Agendada",
          message: `Uma nova consulta foi agendada para ${scheduledAt.toLocaleDateString('pt-BR')} às ${scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`,
        },
      });

      await db.notification.create({
        data: {
          userId: patient.userId,
          appointmentId: appointment.id,
          type: "APPOINTMENT_CREATED",
          title: "Consulta Agendada",
          message: `Sua consulta foi agendada para ${scheduledAt.toLocaleDateString('pt-BR')} às ${scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`,
        },
      });

      // Generate secure tokens for email links
      const doctorToken = await generateVerificationToken({
        userId: doctor.userId,
      });

      const patientToken = await generateVerificationToken({
        userId: patient.userId,
      });

      // Enviar email para o médico
      await sendEmail({
        to: doctor.user.email,
        templateId: "appointmentCreated",
        context: {
          recipientName: doctor.user.name || "Doutor(a)",
          patientName: patient.user.name || "Paciente",
          appointmentDate: scheduledAt.toLocaleDateString('pt-BR'),
          appointmentTime: scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
          consultType: input.consultType,
          joinUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify?token=${encodeURIComponent(doctorToken)}&redirect=/app/dashboard`,
          isDoctor: true
        },
        locale: "pt"
      });

      // Enviar email para o paciente
      await sendEmail({
        to: patient.user.email,
        templateId: "appointmentCreated",
        context: {
          recipientName: patient.user.name || "Paciente",
          doctorName: doctor.user.name || "Doutor(a)",
          appointmentDate: scheduledAt.toLocaleDateString('pt-BR'),
          appointmentTime: scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
          consultType: input.consultType,
          joinUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify?token=${encodeURIComponent(patientToken)}&redirect=/app/dashboard`,
          isDoctor: false
        },
        locale: "pt"
      });

      console.log("[Appointment Create] Completed successfully!");

      // Buscar a consulta completa para retornar
      const fullAppointment = await db.appointment.findUnique({
        where: { id: appointment.id },
        include: {
          doctor: {
            include: {
              user: true,
              specialties: true
            }
          },
          patient: {
            include: {
              user: true
            }
          },
          hospital: true
        }
      });

      if (!fullAppointment) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao buscar a consulta recém-criada",
        });
      }

      return {
        ...appointment,
        amount: amount || null,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error creating appointment:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao criar consulta",
        cause: error,
      });
    }
  });
