import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { AppointmentStatus } from "@prisma/client";

export const cancel = protectedProcedure
  .input(
    z.object({
      id: z.string(),
      reason: z.string().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      const { id, reason } = input;
      const { user } = ctx;

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Usu<PERSON>rio não autenticado",
        });
      }

      // Buscar a consulta
      const appointment = await db.appointment.findUnique({
        where: { id },
        include: {
          doctor: {
            select: {
              userId: true,
            },
          },
          patient: {
            select: {
              userId: true,
            },
          },
        },
      });

      if (!appointment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Consulta não encontrada",
        });
      }

      // Verificar permissão - apenas admin, médico da consulta ou paciente da consulta podem cancelar
      const isAdmin = user.role === "ADMIN";
      const isAppointmentDoctor = appointment.doctor.userId === user.id;
      const isAppointmentPatient = appointment.patient.userId === user.id;

      if (!isAdmin && !isAppointmentDoctor && !isAppointmentPatient) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para cancelar esta consulta",
        });
      }

      // Verificar se a consulta já está completa ou cancelada
      if (appointment.status === AppointmentStatus.COMPLETED) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Não é possível cancelar uma consulta já realizada",
        });
      }

      if (appointment.status === AppointmentStatus.CANCELED) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Esta consulta já está cancelada",
        });
      }

      // Cancelar consulta
      const updatedAppointment = await db.appointment.update({
        where: { id },
        data: {
          status: AppointmentStatus.CANCELED,
          // Salvar motivo do cancelamento se fornecido
          metadata: reason ? { cancellationReason: reason } : undefined,
        },
      });

      // Liberar time slot se existir
      await db.timeSlot.updateMany({
        where: {
          doctorId: appointment.doctorId,
          startTime: appointment.scheduledAt,
        },
        data: {
          isAvailable: true,
          appointmentId: null,
        },
      });

      // Atualizar status da transação se existir
      await db.transaction.updateMany({
        where: { appointmentId: id },
        data: {
          status: "CANCELED",
        },
      });

      // Criar notificações para médico e paciente
      const cancelledBy = isAppointmentDoctor ? "médico" : isAppointmentPatient ? "paciente" : "administrador";
      const cancelMessage = `Consulta de ${new Date(appointment.scheduledAt).toLocaleDateString()} às ${new Date(appointment.scheduledAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} cancelada pelo ${cancelledBy}${reason ? `: ${reason}` : ""}`;

      // Notificação para o médico se não foi ele quem cancelou
      if (!isAppointmentDoctor) {
        await db.notification.create({
          data: {
            userId: appointment.doctor.userId,
            appointmentId: id,
            type: "APPOINTMENT_CANCELED",
            title: "Consulta Cancelada",
            message: cancelMessage,
          },
        });
      }

      // Notificação para o paciente se não foi ele quem cancelou
      if (!isAppointmentPatient) {
        await db.notification.create({
          data: {
            userId: appointment.patient.userId,
            appointmentId: id,
            type: "APPOINTMENT_CANCELED",
            title: "Consulta Cancelada",
            message: cancelMessage,
          },
        });
      }

      return updatedAppointment;
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error cancelling appointment:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao cancelar consulta",
        cause: error,
      });
    }
  });
