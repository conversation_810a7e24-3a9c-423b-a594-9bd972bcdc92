import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const byId = protectedProcedure
  .input(
    z.object({
      id: z.string().min(1, "ID do médico é obrigatório"),
    })
  )
  .query(async ({ input, ctx }) => {
    try {
      const { id } = input;

      // Buscar o médico com todos os detalhes necessários
      const doctor = await db.doctor.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatarUrl: true,
              phone: true,
            },
          },
          specialties: {
            select: {
              id: true,
              name: true,
            },
          },
          doctorSchedules: true,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      // Converter o preço da consulta para número
      return {
        ...doctor,
        consultationPrice: doctor.consultationPrice ? parseFloat(doctor.consultationPrice.toString()) : null,
        consultationDuration: doctor.consultationDuration || null,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching doctor:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar detalhes do médico",
        cause: error,
      });
    }
  });
