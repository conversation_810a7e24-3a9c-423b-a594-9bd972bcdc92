import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const DoctorSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    avatarUrl: z.string().nullable(),
  }),
  specialties: z.array(z.object({
    id: z.string(),
    name: z.string(),
  })),
  consultationPrice: z.number().nullable(),
  consultationDuration: z.number().nullable(),
  rating: z.number().nullable(),
  totalRatings: z.number(),
});

export const getDoctorsBySpecialty = protectedProcedure
  .input(
    z.object({
      specialtyId: z.string().optional(),
      hospitalId: z.string().optional(),
      searchTerm: z.string().optional(),
    }).optional()
  )
  .output(z.array(DoctorSchema))
  .query(async ({ input, ctx }) => {
    try {
      // Construir filtros de busca
      const filters: any = {
        documentStatus: "APPROVED", // Apenas médicos aprovados
      };

      // Filtrar por especialidade se fornecido
      if (input?.specialtyId) {
        filters.specialties = {
          some: {
            id: input.specialtyId,
          },
        };
      }

      // Filtrar por hospital se fornecido
      if (input?.hospitalId) {
        filters.hospitals = {
          some: {
            hospitalId: input.hospitalId,
            isActive: true,
          },
        };
      }

      // Filtrar por termo de busca
      if (input?.searchTerm) {
        const searchTerm = input.searchTerm.trim().toLowerCase();
        filters.OR = [
          {
            user: {
              name: {
                contains: searchTerm,
                mode: "insensitive",
              },
            },
          },
          {
            specialties: {
              some: {
                name: {
                  contains: searchTerm,
                  mode: "insensitive",
                },
              },
            },
          },
          {
            crm: {
              contains: searchTerm,
            },
          },
        ];
      }

      const doctors = await db.doctor.findMany({
        where: filters,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatarUrl: true,
            },
          },
          specialties: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          user: {
            name: "asc",
          },
        },
      });

      // Convertendo o preço da consulta para número
      return doctors.map(doctor => ({
        ...doctor,
        consultationPrice: doctor.consultationPrice ? parseFloat(doctor.consultationPrice.toString()) : null,
        consultationDuration: doctor.consultationDuration || null,
      }));
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching doctors:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar médicos",
        cause: error,
      });
    }
  });
