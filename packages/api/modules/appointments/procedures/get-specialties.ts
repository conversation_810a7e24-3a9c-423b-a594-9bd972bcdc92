import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const SpecialtySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
});

export const getSpecialties = protectedProcedure
  .input(z.void())
  .output(z.array(SpecialtySchema))
  .query(async () => {
    try {
      const specialties = await db.specialty.findMany({
        orderBy: {
          name: "asc",
        },
      });

      return specialties;
    } catch (error) {
      console.error("Error fetching specialties:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar especialidades",
        cause: error,
      });
    }
  });
