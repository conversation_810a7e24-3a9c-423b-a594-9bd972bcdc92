import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

const PatientSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    avatarUrl: z.string().nullable(),
  }),
  cpf: z.string(),
  birthDate: z.date(),
  gender: z.string(),
});

export const getPatients = protectedProcedure
  .input(
    z.object({
      searchTerm: z.string().optional(),
      limit: z.number().min(1).max(100).optional().default(20),
      offset: z.number().min(0).optional().default(0),
    }).optional()
  )
  .output(
    z.object({
      patients: z.array(PatientSchema),
      total: z.number(),
    })
  )
  .query(async ({ input = {}, ctx }) => {
    try {
      const { searchTerm, limit = 20, offset = 0 } = input;

      // Construir filtros de busca
      const where: any = {};

      // Filtrar por termo de busca
      if (searchTerm) {
        const term = searchTerm.trim().toLowerCase();
        where.OR = [
          {
            user: {
              name: {
                contains: term,
                mode: "insensitive",
              },
            },
          },
          {
            user: {
              email: {
                contains: term,
                mode: "insensitive",
              },
            },
          },
          {
            cpf: {
              contains: term,
            },
          },
        ];
      }

      // Buscar pacientes com paginação
      const [patients, total] = await Promise.all([
        db.patient.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatarUrl: true,
              },
            },
          },
          orderBy: {
            user: {
              name: "asc",
            },
          },
          take: limit,
          skip: offset,
        }),
        db.patient.count({ where }),
      ]);

      return {
        patients,
        total,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching patients:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar pacientes",
        cause: error,
      });
    }
  });
