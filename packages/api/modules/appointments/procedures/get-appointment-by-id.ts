import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const getAppointmentById = protectedProcedure
  .input(
    z.object({
      id: z.string().min(1, "ID da consulta é obrigatório"),
    })
  )
  .query(async ({ input, ctx }) => {
    try {
      const { id } = input;
      const { user } = ctx;

      // Buscar a consulta com todos os detalhes necessários
      const appointment = await db.appointment.findUnique({
        where: { id },
        include: {
          doctor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                  phone: true,
                },
              },
              specialties: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                  phone: true,
                },
              },
            },
          },
          hospital: {
            select: {
              id: true,
              name: true,
              logoUrl: true,
              contactEmail: true,
              contactPhone: true,
            },
          },
        },
      });

      if (!appointment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Consulta não encontrada",
        });
      }

      // Verificar permissões - apenas admin, médico da consulta, paciente da consulta
      // ou secretário do hospital podem visualizar detalhes
      const isAdmin = user?.role === "ADMIN";
      const isAppointmentDoctor = appointment.doctor.user.id === user?.id;
      const isAppointmentPatient = appointment.patient.user.id === user?.id;
      const isHospitalStaff = user?.role === "HOSPITAL" && appointment.hospitalId === user.id;

      // Verificar permissão de secretária do hospital
      let isHospitalSecretary = false;
      if (user?.role === "SECRETARY") {
        const secretary = await db.secretary.findUnique({
          where: { userId: user.id },
          select: { hospitalId: true },
        });

        isHospitalSecretary = !!secretary && !!appointment.hospitalId &&
                             secretary.hospitalId === appointment.hospitalId;
      }

      if (!isAdmin && !isAppointmentDoctor && !isAppointmentPatient &&
          !isHospitalStaff && !isHospitalSecretary) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para visualizar detalhes desta consulta",
        });
      }

      // Converter valores numéricos
      return {
        ...appointment,
        amount: appointment.amount ? Number(appointment.amount) : null,
        doctor: {
          ...appointment.doctor,
          consultationPrice: appointment.doctor.consultationPrice
            ? Number(appointment.doctor.consultationPrice)
            : null,
          consultationDuration: appointment.doctor.consultationDuration || null,
        }
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching appointment:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar detalhes da consulta",
        cause: error,
      });
    }
  });
