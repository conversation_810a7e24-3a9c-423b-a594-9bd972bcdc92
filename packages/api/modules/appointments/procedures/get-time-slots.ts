import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { endOfDay, format, parse, startOfDay } from "date-fns";

const TimeSlotSchema = z.object({
  id: z.string(),
  startTime: z.date(),
  endTime: z.date(),
  isAvailable: z.boolean(),
});

export const getTimeSlots = protectedProcedure
  .input(
    z.object({
      doctorId: z.string(),
      date: z.string().refine(value => !isNaN(new Date(value).getTime()), {
        message: "Data inválida",
      }),
    })
  )
  .output(z.array(TimeSlotSchema))
  .query(async ({ input, ctx }) => {
    try {
      const { doctorId, date } = input;

      // Verificar se o médico existe
      const doctor = await db.doctor.findUnique({
        where: { id: doctorId },
        include: {
          doctorSchedules: true,
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Médico não encontrado",
        });
      }

      const targetDate = new Date(date);
      const weekDay = targetDate.getDay(); // 0 = Domingo, 1 = Segunda, ...

      // Buscar configuração do dia da semana
      const schedule = doctor.doctorSchedules.find(s => s.weekDay === weekDay && s.isEnabled);

      if (!schedule) {
        return []; // Retornar lista vazia se não houver atendimento neste dia
      }

      // Buscar consultas existentes
      const appointments = await db.appointment.findMany({
        where: {
          doctorId,
          scheduledAt: {
            gte: startOfDay(targetDate),
            lte: endOfDay(targetDate),
          },
          status: {
            notIn: ["CANCELED", "COMPLETED"],
          },
        },
        select: {
          scheduledAt: true,
          duration: true,
        },
      });

      // Buscar bloqueios de agenda
      const blocks = await db.scheduleBlock.findMany({
        where: {
          doctorId,
          startTime: {
            lte: endOfDay(targetDate),
          },
          endTime: {
            gte: startOfDay(targetDate),
          },
        },
      });

      // Gerar slots disponíveis
      const slots = generateTimeSlots(
        schedule.startTime,
        schedule.endTime,
        targetDate,
        doctor.consultationDuration || 30,
        appointments,
        blocks,
      );

      // Buscar time slots já existentes no banco
      const existingTimeSlots = await db.timeSlot.findMany({
        where: {
          doctorId,
          startTime: {
            gte: startOfDay(targetDate),
            lte: endOfDay(targetDate),
          },
        },
      });

      // Mapear existentes para evitar duplicações
      const existingSlotMap = new Map();
      for (const slot of existingTimeSlots) {
        const key = format(slot.startTime, 'HH:mm');
        existingSlotMap.set(key, slot);
      }

      // Criar time slots no banco que não existem ainda
      const results = await Promise.all(
        slots.map(async (slot) => {
          const startTimeKey = format(slot.startTime, 'HH:mm');

          if (existingSlotMap.has(startTimeKey)) {
            const existingSlot = existingSlotMap.get(startTimeKey);
            return {
              id: existingSlot.id,
              startTime: existingSlot.startTime,
              endTime: existingSlot.endTime,
              isAvailable: existingSlot.isAvailable,
            };
          } else {
            // Criar novo slot
            const newSlot = await db.timeSlot.create({
              data: {
                doctorId,
                startTime: slot.startTime,
                endTime: slot.endTime,
                isAvailable: slot.isAvailable,
              },
            });

            return {
              id: newSlot.id,
              startTime: newSlot.startTime,
              endTime: newSlot.endTime,
              isAvailable: newSlot.isAvailable,
            };
          }
        })
      );

      return results;
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error("Error fetching time slots:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar horários disponíveis",
        cause: error,
      });
    }
  });

// Função auxiliar para gerar slots de tempo
function generateTimeSlots(
  startTime: string,
  endTime: string,
  date: Date,
  duration: number,
  appointments: { scheduledAt: Date; duration: number }[],
  blocks: { startTime: Date; endTime: Date }[]
) {
  const slots = [];
  const formatDatePart = format(date, 'yyyy-MM-dd');

  // Parse das horas de início e fim
  const start = parse(`${formatDatePart} ${startTime}`, 'yyyy-MM-dd HH:mm', new Date());
  const end = parse(`${formatDatePart} ${endTime}`, 'yyyy-MM-dd HH:mm', new Date());

  // Gerar slots
  let current = new Date(start);
  while (current < end) {
    const slotEnd = new Date(current.getTime() + duration * 60000);

    // Verificar se o slot está bloqueado
    const isBlocked = blocks.some((block) => {
      return (
        (current >= block.startTime && current < block.endTime) ||
        (slotEnd > block.startTime && slotEnd <= block.endTime) ||
        (current <= block.startTime && slotEnd >= block.endTime)
      );
    });

    // Verificar se o slot está ocupado por outra consulta
    const isBooked = appointments.some((apt) => {
      const aptStart = apt.scheduledAt;
      const aptEnd = new Date(aptStart.getTime() + apt.duration * 60000);
      return (
        (current >= aptStart && current < aptEnd) ||
        (slotEnd > aptStart && slotEnd <= aptEnd) ||
        (current <= aptStart && slotEnd >= aptEnd)
      );
    });

    // Adicionar slot se disponível
    slots.push({
      startTime: new Date(current),
      endTime: new Date(slotEnd),
      isAvailable: !isBlocked && !isBooked,
    });

    // Avançar para o próximo slot
    current = new Date(slotEnd);
  }

  return slots;
}
