import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";

// Local implementation of formatPhoneForWhatsApp
function formatPhoneForWhatsApp(phone: string): string {
  if (!phone) return "";
  // Remove non-numeric characters
  let phoneNumber = phone.replace(/\D/g, "");
  // Add country code if needed
  const hasInternationalPrefix = /^(1|55|44|33|49|86|81)/i.test(phoneNumber);
  if (!hasInternationalPrefix && (phoneNumber.length === 10 || phoneNumber.length === 11)) {
    phoneNumber = `55${phoneNumber}`;
  }
  console.log("[NOTIFY_DOCTORS] Formatted phone:", { original: phone, formatted: phoneNumber });
  return phoneNumber;
}

// Local implementation of EvolutionService
class EvolutionService {
  private readonly apiKey: string;
  private readonly instance: string;
  private readonly baseUrl: string;

  constructor() {
    this.apiKey = process.env.EVOLUTION_API_KEY || "";
    this.instance = process.env.EVOLUTION_INSTANCE || "";
    this.baseUrl = process.env.EVOLUTION_URL || "https://evo2.clouds3.nextrusti.com";

    console.log("[EVOLUTION_SERVICE] Initialized with:", {
      hasApiKey: !!this.apiKey,
      hasInstance: !!this.instance,
      baseUrl: this.baseUrl
    });
  }

  async sendMessagesWithDelay(messages: string[], number: string): Promise<any[]> {
    const responses = [];
    const MAX_RETRIES = 2;

    console.log("[EVOLUTION_SERVICE] Sending messages to:", {
      number,
      messageCount: messages.length
    });

    for (const text of messages) {
      try {
        // For debugging purposes
        console.log("[EVOLUTION_SERVICE] Preparing message:", {
          textLength: text.length,
          number
        });

        // Create request payload
        const payload = {
          number,
          text,
          delay: 1000,
          linkPreview: false,
        };

        // Validar payload antes de serializar
        console.log("[EVOLUTION_SERVICE] Payload validation:", {
          payloadType: typeof payload,
          payloadIsNull: payload === null,
          payloadIsUndefined: payload === undefined,
          hasNumber: !!payload.number,
          hasText: !!payload.text,
          numberType: typeof payload.number,
          textType: typeof payload.text
        });

        if (!payload || payload === null) {
          throw new Error("Payload is null or undefined");
        }

        if (!payload.number || !payload.text) {
          throw new Error("Payload missing required fields: number or text");
        }

        // Try sending with retries
        let success = false;
        let retryCount = 0;
        let lastError = null;

        while (!success && retryCount <= MAX_RETRIES) {
          try {
            const url = `${this.baseUrl}/message/sendText/${this.instance}`;
            console.log(`[EVOLUTION_SERVICE] Sending request (attempt ${retryCount + 1}):`, { url });

            // Validar payload novamente antes de JSON.stringify
            let serializedPayload;
            try {
              serializedPayload = JSON.stringify(payload);
              console.log("[EVOLUTION_SERVICE] Payload serialized successfully:", {
                length: serializedPayload.length
              });
            } catch (serializeError) {
              console.error("[EVOLUTION_SERVICE] Error serializing payload:", serializeError);
              console.error("[EVOLUTION_SERVICE] Problematic payload:", {
                payload,
                payloadType: typeof payload,
                payloadConstructor: payload?.constructor?.name
              });
              throw new Error("Failed to serialize payload");
            }

            const response = await fetch(url, {
              method: 'POST',
              headers: {
                apikey: this.apiKey,
                'Content-Type': 'application/json',
              },
              body: serializedPayload,
            });

            // Log response status
            console.log(`[EVOLUTION_SERVICE] Response status:`, { status: response.status });

            // Handle non-OK responses
            if (!response.ok) {
              const errorText = await response.text().catch(() => "Could not read error response");
              console.error(`[EVOLUTION_SERVICE] API Error (${response.status}):`, errorText);
              throw new Error(`Evolution API Error: ${response.status} - ${errorText}`);
            }

            // Parse response
            const data = await response.json().catch(err => {
              console.error("[EVOLUTION_SERVICE] Failed to parse JSON response:", err);
              throw new Error("Invalid JSON response from API");
            });

            responses.push(data);
            success = true;
            console.log("[EVOLUTION_SERVICE] Message sent successfully");
          } catch (error) {
            retryCount++;
            lastError = error;
            console.error(`[EVOLUTION_SERVICE] Error on attempt ${retryCount}:`, error);

            if (retryCount <= MAX_RETRIES) {
              // Wait before retry
              const delay = retryCount * 1000; // Increase delay with each retry
              console.log(`[EVOLUTION_SERVICE] Retrying in ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }

        // If all retries failed
        if (!success) {
          console.error("[EVOLUTION_SERVICE] All retries failed, recording error");
          responses.push({ error: true, message: lastError?.message || "Max retries exceeded" });
        }

        // Wait between messages regardless of success
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error("[EVOLUTION_API_ERROR]", error);
        responses.push({ error: true, message: error?.message || "Unknown error" });
      }
    }

    console.log("[EVOLUTION_SERVICE] Finished sending messages:", {
      totalSent: responses.filter(r => !r.error).length,
      totalFailed: responses.filter(r => r.error).length,
      totalMessages: messages.length
    });

    return responses;
  }
}

const notifyDoctorsSchema = z.object({
  appointmentId: z.string(),
  urgencyLevel: z.enum(["HIGH", "MEDIUM", "LOW"]),
  patientName: z.string(),
});

export const notifyOnlineDoctors = publicProcedure
  .input(notifyDoctorsSchema)
  .mutation(async ({ input }) => {
    try {
      console.log("[NOTIFY_DOCTORS] Iniciando notificação para médicos online:", {
        appointmentId: input.appointmentId,
        urgencyLevel: input.urgencyLevel,
        patientName: input.patientName
      });

      // 1. Buscar todos os médicos que estão online
      // Para teste, vamos buscar apenas o médico específico
      const testDoctorId = "cm8gg9pgy0001k42miezlxipw"; // ID do Dr. Alysson Beckert

      const onlineDoctors = await db.doctor.findMany({
        where: {
          userId: testDoctorId, // Para teste, usar apenas o médico específico
          // Em produção, descomentar estas condições:
          // onlineStatus: "ONLINE",
          // isAvailableForOnDuty: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true
            }
          }
        }
      });

      console.log("[NOTIFY_DOCTORS] Encontrados médicos para teste:", {
        count: onlineDoctors.length,
        doctors: onlineDoctors.map(d => ({ id: d.id, name: d.user.name, hasPhone: !!d.user.phone }))
      });

      if (onlineDoctors.length === 0) {
        console.warn("[NOTIFY_DOCTORS] Médico de teste não encontrado");
        return {
          success: true,
          notifiedCount: 0,
          totalDoctors: 0,
          message: "Médico de teste não encontrado"
        };
      }

      // 2. Configurar URLs e mensagens
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com";
      const plantaoUrl = `${baseUrl}/app/plantao`;

      // 3. Preparar dados para notificação
      const urgencyLabels = {
        "HIGH": "🔴 Muito Urgente",
        "MEDIUM": "🟡 Urgente",
        "LOW": "🟢 Pouco Urgente"
      };

      const urgencyLabel = urgencyLabels[input.urgencyLevel];

      // 4. Enviar notificações WhatsApp para cada médico
      const notificationResults = [];
      const evolutionService = new EvolutionService();

      for (const doctor of onlineDoctors) {
        try {
          // Verificar se o médico tem telefone
          if (!doctor.user.phone || doctor.user.phone.trim().length < 10) {
            console.warn("[NOTIFY_DOCTORS] Médico sem telefone válido:", {
              doctorId: doctor.id,
              doctorName: doctor.user.name,
              phone: doctor.user.phone || "não informado"
            });

            // Adicionar ao resultado mesmo sem enviar mensagem
            notificationResults.push({
              doctorId: doctor.id,
              doctorName: doctor.user.name,
              success: false,
              error: "Telefone não disponível ou inválido"
            });

            continue;
          }

          // Preparar mensagens personalizadas para o médico
          const messages = [
            `🏥 *Nova consulta de plantão!*`,
            `Olá Dr(a). ${doctor.user.name}!`,
            `📋 *Detalhes do Atendimento*\n\n👤 Paciente: ${input.patientName}\n⚠️ Urgência: ${urgencyLabel}\n🕐 Agora mesmo`,
            `Para aceitar este atendimento e ver todos os detalhes, acesse:\n${plantaoUrl}`,
            `⚡ *Ação rápida necessária!* Pacientes com essa urgência precisam de atendimento imediato.`
          ];

          // Formatar telefone para WhatsApp
          const formattedPhone = formatPhoneForWhatsApp(doctor.user.phone);

          console.log("[NOTIFY_DOCTORS] Enviando notificação para:", {
            doctorId: doctor.id,
            doctorName: doctor.user.name,
            phone: formattedPhone,
            appointmentId: input.appointmentId
          });

          // Enviar mensagens
          const responses = await evolutionService.sendMessagesWithDelay(messages, formattedPhone);

          // Verificar se houve erro em alguma mensagem
          const hasErrors = responses.some(r => r.error);

          notificationResults.push({
            doctorId: doctor.id,
            doctorName: doctor.user.name,
            success: !hasErrors,
            messageCount: responses.filter(r => !r.error).length,
            errorCount: responses.filter(r => r.error).length
          });

          console.log("[NOTIFY_DOCTORS] Resultado do envio:", {
            doctorId: doctor.id,
            doctorName: doctor.user.name,
            success: !hasErrors,
            messageCount: responses.filter(r => !r.error).length,
            errorCount: responses.filter(r => r.error).length
          });

        } catch (doctorNotificationError) {
          console.error("[NOTIFY_DOCTORS] Erro ao notificar médico:", {
            doctorId: doctor.id,
            doctorName: doctor.user.name,
            error: doctorNotificationError instanceof Error
              ? doctorNotificationError.message
              : String(doctorNotificationError)
          });

          notificationResults.push({
            doctorId: doctor.id,
            doctorName: doctor.user.name,
            success: false,
            error: doctorNotificationError instanceof Error
              ? doctorNotificationError.message
              : "Erro desconhecido"
          });
        }
      }

      // 5. Registrar tentativa de notificação no banco (opcional)
      try {
        // Criar registro de notificação para cada médico
        const notificationPromises = onlineDoctors.map(doctor =>
          db.notification.create({
            data: {
              userId: doctor.user.id,
              appointmentId: input.appointmentId,
              type: "APPOINTMENT_CREATED",
              title: "Nova consulta de plantão disponível",
              message: `Paciente ${input.patientName} solicita atendimento de plantão (${urgencyLabel})`,
              read: false
            }
          }).catch(error => {
            console.error("[NOTIFY_DOCTORS] Erro ao criar notificação no banco:", error);
          })
        );

        await Promise.allSettled(notificationPromises);
      } catch (dbNotificationError) {
        console.error("[NOTIFY_DOCTORS] Erro ao salvar notificações no banco:", dbNotificationError);
        // Não falhar o processo por erro no banco
      }

      // 6. Calcular estatísticas
      const successfulNotifications = notificationResults.filter(r => r.success).length;
      const failedNotifications = notificationResults.filter(r => !r.success).length;

      console.log("[NOTIFY_DOCTORS] Processo concluído:", {
        appointmentId: input.appointmentId,
        totalDoctors: onlineDoctors.length,
        successfulNotifications,
        failedNotifications,
        results: notificationResults
      });

      return {
        success: true,
        notifiedCount: successfulNotifications,
        failedCount: failedNotifications,
        totalDoctors: onlineDoctors.length,
        details: notificationResults,
        message: successfulNotifications > 0
          ? `${successfulNotifications} médicos notificados com sucesso`
          : "Nenhum médico foi notificado com sucesso"
      };
    } catch (error) {
      console.error("[NOTIFY_DOCTORS] Erro crítico:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: error instanceof Error ? error.message : "Erro desconhecido"
      });
    }
  });
