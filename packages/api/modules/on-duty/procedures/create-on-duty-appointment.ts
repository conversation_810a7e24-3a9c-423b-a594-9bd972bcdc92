import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";
import { db } from "database";
import { ON_DUTY_CONFIG } from "../../../constants/on-duty";

export const createOnDutyAppointment = publicProcedure
  .input(
    z.object({
      patientData: z.object({
        name: z.string(),
        email: z.string().email(),
        phone: z.string(),
        cpf: z.string(),
        birthDate: z.string().optional(),
        gender: z.string().optional(),
      }),
      urgencyLevel: z.enum(["high", "medium", "low"]),
      symptoms: z.string(),
      paymentData: z.object({
        paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
        amount: z.number(),
        paymentId: z.string().optional(),
      }),
    })
  )
  .mutation(async ({ input }) => {
    try {
      // 1. <PERSON><PERSON>r ou encontrar o usuário/paciente
      let user = await db.user.findUnique({
        where: { email: input.patientData.email },
      });

      if (!user) {
        // Criar novo usuário
        user = await db.user.create({
          data: {
            name: input.patientData.name,
            email: input.patientData.email,
            phone: input.patientData.phone,
            role: "PATIENT",
            emailVerified: false,
          },
        });
      }

      // 2. Criar ou encontrar o paciente
      let patient = await db.patient.findUnique({
        where: { userId: user.id },
      });

      if (!patient) {
        patient = await db.patient.create({
          data: {
            userId: user.id,
            cpf: input.patientData.cpf,
            birthDate: input.patientData.birthDate
              ? new Date(input.patientData.birthDate)
              : new Date(Date.now() - 30 * 365 * 24 * 60 * 60 * 1000), // 30 anos como padrão
            gender: input.patientData.gender || "Não informado",
            address: {},
          },
        });
      }

      // 3. Determinar preço baseado no nível de urgência
      const urgencyConfig = ON_DUTY_CONFIG[input.urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG];
      const amount = urgencyConfig.price;

      // 4. Mapear nível de urgência para o formato do banco
      const urgencyMap = {
        'high': 'HIGH',
        'medium': 'MEDIUM',
        'low': 'LOW'
      };

      // 5. Criar o appointment de plantão
      const appointment = await db.appointment.create({
        data: {
          doctorId: "cm8rcb9lg0002zfdm99qvl7fp", // ID do médico padrão para plantão
          patientId: patient.id,
          scheduledAt: new Date(), // Agendado para agora
          duration: urgencyConfig.duration,
          status: "SCHEDULED",
          paymentStatus: input.paymentData.paymentId ? "PAID" : "PENDING",
          consultType: "CHAT",
          amount: amount,
          appointmentType: "TELEMEDICINE",
          symptoms: input.symptoms, // Use only the actual symptoms, not the raw database prefix
          paymentId: input.paymentData.paymentId,
          // Campos específicos para plantão
          isOnDuty: true,
          urgencyLevel: urgencyMap[input.urgencyLevel] as any, // HIGH, MEDIUM, LOW
        },
        include: {
          patient: {
            include: {
              user: true,
            },
          },
          doctor: {
            include: {
              user: true,
            },
          },
        },
      });

      // 6. Criar notificação para médicos disponíveis
      // TODO: Implementar sistema de notificação para médicos

      return {
        success: true,
        appointment,
        queuePosition: 1, // Será calculado dinamicamente
        estimatedWaitTime: urgencyConfig.waitTime,
      };
    } catch (error) {
      console.error("[CREATE_ON_DUTY_APPOINTMENT_ERROR]", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao criar consulta de plantão",
      });
    }
  });
