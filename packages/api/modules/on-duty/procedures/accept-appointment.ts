import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { db } from "database";

// Function to notify patient via WhatsApp when a doctor accepts their appointment
async function notifyPatient(appointmentId: string) {
  try {
    // Dynamic import to avoid circular dependencies
    const { sendPlantaoAcceptedNotification } = await import("../../../../../apps/web/actions/appointments/send-plantao-notifications");
    return await sendPlantaoAcceptedNotification(appointmentId);
  } catch (error) {
    console.error("[ACCEPT_APPOINTMENT] Error notifying patient:", error);
    return { success: false, error };
  }
}

export const acceptAppointment = protectedProcedure
  .input(
    z.object({
      appointmentId: z.string(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar se o usuário é um médico
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
        include: {
          user: {
            select: {
              name: true,
              phone: true,
            },
          },
          specialties: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Apenas médicos podem aceitar atendimentos",
        });
      }

      // Buscar o appointment
      const appointment = await db.appointment.findUnique({
        where: { id: input.appointmentId },
        include: {
          patient: {
            include: {
              user: true,
            },
          },
        },
      });

      if (!appointment) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Consulta não encontrada",
        });
      }

      if (appointment.status !== "SCHEDULED") {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Esta consulta não está disponível para aceitar",
        });
      }

      if (appointment.paymentStatus !== "PAID") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Pagamento não confirmado para esta consulta",
        });
      }

      // Atualizar o appointment para IN_PROGRESS e associar ao médico
      const updatedAppointment = await db.appointment.update({
        where: { id: input.appointmentId },
        data: {
          status: "IN_PROGRESS",
          doctorId: doctor.id, // Associar ao médico que aceitou
          scheduledAt: new Date(), // Agendar para agora
          // Campos específicos para plantão aceito
          acceptedAt: new Date(),
          acceptedByDoctorId: doctor.id,
        },
        include: {
          patient: {
            include: {
              user: true,
            },
          },
          doctor: {
            include: {
              user: true,
            },
          },
        },
      });

      // Criar notificação para o paciente
      await db.notification.create({
        data: {
          userId: appointment.patient.userId,
          appointmentId: appointment.id,
          type: "APPOINTMENT_CREATED",
          title: "Médico aceitou seu atendimento",
          message: `O Dr. ${doctor.user.name || 'Médico'} aceitou seu atendimento de plantão. A consulta será iniciada em breve.`,
        },
      });

      // Enviar Mensagem 2 aprimorada (médico aceito) para o paciente
      try {
        console.log("[ACCEPT_APPOINTMENT] Enviando Mensagem 2 aprimorada para paciente");

        // Import dinâmico para evitar ciclos
        const { sendPlantaoAcceptedImprovedMessage } = await import(
          "../../../../../apps/web/lib/plantao-whatsapp-messages"
        );

        if (updatedAppointment.patient.user.phone) {
          // Buscar especialidades do médico
          const doctorSpecialty = doctor.specialties?.map((s: { name: string }) => s.name).join(', ') || undefined;

          const whatsappResult = await sendPlantaoAcceptedImprovedMessage({
            appointmentId: updatedAppointment.id,
            patientName: updatedAppointment.patient.user.name || "Paciente",
            patientPhone: updatedAppointment.patient.user.phone,
            doctorName: doctor.user.name || "Médico",
            doctorSpecialty
          });

          console.log("[ACCEPT_APPOINTMENT] Mensagem 2 enviada:", {
            success: whatsappResult.success,
            messagesTotal: whatsappResult.messagesTotal,
            messagesSuccess: whatsappResult.messagesSuccess
          });
        } else {
          console.warn("[ACCEPT_APPOINTMENT] Paciente sem telefone, pulando WhatsApp");
        }

        // Também enviar notificações por email como backup
        const { sendAppointmentNotifications } = await import(
          "../../../../../apps/web/actions/checkout/notifications/send-notifications"
        );

                  // Disparar envio de email em background
        void sendAppointmentNotifications(
          {
            id: updatedAppointment.id,
            scheduledAt: updatedAppointment.scheduledAt,
            type: (updatedAppointment as any)?.appointmentType,
          },
          {
            id: updatedAppointment.patient.id,
            user: {
              id: updatedAppointment.patient.user.id,
              name: updatedAppointment.patient.user.name || "Paciente",
              email: updatedAppointment.patient.user.email || "",
              phone: updatedAppointment.patient.user.phone || undefined,
            },
            isNewUser: false,
          },
          {
            id: doctor.id,
            user: {
              id: doctor.userId,
              name: doctor.user.name || "Médico",
              email: updatedAppointment.doctor?.user?.email || user.email || "",
              phone: updatedAppointment.doctor?.user?.phone || undefined,
            },
          },
          { sendEmail: true, sendWhatsApp: false, useDirectLinks: true } // WhatsApp já enviado acima
        );

        console.log("[ACCEPT_APPOINTMENT] Notificações disparadas com sucesso", {
          appointmentId: updatedAppointment.id,
        });
      } catch (notificationError) {
        console.error("[ACCEPT_APPOINTMENT] Erro ao enviar notificações:", notificationError);
        // Não falhar a operação por erro de notificação
      }

      return updatedAppointment;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error("[ACCEPT_APPOINTMENT_ERROR]", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao aceitar atendimento",
      });
    }
  });
