import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { db } from "database";

export const getDoctorQueue = protectedProcedure
  .input(
    z.object({
      status: z.enum(["SCHEDULED", "IN_PROGRESS"]).optional(),
    })
  )
  .query(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar se o usuário é um médico
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
      });

      if (!doctor) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Apenas médicos podem acessar esta fila",
        });
      }

      // Buscar appointments que foram aceitos por este médico específico
      const where: any = {
        paymentStatus: "PAID",
        appointmentType: "TELEMEDICINE",
        isOnDuty: true,
        acceptedByDoctorId: doctor.id, // Apenas pacientes aceitos por este médico
      };

      if (input.status) {
        where.status = input.status;
      } else {
        where.status = { in: ["SCHEDULED", "IN_PROGRESS"] };
      }

      const appointments = await db.appointment.findMany({
        where,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
          acceptedByDoctor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
            },
          },
        },
        orderBy: [
          // Ordenar por urgência primeiro
          {
            urgencyLevel: "desc", // HIGH, MEDIUM, LOW
          },
          // Depois por data de aceitação (mais antigos primeiro)
          {
            acceptedAt: "asc",
          },
          // Por último, por data de criação
          {
            createdAt: "asc",
          },
        ],
      });

      // Mapear os dados para incluir informações calculadas
      const mappedAppointments = appointments.map((appointment, index) => {
        // Calcular tempo de espera desde a aceitação
        const acceptedAt = appointment.acceptedAt;
        const waitTimeMinutes = acceptedAt
          ? Math.floor((Date.now() - new Date(acceptedAt).getTime()) / (1000 * 60))
          : 0;

        // Mapear nível de urgência para formato legível
        const urgencyLevel = appointment.urgencyLevel || "LOW";

        return {
          id: appointment.id,
          doctorId: appointment.doctorId,
          patientId: appointment.patientId,
          scheduledAt: appointment.scheduledAt,
          consultType: appointment.consultType,
          duration: appointment.duration,
          status: appointment.status,
          appointmentType: appointment.appointmentType,
          amount: appointment.amount,
          paymentStatus: appointment.paymentStatus,
          symptoms: appointment.symptoms,
          createdAt: appointment.createdAt,
          updatedAt: appointment.updatedAt,
          patient: appointment.patient,
          doctor: appointment.doctor,
          queuePosition: index + 1,
          waitTimeMinutes,
          urgencyLevel,
          // Campos específicos para plantão
          isOnDuty: appointment.isOnDuty,
          acceptedAt: appointment.acceptedAt,
          acceptedByDoctorId: appointment.acceptedByDoctorId,
          acceptedByDoctor: appointment.acceptedByDoctor,
        };
      });

      return mappedAppointments;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error("[GET_DOCTOR_QUEUE_ERROR]", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar fila do médico",
      });
    }
  });
