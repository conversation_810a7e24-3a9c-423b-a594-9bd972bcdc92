// packages/api/modules/patients/procedures/create.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { sendEmail } from "mail";
import { generateVerificationToken } from "auth/lib/tokens";

// Schema para validar os dados de entrada
const PatientInputSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().optional(),
  cpf: z.string().min(11, "CPF deve ter pelo menos 11 caracteres").max(14, "CPF inválido"),
  birthDate: z.string().or(z.date()),
  gender: z.enum(["M", "F", "O", "N"]),
  bloodType: z.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", ""]).optional(),
  allergies: z.string().optional(),
  chronicConditions: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    number: z.string().optional(),
    complement: z.string().optional(),
    neighborhood: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
  }).optional(),
  hospitalId: z.string().optional(),
  doctorId: z.string().optional(),
});

// Schema para padronizar a saída
const PatientOutputSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    phone: z.string().nullable(),
  }),
  cpf: z.string(),
  birthDate: z.date(),
  gender: z.string(),
  bloodType: z.string().nullable(),
  allergies: z.array(z.string()),
  chronicConditions: z.array(z.string()),
  address: z.record(z.any()).nullable(),
});

export const create = protectedProcedure
  .input(PatientInputSchema)
  .output(PatientOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      console.log("[Patient Create] Starting patient creation with input:", {
        ...input,
        email: input.email?.toLowerCase() // log sanitized email
      });

      const { user } = ctx;

      // Verificar permissões - apenas admin, hospital ou médico podem criar pacientes
      if (!["ADMIN", "HOSPITAL", "DOCTOR", "SECRETARY"].includes(user?.role || "")) {
        console.log("[Patient Create] Permission denied for role:", user?.role);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para criar pacientes",
        });
      }

      // Verificar se já existe usuário com este email
      const existingUser = await db.user.findUnique({
        where: { email: input.email.toLowerCase() },
      });

      console.log("[Patient Create] Existing user check:", {
        exists: !!existingUser,
        userId: existingUser?.id
      });

      // Verificar se já existe paciente com este CPF
      const existingPatient = await db.patient.findFirst({
        where: { cpf: input.cpf.replace(/\D/g, "") }, // Remove caracteres não numéricos
      });

      if (existingPatient) {
        console.log("[Patient Create] CPF already exists:", input.cpf);
        throw new TRPCError({
          code: "CONFLICT",
          message: "CPF já cadastrado no sistema",
        });
      }

      // Formatando o CPF para guardar apenas números
      const formattedCpf = input.cpf.replace(/\D/g, "");

      // Criando o usuário se não existir
      let isNewUser = false;
      let userId = existingUser?.id;

      // Determinar a qual Time (team) o paciente estará associado
      let teamId: string | null = null;

      // Se tiver hospitalId, buscar o time associado ao hospital
      if (input.hospitalId) {
        const hospital = await db.hospital.findUnique({
          where: { id: input.hospitalId },
          select: { teamId: true }
        });

        if (hospital) {
          teamId = hospital.teamId;
          console.log(`[Patient Create] Found hospital team: ${teamId}`);
        } else {
          console.log(`[Patient Create] Hospital not found for ID: ${input.hospitalId}`);
        }
      }

      // Se não tiver hospital, usar o time do médico (se disponível)
      if (!teamId && input.doctorId) {
        const doctor = await db.doctor.findUnique({
          where: { id: input.doctorId },
          include: {
            hospitals: {
              take: 1,
              include: {
                hospital: true
              }
            }
          }
        });

        if (doctor?.hospitals?.[0]?.hospital?.teamId) {
          teamId = doctor.hospitals[0].hospital.teamId;
          console.log(`[Patient Create] Using doctor's hospital team: ${teamId}`);
        }
      }

      // Caso não tenha time, buscar um time padrão para pacientes, ou o primeiro time disponível
      if (!teamId) {
        // Buscar time padrão ou algum time existente
        const defaultTeam = await db.team.findFirst({
          where: {
            OR: [
              { teamType: "HOSPITAL" },
              { teamType: null }
            ]
          },
          orderBy: { id: 'asc' }
        });

        if (defaultTeam) {
          teamId = defaultTeam.id;
          console.log(`[Patient Create] Using default team: ${teamId}`);
        } else {
          console.log(`[Patient Create] No team found. Creating a default team.`);
          // Criar um time padrão se não existir nenhum
          const newTeam = await db.team.create({
            data: {
              name: "Pacientes",
              teamType: "HOSPITAL"
            }
          });
          teamId = newTeam.id;
          console.log(`[Patient Create] Created default team with ID: ${teamId}`);
        }
      }

      // Se não existir usuário, criar um novo
      if (!userId) {
        console.log(`[Patient Create] Creating new user with team: ${teamId}`);
        isNewUser = true;

        // Criar usuário em transação para garantir que também tenha associação ao time
        const result = await db.$transaction(async (tx) => {
          // Criar o usuário
          const newUser = await tx.user.create({
            data: {
              name: input.name,
              email: input.email.toLowerCase(),
              phone: input.phone,
              role: "PATIENT",
              onboardingComplete: true,
              emailVerified: true,
            },
          });

          // Criar a associação com o time
          if (teamId) {
            await tx.teamMembership.create({
              data: {
                userId: newUser.id,
                teamId: teamId,
                role: "MEMBER",
              }
            });
            console.log(`[Patient Create] User associated with team ${teamId}`);
          }

          return newUser;
        });

        userId = result.id;
        console.log("[Patient Create] Created new user with ID:", userId);
      } else {
        // Se o usuário já existe, verificar se já tem associação com o time
        const existingMembership = await db.teamMembership.findFirst({
          where: {
            userId: userId,
            teamId: teamId || undefined
          }
        });

        // Associar ao time se ainda não estiver associado e temos um time definido
        if (!existingMembership && teamId) {
          await db.teamMembership.create({
            data: {
              userId: userId,
              teamId: teamId,
              role: "MEMBER",
            }
          });
          console.log(`[Patient Create] Existing user associated with team ${teamId}`);
        }
      }

      // Converter string de data para objeto Date
      const birthDate = typeof input.birthDate === 'string'
        ? new Date(input.birthDate)
        : input.birthDate;

      // Converter allergies e chronicConditions para arrays
      const allergies = input.allergies
        ? input.allergies.split(',').map(item => item.trim()).filter(Boolean)
        : [];

      const chronicConditions = input.chronicConditions
        ? input.chronicConditions.split(',').map(item => item.trim()).filter(Boolean)
        : [];

      console.log("[Patient Create] Processed data:", {
        userId,
        birthDate,
        bloodType: input.bloodType || null,
        allergyCount: allergies.length,
        conditionsCount: chronicConditions.length,
        hasAddress: !!input.address
      });

      try {
        // Criar o paciente
        const patient = await db.patient.create({
          data: {
            userId,
            cpf: formattedCpf,
            birthDate,
            gender: input.gender,
            bloodType: input.bloodType || null,
            allergies,
            chronicConditions,
            address: input.address || {}, // Sempre enviar um objeto, mesmo que vazio
          },
          include: {
            user: true,
          },
        });

        console.log("[Patient Create] Successfully created patient:", {
          patientId: patient.id,
          userId: patient.userId
        });

        // Enviar email de boas-vindas para novos usuários
        if (isNewUser) {
          try {
            // Gerar token de verificação para o novo usuário
            const verificationToken = await generateVerificationToken({ userId: userId });

            console.log(`[Patient Create] Sending welcome email to ${input.email} with token ${verificationToken}`);

            // Enviar email de boas-vindas com link de verificação
            await sendEmail({
              to: input.email.toLowerCase(),
              templateId: "newPatient",
              context: {
                name: input.name,
                url: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/verify?token=${verificationToken}`,
                otp: "",
              },
              locale: "pt",
            });

            console.log(`[Patient Create] Welcome email sent to ${input.email}`);
          } catch (emailError) {
            // Apenas logar o erro, não interromper o fluxo
            console.error("[Patient Create] Error sending welcome email:", emailError);
          }
        }

        // Corrigir os tipos para o retorno
        return {
          id: patient.id,
          user: {
            id: patient.user.id,
            name: patient.user.name,
            email: patient.user.email,
            phone: patient.user.phone,
          },
          cpf: patient.cpf,
          birthDate: new Date(patient.birthDate),
          gender: patient.gender,
          bloodType: patient.bloodType,
          allergies: Array.isArray(patient.allergies) ? patient.allergies : [],
          chronicConditions: Array.isArray(patient.chronicConditions) ? patient.chronicConditions : [],
          address: patient.address as Record<string, any> | null,
        };
      } catch (dbError) {
        console.error("[Patient Create] Database error:", dbError);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao criar o registro do paciente no banco de dados",
          cause: dbError,
        });
      }
    } catch (error) {
      console.error("[Patient Create] Error creating patient:", error);

      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao criar paciente",
        cause: error,
      });
    }
  });
