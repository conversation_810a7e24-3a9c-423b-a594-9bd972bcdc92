// packages/api/modules/patients/procedures/list.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada
const ListPatientsInputSchema = z.object({
	searchTerm: z.string().optional(),
	hospitalId: z.string().optional().nullable(),
	doctorId: z.string().optional().nullable(),
	page: z.number().min(1).default(1),
	perPage: z.number().min(1).max(100).default(20),
});

// Schema para padronizar a saída
const PatientOutputSchema = z.object({
	id: z.string(),
	user: z.object({
		id: z.string(),
		name: z.string().nullable(),
		email: z.string(),
		phone: z.string().nullable(),
		avatarUrl: z.string().nullable(),
	}),
	cpf: z.string(),
	birthDate: z.date(),
	gender: z.string(),
	address: z.record(z.any()).nullable().optional(),
	appointments: z.array(
		z.object({
			id: z.string(),
		}),
	),
});

const PaginationOutputSchema = z.object({
	patients: z.array(PatientOutputSchema),
	pagination: z.object({
		total: z.number(),
		pages: z.number(),
		page: z.number(),
		perPage: z.number(),
	}),
});

// Procedure para médicos e secretários
export const list = protectedProcedure
	.input(ListPatientsInputSchema)
	.output(PaginationOutputSchema)
	.query(async ({ input, ctx }) => {
		try {
			const { user } = ctx;

			// Verificar permissões - apenas doctor ou secretary podem usar esta procedure
			// if (!["DOCTOR", "SECRETARY", "HOSPITAL"].includes(user?.role || "")) {
			//   throw new TRPCError({
			//     code: "FORBIDDEN",
			//     message: "Sem permissão para listar pacientes. Use listAll para administradores.",
			//   });
			// }

			// Construir query base
			const searchTerm = input.searchTerm?.trim().toLowerCase() || "";

			// Filtros iniciais
			const whereClause: any = {};

			// Se busca especificada, adicionar aos filtros
			if (searchTerm) {
				whereClause.OR = [
					{
						user: {
							name: {
								contains: searchTerm,
								mode: "insensitive",
							},
						},
					},
					{
						user: {
							email: {
								contains: searchTerm,
								mode: "insensitive",
							},
						},
					},
					{
						cpf: {
							contains: searchTerm,
						},
					},
				];
			}

			// DOCTOR: filtra pelos pacientes do médico
			if (user?.role === "DOCTOR") {
				// Se doctorId foi fornecido diretamente no input
				if (input.doctorId) {
					whereClause.appointments = {
						some: {
							doctorId: input.doctorId,
							...(input.hospitalId ? { hospitalId: input.hospitalId } : {}),
						},
					};
				} else {
					// Tenta buscar o doctorId do usuário atual
					const doctor = await db.doctor.findFirst({
						where: { userId: user.id },
						select: { id: true, hospitals: { select: { hospitalId: true } } },
					});

					if (doctor) {
						// Usar hospitalId do input ou do perfil do médico
						const hospitalFilter = input.hospitalId
							? { hospitalId: input.hospitalId }
							: doctor.hospitals && doctor.hospitals.length > 0
								? { hospitalId: doctor.hospitals[0].hospitalId }
								: {};

						whereClause.appointments = {
							some: {
								doctorId: doctor.id,
								...hospitalFilter,
							},
						};
					} else {
						// Se o médico não tem perfil completo, retorna lista vazia
						return {
							patients: [],
							pagination: {
								total: 0,
								pages: 0,
								page: input.page,
								perPage: input.perPage,
							},
						};
					}
				}
			}
			// SECRETARY ou HOSPITAL: filtra pelos pacientes do hospital
			else if (user?.role === "SECRETARY" || user?.role === "HOSPITAL") {
				// Se hospitalId foi fornecido no input
				if (input.hospitalId) {
					whereClause.appointments = {
						some: {
							hospitalId: input.hospitalId,
							...(input.doctorId ? { doctorId: input.doctorId } : {}),
						},
					};
				} else {
					// Tenta buscar o hospitalId do usuário atual
					const teamMembership = ctx.teamMemberships?.[0];

					if (!teamMembership) {
						return {
							patients: [],
							pagination: {
								total: 0,
								pages: 0,
								page: input.page,
								perPage: input.perPage,
							},
						};
					}

					const hospital = await db.hospital.findFirst({
						where: {
							teamId: teamMembership.teamId,
						},
						select: { id: true },
					});

					if (hospital) {
						whereClause.appointments = {
							some: {
								hospitalId: hospital.id,
								...(input.doctorId ? { doctorId: input.doctorId } : {}),
							},
						};
					} else {
						// Se o usuário não está vinculado a nenhum hospital, retorna lista vazia
						return {
							patients: [],
							pagination: {
								total: 0,
								pages: 0,
								page: input.page,
								perPage: input.perPage,
							},
						};
					}
				}
			}

			// Log para debug
			console.log(
				"Filtro final para busca de pacientes:",
				JSON.stringify(whereClause, null, 2),
			);

			// Calcular paginação
			const skip = (input.page - 1) * input.perPage;
			const take = input.perPage;

			// Executar query para contar total
			const total = await db.patient.count({
				where: whereClause,
			});

			// Executar query para buscar pacientes
			const patients = await db.patient.findMany({
				where: whereClause,
				skip,
				take,
				orderBy: {
					user: {
						name: "asc",
					},
				},
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
							phone: true,
							avatarUrl: true,
						},
					},
					appointments: {
						select: {
							id: true,
						},
					},
				},
			});

			// Preparar pacientes para saída segura
			const safePatients = patients.map((patient) => ({
				...patient,
				// Garantir que dados incompatíveis sejam tratados
				address: patient.address || null,
				birthDate: new Date(patient.birthDate),
			}));

			// Calcular total de páginas
			const pages = Math.ceil(total / input.perPage);

			return {
				patients: safePatients,
				pagination: {
					total,
					pages,
					page: input.page,
					perPage: input.perPage,
				},
			};
		} catch (error) {
			console.error("Erro ao listar pacientes:", error);
			if (error instanceof TRPCError) throw error;

			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Erro ao listar pacientes",
				cause: error,
			});
		}
	});
