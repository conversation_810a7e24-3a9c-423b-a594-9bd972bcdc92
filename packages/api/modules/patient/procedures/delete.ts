// packages/api/modules/patients/procedures/delete.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada
const PatientDeleteInputSchema = z.object({
  id: z.string().min(1, "ID do paciente é obrigatório"),
});

// Schema para padronizar a saída
const PatientDeleteOutputSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

export const delete_ = protectedProcedure
  .input(PatientDeleteInputSchema)
  .output(PatientDeleteOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      console.log("[Patient Delete] Starting deletion process for patient ID:", input.id);
      const { user } = ctx;
      console.log("[Patient Delete] User attempting deletion:", {
        userId: user?.id,
        userRole: user?.role,
        userEmail: user?.email
      });

      // Verificar permissões - permitir roles específicos para excluir pacientes
      if (!["ADMIN", "HOSPITAL", "DOCTOR", "SECRETARY"].includes(user?.role || "")) {
        console.log("[Patient Delete] Permission denied for role:", user?.role);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para excluir pacientes",
        });
      }
      console.log("[Patient Delete] Permission check passed");

      // Verificar se paciente existe
      console.log("[Patient Delete] Fetching patient data");
      let patient;
      try {
        patient = await db.patient.findUnique({
          where: { id: input.id },
          include: {
            appointments: true,
            medicalDocuments: true,
            user: true,
          },
        });
      } catch (fetchError) {
        console.error("[Patient Delete] Error fetching patient:", fetchError);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao buscar dados do paciente",
          cause: fetchError,
        });
      }

      if (!patient) {
        console.log("[Patient Delete] Patient not found with ID:", input.id);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Paciente não encontrado",
        });
      }
      console.log("[Patient Delete] Found patient:", {
        patientId: patient.id,
        userId: patient.userId,
        patientName: patient.user?.name,
        appointmentsCount: patient.appointments.length,
        documentsCount: patient.medicalDocuments.length
      });

      // Verificar se há consultas em andamento ou agendadas
      const activeAppointments = patient.appointments.filter(appt =>
        ["SCHEDULED", "IN_PROGRESS"].includes(appt.status)
      );
      console.log("[Patient Delete] Active appointments count:", activeAppointments.length);

      if (activeAppointments.length > 0) {
        console.log("[Patient Delete] Cannot delete patient with active appointments");
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Não é possível excluir paciente com consultas ativas ou agendadas",
        });
      }

      // Remover relacionamentos em transação
      console.log("[Patient Delete] Starting database transaction");
      try {
        // Usar transações menores e independentes para reduzir chance de deadlocks

        // 1. Anonimizar o usuário - faça isso primeiro para evitar problemas com chaves estrangeiras
        const userId = patient.userId;
        console.log("[Patient Delete] Anonymizing user data");
        await db.user.update({
          where: { id: userId },
          data: {
            name: "Usuário removido",
            email: `deleted-${Date.now()}-${userId}@example.com`, // Adicionar timestamp para garantir unicidade
            avatarUrl: null,
            phone: null,
          },
        });
        console.log("[Patient Delete] User anonymized successfully");

        // 2. Remover documentos médicos
        if (patient.medicalDocuments.length > 0) {
          console.log("[Patient Delete] Deleting medical documents");
          await db.medicalDocument.deleteMany({
            where: { patientId: input.id },
          });
          console.log("[Patient Delete] Medical documents deleted");
        }

        // 3. Processar avaliações médicas
        const evaluations = await db.doctorEvaluation.findMany({
          where: { patientId: input.id },
          select: { id: true }
        });

        if (evaluations.length > 0) {
          console.log("[Patient Delete] Deleting evaluations");
          await db.doctorEvaluation.deleteMany({
            where: { patientId: input.id }
          });
          console.log("[Patient Delete] Evaluations deleted");
        }

        // 4. Processar prescrições relacionadas a este paciente
        const prescriptions = await db.prescription.findMany({
          where: {
            appointment: {
              patientId: input.id
            }
          },
          select: { id: true }
        });

        if (prescriptions.length > 0) {
          console.log("[Patient Delete] Deleting prescriptions");
          await db.prescription.deleteMany({
            where: {
              id: {
                in: prescriptions.map(p => p.id)
              }
            }
          });
          console.log("[Patient Delete] Prescriptions deleted");
        }

        // 5. Atualizar timeSlots associados
        console.log("[Patient Delete] Updating time slots");
        await db.timeSlot.updateMany({
          where: {
            appointment: {
              patientId: input.id
            }
          },
          data: {
            appointmentId: null,
            isAvailable: true
          }
        });
        console.log("[Patient Delete] Time slots updated");

        // 6. Remover notificações e mensagens
        console.log("[Patient Delete] Deleting notifications and messages");
        await db.notification.deleteMany({
          where: {
            appointment: {
              patientId: input.id
            }
          }
        });

        await db.message.deleteMany({
          where: {
            appointment: {
              patientId: input.id
            }
          }
        });

        await db.attachment.deleteMany({
          where: {
            appointment: {
              patientId: input.id
            }
          }
        });
        console.log("[Patient Delete] Notifications, messages and attachments deleted");

        // 7. Remover transações e faturas
        const transactions = await db.transaction.findMany({
          where: {
            appointment: {
              patientId: input.id
            }
          },
          select: { id: true }
        });

        if (transactions.length > 0) {
          console.log("[Patient Delete] Deleting invoices");
          await db.invoice.deleteMany({
            where: {
              transactionId: {
                in: transactions.map(t => t.id)
              }
            }
          });

          console.log("[Patient Delete] Deleting transactions");
          await db.transaction.deleteMany({
            where: {
              id: {
                in: transactions.map(t => t.id)
              }
            }
          });
        }

        // 8. Processar consultas
        console.log("[Patient Delete] Processing appointments");
        await db.appointment.deleteMany({
          where: { patientId: input.id }
        });
        console.log("[Patient Delete] Appointments deleted");

        // 9. Finalmente, remover o paciente
        console.log("[Patient Delete] Deleting patient record");
        await db.patient.delete({
          where: { id: input.id },
        });
        console.log("[Patient Delete] Patient record deleted successfully");

      } catch (txError: any) {
        console.error("[Patient Delete] Error:", txError);
        console.error("[Patient Delete] Error code:", txError.code);
        console.error("[Patient Delete] Error message:", txError.message);

        // Erro de registro não encontrado
        if (txError.code === 'P2025') {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Um ou mais registros associados não foram encontrados",
          });
        }

        // Erro de restrição de chave estrangeira
        if (txError.code === 'P2003') {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Não é possível excluir o paciente devido a registros relacionados",
          });
        }

        // Erro de timeout da transação
        if (txError.code === 'P2024') {
          throw new TRPCError({
            code: "TIMEOUT",
            message: "Tempo esgotado ao tentar excluir o paciente. Tente novamente.",
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao excluir paciente: " + txError.message,
        });
      }

      console.log("[Patient Delete] Patient deletion completed successfully");
      return { success: true, message: "Paciente excluído com sucesso" };
    } catch (error) {
      console.error("[Patient Delete] Error details:", error);

      // Se já for um TRPCError, apenas repasse
      if (error instanceof TRPCError) throw error;

      // Caso contrário, crie um novo TRPCError com detalhes
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao excluir paciente",
        cause: error,
      });
    }
  });
