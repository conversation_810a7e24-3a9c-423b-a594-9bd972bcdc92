// packages/api/modules/patients/procedures/update.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada
const PatientUpdateInputSchema = z.object({
  id: z.string().min(1, "ID do paciente é obrigatório"),
  name: z.string().min(1, "Nome é obrigatório").optional(),
  phone: z.string().optional(),
  birthDate: z.string().or(z.date()).optional(),
  gender: z.enum(["M", "F", "O", "N"]).optional(),
  bloodType: z.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", ""]).optional(),
  allergies: z.string().optional(),
  chronicConditions: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    number: z.string().optional(),
    complement: z.string().optional(),
    neighborhood: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
  }).optional(),
});

// Schema para padronizar a saída
const PatientOutputSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    phone: z.string().nullable(),
  }),
  cpf: z.string(),
  birthDate: z.date(),
  gender: z.string(),
  bloodType: z.string().nullable(),
  allergies: z.array(z.string()),
  chronicConditions: z.array(z.string()),
  address: z.record(z.any()).nullable(),
});

export const update = protectedProcedure
  .input(PatientUpdateInputSchema)
  .output(PatientOutputSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      console.log("[Patient Update] Starting patient update with input:", input);

      const { user } = ctx;

      // Verificar permissões - apenas admin, hospital, médico ou o próprio paciente podem atualizar
      if (!["ADMIN", "HOSPITAL", "DOCTOR", "SECRETARY", "PATIENT"].includes(user?.role || "")) {
        console.log("[Patient Update] Permission denied for role:", user?.role);
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para atualizar paciente",
        });
      }

      // Buscar paciente existente
      const existingPatient = await db.patient.findUnique({
        where: { id: input.id },
        include: {
          user: true,
        },
      });

      if (!existingPatient) {
        console.log("[Patient Update] Patient not found with ID:", input.id);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Paciente não encontrado",
        });
      }

      console.log("[Patient Update] Found existing patient:", {
        patientId: existingPatient.id,
        userId: existingPatient.userId
      });

      // Se for paciente, só pode atualizar os próprios dados
      if (user?.role === "PATIENT" && existingPatient.userId !== user.id) {
        console.log("[Patient Update] Patient trying to update another patient:", {
          requestingUserId: user.id,
          patientUserId: existingPatient.userId
        });
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para atualizar este paciente",
        });
      }

      // Se for médico, verificar se o paciente já teve consulta com este médico
      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: { userId: user.id },
        });

        if (!doctor) {
          console.log("[Patient Update] Doctor profile not found for user:", user.id);
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Perfil de médico não encontrado",
          });
        }

        // Verificar se existe algum agendamento entre o médico e o paciente
        const hasAppointmentWithPatient = await db.appointment.findFirst({
          where: {
            doctorId: doctor.id,
            patientId: input.id,
          },
        });

        if (!hasAppointmentWithPatient) {
          console.log("[Patient Update] Doctor not authorized to update patient:", {
            doctorId: doctor.id,
            patientId: input.id
          });
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Sem permissão para atualizar este paciente",
          });
        }
      }

      // Preparar dados para atualização
      const updateUserData: any = {};
      const updatePatientData: any = {};

      // Atualizar nome do usuário se fornecido
      if (input.name) {
        updateUserData.name = input.name;
      }

      // Atualizar telefone do usuário se fornecido
      if (input.phone !== undefined) {
        updateUserData.phone = input.phone;
      }

      // Atualizar data de nascimento se fornecida
      if (input.birthDate) {
        updatePatientData.birthDate = typeof input.birthDate === 'string'
          ? new Date(input.birthDate)
          : input.birthDate;
      }

      // Atualizar gênero se fornecido
      if (input.gender) {
        updatePatientData.gender = input.gender;
      }

      // Atualizar tipo sanguíneo se fornecido
      if (input.bloodType) {
        updatePatientData.bloodType = input.bloodType;
      }

      // Atualizar alergias se fornecidas
      if (input.allergies !== undefined) {
        updatePatientData.allergies = input.allergies
          ? input.allergies.split(',').map(item => item.trim()).filter(Boolean)
          : [];
      }

      // Atualizar condições crônicas se fornecidas
      if (input.chronicConditions !== undefined) {
        updatePatientData.chronicConditions = input.chronicConditions
          ? input.chronicConditions.split(',').map(item => item.trim()).filter(Boolean)
          : [];
      }

      // Atualizar endereço se fornecido
      if (input.address) {
        // Garantir que o endereço atual seja um objeto válido
        const currentAddress = typeof existingPatient.address === 'object' && existingPatient.address !== null
          ? existingPatient.address
          : {};

        // Mesclar com endereço existente
        updatePatientData.address = {
          ...currentAddress,
          ...input.address,
        };
      }

      console.log("[Patient Update] Prepared update data:", {
        userUpdates: Object.keys(updateUserData),
        patientUpdates: Object.keys(updatePatientData)
      });

      try {
        // Realizar atualizações em transação
        const transaction = await db.$transaction(async (tx) => {
          // Atualizar dados do usuário se houver alterações
          if (Object.keys(updateUserData).length > 0) {
            await tx.user.update({
              where: { id: existingPatient.userId },
              data: updateUserData,
            });
            console.log("[Patient Update] Updated user data for userId:", existingPatient.userId);
          }

          // Atualizar dados do paciente se houver alterações
          if (Object.keys(updatePatientData).length > 0) {
            const updatedPatient = await tx.patient.update({
              where: { id: input.id },
              data: updatePatientData,
              include: {
                user: true,
              },
            });
            console.log("[Patient Update] Updated patient data for patientId:", input.id);
            return updatedPatient;
          }

          // Se não houver atualizações, retornar o paciente existente
          return existingPatient;
        });

        const updatedPatient = transaction;
        console.log("[Patient Update] Transaction completed successfully");

        // Garantir que o retorno está no formato correto
        return {
          id: updatedPatient.id,
          user: {
            id: updatedPatient.user.id,
            name: updatedPatient.user.name,
            email: updatedPatient.user.email,
            phone: updatedPatient.user.phone,
          },
          cpf: updatedPatient.cpf,
          birthDate: new Date(updatedPatient.birthDate),
          gender: updatedPatient.gender,
          bloodType: updatedPatient.bloodType,
          allergies: Array.isArray(updatedPatient.allergies) ? updatedPatient.allergies : [],
          chronicConditions: Array.isArray(updatedPatient.chronicConditions) ? updatedPatient.chronicConditions : [],
          address: updatedPatient.address as Record<string, any> | null,
        };
      } catch (dbError) {
        console.error("[Patient Update] Database error:", dbError);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Erro ao atualizar o registro do paciente no banco de dados",
          cause: dbError,
        });
      }
    } catch (error) {
      console.error("[Patient Update] Error updating patient:", error);

      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao atualizar paciente",
        cause: error,
      });
    }
  });
