// packages/api/modules/patients/procedures/by-id.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada
const PatientByIdInputSchema = z.object({
  id: z.string().min(1, "ID do paciente é obrigatório"),
});

// Schema para padronizar a saída
const PatientOutputSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    phone: z.string().nullable(),
    avatarUrl: z.string().nullable(),
  }),
  cpf: z.string(),
  birthDate: z.date(),
  gender: z.string(),
  bloodType: z.string().nullable(),
  allergies: z.array(z.string()),
  chronicConditions: z.array(z.string()),
  address: z.record(z.any()).nullable(),
  appointments: z.array(z.object({
    id: z.string(),
    doctorId: z.string(),
    scheduledAt: z.date(),
    status: z.string(),
  })),
});

export const byId = protectedProcedure
  .input(PatientByIdInputSchema)
  .output(PatientOutputSchema)
  .query(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar permissões - apenas admin, hospital ou médico podem ver detalhes de pacientes
      if (!["ADMIN", "HOSPITAL", "DOCTOR", "SECRETARY", "PATIENT"].includes(user?.role || "")) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para acessar detalhes do paciente",
        });
      }

      // Se for paciente, só pode ver os próprios dados
      if (user?.role === "PATIENT") {
        const ownPatient = await db.patient.findFirst({
          where: { userId: user.id },
        });

        if (!ownPatient || ownPatient.id !== input.id) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Sem permissão para acessar detalhes deste paciente",
          });
        }
      }

      // Se for médico, verificar se o paciente é seu paciente
      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: { userId: user.id },
        });

        if (!doctor) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Perfil de médico não encontrado",
          });
        }

        const isPatientOfDoctor = await db.patientDoctor.findFirst({
          where: {
            doctorId: doctor.id,
            patientId: input.id,
          },
        });

        // Verificar se tem consultas com este paciente
        const hasAppointments = await db.appointment.findFirst({
          where: {
            doctorId: doctor.id,
            patientId: input.id,
          },
        });

        if (!isPatientOfDoctor && !hasAppointments) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Sem permissão para acessar detalhes deste paciente",
          });
        }
      }

      // Buscar paciente com informações relacionadas
      const patient = await db.patient.findUnique({
        where: { id: input.id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              avatarUrl: true,
            },
          },
          appointments: {
            select: {
              id: true,
              doctorId: true,
              scheduledAt: true,
              status: true,
            },
            orderBy: {
              scheduledAt: "desc",
            },
          },
        },
      });

      if (!patient) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Paciente não encontrado",
        });
      }

      return patient;
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar detalhes do paciente",
        cause: error,
      });
    }
  });
