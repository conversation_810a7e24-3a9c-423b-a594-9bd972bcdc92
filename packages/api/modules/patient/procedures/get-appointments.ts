// packages/api/modules/patients/procedures/get-appointments.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";
import { AppointmentStatus } from "@prisma/client";

// Schema para validar os dados de entrada
const GetAppointmentsInputSchema = z.object({
  patientId: z.string().min(1, "ID do paciente é obrigatório"),
  status: z.nativeEnum(AppointmentStatus).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(50).default(10),
});

export const getAppointments = protectedProcedure
  .input(GetAppointmentsInputSchema)
  .query(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar permissões
      if (!["ADMIN", "HOSPITAL", "DOCTOR", "SECRETARY", "PATIENT"].includes(user?.role || "")) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Sem permissão para visualizar consultas do paciente",
        });
      }

      // Se for paciente, verificar se está acessando suas próprias consultas
      if (user?.role === "PATIENT") {
        const ownPatient = await db.patient.findFirst({
          where: { userId: user.id },
        });

        if (!ownPatient || ownPatient.id !== input.patientId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Sem permissão para acessar consultas deste paciente",
          });
        }
      }

      // Se for médico, verificar se o paciente tem consultas com este médico
      if (user?.role === "DOCTOR") {
        const doctor = await db.doctor.findFirst({
          where: { userId: user.id },
        });

        if (!doctor) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Perfil de médico não encontrado",
          });
        }

        const hasAppointments = await db.appointment.findFirst({
          where: {
            doctorId: doctor.id,
            patientId: input.patientId,
          },
        });

        if (!hasAppointments) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Sem permissão para acessar consultas deste paciente",
          });
        }
      }

      // Construir filtros para a consulta
      const where: any = {
        patientId: input.patientId,
      };

      // Adicionar filtro por status se fornecido
      if (input.status) {
        where.status = input.status;
      }

      // Adicionar filtros de data se fornecidos
      if (input.startDate) {
        where.scheduledAt = {
          ...(where.scheduledAt || {}),
          gte: new Date(input.startDate),
        };
      }

      if (input.endDate) {
        where.scheduledAt = {
          ...(where.scheduledAt || {}),
          lte: new Date(input.endDate),
        };
      }

      // Calcular paginação
      const skip = (input.page - 1) * input.perPage;
      const take = input.perPage;

      // Contar total de consultas
      const total = await db.appointment.count({
        where,
      });

      // Buscar consultas com relações importantes
      const appointments = await db.appointment.findMany({
        where,
        skip,
        take,
        orderBy: {
          scheduledAt: "desc",
        },
        include: {
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                  avatarUrl: true,
                },
              },
              specialties: true,
            },
          },
          hospital: {
            select: {
              id: true,
              name: true,
              logoUrl: true,
            },
          },
          prescription: {
            select: {
              id: true,
              createdAt: true,
              status: true,
            },
          },
        },
      });

      // Calcular total de páginas
      const pages = Math.ceil(total / input.perPage);

      return {
        appointments,
        pagination: {
          total,
          pages,
          page: input.page,
          perPage: input.perPage,
        },
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao buscar consultas do paciente",
        cause: error,
      });
    }
  });
