// packages/api/modules/patients/procedures/listAll.ts
import { TRPCError } from "@trpc/server";
import { db } from "database";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

// Schema para validar os dados de entrada - apenas paginação e busca
const ListAllPatientsInputSchema = z.object({
  searchTerm: z.string().optional(),
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(100).default(20),
});

// Schema para padronizar a saída
const PatientOutputSchema = z.object({
  id: z.string(),
  user: z.object({
    id: z.string(),
    name: z.string().nullable(),
    email: z.string(),
    phone: z.string().nullable(),
    avatarUrl: z.string().nullable(),
  }),
  cpf: z.string(),
  birthDate: z.date(),
  gender: z.string(),
  address: z.record(z.any()).nullable().optional(),
  appointments: z.array(z.object({
    id: z.string(),
  })),
});

const PaginationOutputSchema = z.object({
  patients: z.array(PatientOutputSchema),
  pagination: z.object({
    total: z.number(),
    pages: z.number(),
    page: z.number(),
    perPage: z.number(),
  }),
});

// Usando protectedProcedure em vez de adminProcedure para garantir compatibilidade
export const listAll = protectedProcedure
  .input(ListAllPatientsInputSchema)
  .output(PaginationOutputSchema)
  .query(async ({ input, ctx }) => {
    try {
      const { user } = ctx;

      // Verificar se o usuário é admin
      if (user?.role !== "ADMIN") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Apenas administradores podem listar todos os pacientes",
        });
      }

      // Construir query base
      const searchTerm = input.searchTerm?.trim().toLowerCase() || "";

      // Filtros iniciais (apenas para busca, sem filtro de hospital ou médico)
      let whereClause: any = {};

      // Se busca especificada, adicionar aos filtros
      if (searchTerm) {
        whereClause.OR = [
          {
            user: {
              name: {
                contains: searchTerm,
                mode: "insensitive",
              },
            },
          },
          {
            user: {
              email: {
                contains: searchTerm,
                mode: "insensitive",
              },
            },
          },
          {
            cpf: {
              contains: searchTerm,
            },
          },
        ];
      }

      console.log("listAll: Iniciando busca de pacientes para admin");

      // Calcular paginação
      const skip = (input.page - 1) * input.perPage;
      const take = input.perPage;

      // Executar query para contar total
      const total = await db.patient.count({
        where: whereClause,
      });

      console.log(`listAll: Encontrados ${total} pacientes no total`);

      // Executar query para buscar pacientes
      const patients = await db.patient.findMany({
        where: {
          ...whereClause,
          // Garantir que o usuário existe
          user: {
            isNot: null
          }
        },
        skip,
        take,
        orderBy: {
          user: {
            name: "asc",
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              avatarUrl: true,
            },
          },
          appointments: {
            select: {
              id: true,
            },
          },
        },
      });

      console.log(`listAll: Retornando ${patients.length} pacientes após paginação`);

      // Preparar pacientes para saída segura
      const safePatients = patients.map(patient => {
        // Garantir que todos os campos são seguros para serialização
        return {
          ...patient,
          // Garantir que endereço nunca é undefined
          address: patient.address || null,
          // Garantir que a data de nascimento é um objeto Date válido
          birthDate: new Date(patient.birthDate),
          // Garantir que o array de consultas existe
          appointments: patient.appointments || [],
          // Garantir que o objeto user existe e tem todos os campos necessários
          user: {
            id: patient.user.id,
            name: patient.user.name || null,
            email: patient.user.email,
            phone: patient.user.phone || null,
            avatarUrl: patient.user.avatarUrl || null,
          }
        };
      });

      // Calcular total de páginas
      const pages = Math.ceil(total / input.perPage);

      // Retornar os dados com segurança
      return {
        patients: safePatients,
        pagination: {
          total,
          pages,
          page: input.page,
          perPage: input.perPage,
        },
      };
    } catch (error) {
      console.error("Erro ao listar todos os pacientes:", error);
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Erro ao listar pacientes",
        cause: error,
      });
    }
  });
