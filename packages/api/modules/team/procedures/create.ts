import { TRPCError } from "@trpc/server";
import { TeamMemberRoleSchema, TeamSchema, db } from "database";
import { logger } from "logs";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const create = protectedProcedure
	.input(
		z.object({
			name: z.string(),
		}),
	)
	.output(
		TeamSchema.extend({
			memberships: z.array(
				z.object({
					id: z.string(),
					role: z.enum(["MEMBER", "OWNER", "DOCTOR", "SECRETARY", "ADMIN"]),
					isCreator: z.boolean(),
				}),
			),
		}),
	)
	.mutation(async ({ input: { name }, ctx: { user } }) => {
		try {
			const team = await db.team.create({
				data: {
					name,
					memberships: {
						create: {
							userId: user.id,
							role: TeamMemberRoleSchema.Values.OWNER,
							isCreator: true,
						},
					},
					teamType: null,
					metadata: {},
				},
				select: {
					id: true,
					name: true,
					avatarUrl: true,
					teamType: true,
					metadata: true,
					memberships: {
						select: {
							id: true,
							role: true,
							isCreator: true,
						},
					},
				},
			});

			return team;
		} catch (error) {
			logger.error("Failed to create team:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to create team",
				cause: error,
			});
		}
	});
