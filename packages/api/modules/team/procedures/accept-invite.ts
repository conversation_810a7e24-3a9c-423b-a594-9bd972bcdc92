import { TRPCError } from "@trpc/server";
import { TeamSchema, db } from "database";
import { logger } from "logs";
import { z } from "zod";
import { protectedProcedure } from "../../../trpc/base";

export const acceptInvitation = protectedProcedure
	.input(
		z.object({
			id: z.string(),
			userId: z.string().optional(),
		}),
	)
	.output(TeamSchema.pick({ name: true }))
	.mutation(async ({ input: { id, userId }, ctx: { user } }) => {
		const effectiveUserId = userId || user.id;

		logger.info(`Accepting invitation ${id} for user ${effectiveUserId}`);

		const userData = userId ? await db.user.findUnique({
			where: { id: effectiveUserId },
			select: { role: true }
		}) : { role: user.role };

		if (!userData) {
			logger.error(`User ${effectiveUserId} not found`);
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "User not found",
			});
		}

		logger.info(`Current user role: ${userData.role}`);

		const invitation = await db.teamInvitation.findUnique({
			where: {
				id,
			},
		});

		if (!invitation) {
			logger.warn(`Invitation ${id} not found`);
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Invitation not found.",
			});
		}

		logger.info(`Found invitation with role: ${invitation.role}`);

		if (invitation.expiresAt < new Date()) {
			logger.warn(`Invitation ${id} expired`);
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Invitation expired.",
			});
		}

		try {
			// Primeiro, vamos atualizar a role do usuário ANTES da transação do membership
			// Isso garante que a atualização da role não depende da criação bem-sucedida do membership
			if (invitation.role === "DOCTOR" || invitation.role === "ADMIN" || invitation.role === "SECRETARY") {
				if (userData.role === "USER" || userData.role === "PATIENT") {
					logger.info(`Explicitly updating user ${effectiveUserId} role from ${userData.role} to ${invitation.role}`);

					// Atualizamos a role diretamente, fora da transação
					await db.user.update({
						where: { id: effectiveUserId },
						data: { role: invitation.role }
					});

					// Verificamos se a atualização foi bem-sucedida
					const updatedUser = await db.user.findUnique({
						where: { id: effectiveUserId },
						select: { role: true }
					});

					logger.info(`User role after direct update: ${updatedUser?.role}`);
				} else {
					logger.info(`No role update needed - user has role ${userData.role} which is >= invitation role ${invitation.role}`);
				}
			}

			// Agora criamos o membership em uma transação separada
			const membership = await db.teamMembership.create({
				data: {
					teamId: invitation.teamId,
					userId: effectiveUserId,
					role: invitation.role,
				},
				include: {
					team: {
						select: {
							name: true,
						},
					},
				},
			});

			logger.info(`Created team membership with role: ${invitation.role}`);

			// Depois de tudo bem-sucedido, excluímos o convite
			await db.teamInvitation.delete({
				where: {
					id,
				},
			});

			logger.info(`Invitation ${id} accepted and deleted`);

			return membership.team;
		} catch (error) {
			logger.error("Error during invitation acceptance process:", error);
			throw new TRPCError({
				code: "INTERNAL_SERVER_ERROR",
				message: "Failed to accept invitation",
				cause: error,
			});
		}
	});
