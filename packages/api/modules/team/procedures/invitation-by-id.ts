import { TRPCError } from "@trpc/server";
import { TeamInvitationSchema, db } from "database";
import { logger } from "logs";
import { z } from "zod";
import { publicProcedure } from "../../../trpc/base";

export const invitationById = publicProcedure
	.input(
		z.object({
			id: z.string(),
		}),
	)
	.output(
		TeamInvitationSchema.extend({
			team: z
				.object({
					name: z.string(),
				})
				.nullish(),
		}).nullable(),
	)
	.query(async ({ input: { id } }) => {
		try {
			logger.info(`Fetching invitation with id: ${id}`);

			const invitation = await db.teamInvitation.findUnique({
				where: {
					id,
				},
				include: {
					team: {
						select: {
							name: true,
						},
					},
				},
			});

			if (!invitation) {
				logger.warn(`Invitation not found: ${id}`);
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Invitation not found",
				});
			}

			// Check if invitation is expired
			if (invitation.expiresAt < new Date()) {
				logger.warn(`Invitation expired: ${id}`);
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Invitation has expired",
				});
			}

			logger.info(
				`Invitation found: ${JSON.stringify({
					id: invitation.id,
					email: invitation.email,
					teamId: invitation.teamId,
					expiresAt: invitation.expiresAt,
				})}`,
			);

			return invitation;
		} catch (error) {
			logger.error("Error fetching invitation:", error);
			throw error instanceof TRPCError
				? error
				: new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to fetch invitation",
						cause: error,
					});
		}
	});
