{"dependencies": {"@lemonsqueezy/lemonsqueezy.js": "^3.3.1", "@trpc/server": "11.0.0-rc.601", "auth": "workspace:*", "change-case": "^5.4.4", "chargebee-typescript": "^2.41.0", "database": "workspace:*", "date-fns": "^4.1.0", "logs": "workspace:*", "mail": "workspace:*", "next": "15.0.2", "openai": "^4.86.1", "storage": "workspace:*", "stripe": "^17.3.0", "superjson": "^2.2.1", "use-intl": "^3.23.5", "utils": "workspace:*", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/react": "18.3.12", "encoding": "^0.1.13", "prisma": "^5.21.1", "tsconfig": "workspace:*", "typescript": "5.6.3"}, "main": "./index.tsx", "name": "api", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}