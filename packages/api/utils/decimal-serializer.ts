/**
 * Utility functions for handling Prisma Decimal serialization
 * This prevents "Only plain objects can be passed to Client Components from Server Components" errors
 */

/**
 * Converts a Prisma Decimal to a JavaScript number
 * @param decimal The Decimal object to convert
 * @param defaultValue Value to return if the decimal is null/undefined
 * @returns A JavaScript number
 */
export function serializeDecimal(decimal: any, defaultValue: number | null = null): number | null {
  if (decimal === null || decimal === undefined) {
    return defaultValue;
  }
  return Number(decimal);
}

/**
 * Recursively walks through an object and converts all Decimal properties to numbers
 * @param obj The object to process
 * @returns A new object with all Decimal properties converted to numbers
 */
export function serializeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeObject) as unknown as T;
  }

  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    // Check if it's a Decimal (has toString method but not a native JavaScript type)
    if (value !== null &&
        typeof value === 'object' &&
        'toString' in value &&
        !Array.isArray(value) &&
        Object.prototype.toString.call(value) !== '[object Date]' &&
        !(value instanceof Map) &&
        !(value instanceof Set)) {
      // Likely a Decimal, serialize it
      result[key] = Number(value);
    } else if (value !== null && typeof value === 'object') {
      // Recursively process nested objects
      result[key] = serializeObject(value);
    } else {
      // Keep other values as is
      result[key] = value;
    }
  }

  return result as T;
}
