import { z } from "zod";
import { openai } from "@ai-sdk/openai";
import { streamText, tool } from "ai";

export const doctorAgentSystemPrompt = (
  doctorName: string,
  clinicName?: string,
): string => `
You are an AI clinical assistant helping Dr. ${doctorName}${
  clinicName ? ` at ${clinicName}` : ""
} during patient encounters.

Goals:
- Help triage symptoms, summarize histories, and suggest next diagnostic steps.
- Propose differential diagnoses with probabilities and red-flag alerts.
- Generate concise SOAP notes and patient-friendly explanations in Brazilian Portuguese.

Strict rules:
- You are not a substitute for medical judgment. Always remind to confirm clinically.
- Never give definitive diagnoses. Use probabilistic, safety-first language.
- For medications or dosing, ask for patient specifics and cite standard guidelines when relevant.
- Keep answers brief by default; offer to expand sections on demand.
`;

export const quickSearchToolSchema = z.object({
  query: z
    .string()
    .min(3)
    .describe("Short clinical search query, e.g. 'child fever red flags'")
});

export const quickSearchTool = tool({
  description: "Perform a quick web-like literature search (placeholder stub).",
  parameters: quickSearchToolSchema,
  execute: async ({ query }: { query: string }) => {
    return {
      results: [
        {
          title: "AI SDK docs (exemplo)",
          url: "https://ai-sdk.dev/",
          snippet: "Referência para construir apps de IA com streaming e tools.",
        },
        {
          title: "Assistant UI (exemplo)",
          url: "https://www.assistant-ui.com/",
          snippet: "Componentes para experiências de chat profissionais.",
        },
      ],
    } as const;
  },
});

export const doctorModel = openai("gpt-4o-mini");

export async function streamDoctorChat(options: {
  doctorName: string;
  clinicName?: string;
  messages: Array<{ role: "system" | "user" | "assistant"; content: string }>;
}) {
  const { doctorName, clinicName, messages } = options;
  return streamText({
    model: doctorModel,
    system: doctorAgentSystemPrompt(doctorName, clinicName),
    tools: { quickSearch: quickSearchTool },
    messages,
    maxRetries: 2,
  });
}

export type DoctorMessage = {
  role: "system" | "user" | "assistant";
  content: string;
};


