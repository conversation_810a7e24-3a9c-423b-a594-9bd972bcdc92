{"dependencies": {"@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "i18n": "workspace:*", "lodash-es": "^4.17.21", "logs": "workspace:*", "next-intl": "3.23.5", "nodemailer": "^6.9.16", "react": "18.3.1", "react-dom": "18.3.1", "react-email": "^3.0.1", "use-intl": "^3.23.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/line-clamp": "^0.4.4", "@types/lodash-es": "^4.17.12", "@types/node": "22.8.4", "@types/nodemailer": "^6.4.16", "@types/react": "18.3.12", "tailwind-config": "workspace:*", "tsconfig": "workspace:*"}, "main": "./index.ts", "name": "mail", "scripts": {"export": "email export", "preview": "email dev --port 3005", "type-check": "tsc --noEmit"}, "types": "./index.ts", "version": "0.0.0"}