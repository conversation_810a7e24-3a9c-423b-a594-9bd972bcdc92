'use server';

import { sendEmail as originalSendEmail } from './index';
import type { TemplateId } from './util/templates';
import { config } from '@config';

/**
 * Server-only function to send emails
 * This ensures Nodemailer and its Node.js dependencies are only used server-side
 */
export async function sendServerEmail<T extends TemplateId>(params: {
  to: string;
  templateId: T;
  context: Record<string, unknown>;
  locale?: keyof typeof config.i18n.locales;
}) {
  try {
    const result = await originalSendEmail(params);
    return { success: !!result };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send email'
    };
  }
}
