import { Link, Text } from '@react-email/components';
import type { BaseMailProps } from '../types';
import PrimaryButton from './components/PrimaryButton';
import Wrapper from './components/Wrapper';

export function AppointmentCreated({
	recipientName,
	doctorName,
	patientName,
	appointmentDate,
	appointmentTime,
	consultType,
	joinUrl,
	isDoctor,
	locale,
	translations,
}: {
	recipientName: string;
	doctorName?: string;
	patientName?: string;
	appointmentDate: string;
	appointmentTime: string;
	consultType: string;
	joinUrl: string;
	isDoctor: boolean;
} & BaseMailProps): JSX.Element {
	return (
		<Wrapper>
			<Text>Olá {recipientName},</Text>

			{isDoctor ? (
				<Text>
					Sua consulta com {patientName} está agendada para {appointmentDate} às{' '}
					{appointmentTime}.
				</Text>
			) : (
				<Text>
					Sua consulta com {doctorName} está agendada para {appointmentDate} às{' '}
					{appointmentTime}.
				</Text>
			)}

			<Text>
				{isDoctor
					? 'Você pode acessar a consulta através do link abaixo:'
					: 'Você pode acessar a consulta através do link abaixo:'}
			</Text>

			<PrimaryButton href={joinUrl}>Acessar Consulta</PrimaryButton>

			<Text className='text-muted-foreground text-sm'>
				Se preferir abrir o link em outro navegador, copie e cole este endereço:
				<Link href={joinUrl}>{joinUrl}</Link>
			</Text>

			<Text>
				Lembre-se de estar disponível alguns minutos antes do horário marcado.
			</Text>
		</Wrapper>
	);
}

export default AppointmentCreated;
