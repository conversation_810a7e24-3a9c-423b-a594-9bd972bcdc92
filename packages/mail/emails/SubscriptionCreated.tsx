import { Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";
import React from "react";

export function SubscriptionCreated({
  recipientName,
  planName,
  planPrice,
  nextBillingDate,
  manageUrl,
  locale,
  translations,
}: {
  recipientName: string;
  planName: string;
  planPrice: number | string;
  nextBillingDate?: Date | string | null;
  manageUrl?: string;
} & BaseMailProps): React.ReactElement {
  const userName = recipientName || "Paciente";
  const formattedNextBilling = nextBillingDate
    ? new Date(nextBillingDate).toLocaleDateString("pt-BR")
    : undefined;
  const url = manageUrl || `${process.env.NEXT_PUBLIC_SITE_URL || "https://zapvida.com"}/app`;

  return (
    <Wrapper>
      <Text>Olá {userName},</Text>

      <Text>
        Sua assinatura <strong>{planName}</strong> foi ativada com sucesso.
      </Text>

      <Text>
        Valor do plano: <strong>R$ {Number(planPrice).toFixed(2)}</strong>
        {formattedNextBilling ? (
          <>
            <br />
            Próxima cobrança: <strong>{formattedNextBilling}</strong>
          </>
        ) : null}
      </Text>

      <Text>Você pode gerenciar sua assinatura pelo link abaixo:</Text>

      <PrimaryButton href={url}>Gerenciar assinatura</PrimaryButton>

      <Text className="text-muted-foreground text-sm">
        Se preferir abrir o link em outro navegador, copie e cole este endereço: <Link href={url}>{url}</Link>
      </Text>
    </Wrapper>
  );
}

export default SubscriptionCreated;


