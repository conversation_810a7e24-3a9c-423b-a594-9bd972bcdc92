import { Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";

export function NewUser({
	url,
	name,
	otp,
	locale,
	translations,
}: {
	url: string;
	name: string;
	otp: string;
} & BaseMailProps): JSX.Element {
	return (
		<Wrapper>
			<Text>Olá {name}, bem-vindo à ZapVida! Por favor, confirme seu e-mail para começar.</Text>

			<Text>
				Seu código de verificação:
				<br />
				<strong className="font-bold text-2xl">{otp}</strong>
			</Text>

			<Text>Ou use o link abaixo:</Text>

			<PrimaryButton href={url}>
				Confirmar E-mail &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Se preferir abrir o link em outro navegador, copie e cole este endereço:
				<Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

export default NewUser;
