import { Heading, Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";

export function TeamInvitation({
	url,
	teamName,
	locale,
	translations,
}: {
	url: string;
	teamName: string;
} & BaseMailProps): JSX.Element {
	return (
		<Wrapper>
			<Heading className="text-xl">
				Você foi convidado para se juntar a <strong>{teamName}</strong>
			</Heading>
			<Text>Você foi convidado para fazer parte da equipe {teamName}. Clique no botão abaixo para aceitar o convite.</Text>

			<PrimaryButton href={url}>Aceitar Convite</PrimaryButton>

			<Text className="mt-4 text-muted-foreground text-sm">
				Se preferir abrir o link em outro navegador, copie e cole este endereço:
				<Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

export default TeamInvitation;
