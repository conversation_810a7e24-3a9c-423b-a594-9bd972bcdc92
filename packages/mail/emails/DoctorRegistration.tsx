import React from "react";
import { Heading, Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";

export function DoctorRegistration({
	url,
	name,
}: {
	url: string;
	name: string;
} & Omit<BaseMailProps, 'locale' | 'translations'>): React.ReactElement {
	return (
		<Wrapper>
			<Heading className="text-xl">
				Olá <strong>{name}</strong>, bem-vindo à ZapVida Saúde!
			</Heading>
			<Text>
				Obrigado por se cadastrar como médico na nossa plataforma. Para continuar com seu cadastro, 
				por favor verifique seu e-mail clicando no botão abaixo.
			</Text>

			<PrimaryButton href={url}>Verificar meu e-mail</PrimaryButton>

			<Text className="mt-4 text-muted-foreground text-sm">
				Se o botão não funcionar, copie e cole o link abaixo no seu navegador:
				<Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

export default DoctorRegistration;
