import { Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";
import React from "react";

export function NewPatient({
	url,
	name,
	recipientName,
	doctorName,
	appointmentDate,
	appointmentTime,
	joinUrl,
	tempPassword,
	consultType,
	isNewUser,
	locale,
	translations,
}: {
	url?: string;
	name?: string;
	recipientName?: string;
	doctorName?: string;
	appointmentDate?: string;
	appointmentTime?: string;
	joinUrl?: string;
	tempPassword?: string;
	consultType?: string;
	isNewUser?: boolean;
	otp?: string;
} & BaseMailProps): React.ReactElement {
	// Use recipientName if available, otherwise use name
	const userName = recipientName || name || "Paciente";

	// Determine if this is an appointment confirmation or just a new user email
	const hasAppointment = Boolean(appointmentDate && appointmentTime && doctorName);

	// Use joinUrl if available, otherwise use url
	const actionUrl = joinUrl || url || "";

	return (
		<Wrapper>
			<Text>Olá {userName}, bem-vindo à ZapVida!</Text>

			{hasAppointment ? (
				<>
					<Text>
						Sua consulta com {doctorName} está agendada para {appointmentDate} às {appointmentTime}.
					</Text>



					<Text>Você pode acessar a consulta através do link abaixo:</Text>

					<PrimaryButton href={actionUrl}>
						Acessar Consulta
					</PrimaryButton>
				</>
			) : (
				<>
					<Text>Por favor, confirme seu e-mail clicando no botão abaixo.</Text>

					<PrimaryButton href={actionUrl}>
						Confirmar E-mail &rarr;
					</PrimaryButton>
				</>
			)}

			{tempPassword && (
				<Text className="text-sm">
					<strong>Sua senha temporária é: {tempPassword}</strong><br />
					Recomendamos que você altere sua senha após o primeiro acesso.
				</Text>
			)}

			{!hasAppointment && (
				<Text className="text-sm">
					Após confirmar seu e-mail, você poderá definir uma senha para sua conta.
				</Text>
			)}

			<Text className="text-muted-foreground text-sm">
				Se preferir abrir o link em outro navegador, copie e cole este endereço:
				<Link href={actionUrl}>{actionUrl}</Link>
			</Text>
		</Wrapper>
	);
}

export default NewPatient;

