import { Link, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import PrimaryButton from "./components/PrimaryButton";
import Wrapper from "./components/Wrapper";

export function EmailChange({
	url,
	name,
	locale,
	translations,
}: {
	url: string;
	name: string;
} & BaseMailProps): JSX.Element {
	return (
		<Wrapper>
			<Text>Olá {name}, recebemos uma solicitação para alterar seu endereço de e-mail. Por favor, confirme clicando no botão abaixo.</Text>

			<PrimaryButton href={url}>
				Confirmar E-mail &rarr;
			</PrimaryButton>

			<Text className="text-muted-foreground text-sm">
				Se preferir abrir o link em outro navegador, copie e cole este endereço:
				<Link href={url}>{url}</Link>
			</Text>
		</Wrapper>
	);
}

export default EmailChange;
