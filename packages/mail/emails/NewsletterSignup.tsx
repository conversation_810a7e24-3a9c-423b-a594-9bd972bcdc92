import { Container, Heading, Section, Text } from "@react-email/components";
import type { BaseMailProps } from "../types";
import Wrapper from "./components/Wrapper";

export function NewsletterSignup({
	locale,
	translations,
}: BaseMailProps): JSX.Element {
	return (
		<Wrapper>
			<Section className="bg-card p-8">
				<Container>
					<Heading as="h1">Inscrição na Newsletter Confirmada</Heading>
					<Text>Obrigado por se inscrever na nossa newsletter! Você receberá nossas atualizações em breve.</Text>
				</Container>
			</Section>
		</Wrapper>
	);
}

export default NewsletterSignup;
