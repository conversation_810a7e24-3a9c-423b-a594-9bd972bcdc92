import { sendEmail } from './send';

interface SendAppointmentConfirmationParams {
  transaction: {
    id: string;
    amount: string;
  };
  appointment: {
    id: string;
    doctorName: string;
    scheduledAt: Date;
    consultType: string;
  };
  user: {
    name: string;
    email: string;
  };
  locale: string;
}

/**
 * Sends an appointment confirmation email to the user
 */
export async function sendAppointmentConfirmationEmail(
  params: SendAppointmentConfirmationParams
): Promise<boolean> {
  const { transaction, appointment, user, locale } = params;

  // Format date and time for email using native Date methods
  const appointmentDate = new Date(appointment.scheduledAt).toLocaleDateString(
    locale === 'en' ? 'en-US' : 'pt-BR',
    { day: '2-digit', month: '2-digit', year: 'numeric' }
  );

  const appointmentTime = new Date(appointment.scheduledAt).toLocaleTimeString(
    locale === 'en' ? 'en-US' : 'pt-BR',
    { hour: '2-digit', minute: '2-digit', hour12: false }
  );

  // Generate join URL
  const joinUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/account/appointment/${appointment.id}`;

  return sendEmail({
    to: user.email,
    templateId: 'appointmentCreated',
    context: {
      recipientName: user.name,
      doctorName: appointment.doctorName,
      appointmentDate,
      appointmentTime,
      consultType: appointment.consultType,
      joinUrl,
      isDoctor: false,
    },
    locale: locale as any,
  });
}
