import type { Locale } from "@config";
import { render } from "@react-email/render";
import { getMessagesForLocale } from "i18n/lib";
import type { Messages } from "i18n/types";
import { EmailChange } from "../emails/EmailChange";
import { ForgotPassword } from "../emails/ForgotPassword";
import { MagicLink } from "../emails/MagicLink";
import { NewUser } from "../emails/NewUser";
import { NewPatient } from "../emails/NewPatient";
import { NewsletterSignup } from "../emails/NewsletterSignup";
import { TeamInvitation } from "../emails/TeamInvitation";
import { AppointmentCreated } from "../emails/AppointmentCreated";
import { DoctorRegistration } from "../emails/DoctorRegistration";
import { SubscriptionCreated } from "../emails/SubscriptionCreated";




export const mailTemplates = {
	magicLink: MagicLink,
	forgotPassword: ForgotPassword,
	newUser: NewUser,
	newPatient: NewPatient,
	newsletterSignup: NewsletterSignup,
	teamInvitation: TeamInvitation,
	emailChange: EmailChange,
	appointmentCreated: AppointmentCreated,
	doctorRegistration: DoctorRegistration,
	subscriptionCreated: SubscriptionCreated,
};

export async function getTemplate<T extends TemplateId>({
	templateId,
	context,
	locale,
}: {
	templateId: T;
	context: Omit<
		Parameters<(typeof mailTemplates)[T]>[0],
		"locale" | "translations"
	>;
	locale: Locale;
}) {
	const template = mailTemplates[templateId];
	const translations = await getMessagesForLocale(locale);

	const email = template({
		...(context as any),
		locale,
		translations,
	});

	const subject =
		translations.mail[templateId as keyof Messages["mail"]] &&
		"subject" in translations.mail[templateId as keyof Messages["mail"]]
			? translations.mail[templateId as keyof Messages["mail"]].subject
			: "";

	const html = await render(email);
	const text = await render(email, { plainText: true });
	return { html, text, subject };
}

export type TemplateId = keyof typeof mailTemplates;
