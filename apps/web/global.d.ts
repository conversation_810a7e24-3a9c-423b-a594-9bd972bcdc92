import type { Messages } from "i18n/types";
import type { JSX as Jsx } from "react/jsx-runtime";
import { ReactNode } from "react";

// temporary fix for mdx types
// TODO: remove once mdx has fully compatibility with react 19
declare global {
	namespace JSX {
		type ElementClass = Jsx.ElementClass;
		type Element = Jsx.Element;
		type IntrinsicElements = Jsx.IntrinsicElements;
	}
}

declare global {
	interface IntlMessages extends Messages {}
}


// Declaração de módulo para estender as definições de tipos do Next.js
declare module "next" {
	// Nova interface que funciona com params sincronizados e prometidos
	export interface PageProps {
	  params?: any;
	  searchParams?: Record<string, string | string[]>;
	  children?: ReactNode;
	}
  }

  // Tipo de utilidade para parâmetros de rota
  export type RouteParams<T extends Record<string, string> = Record<string, string>> =
	T | Promise<T>;

  // Interface para ser usada em todas as páginas com rotas dinâmicas
  export interface PagePropsWithParams<T extends Record<string, string> = Record<string, string>> {
	params: RouteParams<T>;
	searchParams?: Record<string, string | string[]>;
  }
