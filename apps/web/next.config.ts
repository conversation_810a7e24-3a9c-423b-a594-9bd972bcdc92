import { withContentCollections } from "@content-collections/next";
import type { NextConfig } from "next";
import nextIntlPlugin from "next-intl/plugin";

const withNextIntl = nextIntlPlugin("./modules/i18n/request.ts");

const nextConfig: NextConfig = {
	transpilePackages: ["api", "auth"],
	output: 'standalone',
	typescript: {
		ignoreBuildErrors: true,
	},
	env: {
		// Forçar o carregamento das variáveis de ambiente
		DATABASE_URL: process.env.DATABASE_URL,
		DIRECT_URL: process.env.DIRECT_URL,
		NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
		NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
		SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
		NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME: process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME,
	},
	images: {
		remotePatterns: [
			{
				// google profile images
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				// github profile images
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
			{
				hostname: "*.zapvida.com",
			},
			{
				hostname: "www.zapvidaom",
			},
			{
				hostname: "randomuser.me",
			},
			{
				hostname: "images.unsplash.com",
			},
		],
	},
	async redirects() {
		return [
			{
				source: "/app",
				destination: "/app/dashboard",
				permanent: true,
			},
			{
				source: "/app/settings",
				destination: "/app/settings/account/general",
				permanent: true,
			},
			{
				source: "/app/admin",
				destination: "/app/admin/users",
				permanent: true,
			},
		];
	},
	webpack: (config) => {
		// Handle Node.js specific modules
		config.externals.push("@node-rs/argon2");

		// Handle Node.js builtin modules used by Nodemailer
		config.resolve.fallback = {
			...config.resolve.fallback,
			fs: false,
			net: false,
			tls: false,
			dns: false,
		};

		return config;
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
};

export default withContentCollections(withNextIntl(nextConfig));
