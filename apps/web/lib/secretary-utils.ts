// apps/web/lib/secretary-utils.ts
import { db } from "database";

/**
 * Verifica se um usuário possui um perfil de secretária
 * @param userId ID do usuário
 * @returns true se existir um perfil de secretária para o usuário
 */
export async function secretaryProfileExists(userId: string): Promise<boolean> {
  try {
    // Aqui estamos assumindo que haverá um modelo de Secretary no futuro.
    // Como esse modelo não existe no schema.prisma compartilhado,
    // estamos usando uma verificação baseada na role do usuário
    // e membership de equipe.

    // Verificar se o usuário tem a role SECRETARY
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        role: true,
        teamMemberships: {
          select: {
            role: true,
            teamId: true
          }
        }
      }
    });

    if (!user) return false;

    // Verificar se é um SECRETARY que pertence a uma equipe
    return user.role === "SECRETARY" &&
           user.teamMemberships &&
           user.teamMemberships.length > 0;

    // Quando você tiver um modelo específico de Secretary, a consulta abaixo seria mais adequada:
    /*
    const secretary = await db.secretary.findUnique({
      where: { userId },
    });
    return !!secretary;
    */
  } catch (error) {
    console.error("Erro ao verificar perfil de secretária:", error);
    return false;
  }
}
