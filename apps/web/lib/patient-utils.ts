import { db } from "database";
import type { User } from "@prisma/client";

export async function getPatientProfile(userId: string) {
  const patient = await db.patient.findUnique({
    where: { userId }
  });

  return patient;
}

export async function patientProfileExists(userId: string): Promise<boolean> {
  const patient = await getPatientProfile(userId);
  return !!patient;
}

/**
 * Determina para qual onboarding o usuário deve ser direcionado com base no role
 */
export function getOnboardingPath(user: User): string {
  if (!user) return "/auth/login";

  if (user.role === "DOCTOR") {
    return "/onboarding/doctor";
  } else if (user.role === "HOSPITAL") {
    return "/onboarding/hospital";
  } else {
    // USER ou PATIENT
    return "/onboarding/patient";
  }
}
