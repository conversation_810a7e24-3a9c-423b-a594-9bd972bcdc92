import { getAsaasConfig, buildAsaasHeaders } from 'api/modules/asaas/client';

export class AsaasClient {
	private baseUrl: string;
	private apiKey: string;

	constructor() {
    // Load config via shared helper
    const { baseUrl, apiKey } = getAsaasConfig();
    this.baseUrl = baseUrl;

		console.log("[ASAAS_CLIENT_INIT] Initializing with API URL:", this.baseUrl);
		console.log("[ASAAS_CLIENT_INIT] API Key check:", {
			apiKeyExists: Boolean(apiKey),
			apiKeyLength: apiKey?.length || 0,
			apiKeyStartsWithDollar: apiKey?.startsWith('$') || false
		});

    // Already normalized by getAsaasConfig
    this.apiKey = apiKey;

		if (!this.apiKey || this.apiKey === '$undefined' || this.apiKey === '$null' || this.apiKey === '$') {
			console.error("[ASAAS_CLIENT_ERROR] Invalid API key:", this.apiKey);
			throw new Error("ASAAS_API_KEY não configurada corretamente");
		}

		console.log("[ASAAS_CLIENT_INIT] Successfully initialized client");
	}

	// Simple wrapper for API requests with proper error handling
	private async makeRequest(endpoint: string, method: string, data?: any) {
		console.log(`[ASAAS_CLIENT_REQUEST] ${method} ${endpoint}`, data ? {
			dataPresent: true,
			sensitiveDataHidden: Boolean(data.creditCard || data.creditCardHolderInfo)
		} : { dataPresent: false });

		try {
      const headers: Record<string, string> = buildAsaasHeaders({ apiKey: this.apiKey });

			const options: RequestInit = {
				method,
				headers,
				...(data ? { body: JSON.stringify(data) } : {})
			};

			console.log("[ASAAS_CLIENT_REQUEST] Request options:", {
				method,
				url: `${this.baseUrl}${endpoint}`,
				headersPresent: Object.keys(headers),
				bodyPresent: Boolean(data)
			});

			const response = await fetch(`${this.baseUrl}${endpoint}`, options);
			console.log("[ASAAS_CLIENT_RESPONSE] Status:", response.status, response.statusText);

			const responseText = await response.text();
			console.log("[ASAAS_CLIENT_RESPONSE] Response body length:", responseText.length);

			if (responseText.length > 0) {
				try {
					const responseData = JSON.parse(responseText);

					if (!response.ok) {
						console.error("[ASAAS_CLIENT_ERROR] Request failed:", {
							status: response.status,
							error: responseData.errors?.[0]?.description || responseData.message || "Unknown error"
						});
						throw new Error(responseData.errors?.[0]?.description || responseData.message || `Error ${response.status}`);
					}

					return responseData;
				} catch (parseError: any) {
					console.error("[ASAAS_CLIENT_ERROR] Failed to parse JSON response:", parseError.message);
					throw new Error(`Failed to parse API response: ${parseError.message}`);
				}
			} else {
				console.error("[ASAAS_CLIENT_ERROR] Empty response");
				throw new Error("Empty response from API");
			}
		} catch (error: any) {
			console.error("[ASAAS_CLIENT_ERROR]", error);
			throw error;
		}
	}

	async createPayment(data: any) {
		console.log("[ASAAS_PAYMENT] Creating payment", {
			customer: data.customer,
			billingType: data.billingType,
			value: data.value,
			creditCardPresent: Boolean(data.creditCard),
			holderInfoPresent: Boolean(data.creditCardHolderInfo)
		});

		try {
			return await this.makeRequest("/payments", "POST", data);
		} catch (error: any) {
			console.error("[ASAAS_PAYMENT_ERROR]", error);
			throw error;
		}
	}

	async createCustomer(data: {
		name: string;
		email: string;
		cpf: string;
		phone?: string;
	}) {
		// Clean and validate CPF
		const cleanCpf = data.cpf.replace(/\D/g, "");
		if (cleanCpf.length !== 11) {
			throw new Error("CPF inválido: deve conter 11 dígitos");
		}

		// Validate email format
		if (!data.email || !data.email.includes('@')) {
			throw new Error("Email inválido");
		}

		return this.makeRequest("/customers", "POST", {
			name: data.name,
			email: data.email,
			cpfCnpj: cleanCpf,
			mobilePhone: data.phone?.replace(/\D/g, ""),
			notificationDisabled: true,
		});
	}

	async findCustomerByEmail(email: string) {
		try {
			const response = await this.makeRequest(`/customers?email=${encodeURIComponent(email)}`, "GET");
			return response?.data?.[0] || null;
		} catch (error) {
			console.error("[ASAAS_FIND_CUSTOMER_ERROR]:", error);
			return null; // Return null instead of throwing to make this more resilient
		}
	}

	async getPixQRCode(paymentId: string) {
		try {
			const response = await this.makeRequest(`/payments/${paymentId}/pixQrCode`, "GET");
			return {
				encodedImage: response.encodedImage,
				payload: response.payload
			};
		} catch (error) {
			console.error("[ASAAS_PIX_ERROR]:", error);
			throw error;
		}
	}
}

// Helper function to check environment variables when needed
export function checkAsaasEnvironment() {
	const { baseUrl: ASAAS_API_URL, apiKey: ASAAS_API_KEY } = getAsaasConfig();

	console.log("[ASAAS_MODULE_LOAD] Environment check:", {
		apiUrlExists: Boolean(ASAAS_API_URL),
		apiKeyExists: Boolean(ASAAS_API_KEY),
		apiKeyLength: ASAAS_API_KEY?.length || 0
	});

	return {
		apiUrlExists: Boolean(ASAAS_API_URL),
		apiKeyExists: Boolean(ASAAS_API_KEY),
		apiKeyLength: ASAAS_API_KEY?.length || 0
	};
}
