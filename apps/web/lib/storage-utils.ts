import { apiClient } from "@shared/lib/api-client";

/**
 * Get a signed URL for an object in S3 storage
 *
 * @param path The path of the object in the bucket
 * @param options Options for the signed URL
 * @returns A signed URL for the object
 */
export async function getSignedUrl(path: string, options: { bucket: string; expiresIn?: number }) {
  try {
    // Use the API client to get a signed URL
    // This is a simplified implementation - in a real app, you'd use a more robust approach

    // We can't directly import from the storage package in client components,
    // so we need to make an API call instead

    // Call the API endpoint that handles signed URLs
    const response = await fetch(`/api/signed-url?path=${encodeURIComponent(path)}&bucket=${encodeURIComponent(options.bucket)}`);

    if (!response.ok) {
      throw new Error(`Failed to get signed URL: ${response.statusText}`);
    }

    const data = await response.json();
    return data.url;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    // Fallback to a direct URL (might not work for private buckets)
    const bucketUrl = process.env.NEXT_PUBLIC_S3_ENDPOINT || "https://kukvzjoqltvaqxvyseav.supabase.co/storage/v1/s3";
    return `${bucketUrl}/${options.bucket}/${path}`;
  }
}
