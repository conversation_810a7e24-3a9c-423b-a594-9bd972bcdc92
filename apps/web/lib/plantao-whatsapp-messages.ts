import { EvolutionService } from '../actions/checkout/integrations/evolution/evolution.service';
import { formatPhoneForWhatsApp } from './utils/format-phone';
import { getBaseUrl } from 'utils';

interface PlantaoWhatsAppData {
  appointmentId: string;
  patientName: string;
  urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
  phone: string;
  estimatedWaitTime?: number; // em minutos
}

/**
 * Envia mensagem WhatsApp melhorada para paciente de plantão
 */
export async function sendImprovedPlantaoPatientMessage(data: PlantaoWhatsAppData) {
  try {
    console.log('[PLANTAO_WHATSAPP] Enviando mensagem aprimorada para paciente:', {
      appointmentId: data.appointmentId,
      patientName: data.patientName,
      urgencyLevel: data.urgencyLevel,
      hasPhone: !!data.phone
    });

    if (!data.phone) {
      return {
        success: false,
        error: 'Telefone do paciente não encontrado'
      };
    }

    // Mapear nível de urgência para informações específicas
    const urgencyInfo = {
      HIGH: {
        emoji: '🚨',
        label: 'Muito <PERSON>e',
        waitTime: '5-10 minutos',
        priority: 'MÁXIMA PRIORIDADE',
        description: 'Você está na fila de atendimento prioritário'
      },
      MEDIUM: {
        emoji: '⚠️',
        label: 'Urgente',
        waitTime: '15-25 minutos',
        priority: 'ALTA PRIORIDADE',
        description: 'Você está na fila de atendimento com prioridade'
      },
      LOW: {
        emoji: '📋',
        label: 'Consulta de Rotina',
        waitTime: '20-40 minutos',
        priority: 'PRIORIDADE NORMAL',
        description: 'Você está na fila de atendimento normal'
      }
    };

    const info = urgencyInfo[data.urgencyLevel];
    const baseUrl = getBaseUrl();

        // Gerar magic link para acesso direto ao ZapChat
    let linkConsulta: string | null = null;
    try {
      linkConsulta = await generatePlantaoMagicLink(data.appointmentId);
      console.log('[PLANTAO_WHATSAPP] Magic link gerado:', {
        appointmentId: data.appointmentId,
        hasMagicLink: !!linkConsulta,
        linkPreview: linkConsulta ? linkConsulta.substring(0, 50) + '...' : null
      });
    } catch (error) {
      console.error('[PLANTAO_WHATSAPP] Erro ao gerar magic link:', error);
    }

    // Usar magic link se disponível, senão fallback para página de status
    const statusUrl = linkConsulta || `${baseUrl}/plantao/status/${data.appointmentId}`;
    const isMagicLink = !!linkConsulta;

    const now = new Date();
    const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

    // Mensagens personalizadas baseadas na urgência
    const messages = [
      `${info.emoji} *ZapVida - Plantão Médico*`,

      `Olá ${data.patientName}! ✅\n\n*Seu pagamento foi confirmado com sucesso!*`,

      `🏥 **Status do Atendimento**\n\n${info.emoji} **Urgência:** ${info.label}\n🎯 **${info.priority}**\n⏱️ **Tempo estimado:** ${info.waitTime}\n📋 **${info.description}**`,

      `👨‍⚕️ **Próximos Passos:**\n\n1️⃣ Médicos online foram notificados\n2️⃣ Em instantes um médico aceitará seu caso\n3️⃣ Você receberá uma nova mensagem quando o atendimento começar`,

      data.urgencyLevel === 'HIGH'
        ? `🚨 **ATENÇÃO ESPECIAL**\n\nCaso MUITO URGENTE detectado! Nossos médicos estão sendo alertados com máxima prioridade. Aguarde, pois o atendimento será iniciado em poucos minutos.`
        : data.urgencyLevel === 'MEDIUM'
        ? `⚡ **PRIORIDADE ALTA**\n\nSeu caso foi classificado como urgente. Você terá prioridade no atendimento e será atendido rapidamente.`
        : `📋 **ATENDIMENTO NORMAL**\n\nSeu caso está na fila de atendimento. Aguarde alguns minutos enquanto um médico se disponibiliza para você.`,

      `📱 **Acesse sua consulta AGORA:**\n${statusUrl}\n\n*${isMagicLink ? '🔗 LOGIN AUTOMÁTICO: Clique no link acima para acessar diretamente o chat sem precisar fazer login!' : '💡 Dica: Salve este link para acompanhar o status em tempo real'}*`,

      `🔔 **Importante:**\n\n• Mantenha seu WhatsApp aberto\n• Você será avisado quando o médico aceitar\n• O atendimento será feito por chat/vídeo\n• Tenha seus documentos em mãos`,

      `✨ *Obrigado por escolher a ZapVida! Estamos cuidando de você com carinho e profissionalismo.*\n\n💚 **Equipe ZapVida**`
    ];

    // Usar o EvolutionService para enviar as mensagens
    const evolutionService = new EvolutionService();
    const formattedPhone = formatPhoneForWhatsApp(data.phone);

    const responses = await evolutionService.sendMessagesWithDelay(messages, formattedPhone);

    const successCount = responses.filter(r => r && r.key && r.key.id).length;

    console.log('[PLANTAO_WHATSAPP] Resultado do envio aprimorado:', {
      appointmentId: data.appointmentId,
      messagesTotal: messages.length,
      messagesSuccess: successCount,
      urgencyLevel: data.urgencyLevel
    });

    return {
      success: successCount > 0,
      messagesTotal: messages.length,
      messagesSuccess: successCount,
      urgencyInfo: info
    };

  } catch (error) {
    console.error('[PLANTAO_WHATSAPP] Erro ao enviar mensagem aprimorada:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Gera magic link para sala de consulta
 */
async function generateConsultMagicLink(appointmentId: string): Promise<string | null> {
  try {
    const baseUrl = getBaseUrl();
    const response = await fetch(`${baseUrl}/api/auth/magic-link`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transactionId: appointmentId, // Usar appointmentId como fallback
        type: 'CONSULTATION',
        appointmentId
      })
    });

    if (!response.ok) {
      console.error('[MAGIC_LINK] Erro ao gerar magic link para consulta:', response.status);
      return null;
    }

    const data = await response.json();
    return data.success ? data.magicUrl : null;
  } catch (error) {
    console.error('[MAGIC_LINK] Erro ao gerar magic link:', error);
    return null;
  }
}

/**
 * Gera link da consulta para plantão que leva direto ao ZapChat
 */
async function generatePlantaoMagicLink(appointmentId: string): Promise<string | null> {
  try {
    const baseUrl = getBaseUrl();

    // Tentar gerar link da consulta via API
    const response = await fetch(`${baseUrl}/api/auth/magic-link`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        appointmentId: appointmentId,
        type: 'PLANTAO' // Usar tipo PLANTAO que agora é suportado
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.magicUrl) {
        console.log('[PLANTAO_LINK_CONSULTA] Link da consulta gerado com sucesso');
        return data.magicUrl;
      }
    }

    // Fallback: gerar link direto para o zapchat (sem magic link)
    console.log('[PLANTAO_LINK_CONSULTA] Usando fallback - link direto para zapchat');
    return `${baseUrl}/patient/zapchat/${appointmentId}`;

  } catch (error) {
    console.error('[PLANTAO_LINK_CONSULTA] Erro ao gerar link da consulta:', error);

    // Fallback em caso de erro
    const baseUrl = getBaseUrl();
    return `${baseUrl}/patient/zapchat/${appointmentId}`;
  }
}

/**
 * Envia mensagem quando médico aceita o plantão
 */
export async function sendPlantaoAcceptedImprovedMessage(data: {
  appointmentId: string;
  patientName: string;
  patientPhone: string;
  doctorName: string;
  doctorSpecialty?: string;
}) {
  try {
    const baseUrl = getBaseUrl();

    // Tentar gerar magic link, usar fallback se falhar
    const magicUrl = await generateConsultMagicLink(data.appointmentId);
    const chatUrl = magicUrl || `${baseUrl}/chats/${data.appointmentId}`;

    console.log('[PLANTAO_WHATSAPP] Link gerado para consulta:', {
      appointmentId: data.appointmentId,
      hasMagicLink: !!magicUrl,
      urlUsed: chatUrl
    });

    const messages = [
      `🎉 *MÉDICO ENCONTRADO!*`,

      `Olá ${data.patientName}! ✅\n\n*Ótimas notícias! Um médico aceitou seu atendimento.*`,

      `👨‍⚕️ **Seu Médico:**\n\n🩺 **Dr(a). ${data.doctorName}**${data.doctorSpecialty ? `\n📋 **Especialidade:** ${data.doctorSpecialty}` : ''}\n⭐ **Médico verificado e qualificado**`,

      `💬 **ACESSE SEU ATENDIMENTO AGORA:**\n${chatUrl}\n\n*👆 Clique no link acima para iniciar ${magicUrl ? 'automaticamente' : ''} o chat com seu médico*`,

      `📋 **Durante a consulta:**\n\n• Descreva seus sintomas detalhadamente\n• Tire todas suas dúvidas\n• O médico pode solicitar fotos se necessário\n• Receitas serão enviadas digitalmente`,

      `⏰ **Importante:** O médico está te esperando! Acesse o link agora para não perder sua vez na fila.`,

      magicUrl ?
        `✨ **Acesso facilitado:** Este link te dará acesso direto à consulta sem precisar fazer login!` :
        `🔑 **Login:** Você pode precisar fazer login na plataforma para acessar a consulta.`,

      `💚 **Bom atendimento! Estamos aqui para cuidar de você.**`
    ];

    const evolutionService = new EvolutionService();
    const formattedPhone = formatPhoneForWhatsApp(data.patientPhone);

    const responses = await evolutionService.sendMessagesWithDelay(messages, formattedPhone);
    const successCount = responses.filter(r => r && r.key && r.key.id).length;

    return {
      success: successCount > 0,
      messagesTotal: messages.length,
      messagesSuccess: successCount,
      magicLinkGenerated: !!magicUrl
    };

  } catch (error) {
    console.error('[PLANTAO_WHATSAPP] Erro ao enviar mensagem de médico aceito:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
