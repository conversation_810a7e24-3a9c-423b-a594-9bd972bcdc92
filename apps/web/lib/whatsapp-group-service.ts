import { EvolutionService } from '../actions/checkout/integrations/evolution/evolution.service';

/**
 * Serviço para enviar mensagens para grupos WhatsApp
 */
export class WhatsAppGroupService {
  private evolutionService: EvolutionService;

  constructor() {
    this.evolutionService = new EvolutionService();
  }

  /**
   * Envia mensagem para grupo de médicos sobre novo plantão
   */
  async sendToMedicalGroup(appointmentData: {
    appointmentId: string;
    patientName: string;
    urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
    scheduledAt?: Date;
  }) {
    try {
      console.log('[WHATSAPP_GROUP] Enviando para grupo de médicos:', appointmentData);

      // Verificar variáveis de ambiente
      const groupChatId = process.env.WHATSAPP_DOCTORS_GROUP_ID;
      if (!groupChatId) {
        console.warn('[WHATSAPP_GROUP] WHATSAPP_DOCTORS_GROUP_ID não configurado');
        return {
          success: false,
          error: 'Grupo de médicos não configurado',
          fallback: 'Enviando apenas notificações individuais'
        };
      }

      // Mapear nível de urgência
      const urgencyLabels = {
        HIGH: '🔴 MUITO URGENTE',
        MEDIUM: '🟡 URGENTE',
        LOW: '🟢 POUCO URGENTE'
      };

      const urgencyEmojis = {
        HIGH: '🚨🚨🚨',
        MEDIUM: '⚠️⚠️',
        LOW: '📋'
      };

      const urgencyLabel = urgencyLabels[appointmentData.urgencyLevel];
      const urgencyEmoji = urgencyEmojis[appointmentData.urgencyLevel];

      // Preparar mensagem única consolidada para o grupo
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com';
      const plantaoUrl = `${baseUrl}/app/plantao`;

      const now = new Date();
      const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

      // Mensagem única consolidada
      const consolidatedMessage = `${urgencyEmoji} *NOVO PLANTÃO DISPONÍVEL* ${urgencyEmoji}

👤 **Paciente:** ${appointmentData.patientName}
🚨 **Urgência:** ${urgencyLabel}
⏰ **Horário:** ${timeStr}

🔗 **Link para atendimento:**
${plantaoUrl}

📱 *Médicos online, verifiquem a fila de plantão!*`;

      // Enviar uma única mensagem para o grupo
      const result = await this.evolutionService.sendGroupMessage(consolidatedMessage, groupChatId);

      const success = result && result.key && result.key.id;
      const totalMessages = 1; // Agora enviamos apenas 1 mensagem consolidada

      console.log('[WHATSAPP_GROUP] Resultado do envio para grupo de médicos:', {
        groupId: groupChatId,
        success,
        totalMessages
      });

      return {
        success,
        messagesCount: totalMessages,
        successCount: success ? 1 : 0,
        groupId: groupChatId
      };

    } catch (error) {
      console.error('[WHATSAPP_GROUP] Erro ao enviar para grupo de médicos:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        messagesCount: 0,
        successCount: 0
      };
    }
  }

  /**
   * Envia mensagem para grupo administrativo
   */
  async sendToAdminGroup(appointmentData: {
    appointmentId: string;
    patientName: string;
    urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
    scheduledAt?: Date;
  }) {
    try {
      console.log('[WHATSAPP_GROUP] Enviando para grupo admin:', appointmentData);

      // Verificar variáveis de ambiente
      const groupChatId = process.env.WHATSAPP_ADMIN_GROUP_ID;
      if (!groupChatId) {
        console.warn('[WHATSAPP_GROUP] WHATSAPP_ADMIN_GROUP_ID não configurado');
        return {
          success: false,
          error: 'Grupo admin não configurado'
        };
      }

      // Preparar mensagem administrativa consolidada
      const now = new Date();
      const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
      const dateStr = now.toLocaleDateString('pt-BR');

      // Mensagem única consolidada para admin
      const consolidatedMessage = `📊 *NOTIFICAÇÃO ADMINISTRATIVA - PLANTÃO*

👤 **Paciente:** ${appointmentData.patientName}
🚨 **Urgência:** ${appointmentData.urgencyLevel}
📅 **Data:** ${dateStr}
⏰ **Horário:** ${timeStr}
🆔 **ID:** ${appointmentData.appointmentId}

📋 *Sistema funcionando normalmente*`;

      // Enviar uma única mensagem para o grupo admin
      const result = await this.evolutionService.sendGroupMessage(consolidatedMessage, groupChatId);

      const success = result && result.key && result.key.id;

      return {
        success,
        messagesCount: 1, // Agora enviamos apenas 1 mensagem consolidada
        successCount: success ? 1 : 0
      };

    } catch (error) {
      console.error('[WHATSAPP_GROUP] Erro ao enviar para grupo admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
