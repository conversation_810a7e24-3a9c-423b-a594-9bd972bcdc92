import { MessageType } from "@prisma/client";

/**
 * Format message preview for display in the chat list
 */
export function formatMessagePreview(content: string, type: MessageType): string {
  switch (type) {
    case 'TEXT':
      return content.length > 50 ? `${content.slice(0, 50)}...` : content;
    case 'AUDIO':
      return '🎤 Mensagem de áudio';
    case 'FILE':
      return '📎 Arquivo anexado';
    case 'SYSTEM':
      return content;
    default:
      return content;
  }
}

/**
 * Get file type icon based on content type
 */
export function getFileIcon(contentType: string): string {
  if (contentType.startsWith('image/')) {
    return '🖼️';
  } else if (contentType.startsWith('audio/')) {
    return '🎵';
  } else if (contentType.startsWith('video/')) {
    return '🎬';
  } else if (contentType.includes('pdf')) {
    return '📄';
  } else if (contentType.includes('word') || contentType.includes('document')) {
    return '📝';
  } else if (contentType.includes('excel') || contentType.includes('spreadsheet')) {
    return '📊';
  } else if (contentType.includes('powerpoint') || contentType.includes('presentation')) {
    return '📑';
  } else {
    return '📁';
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  } else {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  }
}
