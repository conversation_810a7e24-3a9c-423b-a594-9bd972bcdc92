/**
 * Helper function to get the complete image URL from a path or url
 *
 * @param imagePath The image path or URL
 * @returns The complete URL to the image
 */
export const getCompleteImageUrl = (imagePath: string | null) => {
	if (!imagePath) return "";

	// If already a full URL with signature parameters, return it
	if (imagePath.startsWith("http") && imagePath.includes("X-Amz-Signature")) return imagePath;

	// If it's already an http URL but without signature, just return it (for external images)
	if (imagePath.startsWith("http")) return imagePath;

	// For paths without the bucket endpoint, we need to construct a URL
	// This should match how the UserProfileDesktop component does it
	// Note: Ideally this should fetch signed URLs from the server

	// Use the Supabase storage endpoint directly
	const bucketUrl = process.env.NEXT_PUBLIC_S3_ENDPOINT || "https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/";
	const bucketName = process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME || "avatars";

	// In production, this would generate a signed URL using a server action or API
	// For now, we'll use a direct URL, which may require public access to be enabled on the bucket
	return `${bucketUrl}/${bucketName}/${imagePath}`;
};
