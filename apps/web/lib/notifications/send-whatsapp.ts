"use server";

import { EvolutionService } from "../../actions/checkout/integrations/evolution/evolution.service";
import { formatPhoneForWhatsApp } from "../utils/format-phone";

export async function sendWhatsAppNotification(
  appointmentId: string,
  patientPhone: string,
  patientName: string,
  doctorName: string,
  appointmentDate: Date,
  appointmentTime: string,
  appointmentType: "CONSULTATION" | "PLANTAO"
): Promise<{ success: boolean; error?: string }> {
  if (process.env.NODE_ENV === 'development') {
    console.log("[WHATSAPP_DEBUG] Function called with data:", {
      appointmentId,
      patientPhone,
      patientName,
      doctorName,
      appointmentDate,
      appointmentTime,
      appointmentType
    });
  }

  try {
    // Validar dados de entrada
    if (!appointmentId || !patientPhone || !patientName || !doctorName || !appointmentDate || !appointmentTime) {
      throw new Error("Dados obrigatórios não fornecidos");
    }

    // Formatar telefone para WhatsApp
    let phone = patientPhone.replace(/\D/g, "");

    // Adicionar código do país se não estiver presente
    if (!phone.startsWith("55")) {
      phone = "55" + phone;
    }

    // Formatar data
    const formattedDate = appointmentDate.toLocaleDateString("pt-BR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric"
    });

    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Formatted date:", formattedDate);
    }

    // Preparar mensagens baseadas no tipo de consulta
    let messages: string[] = [];

    if (appointmentType === "CONSULTATION") {
      messages = [
        `Olá ${patientName}! 👋`,
        "",
        `Sua consulta com o Dr. ${doctorName} foi confirmada para ${formattedDate} às ${appointmentTime}.`,
        "",
        "📋 **Informações importantes:**",
        "• Chegue com 10 minutos de antecedência",
        "• Traga seus documentos e exames",
        "• Em caso de cancelamento, avise com 24h de antecedência",
        "",
        "🏥 **Local:** Consultório Dr. ${doctorName}",
        "",
        "❓ **Dúvidas?** Entre em contato conosco.",
        "",
        "Agradecemos sua confiança! 🙏",
        "",
        "Até lá! 👨‍⚕️💙"
      ];
    } else if (appointmentType === "PLANTAO") {
      messages = [
        `Olá ${patientName}! 👋`,
        "",
        `Seu plantão com o Dr. ${doctorName} foi confirmado para ${formattedDate} às ${appointmentTime}.`,
        "",
        "🚨 **Plantão - Atendimento de Emergência:**",
        "• Chegue imediatamente ao hospital",
        "• Traga documentos pessoais",
        "• Este é um atendimento prioritário",
        "",
        "🏥 **Local:** Hospital - Plantão",
        "",
        "⚠️ **Importante:** Em caso de plantão, não há necessidade de agendamento prévio.",
        "",
        "Agradecemos sua compreensão! 🙏",
        "",
        "Até lá! 👨‍⚕️💙"
      ];
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Messages prepared:", messages);
    }

    // Formatar telefone para WhatsApp
    const whatsappPhone = phone + "@c.us";

    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Formatted phone for WhatsApp:", phone);
    }

    // Inicializar serviço Evolution
    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Initializing Evolution service");
    }

    const evolutionService = new EvolutionService();

    // Enviar mensagens com delay para evitar spam
    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Sending messages with delay");
    }

    const responses = await evolutionService.sendMessagesWithDelay(whatsappPhone, messages);

    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DEBUG] Messages sent successfully:", responses.length);
    }

    return { success: true };

  } catch (error) {
    console.error("[WHATSAPP_ERROR] Error sending WhatsApp notification:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
}

export async function sendWhatsAppNotificationToDoctor(
  appointmentId: string,
  doctorPhone: string,
  doctorName: string,
  patientName: string,
  appointmentDate: Date,
  appointmentTime: string,
  appointmentType: "CONSULTATION" | "PLANTAO"
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validar dados de entrada
    if (!appointmentId || !doctorPhone || !doctorName || !patientName || !appointmentDate || !appointmentTime) {
      throw new Error("Dados obrigatórios não fornecidos");
    }

    // Formatar telefone para WhatsApp
    let phone = doctorPhone.replace(/\D/g, "");

    // Adicionar código do país se não estiver presente
    if (!phone.startsWith("55")) {
      phone = "55" + phone;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log("[WHATSAPP_DOCTOR_DEBUG] Formatted doctor phone for WhatsApp:", phone);
    }
