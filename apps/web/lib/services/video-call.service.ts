import { Room, RoomEvent, RemoteTrack, Track, TrackPublication } from 'livekit-client';

export interface VideoCallParticipant {
  id: string;
  name: string;
  role: 'doctor' | 'patient';
  isLocal: boolean;
  audioTrack?: RemoteTrack;
  videoTrack?: RemoteTrack;
  audioEnabled: boolean;
  videoEnabled: boolean;
}

export interface VideoCallStatus {
  status: 'idle' | 'connecting' | 'connected' | 'disconnected' | 'error';
  participants: VideoCallParticipant[];
  localAudioEnabled: boolean;
  localVideoEnabled: boolean;
  roomName?: string;
  error?: string;
}

export interface VideoCallOptions {
  audio?: boolean;
  video?: boolean;
  screen?: boolean;
}

export class VideoCallService {
  private room: Room | null = null;
  private status: VideoCallStatus = {
    status: 'idle',
    participants: [],
    localAudioEnabled: true,
    localVideoEnabled: true
  };
  private statusHandlers: ((status: VideoCallStatus) => void)[] = [];
  private isDestroyed = false;

  constructor() {
    console.log('[VideoCall] Serviço inicializado');
  }

  /**
   * Criar uma nova sala de vídeo chamada
   */
  async createRoom(appointmentId: string, userId: string): Promise<{ roomName: string; token: string }> {
    try {
      console.log(`[VideoCall] Criando sala para appointment ${appointmentId}`);

      const response = await fetch('/api/video-calls/create-room', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId,
          userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.roomName || !data.token) {
        throw new Error('Resposta inválida do servidor');
      }

      console.log(`[VideoCall] Sala criada: ${data.roomName}`);
      return data;
    } catch (error) {
      console.error('[VideoCall] Erro ao criar sala:', error);
      throw new Error(`Falha ao criar sala: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Entrar em uma sala de vídeo chamada
   */
  async joinRoom(
    roomName: string,
    token: string,
    options: VideoCallOptions = { audio: true, video: true }
  ): Promise<void> {
    if (this.room) {
      throw new Error('Já conectado a uma sala');
    }

    this.updateStatus({ status: 'connecting' });

    try {
      this.room = new Room({
        adaptiveStream: true,
        dynacast: true,
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        videoCaptureDefaults: {
          resolution: {
            width: 1280,
            height: 720,
          },
          frameRate: 30,
        },
      });

      this.setupRoomEvents();

      const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;
      if (!serverUrl) {
        throw new Error('URL do LiveKit não configurada');
      }

      // Preparar URL do servidor com protocolo correto
      const formattedServerUrl = serverUrl.startsWith("wss://")
        ? serverUrl
        : `wss://${serverUrl}`;

      await this.room.connect(formattedServerUrl, token);

      // Ativar faixas locais se solicitado
      if (options.audio) {
        await this.room.localParticipant.setMicrophoneEnabled(true);
      }

      if (options.video) {
        await this.room.localParticipant.setCameraEnabled(true);
      }

      this.updateStatus({
        status: 'connected',
        roomName,
        localAudioEnabled: options.audio ?? true,
        localVideoEnabled: options.video ?? true,
      });

      console.log(`[VideoCall] Conectado à sala: ${roomName}`);
    } catch (error) {
      console.error('[VideoCall] Erro ao conectar à sala:', error);
      this.updateStatus({
        status: 'error',
        error: error instanceof Error ? error.message : 'Erro de conexão',
      });
      throw error;
    }
  }

  /**
   * Sair da sala de vídeo chamada
   */
  async leaveRoom(): Promise<void> {
    if (!this.room) {
      return;
    }

    try {
      console.log('[VideoCall] Saindo da sala...');
      await this.room.disconnect();
      this.room = null;

      this.updateStatus({
        status: 'disconnected',
        participants: [],
        localAudioEnabled: true,
        localVideoEnabled: true,
      });
    } catch (error) {
      console.error('[VideoCall] Erro ao sair da sala:', error);
      throw error;
    }
  }

  /**
   * Alternar áudio local
   */
  async toggleAudio(): Promise<boolean> {
    if (!this.room) {
      throw new Error('Não conectado a uma sala');
    }

    try {
      const enabled = !this.status.localAudioEnabled;
      await this.room.localParticipant.setMicrophoneEnabled(enabled);

      this.updateStatus({ localAudioEnabled: enabled });
      console.log(`[VideoCall] Áudio ${enabled ? 'ativado' : 'desativado'}`);

      return enabled;
    } catch (error) {
      console.error('[VideoCall] Erro ao alternar áudio:', error);
      throw error;
    }
  }

  /**
   * Alternar vídeo local
   */
  async toggleVideo(): Promise<boolean> {
    if (!this.room) {
      throw new Error('Não conectado a uma sala');
    }

    try {
      const enabled = !this.status.localVideoEnabled;
      await this.room.localParticipant.setCameraEnabled(enabled);

      this.updateStatus({ localVideoEnabled: enabled });
      console.log(`[VideoCall] Vídeo ${enabled ? 'ativado' : 'desativado'}`);

      return enabled;
    } catch (error) {
      console.error('[VideoCall] Erro ao alternar vídeo:', error);
      throw error;
    }
  }

  /**
   * Compartilhar tela
   */
  async shareScreen(): Promise<void> {
    if (!this.room) {
      throw new Error('Não conectado a uma sala');
    }

    try {
      await this.room.localParticipant.setScreenShareEnabled(true);
      console.log('[VideoCall] Compartilhamento de tela iniciado');
    } catch (error) {
      console.error('[VideoCall] Erro ao compartilhar tela:', error);
      throw error;
    }
  }

  /**
   * Parar compartilhamento de tela
   */
  async stopScreenShare(): Promise<void> {
    if (!this.room) {
      throw new Error('Não conectado a uma sala');
    }

    try {
      await this.room.localParticipant.setScreenShareEnabled(false);
      console.log('[VideoCall] Compartilhamento de tela parado');
    } catch (error) {
      console.error('[VideoCall] Erro ao parar compartilhamento de tela:', error);
      throw error;
    }
  }

  /**
   * Obter status atual da chamada
   */
  getStatus(): VideoCallStatus {
    return { ...this.status };
  }

  /**
   * Registrar handler para mudanças de status
   */
  onStatusChange(handler: (status: VideoCallStatus) => void): () => void {
    this.statusHandlers.push(handler);

    // Enviar status atual imediatamente
    handler({ ...this.status });

    return () => {
      const index = this.statusHandlers.indexOf(handler);
      if (index > -1) {
        this.statusHandlers.splice(index, 1);
      }
    };
  }

  /**
   * Destruir o serviço
   */
  destroy(): void {
    this.isDestroyed = true;

    if (this.room) {
      this.room.disconnect();
      this.room = null;
    }

    this.statusHandlers = [];
    this.updateStatus({ status: 'idle', participants: [] });
  }

  private setupRoomEvents(): void {
    if (!this.room) return;

    this.room.on(RoomEvent.Connected, () => {
      console.log('[VideoCall] Conectado à sala');
      this.updateParticipants();
    });

    this.room.on(RoomEvent.Disconnected, (reason) => {
      console.log('[VideoCall] Desconectado da sala:', reason);
      this.updateStatus({ status: 'disconnected', participants: [] });
    });

    this.room.on(RoomEvent.ParticipantConnected, (participant) => {
      console.log('[VideoCall] Participante conectado:', participant.identity);
      this.updateParticipants();
    });

    this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      console.log('[VideoCall] Participante desconectado:', participant.identity);
      this.updateParticipants();
    });

    this.room.on(RoomEvent.TrackSubscribed, (track: RemoteTrack, publication: TrackPublication) => {
      console.log('[VideoCall] Track subscribed:', track.kind);
      this.updateParticipants();
    });

    this.room.on(RoomEvent.TrackUnsubscribed, (track: RemoteTrack, publication: TrackPublication) => {
      console.log('[VideoCall] Track unsubscribed:', track.kind);
      this.updateParticipants();
    });

    this.room.on(RoomEvent.LocalTrackPublished, (publication: TrackPublication) => {
      console.log('[VideoCall] Local track published:', publication.kind);
      this.updateParticipants();
    });

    this.room.on(RoomEvent.LocalTrackUnpublished, (publication: TrackPublication) => {
      console.log('[VideoCall] Local track unpublished:', publication.kind);
      this.updateParticipants();
    });
  }

  private updateParticipants(): void {
    if (!this.room) return;

    const participants: VideoCallParticipant[] = [];

    // Adicionar participante local
    const localParticipant = this.room.localParticipant;
    participants.push({
      id: localParticipant.identity,
      name: localParticipant.name || 'Você',
      role: 'doctor', // Determinar baseado no contexto
      isLocal: true,
      audioEnabled: localParticipant.isMicrophoneEnabled,
      videoEnabled: localParticipant.isCameraEnabled,
    });

    // Adicionar participantes remotos
    this.room.remoteParticipants.forEach((participant) => {
      const audioTrack = participant.getTrack(Track.Source.Microphone)?.track as RemoteTrack;
      const videoTrack = participant.getTrack(Track.Source.Camera)?.track as RemoteTrack;

      participants.push({
        id: participant.identity,
        name: participant.name || 'Participante',
        role: 'patient', // Determinar baseado no contexto
        isLocal: false,
        audioTrack,
        videoTrack,
        audioEnabled: audioTrack?.isEnabled ?? false,
        videoEnabled: videoTrack?.isEnabled ?? false,
      });
    });

    this.updateStatus({ participants });
  }

  private updateStatus(updates: Partial<VideoCallStatus>): void {
    this.status = { ...this.status, ...updates };

    if (!this.isDestroyed) {
      this.statusHandlers.forEach(handler => {
        try {
          handler({ ...this.status });
        } catch (error) {
          console.error('[VideoCall] Erro ao notificar handler de status:', error);
        }
      });
    }
  }
}

// Singleton instance
let videoCallService: VideoCallService | null = null;

export function getVideoCallService(): VideoCallService {
  if (!videoCallService) {
    videoCallService = new VideoCallService();
  }
  return videoCallService;
}

export function destroyVideoCallService(): void {
  if (videoCallService) {
    videoCallService.destroy();
    videoCallService = null;
  }
}
