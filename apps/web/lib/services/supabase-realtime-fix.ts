import { createClient } from '@supabase/supabase-js';
import { getSupabaseConfig } from '../config/supabase.config';

export interface RealtimeDiagnosticResult {
  success: boolean;
  issues: string[];
  recommendations: string[];
  canConnect: boolean;
  realtimeEnabled: boolean;
  tablesConfigured: string[];
}

export class SupabaseRealtimeFixer {
  private client: SupabaseClient;

  constructor() {
    const config = getSupabaseConfig();
    this.client = createClient(config.url, config.anonKey);
  }

  /**
   * Diagnóstico completo da configuração do Supabase Realtime
   */
  async diagnoseRealtime(): Promise<RealtimeDiagnosticResult> {
    const result: RealtimeDiagnosticResult = {
      success: false,
      issues: [],
      recommendations: [],
      canConnect: false,
      realtimeEnabled: false,
      tablesConfigured: []
    };

    try {
      // 1. Testar conexão básica
      console.log('[RealtimeFixer] Testando conexão básica...');
      const { data: sessionData, error: sessionError } = await this.client.auth.getSession();

      if (sessionError) {
        result.issues.push(`Erro de autenticação: ${sessionError.message}`);
        result.recommendations.push('Verificar se as chaves do Supabase estão corretas');
        result.recommendations.push('Verificar se o projeto está ativo');
      } else {
        result.canConnect = true;
        console.log('[RealtimeFixer] Conexão básica OK');
      }

      // 2. Testar realtime básico
      console.log('[RealtimeFixer] Testando realtime básico...');
      const realtimeTest = await this.testBasicRealtime();
      if (realtimeTest.success) {
        result.realtimeEnabled = true;
        console.log('[RealtimeFixer] Realtime básico OK');
      } else {
        result.issues.push(`Realtime não funcional: ${realtimeTest.error}`);
        result.recommendations.push('Verificar configuração do realtime no dashboard do Supabase');
        result.recommendations.push('Verificar se a tabela messages está na publicação supabase_realtime');
      }

      // 3. Testar canal específico para mensagens
      console.log('[RealtimeFixer] Testando canal de mensagens...');
      const messageChannelTest = await this.testMessageChannel();
      if (messageChannelTest.success) {
        result.tablesConfigured.push('messages');
        console.log('[RealtimeFixer] Canal de mensagens OK');
      } else {
        result.issues.push(`Canal de mensagens falhou: ${messageChannelTest.error}`);
        result.recommendations.push('Executar SQL para configurar realtime para a tabela messages');
        result.recommendations.push('Verificar permissões RLS na tabela messages');
      }

      // 4. Verificar configurações específicas
      const configIssues = await this.checkSpecificConfigurations();
      result.issues.push(...configIssues);

      // Determinar sucesso geral
      result.success = result.canConnect && result.realtimeEnabled && result.tablesConfigured.includes('messages');

      if (result.success) {
        console.log('[RealtimeFixer] Diagnóstico completo: TUDO OK');
      } else {
        console.log('[RealtimeFixer] Diagnóstico completo: PROBLEMAS ENCONTRADOS');
        console.log('[RealtimeFixer] Issues:', result.issues);
        console.log('[RealtimeFixer] Recomendações:', result.recommendations);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      result.issues.push(`Erro durante diagnóstico: ${errorMessage}`);
      result.recommendations.push('Verificar logs do console para mais detalhes');
      console.error('[RealtimeFixer] Erro durante diagnóstico:', error);
    }

    return result;
  }

  /**
   * Testar realtime básico
   */
  private async testBasicRealtime(): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      try {
        const testChannel = this.client.channel('test-realtime');
        const subscription = testChannel.subscribe();

        const timeout = setTimeout(() => {
          testChannel.unsubscribe();
          resolve({ success: false, error: 'Timeout ao testar realtime' });
        }, 10000);

        subscription.subscribe((status) => {
          clearTimeout(timeout);
          testChannel.unsubscribe();

          if (status === 'SUBSCRIBED') {
            resolve({ success: true });
          } else if (status === 'CHANNEL_ERROR') {
            resolve({ success: false, error: 'CHANNEL_ERROR ao testar realtime' });
          } else if (status === 'TIMED_OUT') {
            resolve({ success: false, error: 'TIMED_OUT ao testar realtime' });
          } else {
            resolve({ success: false, error: `Status inesperado: ${status}` });
          }
        });
      } catch (error) {
        resolve({
          success: false,
          error: error instanceof Error ? error.message : 'Erro ao criar canal de teste'
        });
      }
    });
  }

  /**
   * Testar canal específico para mensagens
   */
  private async testMessageChannel(): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      try {
        const testChannel = this.client
          .channel('test-messages')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'messages'
            },
            () => {
              // Não esperamos mensagens durante o teste
            }
          )
          .subscribe();

        const timeout = setTimeout(() => {
          testChannel.unsubscribe();
          resolve({ success: false, error: 'Timeout ao testar canal de mensagens' });
        }, 10000);

        testChannel.subscribe((status) => {
          clearTimeout(timeout);
          testChannel.unsubscribe();

          if (status === 'SUBSCRIBED') {
            resolve({ success: true });
          } else if (status === 'CHANNEL_ERROR') {
            resolve({ success: false, error: 'CHANNEL_ERROR no canal de mensagens' });
          } else if (status === 'TIMED_OUT') {
            resolve({ success: false, error: 'TIMED_OUT no canal de mensagens' });
          } else {
            resolve({ success: false, error: `Status inesperado no canal de mensagens: ${status}` });
          }
        });
      } catch (error) {
        resolve({
          success: false,
          error: error instanceof Error ? error.message : 'Erro ao criar canal de mensagens'
        });
      }
    });
  }

  /**
   * Verificar configurações específicas
   */
  private async checkSpecificConfigurations(): Promise<string[]> {
    const issues: string[] = [];

    try {
      // Verificar se estamos em ambiente de desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.log('[RealtimeFixer] Ambiente de desenvolvimento detectado');

        // Verificar se as variáveis de ambiente estão configuradas
        const config = getSupabaseConfig();
        if (!config.url.includes('supabase.co')) {
          issues.push('URL do Supabase parece inválida - verificar NEXT_PUBLIC_SUPABASE_URL');
        }

        if (config.anonKey.length < 50) {
          issues.push('Chave anônima parece inválida - verificar NEXT_PUBLIC_SUPABASE_ANON_KEY');
        }
      }

      // Verificar se o navegador suporta WebSocket
      if (typeof WebSocket === 'undefined') {
        issues.push('WebSocket não suportado pelo navegador');
      }

      // Verificar se estamos online
      if (!navigator.onLine) {
        issues.push('Navegador está offline');
      }

    } catch (error) {
      issues.push(`Erro ao verificar configurações: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }

    return issues;
  }

  /**
   * Gerar relatório de diagnóstico
   */
  async generateDiagnosticReport(): Promise<string> {
    const diagnostic = await this.diagnoseRealtime();

    let report = `# 🔍 Relatório de Diagnóstico do Supabase Realtime\n\n`;
    report += `**Data/Hora:** ${new Date().toLocaleString()}\n`;
    report += `**Status Geral:** ${diagnostic.success ? '✅ FUNCIONANDO' : '❌ PROBLEMAS'}\n\n`;

    report += `## 📊 Resumo\n`;
    report += `- **Conexão:** ${diagnostic.canConnect ? '✅ OK' : '❌ FALHOU'}\n`;
    report += `- **Realtime:** ${diagnostic.realtimeEnabled ? '✅ OK' : '❌ FALHOU'}\n`;
    report += `- **Tabelas Configuradas:** ${diagnostic.tablesConfigured.join(', ') || 'Nenhuma'}\n\n`;

    if (diagnostic.issues.length > 0) {
      report += `## 🚨 Problemas Identificados\n`;
      diagnostic.issues.forEach(issue => {
        report += `- ${issue}\n`;
      });
      report += `\n`;
    }

    if (diagnostic.recommendations.length > 0) {
      report += `## 💡 Recomendações\n`;
      diagnostic.recommendations.forEach(rec => {
        report += `- ${rec}\n`;
      });
      report += `\n`;
    }

    report += `## 🛠️ Próximos Passos\n`;
    if (diagnostic.success) {
      report += `✅ O realtime está funcionando corretamente!\n`;
    } else {
      report += `1. Verificar variáveis de ambiente\n`;
      report += `2. Verificar configuração no dashboard do Supabase\n`;
      report += `3. Executar scripts de configuração se necessário\n`;
      report += `4. Testar novamente após correções\n`;
    }

    return report;
  }

  /**
   * Tentar corrigir problemas automaticamente
   */
  async attemptAutoFix(): Promise<{ success: boolean; actions: string[] }> {
    const actions: string[] = [];
    let success = false;

    try {
      console.log('[RealtimeFixer] Tentando correção automática...');

      // 1. Verificar se é um problema de configuração
      const diagnostic = await this.diagnoseRealtime();

      if (!diagnostic.canConnect) {
        actions.push('❌ Não é possível corrigir problemas de conexão automaticamente');
        actions.push('Verificar variáveis de ambiente e status do projeto');
        return { success: false, actions };
      }

      // 2. Tentar reconectar
      if (!diagnostic.realtimeEnabled) {
        actions.push('🔄 Tentando reconectar ao realtime...');
        try {
          // Forçar nova instância do cliente
          const config = getSupabaseConfig();
          const newClient = createClient(config.url, config.anonKey, {
            realtime: {
              params: { eventsPerSecond: 10 },
              config: { presence: { key: 'zapchat' } }
            }
          });

          // Testar nova conexão
          const testResult = await this.testBasicRealtime();
          if (testResult.success) {
            actions.push('✅ Reconexão bem-sucedida');
            success = true;
          } else {
            actions.push(`❌ Reconexão falhou: ${testResult.error}`);
          }
        } catch (error) {
          actions.push(`❌ Erro durante reconexão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        }
      }

      // 3. Se ainda houver problemas, sugerir ações manuais
      if (!success) {
        actions.push('📋 Correção automática não foi possível');
        actions.push('Executar diagnóstico completo e seguir recomendações');
      }

    } catch (error) {
      actions.push(`❌ Erro durante correção automática: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }

    return { success, actions };
  }
}

// Função de conveniência para diagnóstico rápido
export async function quickRealtimeDiagnostic(): Promise<RealtimeDiagnosticResult> {
  const fixer = new SupabaseRealtimeFixer();
  return fixer.diagnoseRealtime();
}

// Função para gerar relatório
export async function generateRealtimeReport(): Promise<string> {
  const fixer = new SupabaseRealtimeFixer();
  return fixer.generateDiagnosticReport();
}
