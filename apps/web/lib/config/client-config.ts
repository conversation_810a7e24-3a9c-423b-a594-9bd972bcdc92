/**
 * Configuração do cliente que força o carregamento das variáveis de ambiente
 * Este arquivo é executado no browser para garantir que as variáveis estejam disponíveis
 */

// Forçar o carregamento das variáveis de ambiente
declare global {
  interface Window {
    __ENV__: {
      NEXT_PUBLIC_SUPABASE_URL: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
      NODE_ENV: string;
    };
  }
}

// Função para obter as variáveis de ambiente do cliente
export function getClientEnvVars() {
  // Tentar obter do process.env primeiro
  let supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  let supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  let nodeEnv = process.env.NODE_ENV;

  // Se não estiverem disponíveis no process.env, tentar do window.__ENV__
  if (typeof window !== 'undefined' && window.__ENV__) {
    if (!supabaseUrl) supabaseUrl = window.__ENV__.NEXT_PUBLIC_SUPABASE_URL;
    if (!supabaseKey) supabaseKey = window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    if (!nodeEnv) nodeEnv = window.__ENV__.NODE_ENV;
  }

  // Se ainda não estiverem disponíveis, usar valores hardcoded para desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    if (!supabaseUrl) {
      console.warn('⚠️ NEXT_PUBLIC_SUPABASE_URL não encontrada, usando valor padrão para desenvolvimento');
      supabaseUrl = 'https://moupvfqlulvqbzwajkif.supabase.co';
    }
    if (!supabaseKey) {
      console.warn('⚠️ NEXT_PUBLIC_SUPABASE_ANON_KEY não encontrada, usando valor padrão para desenvolvimento');
      supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vdXB2ZnFsdWx2cWJ6d2Fqa2lmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEyMTk2NDYsImV4cCI6MjA1Njc5NTY0Nn0.MFeB-phlRJBVc_a2ZeS-yUP6LOLc9C0L4jF6BIqv0i0';
    }
  }

  return {
    NEXT_PUBLIC_SUPABASE_URL: supabaseUrl,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: supabaseKey,
    NODE_ENV: nodeEnv || 'development'
  };
}

// Função para validar se as variáveis estão disponíveis
export function validateClientEnvVars(): { valid: boolean; missing: string[] } {
  const vars = getClientEnvVars();
  const missing: string[] = [];

  if (!vars.NEXT_PUBLIC_SUPABASE_URL) {
    missing.push('NEXT_PUBLIC_SUPABASE_URL');
  }
  if (!vars.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    missing.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
  }

  return {
    valid: missing.length === 0,
    missing
  };
}

// Função para obter configuração do Supabase
export function getClientSupabaseConfig() {
  const vars = getClientEnvVars();

  if (!vars.NEXT_PUBLIC_SUPABASE_URL || !vars.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error(
      'Supabase configuration missing. Please check your environment variables:\n' +
      `NEXT_PUBLIC_SUPABASE_URL: ${vars.NEXT_PUBLIC_SUPABASE_URL ? '✓' : '✗'}\n` +
      `NEXT_PUBLIC_SUPABASE_ANON_KEY: ${vars.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓' : '✗'}`
    );
  }

  return {
    url: vars.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: vars.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  };
}

// Log do status das variáveis no cliente
export function logClientEnvStatus(): void {
  const vars = getClientEnvVars();
  const { valid, missing } = validateClientEnvVars();

  console.log('🔧 Status das variáveis de ambiente (cliente):');
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${vars.NEXT_PUBLIC_SUPABASE_URL ? '✓' : '✗'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${vars.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓' : '✗'}`);
  console.log(`NODE_ENV: ${vars.NODE_ENV}`);
  console.log(`Status geral: ${valid ? '✅ Válido' : '❌ Inválido'}`);

  if (!valid) {
    console.error('❌ Variáveis faltando:', missing);
  }
}

// Executar verificação ao carregar o módulo no cliente
if (typeof window !== 'undefined') {
  logClientEnvStatus();
}
