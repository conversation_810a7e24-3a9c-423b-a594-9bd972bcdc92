/**
 * Carregador de variáveis de ambiente para o Next.js
 * Este arquivo força o carregamento das variáveis necessárias
 */

// Importar dotenv se estiver disponível
let dotenv: any = null;
try {
  dotenv = require('dotenv');
} catch (error) {
  // dotenv não está disponível, continuar sem ele
}

// Carregar variáveis de ambiente
if (dotenv) {
  // Tentar carregar do arquivo .env na raiz do projeto
  const result = dotenv.config({ path: '.env' });
  if (result.error) {
    console.warn('⚠️ Não foi possível carregar .env da raiz:', result.error.message);

    // Tentar carregar do diretório atual
    const localResult = dotenv.config();
    if (localResult.error) {
      console.warn('⚠️ Não foi possível carregar .env local:', localResult.error.message);
    }
  } else {
    console.log('✅ Arquivo .env carregado com sucesso');
  }
}

// Verificar se as variáveis estão disponíveis
export function checkRequiredEnvVars(): { valid: boolean; missing: string[] } {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missing: string[] = [];

  for (const varName of required) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }

  return {
    valid: missing.length === 0,
    missing
  };
}

// Log do status das variáveis
export function logEnvStatus(): void {
  const { valid, missing } = checkRequiredEnvVars();

  console.log('🔧 Status das variáveis de ambiente:');
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓' : '✗'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓' : '✗'}`);
  console.log(`SUPABASE_SERVICE_ROLE_KEY: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '✓' : '✗'}`);
  console.log(`Status geral: ${valid ? '✅ Válido' : '❌ Inválido'}`);

  if (!valid) {
    console.error('❌ Variáveis faltando:', missing);
  }
}

// Executar verificação ao carregar o módulo
logEnvStatus();
