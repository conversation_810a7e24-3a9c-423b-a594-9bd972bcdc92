/**
 * Verificação de variáveis de ambiente no lado do cliente
 * Este arquivo é executado no browser para verificar se as variáveis estão disponíveis
 */

export interface ClientEnvStatus {
  supabaseUrl: boolean;
  supabaseKey: boolean;
  isValid: boolean;
  error?: string;
}

export function checkClientEnvVars(): ClientEnvStatus {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    const status: ClientEnvStatus = {
      supabaseUrl: !!supabaseUrl,
      supabaseKey: !!supabaseKey,
      isValid: !!(supabaseUrl && supabaseKey)
    };

    if (!status.isValid) {
      status.error = `Variáveis de ambiente faltando: ${
        !supabaseUrl ? 'NEXT_PUBLIC_SUPABASE_URL' : ''
      }${
        !supabaseUrl && !supabaseKey ? ', ' : ''
      }${
        !supabaseKey ? 'NEXT_PUBLIC_SUPABASE_ANON_KEY' : ''
      }`;
    }

    return status;
  } catch (error) {
    return {
      supabaseUrl: false,
      supabaseKey: false,
      isValid: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

export function logClientEnvStatus(): void {
  const status = checkClientEnvVars();

  console.log('🔧 Status das variáveis de ambiente (cliente):');
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${status.supabaseUrl ? '✓' : '✗'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${status.supabaseKey ? '✓' : '✗'}`);
  console.log(`Status geral: ${status.isValid ? '✅ Válido' : '❌ Inválido'}`);

  if (!status.isValid && status.error) {
    console.error('❌ Erro:', status.error);
  }
}

// Executar verificação ao carregar o módulo
if (typeof window !== 'undefined') {
  logClientEnvStatus();
}
