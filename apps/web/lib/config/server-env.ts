/**
 * Configuração de variáveis de ambiente no lado do servidor
 * Este arquivo é executado no servidor para garantir que as variáveis sejam carregadas
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar variáveis de ambiente
try {
  // Tentar carregar do arquivo .env na raiz do projeto
  const envPath = resolve(process.cwd(), '.env');
  const result = config({ path: envPath });

  if (result.error) {
    console.warn('⚠️ Não foi possível carregar .env da raiz:', result.error.message);

    // Tentar carregar do diretório atual
    const localResult = config();
    if (localResult.error) {
      console.warn('⚠️ Não foi possível carregar .env local:', localResult.error.message);
    }
  } else {
    console.log('✅ Arquivo .env carregado com sucesso do servidor');
  }
} catch (error) {
  console.warn('⚠️ Erro ao carregar dotenv:', error);
}

// Verificar se as variáveis estão disponíveis
export function checkServerEnvVars(): { valid: boolean; missing: string[] } {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missing: string[] = [];

  for (const varName of required) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }

  return {
    valid: missing.length === 0,
    missing
  };
}

// Log do status das variáveis no servidor
export function logServerEnvStatus(): void {
  const { valid, missing } = checkServerEnvVars();

  console.log('🔧 Status das variáveis de ambiente (servidor):');
  console.log(`NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓' : '✗'}`);
  console.log(`NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓' : '✗'}`);
  console.log(`SUPABASE_SERVICE_ROLE_KEY: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '✓' : '✗'}`);
  console.log(`Status geral: ${valid ? '✅ Válido' : '❌ Inválido'}`);

  if (!valid) {
    console.error('❌ Variáveis faltando:', missing);
  }
}

// Executar verificação ao carregar o módulo no servidor
if (typeof window === 'undefined') {
  logServerEnvStatus();
}
