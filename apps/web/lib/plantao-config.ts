/**
 * Configurações para WhatsApp Groups do Plantão
 *
 * Para configurar os grupos WhatsApp:
 *
 * 1. Adicione as seguintes variáveis no seu .env:
 *
 *    # Grupo WhatsApp para notificar médicos sobre novos plantões
 *    WHATSAPP_DOCTORS_GROUP_ID="<EMAIL>"
 *
 *    # Grupo WhatsApp para notificações administrativas/enfermagem
 *    WHATSAPP_ADMIN_GROUP_ID="<EMAIL>"
 *
 *    # Configurações Evolution API (já existentes)
 *    EVOLUTION_API_KEY="your_api_key"
 *    EVOLUTION_INSTANCE="your_instance"
 *    EVOLUTION_URL="https://your-evolution-url"
 *
 * 2. Como obter o Group ID:
 *    - Adicione o bot Evolution no grupo
 *    - Envie uma mensagem no grupo
 *    - Verifique nos logs da Evolution API o campo "remoteJid"
 *    - Use este valor como Group ID
 *
 * 3. Funcionalidades implementadas:
 *    - Notificação automática para grupo de médicos quando novo plantão é criado
 *    - Mensagens personalizadas por nível de urgência (HIGH/MEDIUM/LOW)
 *    - Notificações administrativas para controle
 *    - Mensagens aprimoradas para pacientes com estimativa de tempo
 */

export interface PlantaoConfig {
  // Grupos WhatsApp
  doctorsGroupId: string | null;
  adminGroupId: string | null;

  // URLs
  siteUrl: string;

  // Configurações de tempo
  urgencyWaitTimes: {
    HIGH: string;
    MEDIUM: string;
    LOW: string;
  };

  // Configurações Evolution
  evolutionConfigured: boolean;
}

export function getPlantaoConfig(): PlantaoConfig {
  return {
    doctorsGroupId: process.env.WHATSAPP_DOCTORS_GROUP_ID || null,
    adminGroupId: process.env.WHATSAPP_ADMIN_GROUP_ID || null,
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com',
    urgencyWaitTimes: {
      HIGH: '5-10 minutos',
      MEDIUM: '15-25 minutos',
      LOW: '20-40 minutos'
    },
    evolutionConfigured: !!(
      process.env.EVOLUTION_API_KEY &&
      process.env.EVOLUTION_INSTANCE &&
      process.env.EVOLUTION_URL
    )
  };
}

/**
 * Verifica se todas as configurações necessárias estão disponíveis
 */
export function validatePlantaoConfig(): {
  isValid: boolean;
  missingConfigs: string[];
  warnings: string[];
} {
  const config = getPlantaoConfig();
  const missing: string[] = [];
  const warnings: string[] = [];

  // Verificar configurações obrigatórias da Evolution
  if (!process.env.EVOLUTION_API_KEY) missing.push('EVOLUTION_API_KEY');
  if (!process.env.EVOLUTION_INSTANCE) missing.push('EVOLUTION_INSTANCE');
  if (!process.env.EVOLUTION_URL) missing.push('EVOLUTION_URL');

  // Verificar configurações opcionais (mas recomendadas)
  if (!config.doctorsGroupId) {
    warnings.push('WHATSAPP_DOCTORS_GROUP_ID não configurado - grupos de médicos não funcionarão');
  }

  if (!config.adminGroupId) {
    warnings.push('WHATSAPP_ADMIN_GROUP_ID não configurado - notificações admin não funcionarão');
  }

  return {
    isValid: missing.length === 0,
    missingConfigs: missing,
    warnings
  };
}

/**
 * Log das configurações atuais (sem expor chaves sensíveis)
 */
export function logPlantaoConfig() {
  const config = getPlantaoConfig();
  const validation = validatePlantaoConfig();

  console.log('[PLANTAO_CONFIG] Configurações atuais:', {
    siteUrl: config.siteUrl,
    doctorsGroupConfigured: !!config.doctorsGroupId,
    adminGroupConfigured: !!config.adminGroupId,
    evolutionConfigured: config.evolutionConfigured,
    isValid: validation.isValid,
    missingConfigs: validation.missingConfigs,
    warnings: validation.warnings
  });

  return { config, validation };
}
