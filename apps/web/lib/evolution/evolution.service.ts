import type {
  EvolutionInstance,
  EvolutionGroup,
  CreateGroupData,
  UpdateGroupMembersData,
  SendMessageData,
  EvolutionConfig,
  EvolutionMetrics,
  TestResult,
} from './types';

export class EvolutionService {
  private config: EvolutionConfig;

  constructor(config: EvolutionConfig) {
    this.config = config;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.apiUrl}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.config.apiKey,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Evolution API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Instance Management
  async createInstance(): Promise<EvolutionInstance> {
    return this.request<EvolutionInstance>('/instance/create', {
      method: 'POST',
      body: JSON.stringify({
        instanceName: this.config.instanceName,
      }),
    });
  }

  async getConnectionState(): Promise<{ state: string }> {
    return this.request<{ state: string }>(`/instance/connectionState/${this.config.instanceName}`);
  }

  async getInstanceInfo(): Promise<EvolutionInstance> {
    return this.request<EvolutionInstance>(`/instance/fetchInstances`);
  }

  // Webhook Management
  async setWebhook(events?: string[]): Promise<void> {
    const data = {
      url: this.config.webhookUrl,
      ...(events && { events }),
    };

    await this.request(`/webhook/set/${this.config.instanceName}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Group Management
  async createGroup(data: CreateGroupData): Promise<EvolutionGroup> {
    return this.request<EvolutionGroup>(`/group/create/${this.config.instanceName}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async fetchAllGroups(): Promise<{ groups: EvolutionGroup[] }> {
    const response = await this.request<any>(`/group/fetchAllGroups/${this.config.instanceName}?getParticipants=true`);

    // A API retorna um array direto, não um objeto com propriedade 'groups'
    if (Array.isArray(response)) {
      return { groups: response };
    }

    // Fallback para o formato esperado
    return response;
  }

  async findGroupByJID(groupJid: string): Promise<EvolutionGroup> {
    return this.request<EvolutionGroup>(`/group/findGroupByJID/${this.config.instanceName}`, {
      method: 'GET',
      body: JSON.stringify({ groupJid }),
    });
  }

  async getGroupMembers(groupJid: string): Promise<{ participants: any[] }> {
    return this.request<{ participants: any[] }>(`/group/findGroupInfos/${this.config.instanceName}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async updateGroupMembers(data: UpdateGroupMembersData): Promise<void> {
    await this.request(`/group/updateGroupMembers/${this.config.instanceName}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateGroupInfo(groupJid: string, subject?: string, description?: string): Promise<void> {
    const data: any = { groupJid };
    if (subject) data.subject = subject;
    if (description) data.description = description;

    await this.request(`/group/updateGroupInfo/${this.config.instanceName}`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async leaveGroup(groupJid: string): Promise<void> {
    await this.request(`/group/leaveGroup/${this.config.instanceName}`, {
      method: 'POST',
      body: JSON.stringify({ groupJid }),
    });
  }

  // Message Management
  async sendTextMessage(data: SendMessageData): Promise<any> {
    const endpoint = data.groupJid
      ? `/message/sendText/${this.config.instanceName}`
      : `/message/sendText/${this.config.instanceName}`;

    const body = data.groupJid
      ? { number: data.groupJid, text: data.text }
      : { number: data.number, text: data.text };

    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async sendGroupMessage(groupJid: string, text: string): Promise<any> {
    return this.sendTextMessage({
      number: groupJid,
      text,
      groupJid,
    });
  }

  // Utility Methods
  async testConnection(): Promise<TestResult> {
    try {
      const state = await this.getConnectionState();
      return {
        id: crypto.randomUUID(),
        type: 'connection',
        status: state.state === 'open' ? 'success' : 'error',
        message: `Connection state: ${state.state}`,
        timestamp: new Date(),
        details: state,
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        type: 'connection',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      };
    }
  }

  async testMessage(number: string, message: string): Promise<TestResult> {
    try {
      const result = await this.sendTextMessage({ number, text: message });
      return {
        id: crypto.randomUUID(),
        type: 'message',
        status: 'success',
        message: `Message sent successfully to ${number}`,
        timestamp: new Date(),
        details: result,
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        type: 'message',
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to send message',
        timestamp: new Date(),
      };
    }
  }

  async testGroupMessage(groupJid: string, message: string): Promise<TestResult> {
    try {
      const result = await this.sendGroupMessage(groupJid, message);
      return {
        id: crypto.randomUUID(),
        type: 'group',
        status: 'success',
        message: `Group message sent successfully`,
        timestamp: new Date(),
        details: result,
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        type: 'group',
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to send group message',
        timestamp: new Date(),
      };
    }
  }

  // Metrics and Monitoring
  async getMetrics(): Promise<EvolutionMetrics> {
    // This would be implemented based on your database or analytics service
    // For now, returning mock data
    return {
      totalMessages: 1250,
      messagesDelivered: 1200,
      messagesFailed: 50,
      totalGroups: 5,
      activeParticipants: 45,
      connectionUptime: 99.5,
      lastConnectionCheck: new Date(),
    };
  }

  // Template helpers for common messages
  static formatPlantaoAlert(patientName: string, urgency: 'BAIXA' | 'MÉDIA' | 'ALTA'): string {
    const urgencyEmoji = {
      'BAIXA': '🟢',
      'MÉDIA': '🟡',
      'ALTA': '🔴'
    };

    return `${urgencyEmoji[urgency]} NOVO PACIENTE NO PLANTÃO

👤 Nome: ${patientName}
⚡ Urgência: ${urgency}
⏰ Entrou na fila: ${new Date().toLocaleTimeString('pt-BR')}

🔗 Acesse o sistema para mais detalhes`;
  }

  static formatPatientNotification(patientName: string, doctorName: string, roomUrl: string): string {
    return `Olá ${patientName}! 👨‍⚕️

Seu atendimento foi aceito pelo Dr. ${doctorName}.

🔗 Clique no link para entrar na consulta:
${roomUrl}

⏰ Aguarde até ser chamado pelo médico.`;
  }

  static formatPaymentConfirmation(patientName: string, amount: number): string {
    return `✅ Pagamento Confirmado

Olá ${patientName}!

Seu pagamento de ${new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount)} foi aprovado.

Você já pode aguardar o atendimento médico.`;
  }
}
