import type { MessageTemplate } from './types';

export const defaultTemplates: MessageTemplate[] = [
  // Templates para Plantão
  {
    id: 'plantao-new-patient',
    name: 'Novo Paciente no Plantão',
    content: `🚨 NOVO PACIENTE NO PLANTÃO

👤 Nome: {{patientName}}
⚡ Urgência: {{urgency}}
⏰ Entrou na fila: {{time}}
🏥 Especialidade: {{specialty}}

🔗 Acesse: {{systemUrl}}`,
    category: 'plantao',
    variables: ['patientName', 'urgency', 'time', 'specialty', 'systemUrl'],
    isActive: true,
  },
  {
    id: 'plantao-urgent-case',
    name: '<PERSON><PERSON><PERSON>',
    content: `🔴 CASO URGENTE - ATENÇÃO!

👤 Paciente: {{patientName}}
🚨 Motivo: {{reason}}
📱 Contato: {{phone}}
⏰ {{time}}

⚡ REQUER ATENDIMENTO IMEDIATO`,
    category: 'plantao',
    variables: ['patientName', 'reason', 'phone', 'time'],
    isActive: true,
  },
  {
    id: 'plantao-shift-change',
    name: '<PERSON><PERSON><PERSON>',
    content: `🔄 TROCA DE TURNO

Turno {{currentShift}} finalizando
Próximo turno: {{nextShift}}

📊 Resumo:
• Pacientes atendidos: {{patientsServed}}
• Pendências: {{pendingCases}}

Boa sorte ao próximo turno! 👨‍⚕️`,
    category: 'plantao',
    variables: ['currentShift', 'nextShift', 'patientsServed', 'pendingCases'],
    isActive: true,
  },

  // Templates para Pacientes
  {
    id: 'patient-doctor-accepted',
    name: 'Médico Aceitou Atendimento',
    content: `Olá {{patientName}}! 👨‍⚕️

Seu atendimento foi aceito pelo Dr. {{doctorName}}.

🔗 Entre na consulta:
{{roomUrl}}

⏰ Aguarde até ser chamado pelo médico.`,
    category: 'paciente',
    variables: ['patientName', 'doctorName', 'roomUrl'],
    isActive: true,
  },
  {
    id: 'patient-payment-confirmed',
    name: 'Pagamento Confirmado',
    content: `✅ Pagamento Confirmado

Olá {{patientName}}!

Seu pagamento de {{amount}} foi aprovado.

Você já pode aguardar o atendimento médico.
Tempo estimado: {{estimatedTime}}`,
    category: 'paciente',
    variables: ['patientName', 'amount', 'estimatedTime'],
    isActive: true,
  },
  {
    id: 'patient-appointment-reminder',
    name: 'Lembrete de Consulta',
    content: `⏰ Lembrete de Consulta

Olá {{patientName}},

Sua consulta está agendada para:
📅 {{date}}
🕐 {{time}}
👨‍⚕️ Dr. {{doctorName}}

🔗 Link da consulta: {{roomUrl}}`,
    category: 'paciente',
    variables: ['patientName', 'date', 'time', 'doctorName', 'roomUrl'],
    isActive: true,
  },
  {
    id: 'patient-consultation-ended',
    name: 'Consulta Finalizada',
    content: `✅ Consulta Finalizada

Obrigado por usar nossos serviços, {{patientName}}!

📋 Receita médica: {{prescriptionUrl}}
📞 Em caso de dúvidas: {{supportPhone}}

Avalie sua experiência: {{ratingUrl}}`,
    category: 'paciente',
    variables: ['patientName', 'prescriptionUrl', 'supportPhone', 'ratingUrl'],
    isActive: true,
  },

  // Templates para Médicos
  {
    id: 'doctor-new-appointment',
    name: 'Nova Consulta Disponível',
    content: `📋 Nova Consulta Disponível

👤 Paciente: {{patientName}}
⏰ Horário: {{time}}
🏥 Especialidade: {{specialty}}
💰 Valor: {{amount}}

🔗 Aceitar: {{acceptUrl}}`,
    category: 'medico',
    variables: ['patientName', 'time', 'specialty', 'amount', 'acceptUrl'],
    isActive: true,
  },
  {
    id: 'doctor-patient-waiting',
    name: 'Paciente Aguardando',
    content: `⏳ Paciente Aguardando

👤 {{patientName}} está na sala de espera há {{waitingTime}}.

🔗 Iniciar consulta: {{roomUrl}}

⚠️ Lembre-se de pontualidade para uma boa experiência.`,
    category: 'medico',
    variables: ['patientName', 'waitingTime', 'roomUrl'],
    isActive: true,
  },

  // Templates Administrativos
  {
    id: 'admin-system-maintenance',
    name: 'Manutenção do Sistema',
    content: `🔧 MANUTENÇÃO PROGRAMADA

⏰ Início: {{startTime}}
⏰ Fim: {{endTime}}
🔄 Duração estimada: {{duration}}

Durante este período o sistema ficará indisponível.

Obrigado pela compreensão!`,
    category: 'admin',
    variables: ['startTime', 'endTime', 'duration'],
    isActive: true,
  },
  {
    id: 'admin-connection-alert',
    name: 'Alerta de Conexão',
    content: `⚠️ ALERTA DE CONEXÃO

A instância {{instanceName}} está com problemas:
Status: {{status}}
Último check: {{lastCheck}}

🔧 Verificar configurações imediatamente.`,
    category: 'admin',
    variables: ['instanceName', 'status', 'lastCheck'],
    isActive: true,
  },
];

export function replaceTemplateVariables(
  template: string,
  variables: Record<string, string>
): string {
  let result = template;

  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, value);
  });

  return result;
}

export function extractTemplateVariables(template: string): string[] {
  const regex = /{{(\w+)}}/g;
  const matches = [];
  let match;

  while ((match = regex.exec(template)) !== null) {
    matches.push(match[1]);
  }

  return [...new Set(matches)]; // Remove duplicates
}

export function validateTemplate(template: string, variables: Record<string, string>): {
  isValid: boolean;
  missingVariables: string[];
} {
  const requiredVariables = extractTemplateVariables(template);
  const providedVariables = Object.keys(variables);
  const missingVariables = requiredVariables.filter(
    variable => !providedVariables.includes(variable)
  );

  return {
    isValid: missingVariables.length === 0,
    missingVariables,
  };
}

export function formatPhoneNumber(phone: string): string {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Add country code if not present
  if (cleaned.length === 11 && cleaned.startsWith('11')) {
    return `55${cleaned}`;
  }

  if (cleaned.length === 10 || cleaned.length === 11) {
    return `55${cleaned}`;
  }

  if (cleaned.length === 13 && cleaned.startsWith('55')) {
    return cleaned;
  }

  return cleaned;
}

export function formatGroupJid(groupId: string): string {
  if (groupId.includes('@g.us')) {
    return groupId;
  }

  return `${groupId}@g.us`;
}

export function formatContactJid(phone: string): string {
  const formattedPhone = formatPhoneNumber(phone);

  if (formattedPhone.includes('@s.whatsapp.net')) {
    return formattedPhone;
  }

  return `${formattedPhone}@s.whatsapp.net`;
}
