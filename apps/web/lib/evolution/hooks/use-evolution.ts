'use client';

import { useState, useCallback, useEffect } from 'react';
import { EvolutionService } from '../evolution.service';
import type {
  EvolutionConfig,
  EvolutionGroup,
  CreateGroupData,
  UpdateGroupMembersData,
  SendMessageData,
  EvolutionMetrics,
  TestResult
} from '../types';

export function useEvolution(config?: EvolutionConfig) {
  const [evolutionService, setEvolutionService] = useState<EvolutionService | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize service when config is provided
  useEffect(() => {
    if (config) {
      const service = new EvolutionService(config);
      setEvolutionService(service);
    }
  }, [config]);

  const handleError = useCallback((error: unknown) => {
    const message = error instanceof Error ? error.message : 'Unknown error occurred';
    setError(message);
    throw error;
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Connection methods
  const checkConnection = useCallback(async () => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const state = await evolutionService.getConnectionState();
      setIsConnected(state.state === 'open');
      return state;
    } catch (error) {
      setIsConnected(false);
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const testConnection = useCallback(async () => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.testConnection();
      return result;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  // Group management
  const createGroup = useCallback(async (data: CreateGroupData) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const group = await evolutionService.createGroup(data);
      return group;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const fetchGroups = useCallback(async () => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.fetchAllGroups();
      return result.groups;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const updateGroupMembers = useCallback(async (data: UpdateGroupMembersData) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      await evolutionService.updateGroupMembers(data);
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const getGroupMembers = useCallback(async (groupJid: string) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.getGroupMembers(groupJid);
      return result.participants;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  // Message methods
  const sendMessage = useCallback(async (data: SendMessageData) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.sendTextMessage(data);
      return result;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const sendGroupMessage = useCallback(async (groupJid: string, text: string) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.sendGroupMessage(groupJid, text);
      return result;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  // Testing methods
  const testMessage = useCallback(async (number: string, message: string) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.testMessage(number, message);
      return result;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  const testGroupMessage = useCallback(async (groupJid: string, message: string) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const result = await evolutionService.testGroupMessage(groupJid, message);
      return result;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  // Metrics
  const getMetrics = useCallback(async () => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      const metrics = await evolutionService.getMetrics();
      return metrics;
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  // Webhook configuration
  const setWebhook = useCallback(async (events?: string[]) => {
    if (!evolutionService) throw new Error('Evolution service not initialized');

    setLoading(true);
    clearError();

    try {
      await evolutionService.setWebhook(events);
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  }, [evolutionService, handleError, clearError]);

  return {
    // State
    isConnected,
    loading,
    error,

    // Methods
    clearError,
    checkConnection,
    testConnection,
    createGroup,
    fetchGroups,
    updateGroupMembers,
    getGroupMembers,
    sendMessage,
    sendGroupMessage,
    testMessage,
    testGroupMessage,
    getMetrics,
    setWebhook,

    // Service instance
    evolutionService,
  };
}

// Hook for managing Evolution configuration
export function useEvolutionConfig() {
  const [config, setConfig] = useState<EvolutionConfig | null>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('evolution-config');
      return stored ? JSON.parse(stored) : null;
    }
    return null;
  });

  const updateConfig = useCallback((newConfig: EvolutionConfig) => {
    setConfig(newConfig);
    if (typeof window !== 'undefined') {
      localStorage.setItem('evolution-config', JSON.stringify(newConfig));
    }
  }, []);

  const clearConfig = useCallback(() => {
    setConfig(null);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('evolution-config');
    }
  }, []);

  return {
    config,
    updateConfig,
    clearConfig,
  };
}

// Hook for managing webhook events configuration
export function useWebhookEvents() {
  const [events, setEvents] = useState<Record<string, boolean>>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('evolution-webhook-events');
      return stored ? JSON.parse(stored) : {
        message: true,
        group_update: true,
        connection_update: true,
        status: true,
        presence: false,
        typing: false,
      };
    }
    return {
      message: true,
      group_update: true,
      connection_update: true,
      status: true,
      presence: false,
      typing: false,
    };
  });

  const updateEvents = useCallback((newEvents: Record<string, boolean>) => {
    setEvents(newEvents);
    if (typeof window !== 'undefined') {
      localStorage.setItem('evolution-webhook-events', JSON.stringify(newEvents));
    }
  }, []);

  const toggleEvent = useCallback((eventName: string) => {
    setEvents(prev => {
      const updated = { ...prev, [eventName]: !prev[eventName] };
      if (typeof window !== 'undefined') {
        localStorage.setItem('evolution-webhook-events', JSON.stringify(updated));
      }
      return updated;
    });
  }, []);

  return {
    events,
    updateEvents,
    toggleEvent,
  };
}
