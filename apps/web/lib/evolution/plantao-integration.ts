import { EvolutionService } from './evolution.service';
import { replaceTemplateVariables } from './message-templates';
import type { EvolutionConfig } from './types';

/**
 * Integration service for Evolution API with plantão (on-duty) system
 * This service handles WhatsApp notifications for medical on-duty scenarios
 */
export class PlantaoEvolutionIntegration {
  private evolutionService: EvolutionService;
  private plantaoGroupId: string;

  constructor(config: EvolutionConfig) {
    this.evolutionService = new EvolutionService(config);
    this.plantaoGroupId = config.plantaoGroupId || '';
  }

  /**
   * Notify plantão group about a new patient
   */
  async notifyNewPatient(data: {
    patientName: string;
    urgency: 'BAIXA' | 'MÉDIA' | 'ALTA';
    specialty: string;
    patientId: string;
    systemUrl: string;
  }) {
    const template = `🚨 NOVO PACIENTE NO PLANTÃO

👤 Nome: {{patientName}}
⚡ Urgência: {{urgency}}
🏥 Especialidade: {{specialty}}
⏰ Entrou na fila: {{time}}

🔗 Acesse: {{systemUrl}}/plantao/paciente/{{patientId}}`;

    const message = replaceTemplateVariables(template, {
      patientName: data.patientName,
      urgency: data.urgency,
      specialty: data.specialty,
      time: new Date().toLocaleTimeString('pt-BR'),
      systemUrl: data.systemUrl,
      patientId: data.patientId,
    });

    if (!this.plantaoGroupId) {
      throw new Error('Plantão group ID not configured');
    }

    return this.evolutionService.sendGroupMessage(this.plantaoGroupId, message);
  }

  /**
   * Notify patient that a doctor accepted their appointment
   */
  async notifyPatientDoctorAccepted(data: {
    patientPhone: string;
    patientName: string;
    doctorName: string;
    roomUrl: string;
  }) {
    const template = `Olá {{patientName}}! 👨‍⚕️

Seu atendimento foi aceito pelo Dr. {{doctorName}}.

🔗 Entre na consulta:
{{roomUrl}}

⏰ Aguarde até ser chamado pelo médico.`;

    const message = replaceTemplateVariables(template, {
      patientName: data.patientName,
      doctorName: data.doctorName,
      roomUrl: data.roomUrl,
    });

    return this.evolutionService.sendTextMessage({
      number: data.patientPhone,
      text: message,
    });
  }

  /**
   * Notify patient about payment confirmation
   */
  async notifyPaymentConfirmed(data: {
    patientPhone: string;
    patientName: string;
    amount: number;
    estimatedTime?: string;
  }) {
    const template = `✅ Pagamento Confirmado

Olá {{patientName}}!

Seu pagamento de {{amount}} foi aprovado.

Você já pode aguardar o atendimento médico.
{{estimatedTime}}`;

    const variables: Record<string, string> = {
      patientName: data.patientName,
      amount: new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(data.amount),
    };

    if (data.estimatedTime) {
      variables.estimatedTime = `Tempo estimado: ${data.estimatedTime}`;
    } else {
      variables.estimatedTime = '';
    }

    const message = replaceTemplateVariables(template, variables);

    return this.evolutionService.sendTextMessage({
      number: data.patientPhone,
      text: message,
    });
  }

  /**
   * Notify plantão group about urgent case
   */
  async notifyUrgentCase(data: {
    patientName: string;
    reason: string;
    patientPhone: string;
    patientId: string;
    systemUrl: string;
  }) {
    const template = `🔴 CASO URGENTE - ATENÇÃO!

👤 Paciente: {{patientName}}
🚨 Motivo: {{reason}}
📱 Contato: {{phone}}
⏰ {{time}}

⚡ REQUER ATENDIMENTO IMEDIATO

🔗 Acesse: {{systemUrl}}/plantao/paciente/{{patientId}}`;

    const message = replaceTemplateVariables(template, {
      patientName: data.patientName,
      reason: data.reason,
      phone: data.patientPhone,
      time: new Date().toLocaleTimeString('pt-BR'),
      systemUrl: data.systemUrl,
      patientId: data.patientId,
    });

    if (!this.plantaoGroupId) {
      throw new Error('Plantão group ID not configured');
    }

    return this.evolutionService.sendGroupMessage(this.plantaoGroupId, message);
  }

  /**
   * Notify patient about consultation reminder
   */
  async notifyAppointmentReminder(data: {
    patientPhone: string;
    patientName: string;
    doctorName: string;
    appointmentDate: Date;
    roomUrl: string;
  }) {
    const template = `⏰ Lembrete de Consulta

Olá {{patientName}},

Sua consulta está agendada para:
📅 {{date}}
🕐 {{time}}
👨‍⚕️ Dr. {{doctorName}}

🔗 Link da consulta: {{roomUrl}}`;

    const message = replaceTemplateVariables(template, {
      patientName: data.patientName,
      doctorName: data.doctorName,
      date: data.appointmentDate.toLocaleDateString('pt-BR'),
      time: data.appointmentDate.toLocaleTimeString('pt-BR'),
      roomUrl: data.roomUrl,
    });

    return this.evolutionService.sendTextMessage({
      number: data.patientPhone,
      text: message,
    });
  }

  /**
   * Notify about shift change
   */
  async notifyShiftChange(data: {
    currentShift: string;
    nextShift: string;
    patientsServed: number;
    pendingCases: number;
  }) {
    const template = `🔄 TROCA DE TURNO

Turno {{currentShift}} finalizando
Próximo turno: {{nextShift}}

📊 Resumo:
• Pacientes atendidos: {{patientsServed}}
• Pendências: {{pendingCases}}

Boa sorte ao próximo turno! 👨‍⚕️`;

    const message = replaceTemplateVariables(template, {
      currentShift: data.currentShift,
      nextShift: data.nextShift,
      patientsServed: data.patientsServed.toString(),
      pendingCases: data.pendingCases.toString(),
    });

    if (!this.plantaoGroupId) {
      throw new Error('Plantão group ID not configured');
    }

    return this.evolutionService.sendGroupMessage(this.plantaoGroupId, message);
  }

  /**
   * Notify doctor about new appointment
   */
  async notifyDoctorNewAppointment(data: {
    doctorPhone: string;
    patientName: string;
    specialty: string;
    amount: number;
    acceptUrl: string;
  }) {
    const template = `📋 Nova Consulta Disponível

👤 Paciente: {{patientName}}
⏰ Horário: {{time}}
🏥 Especialidade: {{specialty}}
💰 Valor: {{amount}}

🔗 Aceitar: {{acceptUrl}}`;

    const message = replaceTemplateVariables(template, {
      patientName: data.patientName,
      time: new Date().toLocaleTimeString('pt-BR'),
      specialty: data.specialty,
      amount: new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(data.amount),
      acceptUrl: data.acceptUrl,
    });

    return this.evolutionService.sendTextMessage({
      number: data.doctorPhone,
      text: message,
    });
  }

  /**
   * Send system maintenance notification
   */
  async notifySystemMaintenance(data: {
    startTime: Date;
    endTime: Date;
    duration: string;
  }) {
    const template = `🔧 MANUTENÇÃO PROGRAMADA

⏰ Início: {{startTime}}
⏰ Fim: {{endTime}}
🔄 Duração estimada: {{duration}}

Durante este período o sistema ficará indisponível.

Obrigado pela compreensão!`;

    const message = replaceTemplateVariables(template, {
      startTime: data.startTime.toLocaleString('pt-BR'),
      endTime: data.endTime.toLocaleString('pt-BR'),
      duration: data.duration,
    });

    if (!this.plantaoGroupId) {
      throw new Error('Plantão group ID not configured');
    }

    return this.evolutionService.sendGroupMessage(this.plantaoGroupId, message);
  }

  /**
   * Check connection status
   */
  async checkConnection() {
    return this.evolutionService.testConnection();
  }

  /**
   * Get service metrics
   */
  async getMetrics() {
    return this.evolutionService.getMetrics();
  }
}

/**
 * Factory function to create Evolution integration instance
 */
export function createPlantaoEvolutionIntegration(): PlantaoEvolutionIntegration | null {
  const config = {
    apiUrl: process.env.EVOLUTION_API_URL || '',
    apiKey: process.env.EVOLUTION_API_KEY || '',
    instanceName: process.env.EVOLUTION_INSTANCE || 'zapvida-plantao',
    webhookUrl: process.env.EVOLUTION_WEBHOOK_URL || '',
    plantaoGroupId: process.env.EVOLUTION_GROUP_PLANTAO_ID || '',
  };

  // Validate required environment variables
  if (!config.apiUrl || !config.apiKey) {
    console.warn('Evolution API configuration missing. WhatsApp notifications disabled.');
    return null;
  }

  return new PlantaoEvolutionIntegration(config);
}

/**
 * Utility function to format phone numbers for WhatsApp
 */
export function formatWhatsAppNumber(phone: string): string {
  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Add country code if not present
  if (cleaned.length === 11 && cleaned.startsWith('11')) {
    return `55${cleaned}`;
  }

  if (cleaned.length === 10 || cleaned.length === 11) {
    return `55${cleaned}`;
  }

  if (cleaned.length === 13 && cleaned.startsWith('55')) {
    return cleaned;
  }

  return cleaned;
}

/**
 * Default export for easy importing
 */
export default PlantaoEvolutionIntegration;
