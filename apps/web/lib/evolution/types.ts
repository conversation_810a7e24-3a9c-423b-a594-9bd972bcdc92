export interface EvolutionInstance {
  instanceName: string;
  status: 'created' | 'connected' | 'disconnected' | 'open' | 'connecting' | 'close' | 'logout';
  connectionState?: 'open' | 'connecting' | 'close' | 'logout';
}

export interface EvolutionGroup {
  id: string;
  subject: string;
  description?: string;
  participants: string[];
  participantsCount: number;
  admins: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface EvolutionParticipant {
  id: string;
  name?: string;
  number: string;
  isAdmin: boolean;
  joinedAt?: Date;
}

export interface CreateGroupData {
  subject: string;
  participants: string[];
  description?: string;
}

export interface UpdateGroupMembersData {
  groupJid: string;
  participants: string[];
  action: 'add' | 'remove' | 'promote' | 'demote';
}

export interface SendMessageData {
  number: string;
  text: string;
  groupJid?: string;
}

export interface EvolutionWebhookEvent {
  event: 'message' | 'group_update' | 'connection.update' | 'status';
  instance: string;
  data: any;
  timestamp: number;
}

export interface EvolutionConfig {
  apiUrl: string;
  apiKey: string;
  instanceName: string;
  webhookUrl: string;
  plantaoGroupId?: string;
}

export interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  category: 'plantao' | 'paciente' | 'medico' | 'admin';
  variables?: string[];
  isActive: boolean;
}

export interface EvolutionMetrics {
  totalMessages: number;
  messagesDelivered: number;
  messagesFailed: number;
  totalGroups: number;
  activeParticipants: number;
  connectionUptime: number;
  lastConnectionCheck: Date;
}

export interface TestResult {
  id: string;
  type: 'connection' | 'message' | 'group';
  status: 'success' | 'error';
  message: string;
  timestamp: Date;
  details?: any;
}
