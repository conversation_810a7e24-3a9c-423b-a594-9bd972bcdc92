import { useState, useEffect, useCallback, useRef } from 'react';
import { ChatService, ChatMessage, ConnectionStatus } from '../services/chat.service';

export interface UseZapChatOptions {
  appointmentId: string;
  userId: string;
  autoConnect?: boolean;
}

export interface UseZapChatReturn {
  messages: ChatMessage[];
  connectionStatus: ConnectionStatus;
  isLoading: boolean;
  error: string | null;
  sendTextMessage: (content: string) => Promise<void>;
  sendAudioMessage: (audioBlob: Blob) => Promise<void>;
  sendFileMessage: (file: File) => Promise<void>;
  connect: () => Promise<void>;
  disconnect: () => void;
  refreshMessages: () => Promise<void>;
  clearError: () => void;
}

export function useZapChat(
  appointmentId: string,
  userId: string,
  autoConnect: boolean = true
): UseZapChatReturn {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true,
    fallbackMode: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const chatServiceRef = useRef<ChatService | null>(null);
  const unsubscribeMessageRef = useRef<(() => void) | null>(null);
  const unsubscribeStatusRef = useRef<(() => void) | null>(null);
  const processedMessageIds = useRef(new Set<string>());

  // Função para verificar se uma mensagem já foi processada
  const isMessageProcessed = useCallback((messageId: string) => {
    return processedMessageIds.current.has(messageId);
  }, []);

  // Função para adicionar mensagem ao estado com verificação de duplicatas
  const addMessage = useCallback((message: ChatMessage) => {
    if (!message || !message.id) {
      console.warn("[useZapChat] Tentativa de adicionar mensagem inválida:", message);
      return;
    }

    // Verificar se a mensagem já foi processada
    if (isMessageProcessed(message.id)) {
      console.log("[useZapChat] Mensagem já processada, ignorando:", message.id);
      return;
    }

    // Marcar a mensagem como processada
    processedMessageIds.current.add(message.id);

    console.log("[useZapChat] Adicionando mensagem ao estado:", message.id);

    // Atualizar o estado de mensagens
    setMessages(prevMessages => {
      // Verificação extra para garantir que não haja duplicatas
      if (prevMessages.some(m => m.id === message.id)) {
        console.log("[useZapChat] Mensagem já existe no estado, ignorando:", message.id);
        return prevMessages;
      }

      // Ordenar por data de criação
      const updatedMessages = [...prevMessages, message].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });

      return updatedMessages;
    });
  }, [isMessageProcessed]);

  const initializeChatService = useCallback(() => {
    if (!appointmentId) {
      console.log("[useZapChat] Sem appointmentId para inicializar serviço");
      return;
    }

    // Limpar serviço anterior se existir
    if (chatServiceRef.current) {
      console.log("[useZapChat] Limpando serviço anterior");
      chatServiceRef.current.destroy();
      unsubscribeMessageRef.current?.();
      unsubscribeStatusRef.current?.();
    }

    console.log("[useZapChat] Inicializando ChatService para appointment:", appointmentId);
    chatServiceRef.current = new ChatService(appointmentId);

    // Configurar handlers
    unsubscribeMessageRef.current = chatServiceRef.current.onMessage((message) => {
      console.log("[useZapChat] Mensagem recebida via realtime:", message.id);
      addMessage(message);
    });

    unsubscribeStatusRef.current = chatServiceRef.current.onStatusChange((status) => {
      console.log("[useZapChat] Status de conexão alterado:", status.status);
      setConnectionStatus(status);

      // Limpar erro quando conectar com sucesso
      if (status.status === 'connected' && error) {
        setError(null);
      }

      // Log do modo fallback
      if (status.fallbackMode) {
        console.log("[useZapChat] Modo fallback ativado - usando polling em vez de realtime");
      }
    });
  }, [appointmentId, addMessage, error]);

  const connect = useCallback(async () => {
    if (!chatServiceRef.current) {
      initializeChatService();
    }
    console.log("[useZapChat] Conectando...");
    try {
      await chatServiceRef.current?.connect();
    } catch (error) {
      console.error("[useZapChat] Erro ao conectar:", error);
      setError(error instanceof Error ? error.message : 'Erro ao conectar');
    }
  }, [initializeChatService]);

  const disconnect = useCallback(() => {
    console.log("[useZapChat] Desconectando...");
    chatServiceRef.current?.disconnect();
  }, []);

  const refreshMessages = useCallback(async () => {
    if (!chatServiceRef.current) {
      console.log("[useZapChat] Sem serviço de chat para atualizar mensagens");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("[useZapChat] Buscando mensagens...");
      const fetchedMessages = await chatServiceRef.current.getMessages();

      // Limpar cache de IDs processados e adicionar todas as mensagens
      processedMessageIds.current.clear();
      fetchedMessages.forEach(msg => {
        if (msg && msg.id) {
          processedMessageIds.current.add(msg.id);
        }
      });

      console.log(`[useZapChat] ${fetchedMessages.length} mensagens carregadas`);
      setMessages(fetchedMessages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch messages';
      console.error("[useZapChat] Erro ao buscar mensagens:", errorMessage);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const sendTextMessage = useCallback(async (content: string) => {
    if (!chatServiceRef.current || !content.trim()) {
      console.warn("[useZapChat] Não é possível enviar mensagem:", { hasService: !!chatServiceRef.current, content });
      return;
    }

    setError(null);

    // Criar mensagem otimística
    const optimisticMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      content: content.trim(),
      type: 'TEXT',
      senderId: userId,
      appointmentId: appointmentId,
      createdAt: new Date().toISOString()
    };

    console.log("[useZapChat] Adicionando mensagem otimística:", optimisticMessage.id);
    addMessage(optimisticMessage);

    try {
      const sentMessage = await chatServiceRef.current.sendTextMessage(content.trim(), userId);
      console.log("[useZapChat] Mensagem enviada com sucesso:", sentMessage.id);

      // Remover mensagem otimística e adicionar a mensagem real
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);
    } catch (err) {
      // Remover mensagem otimística em caso de erro
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      console.error("[useZapChat] Erro ao enviar mensagem:", errorMessage);
      setError(errorMessage);
      throw err;
    }
  }, [userId, appointmentId, addMessage]);

  const sendAudioMessage = useCallback(async (audioBlob: Blob) => {
    if (!chatServiceRef.current) {
      console.warn("[useZapChat] Sem serviço de chat para enviar áudio");
      return;
    }

    setError(null);

    // Criar mensagem otimística para áudio
    const optimisticMessage: ChatMessage = {
      id: `temp-audio-${Date.now()}`,
      content: 'Enviando áudio...',
      type: 'AUDIO',
      senderId: userId,
      appointmentId: appointmentId,
      createdAt: new Date().toISOString(),
      file_size: audioBlob.size
    };

    console.log("[useZapChat] Adicionando mensagem de áudio otimística:", optimisticMessage.id);
    addMessage(optimisticMessage);

    try {
      const sentMessage = await chatServiceRef.current.sendAudioMessage(audioBlob, userId);
      console.log("[useZapChat] Áudio enviado com sucesso:", sentMessage.id);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);
    } catch (err) {
      // Remover mensagem otimística em caso de erro
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Failed to send audio';
      console.error("[useZapChat] Erro ao enviar áudio:", errorMessage);
      setError(errorMessage);
      throw err;
    }
  }, [userId, appointmentId, addMessage]);

  const sendFileMessage = useCallback(async (file: File) => {
    if (!chatServiceRef.current) {
      console.warn("[useZapChat] Sem serviço de chat para enviar arquivo");
      return;
    }

    setError(null);

    // Criar mensagem otimística para arquivo
    const optimisticMessage: ChatMessage = {
      id: `temp-file-${Date.now()}`,
      content: file.name,
      type: 'FILE',
      senderId: userId,
      appointmentId: appointmentId,
      createdAt: new Date().toISOString(),
      file_name: file.name,
      file_size: file.size
    };

    console.log("[useZapChat] Adicionando mensagem de arquivo otimística:", optimisticMessage.id);
    addMessage(optimisticMessage);

    try {
      const sentMessage = await chatServiceRef.current.sendFileMessage(file, userId);
      console.log("[useZapChat] Arquivo enviado com sucesso:", sentMessage.id);

      // Remover mensagem otimística
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);
    } catch (err) {
      // Remover mensagem otimística em caso de erro
      setMessages(prev => prev.filter(m => m.id !== optimisticMessage.id));
      processedMessageIds.current.delete(optimisticMessage.id);

      const errorMessage = err instanceof Error ? err.message : 'Failed to send file';
      console.error("[useZapChat] Erro ao enviar arquivo:", errorMessage);
      setError(errorMessage);
      throw err;
    }
  }, [userId, appointmentId, addMessage]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Effect principal - inicializa quando appointmentId muda
  useEffect(() => {
    if (!appointmentId) {
      console.log("[useZapChat] Effect: sem appointmentId, resetando estado");
      setMessages([]);
      setIsLoading(false);
      setError(null);
      return;
    }

    console.log(`[useZapChat] Effect principal executando para appointmentId: ${appointmentId}`);

    // Reset state quando mudar appointment
    setMessages([]);
    setIsLoading(true);
    setError(null);
    processedMessageIds.current.clear();

    // Inicializar serviço
    initializeChatService();

    if (autoConnect) {
      connect();
      refreshMessages();
    }

    // Cleanup
    return () => {
      console.log(`[useZapChat] Cleanup do effect para appointmentId: ${appointmentId}`);
      if (unsubscribeMessageRef.current) {
        unsubscribeMessageRef.current();
        unsubscribeMessageRef.current = null;
      }
      if (unsubscribeStatusRef.current) {
        unsubscribeStatusRef.current();
        unsubscribeStatusRef.current = null;
      }
      if (chatServiceRef.current) {
        chatServiceRef.current.destroy();
        chatServiceRef.current = null;
      }
    };
  }, [appointmentId, autoConnect, initializeChatService, connect, refreshMessages]);

  return {
    messages,
    connectionStatus,
    isLoading,
    error,
    sendTextMessage,
    sendAudioMessage,
    sendFileMessage,
    connect,
    disconnect,
    refreshMessages,
    clearError
  };
}
