import { useState, useEffect, useCallback, useRef } from 'react';
import { OptimizedChatService, ChatMessage, ConnectionStatus, TypingStatus } from '../services/optimized-chat.service';

export interface UseOptimizedChatOptions {
  appointmentId: string;
  userId: string;
  userName: string;
  userRole: 'DOCTOR' | 'PATIENT';
  autoConnect?: boolean;
  enableTypingIndicators?: boolean;
}

export interface UseOptimizedChatReturn {
  // Estado das mensagens
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Estado da conexão
  connectionStatus: ConnectionStatus;
  isConnected: boolean;

  // Estado de digitação
  isTyping: boolean;
  typingUsers: TypingStatus[];

  // Funções de envio
  sendMessage: (content: string) => Promise<void>;
  sendAudio: (audioBlob: Blob) => Promise<void>;
  sendFile: (file: File) => Promise<void>;

  // Controle de conexão
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  reconnect: () => Promise<void>;
  refreshMessages: () => Promise<void>;

  // Controle de digitação
  startTyping: () => void;
  stopTyping: () => void;

  // Utilitários
  clearError: () => void;
}

export function useOptimizedChat({
  appointmentId,
  userId,
  userName,
  userRole,
  autoConnect = true,
  enableTypingIndicators = true,
}: UseOptimizedChatOptions): UseOptimizedChatReturn {
  
  // Estados principais
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true,
  });
  const [typingUsers, setTypingUsers] = useState<TypingStatus[]>([]);
  const [isTyping, setIsTyping] = useState(false);

  // Refs para controle
  const chatServiceRef = useRef<OptimizedChatService | null>(null);
  const isMountedRef = useRef(true);
  const currentAppointmentIdRef = useRef(appointmentId);

  // Computed values
  const isConnected = connectionStatus.status === 'connected';

  // Criar ou atualizar serviço de chat
  const ensureChatService = useCallback(() => {
    if (!appointmentId || !userId) return null;

    // Se o appointmentId mudou, destruir serviço anterior
    if (currentAppointmentIdRef.current !== appointmentId && chatServiceRef.current) {
      chatServiceRef.current.destroy();
      chatServiceRef.current = null;
    }

    // Criar novo serviço se necessário
    if (!chatServiceRef.current) {
      chatServiceRef.current = new OptimizedChatService(appointmentId, userId, userName, userRole);
      currentAppointmentIdRef.current = appointmentId;

      // Configurar handlers
      chatServiceRef.current.onMessage((message) => {
        if (isMountedRef.current) {
          setMessages(prev => {
            // Evitar duplicatas
            if (prev.some(m => m.id === message.id)) {
              return prev;
            }
            
            // Inserir ordenadamente por data
            const newMessages = [...prev, message].sort((a, b) => 
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
            
            return newMessages;
          });
        }
      });

      chatServiceRef.current.onStatusChange((status) => {
        if (isMountedRef.current) {
          setConnectionStatus(status);
        }
      });

      chatServiceRef.current.onTypingChange((typing) => {
        if (isMountedRef.current && enableTypingIndicators) {
          setTypingUsers(typing);
        }
      });

      chatServiceRef.current.onError((error) => {
        if (isMountedRef.current) {
          console.error('[useOptimizedChat] Erro no chat:', error);
          setError(error.message);
        }
      });
    }

    return chatServiceRef.current;
  }, [appointmentId, userId, userName, userRole, enableTypingIndicators]);

  // Carregar mensagens
  const refreshMessages = useCallback(async () => {
    const service = ensureChatService();
    if (!service) return;

    console.log('[useOptimizedChat] Carregando mensagens...');
    setIsLoading(true);
    setError(null);

    try {
      const loadedMessages = await service.loadMessages();
      if (isMountedRef.current) {
        setMessages(loadedMessages);
      }
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao carregar mensagens:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar mensagens';
        setError(errorMessage);
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [ensureChatService]);

  // Conectar ao chat
  const connect = useCallback(async () => {
    const service = ensureChatService();
    if (!service) return;

    console.log('[useOptimizedChat] Conectando ao chat...');
    setError(null);

    try {
      await service.connect();
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao conectar:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao conectar';
        setError(errorMessage);
      }
    }
  }, [ensureChatService]);

  // Desconectar do chat
  const disconnect = useCallback(async () => {
    if (chatServiceRef.current) {
      console.log('[useOptimizedChat] Desconectando do chat...');
      await chatServiceRef.current.disconnect();
    }
  }, []);

  // Reconectar
  const reconnect = useCallback(async () => {
    const service = ensureChatService();
    if (!service) return;

    console.log('[useOptimizedChat] Reconectando ao chat...');
    try {
      await service.reconnect();
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao reconectar:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao reconectar';
        setError(errorMessage);
      }
    }
  }, [ensureChatService]);

  // Enviar mensagem de texto
  const sendMessage = useCallback(async (content: string) => {
    const service = ensureChatService();
    if (!service) throw new Error('Serviço de chat não disponível');

    if (!content.trim()) {
      throw new Error('Mensagem não pode estar vazia');
    }

    console.log('[useOptimizedChat] Enviando mensagem de texto...');
    setError(null);

    // Parar digitação
    if (isTyping) {
      service.stopTyping();
      setIsTyping(false);
    }

    try {
      await service.sendTextMessage(content);
      console.log('[useOptimizedChat] Mensagem enviada com sucesso');
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao enviar mensagem:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar mensagem';
      setError(errorMessage);
      throw err;
    }
  }, [ensureChatService, isTyping]);

  // Enviar áudio
  const sendAudio = useCallback(async (audioBlob: Blob) => {
    const service = ensureChatService();
    if (!service) throw new Error('Serviço de chat não disponível');

    if (!audioBlob) {
      throw new Error('Áudio não fornecido');
    }

    console.log('[useOptimizedChat] Enviando mensagem de áudio...');
    setError(null);

    try {
      await service.sendAudioMessage(audioBlob);
      console.log('[useOptimizedChat] Áudio enviado com sucesso');
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao enviar áudio:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar áudio';
      setError(errorMessage);
      throw err;
    }
  }, [ensureChatService]);

  // Enviar arquivo
  const sendFile = useCallback(async (file: File) => {
    const service = ensureChatService();
    if (!service) throw new Error('Serviço de chat não disponível');

    if (!file) {
      throw new Error('Arquivo não fornecido');
    }

    console.log('[useOptimizedChat] Enviando arquivo...');
    setError(null);

    try {
      await service.sendFileMessage(file);
      console.log('[useOptimizedChat] Arquivo enviado com sucesso');
    } catch (err) {
      console.error('[useOptimizedChat] Erro ao enviar arquivo:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar arquivo';
      setError(errorMessage);
      throw err;
    }
  }, [ensureChatService]);

  // Controle de digitação
  const startTyping = useCallback(() => {
    const service = ensureChatService();
    if (!service || !enableTypingIndicators || isTyping) return;

    console.log('[useOptimizedChat] Iniciando digitação...');
    setIsTyping(true);
    service.startTyping();
  }, [ensureChatService, enableTypingIndicators, isTyping]);

  const stopTyping = useCallback(() => {
    const service = ensureChatService();
    if (!service || !enableTypingIndicators || !isTyping) return;

    console.log('[useOptimizedChat] Parando digitação...');
    setIsTyping(false);
    service.stopTyping();
  }, [ensureChatService, enableTypingIndicators, isTyping]);

  // Limpar erro
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Effect principal - inicializar quando appointmentId mudar
  useEffect(() => {
    if (!appointmentId || !userId) {
      console.log('[useOptimizedChat] Dados insuficientes, resetando estado');
      setMessages([]);
      setIsLoading(false);
      setError(null);
      setTypingUsers([]);
      return;
    }

    console.log(`[useOptimizedChat] Inicializando para ${appointmentId}`);

    // Reset state para novo appointment
    setMessages([]);
    setIsLoading(true);
    setError(null);
    setTypingUsers([]);

    // Inicializar chat
    const initializeChat = async () => {
      try {
        await refreshMessages();
        if (autoConnect) {
          await connect();
        }
      } catch (err) {
        console.error('[useOptimizedChat] Erro na inicialização:', err);
        if (isMountedRef.current) {
          const errorMessage = err instanceof Error ? err.message : 'Erro na inicialização';
          setError(errorMessage);
          setIsLoading(false);
        }
      }
    };

    initializeChat();

    // Cleanup
    return () => {
      console.log(`[useOptimizedChat] Cleanup para ${appointmentId}`);
      if (chatServiceRef.current) {
        chatServiceRef.current.stopTyping();
      }
    };
  }, [appointmentId, userId, autoConnect, refreshMessages, connect]);

  // Effect de limpeza no unmount
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Desconectar e destruir serviço ao desmontar
      if (chatServiceRef.current) {
        chatServiceRef.current.destroy();
        chatServiceRef.current = null;
      }
    };
  }, []);

  return {
    // Estado das mensagens
    messages,
    isLoading,
    error,

    // Estado da conexão
    connectionStatus,
    isConnected,

    // Estado de digitação
    isTyping,
    typingUsers,

    // Funções de envio
    sendMessage,
    sendAudio,
    sendFile,

    // Controle de conexão
    connect,
    disconnect,
    reconnect,
    refreshMessages,

    // Controle de digitação
    startTyping,
    stopTyping,

    // Utilitários
    clearError,
  };
}
