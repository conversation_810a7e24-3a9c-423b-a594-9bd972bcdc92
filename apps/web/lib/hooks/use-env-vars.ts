import { useState, useEffect } from 'react';

interface EnvVars {
  NEXT_PUBLIC_SUPABASE_URL: string | undefined;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string | undefined;
  NODE_ENV: string | undefined;
}

export function useEnvVars() {
  const [envVars, setEnvVars] = useState<EnvVars>({
    NEXT_PUBLIC_SUPABASE_URL: undefined,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: undefined,
    NODE_ENV: undefined,
  });

  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadEnvVars = () => {
      // Tentar obter do process.env primeiro
      let supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      let supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      let nodeEnv = process.env.NODE_ENV;

      // Se não estiverem disponíveis no process.env, tentar do window.__ENV__
      if (typeof window !== 'undefined' && window.__ENV__) {
        if (!supabaseUrl) supabaseUrl = window.__ENV__.NEXT_PUBLIC_SUPABASE_URL;
        if (!supabaseKey) supabaseKey = window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        if (!nodeEnv) nodeEnv = window.__ENV__.NODE_ENV;
      }

      // Se ainda não estiverem disponíveis, usar valores hardcoded para desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        if (!supabaseUrl) {
          console.warn('⚠️ NEXT_PUBLIC_SUPABASE_URL não encontrada, usando valor padrão para desenvolvimento');
          supabaseUrl = 'https://moupvfqlulvqbzwajkif.supabase.co';
        }
        if (!supabaseKey) {
          console.warn('⚠️ NEXT_PUBLIC_SUPABASE_ANON_KEY não encontrada, usando valor padrão para desenvolvimento');
          supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vdXB2ZnFsdWx2cWJ6d2Fqa2lmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEyMTk2NDYsImV4cCI6MjA1Njc5NTY0Nn0.MFeB-phlRJBVc_a2ZeS-yUP6LOLc9C0L4jF6BIqv0i0';
        }
      }

      setEnvVars({
        NEXT_PUBLIC_SUPABASE_URL: supabaseUrl,
        NEXT_PUBLIC_SUPABASE_ANON_KEY: supabaseKey,
        NODE_ENV: nodeEnv,
      });

      setIsLoaded(true);
    };

    loadEnvVars();
  }, []);

  const isValid = !!(envVars.NEXT_PUBLIC_SUPABASE_URL && envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY);

  return {
    envVars,
    isLoaded,
    isValid,
  };
}
