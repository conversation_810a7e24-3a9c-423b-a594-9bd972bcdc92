import { useState, useEffect, useCallback, useRef } from 'react';
import { getRealtimeService, ChatMessage, ConnectionStatus, TypingStatus } from '../services/supabase-realtime.service';
import { getAppointmentMessages, sendTextMessage, sendAudioMessage } from '../../actions/chats/messages';
import { sendAttachment } from '../../actions/appointments/messages/messages';

export interface UseMobileChatOptions {
  appointmentId: string;
  userId: string;
  userName: string;
  userRole: 'DOCTOR' | 'PATIENT';
  autoConnect?: boolean;
  enableTypingIndicators?: boolean;
}

export interface UseMobileChatReturn {
  // Estado das mensagens
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Estado da conexão
  connectionStatus: ConnectionStatus;
  isConnected: boolean;

  // Estado de digitação
  isTyping: boolean;
  typingUsers: TypingStatus[];

  // Ações do chat
  sendMessage: (content: string) => Promise<void>;
  sendAudio: (audioBlob: Blob) => Promise<void>;
  sendFile: (file: File) => Promise<void>;

  // Controle de digitação
  startTyping: () => void;
  stopTyping: () => void;

  // Controles de conexão
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  reconnect: () => Promise<void>;
  refreshMessages: () => Promise<void>;
  clearError: () => void;
}

/**
 * Hook otimizado para chat mobile com melhor UX
 * Foca em performance, reliability e experiência mobile-first
 */
export function useMobileChat({
  appointmentId,
  userId,
  userName,
  userRole,
  autoConnect = true,
  enableTypingIndicators = true,
}: UseMobileChatOptions): UseMobileChatReturn {

  // Estados principais
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true,
  });
  const [typingUsers, setTypingUsers] = useState<TypingStatus[]>([]);
  const [isTyping, setIsTyping] = useState(false);

  // Refs para controle
  const realtimeService = useRef(getRealtimeService());
  const isMountedRef = useRef(true);
  const processedMessageIds = useRef(new Set<string>());
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const unsubscribeRefs = useRef<(() => void)[]>([]);
  const lastMessageId = useRef<string | null>(null);

  // Computed values
  const isConnected = connectionStatus.status === 'connected';

  // Função para verificar se mensagem já foi processada
  const isMessageProcessed = useCallback((messageId: string) => {
    return processedMessageIds.current.has(messageId);
  }, []);

  // Função para adicionar mensagem com verificação de duplicatas e otimização
  const addMessage = useCallback((message: ChatMessage) => {
    if (isMessageProcessed(message.id)) {
      console.log('[useMobileChat] Mensagem já processada, ignorando:', message.id);
      return;
    }

    console.log('[useMobileChat] Adicionando nova mensagem:', message.id);
    processedMessageIds.current.add(message.id);

    setMessages(prevMessages => {
      // Verificar se mensagem já existe
      const exists = prevMessages.some(m => m.id === message.id);
      if (exists) {
        console.log('[useMobileChat] Mensagem já existe na lista:', message.id);
        return prevMessages;
      }

      // Adicionar mensagem ordenadamente
      const newMessages = [...prevMessages, message].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      // Limitar número de mensagens em memória para performance mobile (últimas 100)
      if (newMessages.length > 100) {
        const trimmedMessages = newMessages.slice(-100);
        // Atualizar IDs processados
        const trimmedIds = new Set(trimmedMessages.map(m => m.id));
        processedMessageIds.current = trimmedIds;
        console.log('[useMobileChat] Limitando mensagens para performance, mantendo:', trimmedMessages.length);
        return trimmedMessages;
      }

      return newMessages;
    });

    // Atualizar último ID da mensagem
    lastMessageId.current = message.id;
  }, [isMessageProcessed]);

  // Carregar mensagens
  const loadMessages = useCallback(async (showLoading = true) => {
    if (!appointmentId) {
      console.log('[useMobileChat] Sem appointmentId para carregar mensagens');
      return;
    }

    console.log('[useMobileChat] Carregando mensagens para:', appointmentId);

    if (showLoading) {
      setIsLoading(true);
    }
    setError(null);

    try {
      const response = await getAppointmentMessages(appointmentId);

      if (!isMountedRef.current) return;

      if (response.success && response.data) {
        console.log('[useMobileChat] Mensagens carregadas:', response.data.length);

        // Limpar IDs processados e reprocessar mensagens
        processedMessageIds.current.clear();

        // Ordernar mensagens por data
        const sortedMessages = response.data.sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        // Adicionar todas as mensagens ao conjunto processado
        sortedMessages.forEach(msg => {
          processedMessageIds.current.add(msg.id);
        });

        setMessages(sortedMessages);

        // Atualizar último ID da mensagem
        if (sortedMessages.length > 0) {
          lastMessageId.current = sortedMessages[sortedMessages.length - 1].id;
        }
      } else {
        console.error('[useMobileChat] Erro ao carregar mensagens:', response.error);
        const errorMessage = response.error || 'Erro ao carregar mensagens';
        setError(errorMessage);
      }
    } catch (err) {
      console.error('[useMobileChat] Exceção ao carregar mensagens:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar mensagens';
        setError(errorMessage);
      }
    } finally {
      if (isMountedRef.current && showLoading) {
        setIsLoading(false);
      }
    }
  }, [appointmentId]);

  // Conectar ao chat
  const connect = useCallback(async () => {
    if (!appointmentId || !userId) {
      console.log('[useMobileChat] Dados insuficientes para conectar');
      return;
    }

    console.log(`[useMobileChat] Conectando ao chat ${appointmentId}`);
    setError(null);

    try {
      // Limpar subscriptions anteriores
      unsubscribeRefs.current.forEach(unsub => unsub());
      unsubscribeRefs.current = [];

      // Configurar handlers
      const unsubscribeMessage = realtimeService.current.onMessage(appointmentId, addMessage);
      const unsubscribeStatus = realtimeService.current.onStatusChange(appointmentId, setConnectionStatus);
      const unsubscribeTyping = realtimeService.current.onTypingChange(appointmentId, setTypingUsers);

      unsubscribeRefs.current.push(unsubscribeMessage, unsubscribeStatus, unsubscribeTyping);

      // Conectar ao serviço
      await realtimeService.current.connectToChat(appointmentId, userId);

      // Carregar mensagens após conectar
      await loadMessages();

    } catch (err) {
      console.error('[useMobileChat] Erro ao conectar:', err);
      if (isMountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'Erro ao conectar';
        setError(errorMessage);
      }
    }
  }, [appointmentId, userId, addMessage, loadMessages]);

  // Desconectar do chat
  const disconnect = useCallback(async () => {
    if (!appointmentId) return;

    console.log(`[useMobileChat] Desconectando do chat ${appointmentId}`);

    // Limpar subscriptions
    unsubscribeRefs.current.forEach(unsub => unsub());
    unsubscribeRefs.current = [];

    // Desconectar do serviço
    await realtimeService.current.disconnectFromChat(appointmentId);
  }, [appointmentId]);

  // Reconectar
  const reconnect = useCallback(async () => {
    if (!appointmentId || !userId) return;

    console.log(`[useMobileChat] Reconectando ao chat ${appointmentId}`);
    await disconnect();
    await connect();
  }, [appointmentId, userId, disconnect, connect]);

  // Atualizar mensagens
  const refreshMessages = useCallback(async () => {
    await loadMessages(false); // Não mostrar loading no refresh
  }, [loadMessages]);

  // Enviar mensagem de texto
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    console.log('[useMobileChat] Enviando mensagem de texto');

    try {
      const response = await sendTextMessage(appointmentId, userId, content.trim());

      if (!response.success) {
        throw new Error(response.error || 'Erro ao enviar mensagem');
      }

      console.log('[useMobileChat] Mensagem enviada com sucesso');

      // A mensagem chegará via realtime, não precisamos adicioná-la manualmente

    } catch (err) {
      console.error('[useMobileChat] Erro ao enviar mensagem:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar mensagem';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId]);

  // Enviar áudio
  const sendAudio = useCallback(async (audioBlob: Blob) => {
    console.log('[useMobileChat] Enviando mensagem de áudio');

    try {
      const response = await sendAudioMessage(appointmentId, userId, audioBlob);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao enviar áudio');
      }

      console.log('[useMobileChat] Áudio enviado com sucesso');

    } catch (err) {
      console.error('[useMobileChat] Erro ao enviar áudio:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar áudio';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId]);

  // Enviar arquivo
  const sendFile = useCallback(async (file: File) => {
    console.log('[useMobileChat] Enviando arquivo');

    try {
      const response = await sendAttachment(appointmentId, userId, file);

      if (!response.success) {
        throw new Error(response.error || 'Erro ao enviar arquivo');
      }

      console.log('[useMobileChat] Arquivo enviado com sucesso');

    } catch (err) {
      console.error('[useMobileChat] Erro ao enviar arquivo:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar arquivo';
      setError(errorMessage);
      throw err;
    }
  }, [appointmentId, userId]);

  // Iniciar digitação
  const startTyping = useCallback(() => {
    if (!enableTypingIndicators || isTyping) return;

    setIsTyping(true);
    realtimeService.current.sendTypingIndicator(appointmentId, userId, userName, true);
  }, [appointmentId, userId, userName, enableTypingIndicators, isTyping]);

  // Parar digitação
  const stopTyping = useCallback(() => {
    if (!enableTypingIndicators || !isTyping) return;

    setIsTyping(false);
    realtimeService.current.sendTypingIndicator(appointmentId, userId, userName, false);
  }, [appointmentId, userId, userName, enableTypingIndicators, isTyping]);

  // Limpar erro
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-conectar
  useEffect(() => {
    isMountedRef.current = true;

    if (autoConnect && appointmentId && userId) {
      connect();
    }

    return () => {
      isMountedRef.current = false;

      // Limpar timeout de digitação
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      disconnect();
    };
  }, [autoConnect, appointmentId, userId, connect, disconnect]);

  // Cleanup quando componente desmonta
  useEffect(() => {
    return () => {
      // Limpar timeout de digitação
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    messages,
    isLoading,
    error,
    connectionStatus,
    isConnected,
    isTyping,
    typingUsers,
    sendMessage,
    sendAudio,
    sendFile,
    startTyping,
    stopTyping,
    connect,
    disconnect,
    reconnect,
    refreshMessages,
    clearError,
  };
}
