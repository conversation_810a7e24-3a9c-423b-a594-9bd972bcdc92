"use client";

import { useState, useEffect, useCallback } from "react";
import { getVideoCallService, VideoCallStatus } from "@lib/services/video-call.service";

export function useVideoCall() {
  const [status, setStatus] = useState<VideoCallStatus>({
    status: 'idle',
    participants: [],
    localAudioEnabled: true,
    localVideoEnabled: true
  });
  const [error, setError] = useState<string | null>(null);

  const videoCallService = getVideoCallService();

  useEffect(() => {
    const unsubscribe = videoCallService.onStatusChange((newStatus) => {
      setStatus(newStatus);

      // Limpar erro quando conectado com sucesso
      if (newStatus.status === 'connected') {
        setError(null);
      } else if (newStatus.status === 'error') {
        setError(newStatus.error || 'Erro na chamada de vídeo');
      }
    });

    return unsubscribe;
  }, [videoCallService]);

  const startCall = useCallback(async (appointmentId: string, userId: string) => {
    try {
      setError(null);

      // Criar sala e obter token
      const { roomName, token } = await videoCallService.createRoom(appointmentId, userId);

      // Entrar na sala
      await videoCallService.joinRoom(roomName, token, {
        audio: true,
        video: true
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao iniciar chamada';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  const endCall = useCallback(async () => {
    try {
      await videoCallService.leaveRoom();
      setError(null);
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao finalizar chamada';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  const toggleAudio = useCallback(async () => {
    try {
      const enabled = await videoCallService.toggleAudio();
      return { success: true, enabled };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao alternar áudio';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  const toggleVideo = useCallback(async () => {
    try {
      const enabled = await videoCallService.toggleVideo();
      return { success: true, enabled };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao alternar vídeo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  const shareScreen = useCallback(async () => {
    try {
      await videoCallService.shareScreen();
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao compartilhar tela';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  const stopScreenShare = useCallback(async () => {
    try {
      await videoCallService.stopScreenShare();
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro ao parar compartilhamento';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [videoCallService]);

  return {
    status,
    error,
    startCall,
    endCall,
    toggleAudio,
    toggleVideo,
    shareScreen,
    stopScreenShare,
    isConnected: status.status === 'connected',
    isConnecting: status.status === 'connecting',
    participants: status.participants,
    localAudioEnabled: status.localAudioEnabled,
    localVideoEnabled: status.localVideoEnabled,
  };
}
