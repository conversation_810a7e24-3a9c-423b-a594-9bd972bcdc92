import { validatePlantaoConfig } from './plantao-config';
import { EvolutionService } from '../actions/checkout/integrations/evolution/evolution.service';
import { WhatsAppGroupService } from './whatsapp-group-service';

export interface WhatsAppDiagnostics {
  evolutionApi: {
    configured: boolean;
    apiKey: boolean;
    instance: boolean;
    url: boolean;
    connectionTest?: boolean;
    error?: string;
  };
  groups: {
    doctorsGroup: {
      configured: boolean;
      id?: string;
      testResult?: boolean;
      error?: string;
    };
    adminGroup: {
      configured: boolean;
      id?: string;
      testResult?: boolean;
      error?: string;
    };
  };
  services: {
    evolutionService: boolean;
    whatsappGroupService: boolean;
  };
  recommendations: string[];
}

export async function runWhatsAppDiagnostics(): Promise<WhatsAppDiagnostics> {
  const config = validatePlantaoConfig();
  const recommendations: string[] = [];
  
  const diagnostics: WhatsAppDiagnostics = {
    evolutionApi: {
      configured: config.isValid,
      apiKey: !!process.env.EVOLUTION_API_KEY,
      instance: !!process.env.EVOLUTION_INSTANCE,
      url: !!process.env.EVOLUTION_URL
    },
    groups: {
      doctorsGroup: {
        configured: !!process.env.WHATSAPP_DOCTORS_GROUP_ID,
        id: process.env.WHATSAPP_DOCTORS_GROUP_ID
      },
      adminGroup: {
        configured: !!process.env.WHATSAPP_ADMIN_GROUP_ID,
        id: process.env.WHATSAPP_ADMIN_GROUP_ID
      }
    },
    services: {
      evolutionService: false,
      whatsappGroupService: false
    },
    recommendations
  };

  // Testar Evolution API
  if (diagnostics.evolutionApi.configured) {
    try {
      const evolutionService = new EvolutionService();
      diagnostics.services.evolutionService = true;
      
      // Teste básico de conexão seria implementado aqui
      // Por enquanto, apenas verificamos se o serviço pode ser instanciado
      diagnostics.evolutionApi.connectionTest = true;
    } catch (error) {
      diagnostics.evolutionApi.error = error instanceof Error ? error.message : 'Erro desconhecido';
      diagnostics.evolutionApi.connectionTest = false;
      recommendations.push('Verificar configurações da Evolution API');
    }
  } else {
    recommendations.push('Configurar variáveis de ambiente da Evolution API');
    config.missingConfigs.forEach(missing => {
      recommendations.push(`Adicionar ${missing} ao arquivo .env`);
    });
  }

  // Testar WhatsApp Group Service
  try {
    const whatsappGroupService = new WhatsAppGroupService();
    diagnostics.services.whatsappGroupService = true;
  } catch (error) {
    recommendations.push('Verificar implementação do WhatsAppGroupService');
  }

  // Adicionar recomendações baseadas nos warnings
  config.warnings.forEach(warning => {
    recommendations.push(warning);
  });

  // Recomendações específicas para grupos
  if (!diagnostics.groups.doctorsGroup.configured) {
    recommendations.push('Configurar WHATSAPP_DOCTORS_GROUP_ID para notificações de médicos');
  }

  if (!diagnostics.groups.adminGroup.configured) {
    recommendations.push('Configurar WHATSAPP_ADMIN_GROUP_ID para notificações administrativas');
  }

  return diagnostics;
}

export function generateDiagnosticsReport(diagnostics: WhatsAppDiagnostics): string {
  const lines: string[] = [];
  
  lines.push('=== DIAGNÓSTICO WHATSAPP NOTIFICATIONS ===\n');
  
  // Evolution API Status
  lines.push('🔧 EVOLUTION API:');
  lines.push(`   Configurada: ${diagnostics.evolutionApi.configured ? '✅' : '❌'}`);
  lines.push(`   API Key: ${diagnostics.evolutionApi.apiKey ? '✅' : '❌'}`);
  lines.push(`   Instance: ${diagnostics.evolutionApi.instance ? '✅' : '❌'}`);
  lines.push(`   URL: ${diagnostics.evolutionApi.url ? '✅' : '❌'}`);
  
  if (diagnostics.evolutionApi.connectionTest !== undefined) {
    lines.push(`   Conexão: ${diagnostics.evolutionApi.connectionTest ? '✅' : '❌'}`);
  }
  
  if (diagnostics.evolutionApi.error) {
    lines.push(`   Erro: ${diagnostics.evolutionApi.error}`);
  }
  
  lines.push('');
  
  // Groups Status
  lines.push('👥 GRUPOS WHATSAPP:');
  lines.push(`   Grupo Médicos: ${diagnostics.groups.doctorsGroup.configured ? '✅' : '❌'}`);
  if (diagnostics.groups.doctorsGroup.id) {
    lines.push(`      ID: ${diagnostics.groups.doctorsGroup.id}`);
  }
  
  lines.push(`   Grupo Admin: ${diagnostics.groups.adminGroup.configured ? '✅' : '❌'}`);
  if (diagnostics.groups.adminGroup.id) {
    lines.push(`      ID: ${diagnostics.groups.adminGroup.id}`);
  }
  
  lines.push('');
  
  // Services Status
  lines.push('🛠️ SERVIÇOS:');
  lines.push(`   EvolutionService: ${diagnostics.services.evolutionService ? '✅' : '❌'}`);
  lines.push(`   WhatsAppGroupService: ${diagnostics.services.whatsappGroupService ? '✅' : '❌'}`);
  
  lines.push('');
  
  // Recommendations
  if (diagnostics.recommendations.length > 0) {
    lines.push('💡 RECOMENDAÇÕES:');
    diagnostics.recommendations.forEach((rec, index) => {
      lines.push(`   ${index + 1}. ${rec}`);
    });
  } else {
    lines.push('✅ SISTEMA CONFIGURADO CORRETAMENTE');
  }
  
  return lines.join('\n');
}

export async function testWhatsAppNotification(testData: {
  appointmentId: string;
  patientName: string;
  urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW';
}): Promise<{
  success: boolean;
  results: {
    medicalGroup?: any;
    adminGroup?: any;
  };
  errors: string[];
}> {
  const errors: string[] = [];
  const results: any = {};
  
  try {
    const whatsappGroupService = new WhatsAppGroupService();
    
    // Testar envio para grupo de médicos
    if (process.env.WHATSAPP_DOCTORS_GROUP_ID) {
      try {
        const medicalResult = await whatsappGroupService.sendToMedicalGroup(testData);
        results.medicalGroup = medicalResult;
        
        if (!medicalResult.success) {
          errors.push(`Erro no grupo médicos: ${medicalResult.error}`);
        }
      } catch (error) {
        errors.push(`Erro ao enviar para grupo médicos: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    } else {
      errors.push('WHATSAPP_DOCTORS_GROUP_ID não configurado');
    }
    
    // Testar envio para grupo admin
    if (process.env.WHATSAPP_ADMIN_GROUP_ID) {
      try {
        const adminResult = await whatsappGroupService.sendToAdminGroup(testData);
        results.adminGroup = adminResult;
        
        if (!adminResult.success) {
          errors.push(`Erro no grupo admin: ${adminResult.error}`);
        }
      } catch (error) {
        errors.push(`Erro ao enviar para grupo admin: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    } else {
      errors.push('WHATSAPP_ADMIN_GROUP_ID não configurado');
    }
    
    return {
      success: errors.length === 0,
      results,
      errors
    };
    
  } catch (error) {
    errors.push(`Erro geral: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    
    return {
      success: false,
      results,
      errors
    };
  }
}