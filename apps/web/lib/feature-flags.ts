/**
 * Sistema de Feature Flags para ZapVida
 * Permite controlar funcionalidades em produção sem deploy
 */

export interface FeatureFlags {
  // ZapChat V2 Migration
  useZapChatV2: boolean;
  zapChatV2Beta: boolean;

  // Outras flags futuras podem ser adicionadas aqui
  // enableNewDashboard: boolean;
  // enableAdvancedAnalytics: boolean;
}

/**
 * Configuração de feature flags baseada em variáveis de ambiente
 */
export const featureFlags: FeatureFlags = {
  // ZapChat V2 - Controla se deve usar a nova versão
  useZapChatV2: process.env.NEXT_PUBLIC_ZAPCHAT_V2_ENABLED === 'true',

  // ZapChat V2 Beta - Permite acesso a usuários beta específicos
  zapChatV2Beta: process.env.NEXT_PUBLIC_ZAPCHAT_V2_BETA === 'true',
};

/**
 * Verifica se um usuário tem acesso a uma feature específica
 * Pode ser expandido para incluir lógica baseada em user ID, role, etc.
 */
export function hasFeatureAccess(
  feature: keyof FeatureFlags,
  user?: { id: string; role: string }
): boolean {
  const flagValue = featureFlags[feature];

  // Se a flag está desabilitada globalmente, negar acesso
  if (!flagValue) return false;

  // Lógica específica por feature
  switch (feature) {
    case 'zapChatV2Beta':
      // Permitir para admins durante beta
      return user?.role === 'ADMIN' || user?.role === 'DOCTOR';

    case 'useZapChatV2':
      // Quando habilitada, permitir para todos
      return true;

    default:
      return flagValue;
  }
}

/**
 * Utilitário para debug de feature flags (apenas em desenvolvimento)
 */
export function debugFeatureFlags(): void {
  if (process.env.NODE_ENV === 'development') {
    console.log('🚩 Feature Flags Status:', featureFlags);
  }
}

/**
 * Hook para usar feature flags em componentes React
 */
export function useFeatureFlag(flag: keyof FeatureFlags, user?: { id: string; role: string }): boolean {
  return hasFeatureAccess(flag, user);
}
