import { Metadata } from "next";

/**
 * Formats a number as currency (BRL)
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

/**
 * Constructs metadata for pages
 */
export function constructMetadata({
  title = "ZapVida | Consulta Médica Online e Segura",
  description = "ZapVida é uma plataforma de atendimento médico online e oferece cuidado integral para quem necessita em qualquer circunstância e momento, podendo ser atendido por especialistas qualificados.",
  image = "/images/og-image.png",
  icons = "/favicon.ico",
  noIndex = false,
  canonical = "",
}: {
  title?: string;
  description?: string;
  image?: string;
  icons?: string;
  noIndex?: boolean;
  canonical?: string;
} = {}): Metadata {
  return {
    title,
    description,
    openGraph: {
      type: "website",
      locale: "pt_BR",
      url: "https://zapvida.com",
      title,
      description,
      siteName: "ZapVida",
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image],
      creator: "@zapvida",
    },
    icons,
    metadataBase: new URL("https://zapvida.com"),
    alternates: canonical ? {
      canonical,
    } : undefined,
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
}
