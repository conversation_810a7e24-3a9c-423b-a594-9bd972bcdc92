import { createClient } from '@supabase/supabase-js';

/**
 * Utilitário para testar a conexão do Supabase
 */
export class SupabaseTestUtil {
  private client;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variáveis de ambiente do Supabase não configuradas');
    }

    this.client = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Testa conexão básica com o banco
   */
  async testBasicConnection() {
    try {
      console.log('🔍 Testando conexão básica...');

      const { data, error } = await this.client
        .from('messages')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      console.log('✅ Conexão básica OK:', data);
      return { success: true, data };
    } catch (error) {
      console.error('❌ Erro na conexão básica:', error);
      return { success: false, error };
    }
  }

  /**
   * Testa funcionalidade Realtime
   */
  async testRealtime() {
    try {
      console.log('🔍 Testando Realtime...');

      return new Promise((resolve) => {
        const channel = this.client.channel('test-realtime');

        const subscription = channel.subscribe((status) => {
          console.log('📡 Status do canal:', status);

          if (status === 'SUBSCRIBED') {
            console.log('✅ Realtime funcionando!');
            this.client.removeChannel(channel);
            resolve({ success: true, status });
          } else if (status === 'CHANNEL_ERROR') {
            console.log('❌ Erro no canal Realtime');
            this.client.removeChannel(channel);
            resolve({ success: false, error: 'CHANNEL_ERROR' });
          }
        });

        // Timeout após 10 segundos
        setTimeout(() => {
          console.log('⏰ Timeout do teste Realtime');
          this.client.removeChannel(channel);
          resolve({ success: false, error: 'TIMEOUT' });
        }, 10000);
      });
    } catch (error) {
      console.error('❌ Erro no teste Realtime:', error);
      return { success: false, error };
    }
  }

  /**
   * Testa canal específico para mensagens
   */
  async testMessagesChannel(appointmentId: string) {
    try {
      console.log(`🔍 Testando canal de mensagens para appointment: ${appointmentId}`);

      return new Promise((resolve) => {
        const channel = this.client.channel(`messages-${appointmentId}`);

        const subscription = channel
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `appointmentId=eq.${appointmentId}`
          }, (payload) => {
            console.log('📨 Mensagem recebida:', payload);
          })
          .subscribe((status) => {
            console.log('📡 Status do canal de mensagens:', status);

            if (status === 'SUBSCRIBED') {
              console.log('✅ Canal de mensagens funcionando!');
              this.client.removeChannel(channel);
              resolve({ success: true, status });
            } else if (status === 'CHANNEL_ERROR') {
              console.log('❌ Erro no canal de mensagens');
              this.client.removeChannel(channel);
              resolve({ success: false, error: 'CHANNEL_ERROR' });
            }
          });

        // Timeout após 10 segundos
        setTimeout(() => {
          console.log('⏰ Timeout do teste de mensagens');
          this.client.removeChannel(channel);
          resolve({ success: false, error: 'TIMEOUT' });
        }, 10000);
      });
    } catch (error) {
      console.error('❌ Erro no teste de mensagens:', error);
      return { success: false, error };
    }
  }

  /**
   * Executa todos os testes
   */
  async runAllTests(appointmentId?: string) {
    console.log('🚀 Iniciando testes do Supabase...');

    const results = {
      basic: await this.testBasicConnection(),
      realtime: await this.testRealtime(),
      messages: appointmentId ? await this.testMessagesChannel(appointmentId) : null
    };

    console.log('📊 Resultados dos testes:', results);

    const allPassed = results.basic.success &&
                     results.realtime.success &&
                     (!appointmentId || results.messages?.success);

    if (allPassed) {
      console.log('🎉 Todos os testes passaram!');
    } else {
      console.log('⚠️ Alguns testes falharam. Verifique os logs acima.');
    }

    return { results, allPassed };
  }

  /**
   * Verifica configuração das variáveis de ambiente
   */
  static checkEnvironment() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    console.log('🔧 Verificando configuração do ambiente:');
    console.log('  URL:', supabaseUrl ? '✅ Configurada' : '❌ Não configurada');
    console.log('  Key:', supabaseKey ? '✅ Configurada' : '❌ Não configurada');

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Variáveis de ambiente do Supabase não configuradas!');
      console.error('   Adicione ao .env.local:');
      console.error('   NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co');
      console.error('   NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima');
      return false;
    }

    return true;
  }
}

/**
 * Função de conveniência para testar rapidamente
 */
export async function quickSupabaseTest(appointmentId?: string) {
  try {
    // Verificar ambiente primeiro
    if (!SupabaseTestUtil.checkEnvironment()) {
      return { success: false, error: 'Environment not configured' };
    }

    const tester = new SupabaseTestUtil();
    return await tester.runAllTests(appointmentId);
  } catch (error) {
    console.error('❌ Erro no teste rápido:', error);
    return { success: false, error };
  }
}

/**
 * Teste no console do navegador
 */
export function testInBrowser() {
  console.log('🧪 Teste do Supabase no Browser');
  console.log('Execute: quickSupabaseTest() para testar tudo');
  console.log('Ou use: new SupabaseTestUtil().runAllTests()');

  // Expor para uso no console
  (window as any).SupabaseTestUtil = SupabaseTestUtil;
  (window as any).quickSupabaseTest = quickSupabaseTest;
}
