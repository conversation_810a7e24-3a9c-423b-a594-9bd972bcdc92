/**
 * Formata um número de telefone para o padrão brasileiro: (XX) XXXXX-XXXX
 * @param value String com o telefone a ser formatado
 * @returns String formatada com máscara de telefone celular brasileiro
 */
export const formatPhone = (value: string): string => {
  if (!value) return "";

  // Remove tudo que não for número
  const phoneNumber = value.replace(/\D/g, "");

  // Aplica a formatação
  if (phoneNumber.length <= 2) {
    return phoneNumber.replace(/^(\d{0,2})/, "($1");
  } else if (phoneNumber.length <= 7) {
    return phoneNumber.replace(/^(\d{2})(\d{0,5})/, "($1) $2");
  } else if (phoneNumber.length <= 11) {
    return phoneNumber.replace(/^(\d{2})(\d{5})(\d{0,4})/, "($1) $2-$3");
  } else {
    // <PERSON>ita a 11 dígitos (DDD + 9 dígitos)
    return phoneNumber.slice(0, 11).replace(/^(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  }
};

/**
 * Remove a formatação de um telefone, deixando apenas números
 * @param value String com o telefone formatado
 * @returns String apenas com números
 */
export const unformatPhone = (value: string): string => {
  return value.replace(/\D/g, "");
};

/**
 * Formata um número de telefone para uso em APIs de WhatsApp
 * Adiciona o código de país (55 para Brasil) se necessário
 * @param value String com o telefone a ser formatado
 * @returns String formatada para uso com WhatsApp
 */
export const formatPhoneForWhatsApp = (value: string): string => {
  console.log("[FORMAT_PHONE] Formatting phone for WhatsApp:", { originalPhone: value });

  if (!value) {
    console.warn("[FORMAT_PHONE] Empty phone number provided");
    return "";
  }

  try {
    // Remove tudo que não for número
    let phoneNumber = value.replace(/\D/g, "");
    console.log("[FORMAT_PHONE] After removing non-numeric chars:", { phoneNumber });

    // Verifique se já começa com um código de país comum
    const hasInternationalPrefix = /^(1|55|44|33|49|86|81)/i.test(phoneNumber);
    console.log("[FORMAT_PHONE] Has international prefix:", { hasInternationalPrefix });

    // Para números brasileiros sem código (base 10-11 dígitos)
    // DDD (2 dígitos) + Número (8-9 dígitos)
    if (!hasInternationalPrefix && (phoneNumber.length === 10 || phoneNumber.length === 11)) {
      console.log("[FORMAT_PHONE] Adicionando código brasileiro (55) ao número:", phoneNumber);
      phoneNumber = `55${phoneNumber}`;
    }
    // Para números que parecem ser locais sem DDD (6-9 dígitos), erro provável
    else if (phoneNumber.length < 10) {
      console.warn("[PHONE_FORMAT_WARNING] Número possivelmente sem DDD ou muito curto:", {
        original: value,
        cleaned: phoneNumber,
        length: phoneNumber.length
      });
      // Tenta recuperar adicionando 55, mas pode não ser válido
      if (phoneNumber.length >= 8) {
        console.warn("[PHONE_FORMAT_ATTEMPT] Tentando adicionar código 55 ao número curto");
        phoneNumber = `55${phoneNumber}`;
      }
    }
    // Caso já tenha um formato que parece internacional (12+ dígitos)
    else if (phoneNumber.length >= 12) {
      console.log("[PHONE_FORMAT] Número com formato internacional detectado:", {
        cleaned: phoneNumber,
        length: phoneNumber.length
      });
    }
    // Formato anômalo (número muito longo ou curto demais)
    else {
      console.warn("[PHONE_FORMAT_WARNING] Formato de telefone inesperado:", {
        original: value,
        cleaned: phoneNumber,
        length: phoneNumber.length
      });
    }

    // Garanta que há pelo menos 10 dígitos (ainda que inválido)
    if (phoneNumber.length < 10) {
      console.error("[PHONE_FORMAT_ERROR] Número de telefone inválido após formatação:", phoneNumber);
    }

    console.log("[FORMAT_PHONE] Final formatted phone:", { phoneNumber, length: phoneNumber.length });

    return phoneNumber;
  } catch (error) {
    console.error("[FORMAT_PHONE] Error formatting phone:", error);
    // Return original phone as fallback
    return value.replace(/\D/g, "");
  }
};

/**
 * Identifica se um número tem código de país ou precisa ser prefixado com o código brasileiro (55)
 * @param value Número de telefone (apenas dígitos)
 * @returns True se já tiver código de país, false caso contrário
 */
export const hasCountryCode = (value: string): boolean => {
  if (!value) return false;

  const phoneNumber = value.replace(/\D/g, "");

  // Números brasileiros sem código de país são 10-11 dígitos
  if (phoneNumber.length === 10 || phoneNumber.length === 11) {
    return false;
  }

  // Números com código de país geralmente têm 12+ dígitos
  if (phoneNumber.length >= 12 && phoneNumber.length <= 15) {
    return true;
  }

  // Para números com formato inesperado, assume que não tem código
  return false;
};
