import { cn } from "@ui/lib";
import { Poppins } from "next/font/google";

import "./[locale]/globals.css";
import "@livekit/components-styles";
import "@livekit/components-styles/prefabs";

const sansFont = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-sans",
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans text-foreground antialiased",
          sansFont.variable,
        )}
      >
        {children}
      </body>
    </html>
  );
}


