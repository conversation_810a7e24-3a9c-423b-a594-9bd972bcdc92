import { setupExecSqlFunction } from "../../../../actions/appointments/messages/setup-exec-sql";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    const result = await setupExecSqlFunction();
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json({ success: false, error: "Erro interno no servidor" }, { status: 500 });
  }
}
