import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { AsaasSubscriptionClient } from "api/modules/subscriptions/asaas-subscription";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(
  _req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await currentUser();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    const id = params?.id;
    if (!id) {
      return NextResponse.json({ message: "ID inválido" }, { status: 400 });
    }

    const subscription = await db.patientSubscription.findUnique({ where: { id } });
    if (!subscription) {
      return NextResponse.json({ message: "Assinatura não encontrada" }, { status: 404 });
    }
    if (subscription.status === "CANCELED") {
      return NextResponse.json({ message: "Assinatura já está cancelada" }, { status: 409 });
    }

    // Tenta cancelar no Asaas se houver ID e chave configurada
    if (subscription.asaasSubscriptionId && process.env.ASAAS_API_KEY) {
      try {
        const asaas = new AsaasSubscriptionClient();
        await asaas.cancelSubscription(subscription.asaasSubscriptionId);
      } catch (e) {
        console.error("[ADMIN/CANCEL_SUBSCRIPTION] Erro ao cancelar no Asaas:", e);
        // Continua para cancelar localmente mesmo se Asaas falhar
      }
    }

    const updated = await db.patientSubscription.update({
      where: { id },
      data: { status: "CANCELED", canceledAt: new Date() },
    });

    // Atualiza flag do paciente
    await db.patient.update({
      where: { id: updated.patientId },
      data: { hasActiveSubscription: false },
    }).catch(() => {});

    return NextResponse.json({ success: true, id, status: updated.status });
  } catch (error: any) {
    console.error("[ADMIN/CANCEL_SUBSCRIPTION] Error:", error);
    return NextResponse.json({ message: error?.message || "Erro no servidor" }, { status: 500 });
  }
}


