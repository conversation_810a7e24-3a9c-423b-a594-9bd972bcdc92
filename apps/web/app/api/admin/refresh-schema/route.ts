import { refreshSchemaCache } from "../../../../actions/appointments/messages/refresh-schema-cache";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    const result = await refreshSchemaCache();
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json({ success: false, error: "Erro interno no servidor" }, { status: 500 });
  }
}
