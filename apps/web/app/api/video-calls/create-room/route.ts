import { NextRequest, NextResponse } from 'next/server';
import { AccessToken } from 'livekit-server-sdk';

export async function POST(request: NextRequest) {
  try {
    const { appointmentId, userId } = await request.json();

    if (!appointmentId || !userId) {
      return NextResponse.json(
        { error: 'appointmentId e userId são obrigatórios' },
        { status: 400 }
      );
    }

    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const wsUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !wsUrl) {
      console.error('Variáveis de ambiente do LiveKit não configuradas');
      return NextResponse.json(
        { error: 'Serviço de vídeo não configurado' },
        { status: 500 }
      );
    }

    // Nome da sala baseado no appointment ID
    const roomName = `appointment-${appointmentId}`;

    // Criar token de acesso
    const at = new AccessToken(apiKey, apiSecret, {
      identity: userId,
      name: `User-${userId}`,
    });

    // Adicionar permissões
    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
    });

    const token = at.toJwt();

    console.log(`[VideoCall API] Sala criada: ${roomName} para usuário: ${userId}`);

    return NextResponse.json({
      roomName,
      token,
      serverUrl: wsUrl,
    });

  } catch (error) {
    console.error('[VideoCall API] Erro ao criar sala:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
