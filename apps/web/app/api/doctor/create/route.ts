import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { z } from "zod";
import { Prisma } from "@prisma/client";

// Schema de validação
const createDoctorSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido"),
  crm: z.string().min(4, "CRM inválido"),
  crmState: z.string().length(2, "Estado de registro inválido"),
  specialtyIds: z.array(z.string()).min(1, "Selecione pelo menos uma especialidade"),
  hospitalId: z.string().min(1, "Hospital é obrigatório"),
  consultationDuration: z.number().min(15, "Duração mínima é de 15 minutos"),
  consultationPrice: z.number().min(0).default(0),
  biography: z.string().nullable().optional(),
});

export async function POST(request: NextRequest) {
  console.log("[Doctor Create API] Received request");

  try {
    // Get and validate user session
    const { user } = await currentUser();
    console.log("[Doctor Create API] Current user:", user?.id);

    if (!user) {
      console.log("[Doctor Create API] No authenticated user found");
      return NextResponse.json(
        { error: "Não autenticado" },
        { status: 401 }
      );
    }

    // Check if doctor profile already exists
    const existingDoctor = await db.doctor.findUnique({
      where: { userId: user.id },
    });

    if (existingDoctor) {
      console.log("[Doctor Create API] Doctor profile already exists for user:", user.id);
      return NextResponse.json(
        { error: "Perfil de médico já existe" },
        { status: 400 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (e) {
      console.error("[Doctor Create API] Failed to parse request body:", e);
      return NextResponse.json(
        { error: "Corpo da requisição inválido" },
        { status: 400 }
      );
    }

    console.log("[Doctor Create API] Request body:", JSON.stringify(body, null, 2));

    // Validate data schema
    let data;
    try {
      data = createDoctorSchema.parse(body);
      console.log("[Doctor Create API] Validated data:", JSON.stringify(data, null, 2));
    } catch (e) {
      console.error("[Doctor Create API] Schema validation error:", e);
      return NextResponse.json(
        {
          error: "Dados inválidos",
          details: e instanceof z.ZodError ? e.errors : String(e)
        },
        { status: 400 }
      );
    }

    // Check if CRM is already registered
    const doctorWithCrm = await db.doctor.findFirst({
      where: {
        crm: data.crm,
        crmState: data.crmState,
      },
    });

    if (doctorWithCrm) {
      console.log("[Doctor Create API] CRM already registered:", data.crm, data.crmState);
      return NextResponse.json(
        { error: "CRM já cadastrado no sistema" },
        { status: 409 }
      );
    }

    // Update user phone if provided
    if (data.phone) {
      console.log("[Doctor Create API] Updating user phone:", data.phone);
      await db.user.update({
        where: { id: user.id },
        data: { phone: data.phone },
      });
    }

    // Create doctor profile
    console.log("[Doctor Create API] Creating doctor profile with data:", {
      ...data,
      userId: user.id,
    });

    try {
      const doctor = await db.doctor.create({
        data: {
          userId: user.id,
          crm: data.crm,
          crmState: data.crmState,
          consultationPrice: new Prisma.Decimal(data?.consultationPrice?.toString() || "0"),
          consultationDuration: data.consultationDuration,
          biography: data.biography,
          specialties: {
            connect: data.specialtyIds.map((id) => ({ id })),
          },
          hospitals: {
            create: {
              hospitalId: data.hospitalId,
              isActive: true,
            },
          },
          rating: null,
          totalRatings: 0,
          documentStatus: "PENDING",
        },
        include: {
          user: true,
          specialties: true,
          hospitals: true,
        },
      });

      console.log("[Doctor Create API] Doctor profile created successfully:", doctor.id);

      // Update user role to DOCTOR
      await db.user.update({
        where: { id: user.id },
        data: { role: "DOCTOR" },
      });

      // Serialize the response
      const serializedDoctor = {
        ...doctor,
        consultationPrice: Number(doctor.consultationPrice),
        user: {
          ...doctor.user,
          image: doctor.user.avatarUrl,
        },
      };

      return NextResponse.json(
        {
          success: true,
          doctor: serializedDoctor
        },
        { status: 201 }
      );
    } catch (dbError) {
      console.error("[Doctor Create API] Database error:", dbError);
      return NextResponse.json(
        {
          error: "Erro ao criar perfil de médico",
          details: process.env.NODE_ENV === 'development' ? String(dbError) : undefined
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[Doctor Create API] Server error:", error);
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    );
  }
}
