import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { Prisma } from "@prisma/client";

// Simplificando a rota para diagnóstico
export async function POST(request: NextRequest) {
  console.log("=== [DOCTOR CREATE PROFILE] START ===");

  try {
    // 1. Obter usuário atual
    const sessionResult = await currentUser();
    console.log("[DOCTOR PROFILE] Session result:", JSON.stringify(sessionResult, null, 2));

    if (!sessionResult?.user?.id) {
      console.log("[DOCTOR PROFILE] Authentication failed - no user");
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = sessionResult.user;
    console.log("[DOCTOR PROFILE] User authenticated:", user.id);

    // 2. Verificar se já existe um perfil
    const existingDoctor = await db.doctor.findUnique({
      where: { userId: user.id },
    });

    if (existingDoctor) {
      console.log("[DOCTOR PROFILE] Doctor profile already exists:", existingDoctor.id);
      return NextResponse.json({ error: "Perfil de médico já existe" }, { status: 400 });
    }

    // 3. Processar o body da requisição
    let reqBody;
    try {
      reqBody = await request.json();
      console.log("[DOCTOR PROFILE] Request body:", JSON.stringify(reqBody, null, 2));
    } catch (e) {
      console.error("[DOCTOR PROFILE] Failed to parse request body:", e);
      return NextResponse.json({ error: "Formato de requisição inválido" }, { status: 400 });
    }

    // 4. Validar campos obrigatórios diretamente
    const {
      name,
      email,
      phone,
      crm,
      crmState,
      specialtyIds,
      hospitalId,
      consultationDuration,
      consultationPrice = 0,
      biography = null
    } = reqBody;

    if (!crm || !crmState || !specialtyIds?.length || !hospitalId) {
      const missingFields = [];
      if (!crm) missingFields.push("crm");
      if (!crmState) missingFields.push("crmState");
      if (!specialtyIds?.length) missingFields.push("specialtyIds");
      if (!hospitalId) missingFields.push("hospitalId");

      console.log("[DOCTOR PROFILE] Missing required fields:", missingFields);
      return NextResponse.json({
        error: "Campos obrigatórios ausentes",
        missingFields
      }, { status: 400 });
    }

    // 5. Verificar CRM existente
    const doctorWithCrm = await db.doctor.findFirst({
      where: {
        crm: crm,
        crmState: crmState,
      },
    });

    if (doctorWithCrm) {
      console.log("[DOCTOR PROFILE] CRM already registered:", crm, crmState);
      return NextResponse.json({ error: "CRM já cadastrado no sistema" }, { status: 409 });
    }

    // 6. Atualizar telefone do usuário
    if (phone) {
      try {
        await db.user.update({
          where: { id: user.id },
          data: { phone },
        });
        console.log("[DOCTOR PROFILE] Updated user phone successfully");
      } catch (phoneUpdateError) {
        console.error("[DOCTOR PROFILE] Failed to update phone:", phoneUpdateError);
        // Continuar mesmo se falhar a atualização do telefone
      }
    }

    // 7. Criar o perfil do médico
    console.log("[DOCTOR PROFILE] Creating doctor profile...");
    try {
      // Converter valor decimal com segurança
      let priceValue;
      try {
        priceValue = new Prisma.Decimal(String(consultationPrice || 0));
        console.log("[DOCTOR PROFILE] Price value created:", priceValue);
      } catch (decimalError) {
        console.error("[DOCTOR PROFILE] Failed to create Decimal, using default 0:", decimalError);
        priceValue = new Prisma.Decimal("0");
      }

      // Criar perfil
      const doctor = await db.doctor.create({
        data: {
          userId: user.id,
          crm,
          crmState,
          consultationPrice: priceValue,
          consultationDuration: Number(consultationDuration) || 30,
          biography,
          // Lidar com campo documentStatus conforme o schema atual
          documentStatus: "PENDING",
          specialties: {
            connect: specialtyIds.map((id: string) => ({ id })),
          },
          hospitals: {
            create: {
              hospitalId,
              isActive: true,
            },
          },
          rating: null,
          totalRatings: 0,
        },
        include: {
          user: true,
          specialties: true,
        },
      });

      console.log("[DOCTOR PROFILE] Doctor profile created successfully:", doctor.id);

      // 8. Atualizar role para DOCTOR
      await db.user.update({
        where: { id: user.id },
        data: { role: "DOCTOR" },
      });
      console.log("[DOCTOR PROFILE] Updated user role to DOCTOR");

      // 9. Serializar a resposta
      const serializedResponse = {
        success: true,
        doctor: {
          id: doctor.id,
          crm: doctor.crm,
          crmState: doctor.crmState,
          biography: doctor.biography,
          consultationDuration: doctor.consultationDuration,
          consultationPrice: Number(doctor.consultationPrice),
          specialties: doctor.specialties.map(s => ({
            id: s.id,
            name: s.name
          })),
          user: {
            id: doctor.user.id,
            name: doctor.user.name,
            email: doctor.user.email,
            phone: doctor.user.phone
          }
        }
      };

      return NextResponse.json(serializedResponse, { status: 201 });
    } catch (dbError: any) {
      console.error("[DOCTOR PROFILE] Database error:", dbError);

      // Extrair detalhes de erro mais específicos
      let errorDetails = String(dbError);
      let statusCode = 500;

      if (dbError.code) {
        console.error("[DOCTOR PROFILE] Database error code:", dbError.code);

        // Mapear códigos de erro Prisma para mensagens amigáveis
        if (dbError.code === 'P2002') {
          errorDetails = "Dados duplicados encontrados.";
          statusCode = 409;
        } else if (dbError.code === 'P2003') {
          errorDetails = "Referência inválida (ID não encontrado).";
          statusCode = 400;
        }
      }

      return NextResponse.json({
        error: "Erro ao criar perfil de médico",
        details: errorDetails
      }, { status: statusCode });
    }
  } catch (error: any) {
    console.error("[DOCTOR PROFILE] Unhandled server error:", error);

    return NextResponse.json({
      error: "Erro interno do servidor",
      message: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  } finally {
    console.log("=== [DOCTOR CREATE PROFILE] END ===");
  }
}
