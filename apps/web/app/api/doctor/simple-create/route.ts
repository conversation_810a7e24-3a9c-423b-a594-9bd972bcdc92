// apps/web/app/api/doctor/simple-create/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { Prisma } from "@prisma/client";

export async function POST(request: NextRequest) {
  console.log("=== [DOCTOR CREATE PROFILE] START ===");

  try {
    // 1. Obter usuário atual
    const { user } = await currentUser();

    if (!user?.id) {
      console.log("[DOCTOR PROFILE] Authentication failed - no user");
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    console.log("[DOCTOR PROFILE] User authenticated:", user.id);

    // 2. Verificar se já existe um perfil
    const existingDoctor = await db.doctor.findUnique({
      where: { userId: user.id },
      include: {
        specialties: true,
        hospitals: true
      }
    });

    // 3. Processar o body da requisição
    let reqData;
    try {
      reqData = await request.json();
      console.log("[DOCTOR PROFILE] Request body:", JSON.stringify(reqData, null, 2));
    } catch (e) {
      console.error("[DOCTOR PROFILE] Failed to parse request body:", e);
      return NextResponse.json({ error: "Formato de requisição inválido" }, { status: 400 });
    }

    // 4. Validar campos obrigatórios diretamente
    const {
      name,
      crm,
      crmState,
      specialtyIds,
      hospitalId,
      biography = null,
      phone,
      consultationDuration = 30,
      consultationPrice = 0
    } = reqData;

    if (!crm || !crmState || !specialtyIds?.length || !hospitalId) {
      const missingFields = [];
      if (!crm) missingFields.push("crm");
      if (!crmState) missingFields.push("crmState");
      if (!specialtyIds?.length) missingFields.push("specialtyIds");
      if (!hospitalId) missingFields.push("hospitalId");

      console.log("[DOCTOR PROFILE] Missing required fields:", missingFields);
      return NextResponse.json({
        error: "Campos obrigatórios ausentes",
        missingFields
      }, { status: 400 });
    }

    // 5. Verificar CRM existente (exceto para o médico atual)
    if (!existingDoctor) {
      const doctorWithCrm = await db.doctor.findFirst({
        where: {
          crm: crm,
          crmState: crmState,
        },
      });

      if (doctorWithCrm) {
        console.log("[DOCTOR PROFILE] CRM already registered:", crm, crmState);
        return NextResponse.json({ error: "CRM já cadastrado no sistema" }, { status: 409 });
      }
    }

    // 6. Atualizar ou criar médico
    if (existingDoctor) {
      console.log("[DOCTOR PROFILE] Updating existing doctor:", existingDoctor.id);

      // Atualizar dados do usuário
      if (name || phone) {
        await db.user.update({
          where: { id: user.id },
          data: {
            name: name || undefined,
            phone: phone || undefined
          },
        });
      }

      // Desconectar todas as especialidades existentes
      await db.doctor.update({
        where: { id: existingDoctor.id },
        data: {
          specialties: {
            disconnect: existingDoctor.specialties.map(s => ({ id: s.id }))
          }
        }
      });

      // Atualizar o médico
      await db.doctor.update({
        where: { id: existingDoctor.id },
        data: {
          crm,
          crmState,
          consultationDuration: Number(consultationDuration),
          biography,
          specialties: {
            connect: specialtyIds.map((id: string) => ({ id }))
          }
        }
      });

      // Verificar hospital
      const hasHospital = existingDoctor.hospitals.some(h => h.hospitalId === hospitalId);
      if (!hasHospital) {
        await db.doctorHospital.create({
          data: {
            doctorId: existingDoctor.id,
            hospitalId,
            isActive: true
          }
        });
      }

      // Atualizar role e onboarding
      await db.user.update({
        where: { id: user.id },
        data: {
          role: "DOCTOR",
          onboardingComplete: true
        }
      });

      return NextResponse.json({
        success: true,
        doctor: {
          id: existingDoctor.id,
          crm,
          crmState
        }
      });
    } else {
      console.log("[DOCTOR PROFILE] Creating new doctor");

      // Atualizar dados do usuário
      if (name || phone) {
        await db.user.update({
          where: { id: user.id },
          data: {
            name: name || undefined,
            phone: phone || undefined,
            role: "DOCTOR"
          },
        });
      }

      // Criar o perfil do médico
      try {
        const doctor = await db.doctor.create({
          data: {
            userId: user.id,
            crm,
            crmState,
            consultationDuration: Number(consultationDuration),
            consultationPrice: new Prisma.Decimal(String(consultationPrice || 0)),
            biography,
            documentStatus: "PENDING",
            specialties: {
              connect: specialtyIds.map((id: string) => ({ id }))
            },
            hospitals: {
              create: {
                hospitalId,
                isActive: true
              }
            },
            rating: null,
            totalRatings: 0
          },
          include: {
            user: true,
            specialties: true
          }
        });

        console.log("[DOCTOR PROFILE] Doctor created successfully:", doctor.id);

        // Marcar onboarding como concluído
        await db.user.update({
          where: { id: user.id },
          data: { onboardingComplete: true }
        });

        return NextResponse.json({
          success: true,
          doctor: {
            id: doctor.id,
            crm,
            crmState
          }
        }, { status: 201 });
      } catch (createError: any) {
        console.error("[DOCTOR PROFILE] Error creating doctor:", createError);

        if (createError.code === 'P2002') {
          return NextResponse.json({
            error: "Dados duplicados encontrados",
            details: createError.meta?.target
          }, { status: 409 });
        }

        return NextResponse.json({
          error: "Erro ao criar perfil de médico",
          details: createError.message
        }, { status: 500 });
      }
    }
  } catch (error: any) {
    console.error("[DOCTOR PROFILE] Unhandled server error:", error);

    return NextResponse.json({
      error: "Erro interno do servidor",
      message: process.env.NODE_ENV === 'development' ? String(error) : undefined
    }, { status: 500 });
  } finally {
    console.log("=== [DOCTOR CREATE PROFILE] END ===");
  }
}
