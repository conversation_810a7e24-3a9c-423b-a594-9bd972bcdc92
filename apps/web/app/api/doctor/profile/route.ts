// apps/web/app/api/doctor/profile/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { unformatPhone } from "../../../../lib/utils/format-phone";

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Check if user is a doctor
    if (user.role !== "DOCTOR") {
      return NextResponse.json({ error: "Usuário não é um médico" }, { status: 403 });
    }

    // Get doctor profile
    const doctor = await db.doctor.findUnique({
      where: { userId: user.id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            phone: true,
            avatarUrl: true
          }
        },
        specialties: true,
        hospitals: {
          include: {
            hospital: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    if (!doctor) {
      return NextResponse.json({ error: "Perfil de médico não encontrado" }, { status: 404 });
    }

    // Format the response
    const response = {
      id: doctor.id,
      crm: doctor.crm,
      crmState: doctor.crmState,
      consultationDuration: doctor.consultationDuration,
      consultationPrice: Number(doctor.consultationPrice),
      biography: doctor.biography,
      documentStatus: doctor.documentStatus,
      user: {
        name: doctor.user.name,
        email: doctor.user.email,
        phone: doctor.user.phone,
        avatarUrl: doctor.user.avatarUrl
      },
      specialties: doctor.specialties.map(s => ({
        id: s.id,
        name: s.name
      })),
      hospitals: doctor.hospitals.map(h => ({
        id: h.hospitalId,
        name: h.hospital.name
      }))
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching doctor profile:", error);
    return NextResponse.json(
      { error: "Erro ao buscar perfil do médico" },
      { status: 500 }
    );
  }
}

// Adicionar método para atualizar o perfil
export async function PATCH(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Check if user is a doctor
    if (user.role !== "DOCTOR") {
      return NextResponse.json({ error: "Usuário não é um médico" }, { status: 403 });
    }

    // Get update data from request
    const data = await request.json();
    console.log("[DOCTOR_PROFILE_UPDATE] Received update request:", data);

    // Validar phone se existir
    if (data.phone) {
      const phone = unformatPhone(data.phone);
      if (phone.length < 10 || phone.length > 15) {
        return NextResponse.json(
          { error: "Telefone inválido. Deve ter entre 10 e 15 dígitos." },
          { status: 400 }
        );
      }

      // Update user phone
      await db.user.update({
        where: { id: user.id },
        data: { phone }
      });

      console.log("[DOCTOR_PROFILE_UPDATE] Updated phone:", phone);
    }

    // Check if doctor exists
    const existingDoctor = await db.doctor.findUnique({
      where: { userId: user.id }
    });

    if (!existingDoctor) {
      return NextResponse.json(
        { error: "Perfil de médico não encontrado" },
        { status: 404 }
      );
    }

    // Update doctor profile (excluding specialties, they need a different update)
    const updateData: any = {};
    if (data.biography !== undefined) updateData.biography = data.biography;
    if (data.consultationDuration) updateData.consultationDuration = parseInt(data.consultationDuration);
    if (data.consultationPrice) updateData.consultationPrice = parseFloat(data.consultationPrice);

    // Only update if there are fields to update
    if (Object.keys(updateData).length > 0) {
      await db.doctor.update({
        where: { id: existingDoctor.id },
        data: updateData
      });

      console.log("[DOCTOR_PROFILE_UPDATE] Updated doctor profile:", updateData);
    }

    return NextResponse.json({
      success: true,
      message: "Perfil atualizado com sucesso"
    });

  } catch (error) {
    console.error("Error updating doctor profile:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar perfil do médico" },
      { status: 500 }
    );
  }
}
