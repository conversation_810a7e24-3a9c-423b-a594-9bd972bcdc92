// apps/web/app/api/doctor/schedule/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

// Função para criar uma resposta HTTP de forma segura
function safeResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' }
  });
}

// Função para gerar um ID único para os horários
function generateId(prefix = 'cs_') {
  return prefix + Date.now().toString() + Math.random().toString().slice(2, 8);
}

// Implementação do método GET para obter horários
export async function GET(request: NextRequest) {
  // Obter parâmetros da URL
  const searchParams = request.nextUrl.searchParams;
  let doctorId = searchParams.get('doctorId'); // Tornar doctorId mutável

  // Verificar autenticação
  const sessionResult = await currentUser().catch(() => ({ user: null }));
  const user = sessionResult?.user; // Usar optional chaining

  if (!user) {
    return safeResponse({ success: false, message: "Não autenticado" }, 401);
  }

  // Validar parâmetros
  if (!doctorId) {
    // Se não foi passado um doctorId e o usuário é médico, tentar obter o próprio ID
    if (user.role === "DOCTOR") {
      try {
        const doctorProfile = await db.doctor.findUnique({
          where: { userId: user.id },
          select: { id: true }
        });

        if (doctorProfile) {
          doctorId = doctorProfile.id; // Atribuir o ID do médico logado
          console.log(`[API GET Schedule] User is doctor, fetching own schedule for doctorId: ${doctorId}`);
        } else {
          console.log(`[API GET Schedule] User is doctor but profile not found for userId: ${user.id}`);
          return safeResponse({ success: false, message: "Perfil de médico não encontrado para o usuário atual" }, 404);
        }
      } catch (dbError) {
        console.error("[API GET Schedule] Error fetching doctor profile:", dbError);
        return safeResponse({ success: false, message: "Erro ao buscar perfil do médico" }, 500);
      }
    } else {
      // Se não é médico e não forneceu doctorId
      console.log(`[API GET Schedule] doctorId not provided and user role is not DOCTOR (role: ${user.role})`);
      return safeResponse({ success: false, message: "ID do médico não fornecido" }, 400);
    }
  } else {
    // Se um ID específico foi solicitado, verificar permissões
    console.log(`[API GET Schedule] Requested schedule for specific doctorId: ${doctorId}`);
    if (user.role !== "ADMIN" && user.role !== "HOSPITAL") {
      // Se o usuário é médico, verificar se está solicitando seus próprios horários
      if (user.role === "DOCTOR") {
        try {
          const doctorProfile = await db.doctor.findUnique({
            where: { userId: user.id },
            select: { id: true }
          });

          if (!doctorProfile || doctorProfile.id !== doctorId) {
            console.log(`[API GET Schedule] Permission denied. Doctor ${user.id} trying to access schedule for ${doctorId}`);
            return safeResponse({ success: false, message: "Sem permissão para acessar horários de outro médico" }, 403);
          }
          console.log(`[API GET Schedule] Doctor ${user.id} accessing own schedule (${doctorId})`);
        } catch (dbError) {
          console.error("[API GET Schedule] Error verifying doctor permissions:", dbError);
          return safeResponse({ success: false, message: "Erro ao verificar permissões" }, 500);
        }
      } else {
        // Outros tipos de usuário sem permissão
        console.log(`[API GET Schedule] Permission denied. User role ${user.role} cannot access schedules.`);
        return safeResponse({ success: false, message: "Sem permissão para acessar horários" }, 403);
      }
    } else {
      console.log(`[API GET Schedule] Admin/Hospital user ${user.id} accessing schedule for ${doctorId}`);
    }
  }

  // Buscar horários para o médico solicitado (doctorId agora está definido)
  return await getSchedulesByDoctorId(doctorId);
}

// Função auxiliar para buscar horários de um médico
async function getSchedulesByDoctorId(doctorId: string) {
  console.log(`[API GET Schedule] Fetching schedules for doctorId: ${doctorId}`);
  try {
    // Verificar se o médico existe (opcional, mas bom para clareza)
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      select: { id: true }
    });

    if (!doctor) {
      console.log(`[API GET Schedule] Doctor not found: ${doctorId}`);
      return safeResponse({ success: false, message: "Médico não encontrado" }, 404);
    }

    // Buscar os horários
    const schedules = await db.doctorSchedule.findMany({
      where: { doctorId },
      orderBy: [
        { weekDay: 'asc' },
        { startTime: 'asc' }
      ]
    });
    console.log(`[API GET Schedule] Found ${schedules.length} schedule entries for doctor ${doctorId}`);

    // Agrupar por dia da semana para facilitar uso no frontend (opcional, mas pode ser útil)
    const schedulesByDay = Array.from({ length: 7 }, (_, i) => i).map(weekDay => { // Simplificado
      const daySchedules = schedules.filter(s => s.weekDay === weekDay);
      return {
        weekDay,
        dayName: getDayName(weekDay),
        schedules: daySchedules.map(s => ({
          id: s.id,
          startTime: s.startTime,
          endTime: s.endTime,
          isEnabled: s.isEnabled,
          isBreak: s.isBreak
        }))
      };
    });

    return safeResponse({
      success: true,
      doctorId,
      schedules: schedules, // Retorna a lista plana também
      schedulesByDay: schedulesByDay // Retorna a lista agrupada
    });
  } catch (dbError) {
    console.error(`[API GET Schedule] Error fetching schedules for doctor ${doctorId}:`, dbError);
    return safeResponse({ success: false, message: "Erro ao buscar horários" }, 500);
  }
}

// Função para obter nome do dia da semana
function getDayName(weekDay: number): string {
  const days = [
    "Domingo",
    "Segunda-feira",
    "Terça-feira",
    "Quarta-feira",
    "Quinta-feira",
    "Sexta-feira",
    "Sábado"
  ];

  return days[weekDay] || "";
}

// Método POST existente
export async function POST(request: NextRequest) {
  console.log("[API POST Schedule] Received request");
  // Step 1: Get user
  const sessionResult = await currentUser().catch(() => ({ user: null }));
  const user = sessionResult?.user;

  if (!user) {
    console.log("[API POST Schedule] Authentication failed");
    return safeResponse({ success: false, message: "Não autenticado" }, 401);
  }
  console.log(`[API POST Schedule] Authenticated user: ${user.id}, Role: ${user.role}`);

  // Step 2: Get request data
  let doctorId, schedule;

  try {
    const requestData = await request.json();
    doctorId = requestData.doctorId;
    schedule = requestData.schedule;
    console.log(`[API POST Schedule] Request data for doctorId: ${doctorId}, Schedule items: ${schedule?.length}`);
  } catch (e) {
    console.error("[API POST Schedule] Invalid request body:", e);
    return safeResponse({ success: false, message: "Requisição inválida" }, 400);
  }

  if (!doctorId || !Array.isArray(schedule)) {
    console.log("[API POST Schedule] Invalid data: doctorId or schedule missing/invalid");
    return safeResponse({ success: false, message: "Dados inválidos: doctorId e schedule (array) são obrigatórios" }, 400);
  }

  // Step 3: Check permissions
  if (user.role !== "ADMIN" && user.role !== "HOSPITAL") {
    if (user.role === "DOCTOR") {
      try {
        const doctorProfile = await db.doctor.findUnique({
          where: { userId: user.id },
          select: { id: true }
        });
        if (!doctorProfile || doctorProfile.id !== doctorId) {
          console.log(`[API POST Schedule] Permission denied. Doctor ${user.id} trying to modify schedule for ${doctorId}`);
          return safeResponse({ success: false, message: "Você só pode modificar sua própria agenda" }, 403);
        }
        console.log(`[API POST Schedule] Doctor ${user.id} modifying own schedule (${doctorId})`);
      } catch (dbError) {
        console.error("[API POST Schedule] Error verifying doctor permissions:", dbError);
        return safeResponse({ success: false, message: "Erro ao verificar permissões" }, 500);
      }
    } else {
      console.log(`[API POST Schedule] Permission denied. User role ${user.role} cannot modify schedules.`);
      return safeResponse({ success: false, message: "Permissão negada" }, 403);
    }
  } else {
     console.log(`[API POST Schedule] Admin/Hospital user ${user.id} modifying schedule for ${doctorId}`);
  }


  // Step 4: Check if doctor exists
  try {
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      select: { id: true }
    });
     if (!doctor) {
        console.log(`[API POST Schedule] Doctor not found: ${doctorId}`);
        return safeResponse({ success: false, message: "Médico não encontrado" }, 404);
     }
  } catch (dbError) {
    console.error(`[API POST Schedule] Error checking if doctor ${doctorId} exists:`, dbError);
    return safeResponse({ success: false, message: "Erro ao verificar existência do médico" }, 500);
  }

  // Step 5: Use a transaction
  console.log(`[API POST Schedule] Starting transaction to replace schedule for doctor ${doctorId}`);
  try {
    const transactionResults = await db.$transaction(async (prisma) => {
      const deleteResult = await prisma.doctorSchedule.deleteMany({
        where: { doctorId }
      });
      console.log(`[API POST Schedule] Deleted ${deleteResult.count} existing schedule entries for doctor ${doctorId}`);

      const createPromises = schedule.map(item => {
        if (!item || typeof item !== 'object' || item.weekDay === undefined || !item.startTime || !item.endTime) {
           console.warn("[API POST Schedule] Skipping invalid schedule item:", item);
           return null;
        }
        // Basic time validation
        if (item.startTime >= item.endTime) {
            console.warn(`[API POST Schedule] Skipping invalid time range: ${item.startTime} >= ${item.endTime} for day ${item.weekDay}`);
            return null;
        }
        return prisma.doctorSchedule.create({
          data: {
            doctorId,
            weekDay: Number(item.weekDay),
            startTime: String(item.startTime),
            endTime: String(item.endTime),
            isEnabled: item.isEnabled !== false,
            isBreak: item.isBreak === true,
          }
        });
      }).filter((p): p is NonNullable<typeof p> => p !== null); // Type guard to filter nulls correctly

      if (createPromises.length === 0 && schedule.length > 0) {
          // If all items were invalid
          throw new Error("Nenhum horário válido fornecido para criação.");
      }

      const createdSchedules = await Promise.all(createPromises);
      console.log(`[API POST Schedule] Created ${createdSchedules.length} new schedule entries for doctor ${doctorId}`);
      return createdSchedules;
    });

    // Step 6: Return success response
    console.log(`[API POST Schedule] Transaction successful for doctor ${doctorId}`);
    return safeResponse({
      success: true,
      message: `${transactionResults.length} horários foram configurados com sucesso.`,
      count: transactionResults.length,
      createdSchedules: transactionResults.map(s => s.id)
    }, 200); // <<< Explicitly add status 200

  } catch (error) {
      // Step 7: Handle errors (Simplified)
      console.error(`[API POST Schedule] Transaction or processing failed for doctor ${doctorId}:`);
      // Log the error object directly for inspection
      console.error(error);

      // Determine a user-friendly message
      let message = "Erro ao salvar os horários. Tente novamente.";
      if (error instanceof Error) {
          // You might want to check for specific Prisma error codes here
          // e.g., if (error.code === 'P2002') { message = "Conflito de horário detectado."; }
          if (error.message.includes("Nenhum horário válido")) {
              message = error.message;
          }
      }

      // Return a standard NextResponse.json for errors
      return NextResponse.json({
          success: false,
          message: message,
          // Optionally include error details in non-production environments
          // details: process.env.NODE_ENV !== 'production' && error instanceof Error ? error.message : undefined,
          // code: error instanceof Error ? (error as any).code : undefined // Include Prisma code if available
      }, { status: 500 });
  }
}
