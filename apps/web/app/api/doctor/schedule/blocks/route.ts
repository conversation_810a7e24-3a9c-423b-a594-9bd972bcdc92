// apps/web/app/api/doctor/schedule/blocks/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Get doctorId from query params
    const searchParams = request.nextUrl.searchParams;
    const doctorId = searchParams.get("doctorId");

    if (!doctorId) {
      // If no doctorId provided, find the current user's doctor profile
      if (user.role === "DOCTOR") {
        const doctorProfile = await db.doctor.findUnique({
          where: { userId: user.id },
          select: { id: true }
        });

        if (!doctorProfile) {
          return NextResponse.json({ error: "Perfil de médico não encontrado" }, { status: 404 });
        }

        // Get blocks for the doctor
        const blocks = await db.scheduleBlock.findMany({
          where: { doctorId: doctorProfile.id },
          orderBy: { startTime: "asc" }
        });

        return NextResponse.json(blocks);
      } else {
        return NextResponse.json({ error: "doctorId é obrigatório" }, { status: 400 });
      }
    }

    // Get blocks for the specified doctor
    const blocks = await db.scheduleBlock.findMany({
      where: { doctorId },
      orderBy: { startTime: "asc" }
    });

    return NextResponse.json(blocks);
  } catch (error) {
    console.error("Error fetching doctor blocks:", error);
    return NextResponse.json(
      { error: "Erro ao buscar bloqueios" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Parse request body
    const data = await request.json();
    const { doctorId, startTime, endTime, reason, type = "PERSONAL" } = data;

    // Validate required fields
    if (!doctorId || !startTime || !endTime) {
      return NextResponse.json(
        { error: "doctorId, startTime e endTime são obrigatórios" },
        { status: 400 }
      );
    }

    // Check permissions
    if (user.role === "DOCTOR") {
      // If the user is a doctor, ensure they're only modifying their own blocks
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
        select: { id: true }
      });

      if (!doctor || doctor.id !== doctorId) {
        return NextResponse.json(
          { error: "Você só pode modificar sua própria agenda" },
          { status: 403 }
        );
      }
    } else if (user.role !== "ADMIN" && user.role !== "HOSPITAL") {
      return NextResponse.json(
        { error: "Permissão negada" },
        { status: 403 }
      );
    }

    // Check for schedule conflicts (appointments)
    const conflictingAppointments = await db.appointment.findMany({
      where: {
        doctorId,
        scheduledAt: {
          gte: new Date(startTime),
          lte: new Date(endTime)
        },
        status: {
          notIn: ["CANCELED", "COMPLETED"]
        }
      }
    });

    if (conflictingAppointments.length > 0) {
      return NextResponse.json(
        {
          error: "Existem consultas agendadas neste período",
          conflicts: conflictingAppointments.length
        },
        { status: 409 }
      );
    }

    // Create the block
    const block = await db.scheduleBlock.create({
      data: {
        doctorId,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        reason: reason || null,
        description: null,
        type: type
      }
    });

    return NextResponse.json(block);
  } catch (error) {
    console.error("Error creating schedule block:", error);
    return NextResponse.json(
      { error: "Erro ao criar bloqueio", details: String(error) },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({ error: "ID do bloqueio é obrigatório" }, { status: 400 });
    }

    // Get the block to check ownership
    const block = await db.scheduleBlock.findUnique({
      where: { id },
      include: {
        doctor: {
          select: { userId: true }
        }
      }
    });

    if (!block) {
      return NextResponse.json({ error: "Bloqueio não encontrado" }, { status: 404 });
    }

    // Check permissions
    if (user.role === "DOCTOR" && block.doctor.userId !== user.id) {
      return NextResponse.json({ error: "Permissão negada" }, { status: 403 });
    }

    // Delete the block
    await db.scheduleBlock.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting block:", error);
    return NextResponse.json({ error: "Erro ao excluir bloqueio" }, { status: 500 });
  }
}
