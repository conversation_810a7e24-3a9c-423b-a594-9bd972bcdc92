import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const transactionId = params.id;

  console.log('[TRANSACTION_API] Fetching transaction:', transactionId);

  if (!transactionId) {
    return NextResponse.json(
      { error: 'Missing transaction ID' },
      { status: 400 }
    );
  }

  try {
    // Get transaction from database
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        appointment: {
          include: {
            doctor: {
              include: {
                user: true,
                specialties: true
              }
            },
            patient: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    console.log(`[TRANSACTION_API] Found transaction ${transactionId} with payment method: ${transaction.paymentMethod}`);

    // Return transaction data with appointmentId and more details
    return NextResponse.json({
      id: transaction.id,
      status: transaction.status,
      appointmentId: transaction.appointmentId,
      amount: transaction.amount,
      paymentMethod: transaction.paymentMethod,
      paidAt: transaction.paidAt,
      doctorId: transaction.doctorId,
      asaasId: transaction.asaasId,
      createdAt: transaction.createdAt,
      doctor: transaction.appointment?.doctor ? {
        id: transaction.appointment.doctor.id,
        name: transaction.appointment.doctor.user?.name || 'Médico',
        specialty: transaction.appointment.doctor.specialties[0]?.name || 'Clínico Geral',
      } : null,
      appointment: transaction.appointment ? {
        id: transaction.appointment.id,
        scheduledAt: transaction.appointment.scheduledAt,
        duration: transaction.appointment.duration,
        status: transaction.appointment.status,
        type: transaction.appointment.appointmentType,
        patient: transaction.appointment.patient ? {
          id: transaction.appointment.patient.id,
          name: transaction.appointment.patient.user?.name,
          email: transaction.appointment.patient.user?.email
        } : null
      } : null
    });
  } catch (error) {
    console.error('[TRANSACTION_API] Error fetching transaction:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transaction details' },
      { status: 500 }
    );
  }
}
