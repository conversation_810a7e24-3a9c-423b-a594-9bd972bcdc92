import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';
import { PaymentStatus } from '@prisma/client';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const transactionId = searchParams.get('transactionId');

  // Logar os parâmetros de busca de forma segura
  console.log('[PAYMENT_API] Received request with transactionId:', transactionId);

  // Se tem transactionId, este é um pedido de status
  if (transactionId) {
    const txId = String(transactionId);
    console.log('[PAYMENT_API] Checking status for transaction:', transactionId);

    try {
      // Get transaction from database
      const transaction = await prisma.transaction.findUnique({
        where: { id: txId },
        include: {
          appointment: true
        }
      });

      if (!transaction) {
        return NextResponse.json(
          { error: 'Transaction not found' },
          { status: 404 }
        );
      }

      console.log(`[PAYMENT_API] Found transaction ${transactionId} with status: ${transaction.status}`);

      // Check if payment is already confirmed
      if (transaction.status === PaymentStatus.PAID) {
          return NextResponse.json({
          status: 'PAID',
            transactionId: txId,
          appointmentId: transaction.appointmentId,
          message: 'Pagamento confirmado com sucesso!'
        });
      }

      // If status is still PENDING, check with Asaas for latest status
      if (transaction.status === PaymentStatus.PENDING && transaction.asaasId) {
        try {
          console.log('[PAYMENT_API] Checking Asaas for latest status');
              const { AsaasClient } = await import('api/modules/asaas/client');
    const asaas = new AsaasClient();
          const asaasPaymentStatus = await asaas.getPaymentStatus(transaction.asaasId);

          console.log('[PAYMENT_API] Asaas payment status:', asaasPaymentStatus);

          // If Asaas status has changed, update our database
          if (asaasPaymentStatus.status !== 'PENDING') {
            // Map Asaas status to our PaymentStatus
            let newStatus: PaymentStatus = transaction.status;

            if (asaasPaymentStatus.status === 'CONFIRMED' || asaasPaymentStatus.status === 'RECEIVED') {
              newStatus = 'PAID' as PaymentStatus;
            } else if (asaasPaymentStatus.status === 'REFUNDED' || asaasPaymentStatus.status === 'CHARGEBACK') {
              newStatus = 'REFUNDED' as PaymentStatus;
            } else if (asaasPaymentStatus.status === 'FAILED' || asaasPaymentStatus.status === 'DECLINED') {
              newStatus = 'FAILED' as PaymentStatus;
            }

            // Update transaction with new status
            if (newStatus !== transaction.status) {
              console.log(`[PAYMENT_API] Updating transaction status from ${transaction.status} to ${newStatus}`);

              const ops: any[] = [
                prisma.transaction.update({
                  where: { id: transaction.id },
                  data: {
                    status: newStatus,
                    paidAt: newStatus === 'PAID' ? new Date() : null,
                    refundedAt: newStatus === 'REFUNDED' ? new Date() : null,
                  }
                })
              ];
              if (transaction.appointmentId) {
                ops.push(
                  prisma.appointment.update({
                    where: { id: transaction.appointmentId },
                    data: { paymentStatus: newStatus }
                  })
                );
              }
              await prisma.$transaction(ops);

              // Return the new status
              return NextResponse.json({
                status: newStatus,
                transactionId: txId,
                appointmentId: transaction.appointmentId,
                message: newStatus === 'PAID'
                  ? 'Pagamento confirmado com sucesso!'
                  : newStatus === 'FAILED'
                    ? 'Pagamento falhou'
                    : newStatus === 'REFUNDED'
                      ? 'Pagamento foi reembolsado'
                      : 'Status de pagamento atualizado'
              });
            }
          }
        } catch (asaasError) {
          console.error('[PAYMENT_API] Error checking Asaas status:', asaasError);
          // Continue with the database status if Asaas check fails
        }
      }

      // If not paid or Asaas check didn't change status, return current status
      return NextResponse.json({
        status: transaction.status,
        transactionId: txId,
        appointmentId: transaction.appointmentId,
        message: transaction.status === 'PENDING'
          ? 'Aguardando confirmação de pagamento'
          : transaction.status === 'FAILED'
            ? 'Pagamento falhou'
            : transaction.status === 'REFUNDED'
              ? 'Pagamento foi reembolsado'
              : 'Status de pagamento desconhecido'
      });
    } catch (error) {
      console.error('[PAYMENT_API] Error checking payment status:', error);
      return NextResponse.json(
        { error: 'Failed to check payment status' },
        { status: 500 }
      );
    }
  }

  // Se chegou aqui, não é um pedido de status conhecido
  return NextResponse.json(
    { error: 'Invalid request. Missing required parameters.' },
    { status: 400 }
  );
}
