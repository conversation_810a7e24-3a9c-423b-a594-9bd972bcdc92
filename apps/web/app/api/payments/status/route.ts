import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { PaymentStatus } from '@prisma/client';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const transactionId = searchParams.get('transactionId');

  console.log('[PAYMENT_STATUS_API] Checking payment status for transaction:', transactionId);

  // Log all search params for debugging
  const allParams = Object.fromEntries(searchParams.entries());
  console.log('[PAYMENT_STATUS_API] All search parameters:', allParams);

  if (!transactionId) {
    return NextResponse.json(
      { error: 'Missing transaction ID' },
      { status: 400 }
    );
  }

  try {
    // Verificar se a transação existe no banco de dados
    console.log('[PAYMENT_STATUS_API] Attempting to find transaction in database...');

    // Get transaction from database
    const transaction = await prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        appointment: true
      }
    });

    if (!transaction) {
      console.error(`[PAYMENT_STATUS_API] Transaction not found for ID: ${transactionId}`);

      // Tentar buscar todas as transações recentes para depuração
      const recentTransactions = await prisma.transaction.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          status: true,
          createdAt: true
        }
      });

      console.log('[PAYMENT_STATUS_API] Recent transactions in database:', recentTransactions);

      return NextResponse.json(
        {
          error: 'Transaction not found',
          debug: {
            transactionId,
            recentTransactions
          }
        },
        { status: 404 }
      );
    }

    console.log(`[PAYMENT_STATUS_API] Found transaction ${transactionId} with status: ${transaction.status}`);
    console.log(`[PAYMENT_STATUS_API] Transaction details:`, {
      id: transaction.id,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod,
      asaasId: transaction.asaasId,
      appointmentId: transaction.appointmentId,
      createdAt: transaction.createdAt
    });

    // Check if payment is already confirmed
    if (transaction.status === 'PAID') {
      return NextResponse.json({
        status: 'PAID',
        transactionId,
        appointmentId: transaction.appointmentId,
        message: 'Pagamento confirmado com sucesso!'
      });
    }

    // If status is still PENDING, check with Asaas for latest status
    if (transaction.status === 'PENDING' && transaction.asaasId) {
      try {
        console.log('[PAYMENT_STATUS_API] Checking Asaas for latest status for asaasId:', transaction.asaasId);
            const { AsaasClient } = await import('api/modules/asaas/client');
    const asaas = new AsaasClient();
        const asaasPaymentStatus = await asaas.getPaymentStatus(transaction.asaasId);

        console.log('[PAYMENT_STATUS_API] Asaas payment status:', asaasPaymentStatus);

        // If Asaas status has changed, update our database
        if (asaasPaymentStatus.status !== 'PENDING') {
          // Map Asaas status to our PaymentStatus
          let newStatus: PaymentStatus = transaction.status;

          if (asaasPaymentStatus.status === 'CONFIRMED' || asaasPaymentStatus.status === 'RECEIVED') {
            newStatus = 'PAID' as PaymentStatus;
          } else if (asaasPaymentStatus.status === 'REFUNDED' || asaasPaymentStatus.status === 'CHARGEBACK') {
            newStatus = 'REFUNDED' as PaymentStatus;
          } else if (asaasPaymentStatus.status === 'FAILED' || asaasPaymentStatus.status === 'DECLINED') {
            newStatus = 'FAILED' as PaymentStatus;
          }

          // Update transaction with new status
          if (newStatus !== transaction.status) {
            console.log(`[PAYMENT_STATUS_API] Updating transaction status from ${transaction.status} to ${newStatus}`);

            await prisma.$transaction([
              prisma.transaction.update({
                where: { id: transaction.id },
                data: {
                  status: newStatus,
                  paidAt: newStatus === 'PAID' ? new Date() : null,
                  refundedAt: newStatus === 'REFUNDED' ? new Date() : null,
                }
              }),
              prisma.appointment.update({
                where: { id: transaction.appointmentId },
                data: {
                  paymentStatus: newStatus
                }
              })
            ]);

            // Return the new status
            return NextResponse.json({
              status: newStatus,
              transactionId,
              appointmentId: transaction.appointmentId,
              message: newStatus === 'PAID'
                ? 'Pagamento confirmado com sucesso!'
                : newStatus === 'FAILED'
                  ? 'Pagamento falhou'
                  : newStatus === 'REFUNDED'
                    ? 'Pagamento foi reembolsado'
                    : 'Status de pagamento atualizado'
            });
          }
        }
      } catch (asaasError) {
        console.error('[PAYMENT_STATUS_API] Error checking Asaas status:', asaasError);
        // Continue with the database status if Asaas check fails
      }
    }

    // If not paid or Asaas check didn't change status, return current status
    return NextResponse.json({
      status: transaction.status,
      transactionId,
      appointmentId: transaction.appointmentId,
      message: transaction.status === 'PENDING'
        ? 'Aguardando confirmação de pagamento'
        : transaction.status === 'FAILED'
          ? 'Pagamento falhou'
          : transaction.status === 'REFUNDED'
            ? 'Pagamento foi reembolsado'
            : 'Status de pagamento desconhecido'
    });
  } catch (error) {
    console.error('[PAYMENT_STATUS_API] Error checking payment status:', error);
    return NextResponse.json(
      { error: 'Failed to check payment status', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
