import { getSignedUrl } from '@lib/storage-utils';
import { NextRequest, NextResponse } from 'next/server';


/**
 * API route to proxy image requests and return signed URLs
 * This allows client components to display images from private S3 buckets
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');
    const bucket = searchParams.get('bucket');

    // Validate required parameters
    if (!path) {
      return NextResponse.json({ error: 'Path parameter is required' }, { status: 400 });
    }
    if (!bucket) {
      return NextResponse.json({ error: 'Bucket parameter is required' }, { status: 400 });
    }

    // Generate a signed URL using the storage utility
    const signedUrl = await getSignedUrl(path, {
      bucket,
      expiresIn: 3600 // 1 hour
    });

    // Redirect to the signed URL
    return NextResponse.redirect(signedUrl);
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return NextResponse.json({ error: 'Failed to generate signed URL' }, { status: 500 });
  }
}
