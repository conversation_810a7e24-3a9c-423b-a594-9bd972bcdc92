// apps/web/app/api/hospitals/route.ts
import { db } from "database";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const hospitals = await db.hospital.findMany({
			orderBy: {
				name: "asc",
			},
			select: {
				id: true,
				name: true,
			},
		});

		return NextResponse.json(hospitals);
	} catch (error) {
		console.error("Error fetching hospitals:", error);
		return NextResponse.json(
			{ error: "Failed to fetch hospitals" },
			{ status: 500 },
		);
	}
}

export async function POST(request: Request) {
	try {
		const data = await request.json();

		// Verificar se já existe um time ou criar um novo
		let teamId = data.teamId;

		if (!teamId) {
			// Criar um novo time se não for fornecido
			const team = await db.team.create({
				data: {
					name: `Hospital ${data.name}`,
				},
			});
			teamId = team.id;
		}

		const hospital = await db.hospital.create({
			data: {
				name: data.name,
				address: data.address,
				logoUrl: data.logoUrl,
				teamId: teamId,
				cnpj: data.cnpj || "00000000000000", // Use default if not provided
				contactEmail: data.contactEmail || "<EMAIL>",
				contactPhone: data.contactPhone || data.phone || "0000000000",
				settings: data.settings || {},
			},
		});

		return NextResponse.json(hospital, { status: 201 });
	} catch (error) {
		console.error("Erro ao criar hospital:", error);
		return NextResponse.json(
			{ error: "Erro ao criar hospital" },
			{ status: 500 },
		);
	}
}
