// apps/web/app/api/patient/dashboard/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const session = await currentUser();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Não autenticado" },
        { status: 401 }
      );
    }

    // Verificar se é um paciente
    if (session.user.role !== "PATIENT") {
      return NextResponse.json(
        { error: "Acesso não autorizado" },
        { status: 403 }
      );
    }

    // Buscar ID do paciente
    const patient = await db.patient.findFirst({
      where: { userId: session.user.id },
      select: { id: true },
    });

    if (!patient) {
      return NextResponse.json(
        { error: "Perfil de paciente não encontrado" },
        { status: 404 }
      );
    }

    // Buscar contadores para notificações no dashboard
    const notificationCounts = await getNotificationCounts(patient.id);

    return NextResponse.json({
      success: true,
      data: notificationCounts,
    });
  } catch (error) {
    console.error("Erro na API de dashboard:", error);
    return NextResponse.json(
      { error: "Erro ao processar solicitação" },
      { status: 500 }
    );
  }
}

async function getNotificationCounts(patientId: string) {
  // Buscar dados para mostrar contadores/badges no dashboard
  const [upcomingAppointmentsCount, unreadNotificationsCount, newPrescriptionsCount] = await Promise.all([
    // Contar próximas consultas
    db.appointment.count({
      where: {
        patientId,
        status: "SCHEDULED",
        scheduledAt: {
          gte: new Date(),
        },
      },
    }),

    // Contar notificações não lidas
    db.notification.count({
      where: {
        userId: patientId,
        read: false,
      },
    }),

    // Contar prescrições novas (últimas 24h)
    db.prescription.count({
      where: {
        appointment: {
          patientId,
        },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Últimas 24 horas
        },
      },
    }),
  ]);

  return {
    upcomingAppointments: upcomingAppointmentsCount,
    unreadNotifications: unreadNotificationsCount,
    newPrescriptions: newPrescriptionsCount,
    lastUpdated: new Date().toISOString(),
  };
}
