import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { PatientSubscriptionService } from '../../../../[locale]/(app)/patient/services/patient-subscription.service';

export async function GET(req: NextRequest) {
  try {
    const { user } = await currentUser();
    
    if (!user) {
      return NextResponse.json({ hasActiveSubscription: false }, { status: 401 });
    }

    const hasActiveSubscription = await PatientSubscriptionService.hasActiveSubscription(user.id);
    
    return NextResponse.json({ 
      hasActiveSubscription,
      userId: user.id 
    });
  } catch (error) {
    console.error('Erro ao verificar assinatura:', error);
    return NextResponse.json({ 
      hasActiveSubscription: false,
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}