import { NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function PUT(request: Request) {
  try {
    const { user } = await currentUser();

    if (!user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const userId = user.id;
    const data = await request.json();

    // Verificar se o paciente existe
    const existingPatient = await db.patient.findFirst({
      where: {
        userId,
      },
    });

    if (!existingPatient) {
      return NextResponse.json(
        { error: "Perfil de paciente não encontrado" },
        { status: 404 }
      );
    }

    // Atualizar o telefone no modelo de usuário
    await db.user.update({
      where: {
        id: userId,
      },
      data: {
        phone: data.phone,
      },
    });

    // Atualizar o paciente
    const updatedPatient = await db.patient.update({
      where: {
        id: existingPatient.id,
      },
      data: {
        cpf: data.cpf,
        birthDate: data.birthDate,
        gender: data.gender,
        height: data.height,
        weight: data.weight,
        bloodType: data.bloodType || null,
        allergies: data.allergies,
        // Atualizando o endereço como um campo JSON
        address: data.address,
      },
      include: {
        user: {
          select: {
            phone: true
          }
        }
      }
    });

    // Mesclar os dados atualizados com o telefone
    const patientWithPhone = {
      ...updatedPatient,
      phone: updatedPatient.user?.phone || ""
    };

    return NextResponse.json({
      message: "Perfil atualizado com sucesso",
      patient: patientWithPhone,
    });
  } catch (error) {
    console.error("Erro ao atualizar perfil do paciente:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
