import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    // Verificar autenticação
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Verificar se já existe um perfil de paciente para este usuário
    const existingPatient = await db.patient.findUnique({
      where: { userId: user.id },
    });

    if (existingPatient) {
      return NextResponse.json({ error: "Perfil de paciente já existe" }, { status: 400 });
    }

    // Obter dados do corpo da requisição
    const data = await request.json();

    // Validar dados mínimos necessários
    if (!data.cpf || !data.birthDate || !data.gender || !data.address) {
      return NextResponse.json({ error: "Dados incompletos" }, { status: 400 });
    }

    // Atualizar telefone do usuário se fornecido
    if (data.phone) {
      await db.user.update({
        where: { id: user.id },
        data: { phone: data.phone },
      });
    }

    // Criar perfil de paciente
    const patient = await db.patient.create({
      data: {
        userId: user.id,
        cpf: data.cpf,
        birthDate: data.birthDate,
        gender: data.gender,
        address: data.address,
        height: data.height || null,
        weight: data.weight || null,
        bloodType: data.bloodType || null,
        allergies: data.allergies || [],
        chronicConditions: data.chronicConditions || [],
        medications: data.medications || [],
        familyHistory: data.familyHistory || [],
      },
    });

    // Atualizar o role para PATIENT se for USER, mas preservar DOCTOR e ADMIN
    if (user.role === "USER" && user.role !== "DOCTOR" && user.role !== "ADMIN") {
      await db.user.update({
        where: { id: user.id },
        data: { role: "PATIENT" },
      });
    }

    return NextResponse.json({ success: true, patient }, { status: 201 });
  } catch (error) {
    console.error("Erro ao criar perfil de paciente:", error);
    return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
  }
}
