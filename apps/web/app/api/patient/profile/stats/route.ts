import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET(req: NextRequest) {
	try {
		const { user } = await currentUser();

		if (!user || user.role !== "PATIENT") {
			return NextResponse.json(
				{ error: "Acesso negado" },
				{ status: 403 }
			);
		}

		// Buscar estatísticas do paciente
		const currentDate = new Date();
		const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
		const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

		// Consultas realizadas este mês
		const appointmentsThisMonth = await db.appointment.count({
			where: {
				patientId: user.id,
				createdAt: {
					gte: startOfMonth,
					lte: endOfMonth,
				},
				status: "COMPLETED",
			},
		});

		// Próximas consultas agendadas
		const upcomingAppointments = await db.appointment.count({
			where: {
				patientId: user.id,
				status: "SCHEDULED",
				scheduledFor: {
					gte: new Date(),
				},
			},
		});

		// Última consulta
		const lastAppointment = await db.appointment.findFirst({
			where: {
				patientId: user.id,
				status: "COMPLETED",
			},
			orderBy: {
				createdAt: "desc",
			},
			select: {
				scheduledFor: true,
			},
		});

		// Status da assinatura
		const subscription = await db.patientSubscription.findFirst({
			where: {
				patientId: user.id,
				status: "ACTIVE",
			},
			select: {
				status: true,
			},
		});

		const stats = {
			appointmentsThisMonth,
			upcomingAppointments,
			lastAppointment: lastAppointment?.scheduledFor?.toISOString() || null,
			subscriptionStatus: subscription?.status || "INACTIVE",
		};

		return NextResponse.json(stats);
	} catch (error) {
		console.error("Erro ao carregar estatísticas do perfil:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}
