import { NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET() {
  try {
    const { user } = await currentUser();

    if (!user?.id) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Buscar o perfil do paciente
    const patient = await db.patient.findFirst({
      where: {
        userId,
      },
      include: {
        user: {
          select: {
            phone: true
          }
        }
      }
    });

    if (!patient) {
      return NextResponse.json(
        { error: "Perfil de paciente não encontrado" },
        { status: 404 }
      );
    }

    // Mesclar os dados do paciente com o telefone do usuário
    const patientWithPhone = {
      ...patient,
      phone: patient.user?.phone || ""
    };

    return NextResponse.json(patientWithPhone);
  } catch (error) {
    console.error("Erro ao buscar perfil do paciente:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
