// apps/web/app/api/user/complete-onboarding/route.ts
import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    // Update user to mark onboarding as complete
    await db.user.update({
      where: { id: user.id },
      data: { onboardingComplete: true }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return NextResponse.json(
      { error: "Erro ao completar onboarding" },
      { status: 500 }
    );
  }
}
