import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { AsaasClient } from 'api/modules/asaas/client';
import { ON_DUTY_CONFIG, PARTNER_CONFIG } from 'api/constants/on-duty';

export async function POST(req: NextRequest) {
  try {
    const input = await req.json();

    // Basic validation
    if (!input?.customerData?.name || !input?.customerData?.email) {
      return NextResponse.json({ message: 'Nome e email são obrigatórios' }, { status: 400 });
    }
    if (!input?.customerData?.cpf) {
      return NextResponse.json({ message: 'CPF é obrigatório' }, { status: 400 });
    }
    if (!input?.doctorId) {
      return NextResponse.json({ message: 'ID do médico é obrigatório' }, { status: 400 });
    }

    const doctor = await db.doctor.findUnique({
      where: { id: input.doctorId },
      include: { user: true, specialties: true },
    });
    if (!doctor) {
      return NextResponse.json({ message: 'Médico não encontrado' }, { status: 404 });
    }

    // Determine price and duration
    let amount = Number(doctor.consultationPrice);
    let duration = input?.duration || doctor.consultationDuration || 30;

    // Partner pricing overrides
    if (input?.partner) {
      amount = PARTNER_CONFIG.DEFAULT_PARTNER_PRICE;
      duration = ON_DUTY_CONFIG.LOW.duration; // default for partner consults
    } else if (input?.isOnDuty) {
      const level = String(input?.urgencyLevel || 'low').toUpperCase() as keyof typeof ON_DUTY_CONFIG;
      const cfg = ON_DUTY_CONFIG[level] || ON_DUTY_CONFIG.LOW;
      amount = cfg.price;
      duration = cfg.duration;
    }

    // Normalize scheduledAt
    const scheduledAt = input?.scheduledAt ? new Date(input.scheduledAt) : new Date(Date.now() + 2 * 24 * 60 * 60 * 1000);

    // Create or find user/patient
    let user = await db.user.findUnique({ where: { email: input.customerData.email }, include: { patient: true } });
    if (!user) {
      user = await db.user.create({
        data: {
          email: input.customerData.email,
          name: input.customerData.name,
          phone: input.customerData.phone || null,
          role: 'PATIENT',
        },
        include: { patient: true },
      });
    }
    let patient = user.patient;
    if (!patient) {
      patient = await db.patient.create({
        data: {
          userId: user.id,
          cpf: input.customerData.cpf,
          birthDate: new Date(),
          gender: 'N',
          address: {},
          allergies: [],
          chronicConditions: [],
        },
      });
    }

    // Create appointment
    const appointment = await db.appointment.create({
      data: {
        doctorId: doctor.id,
        patientId: patient.id,
        scheduledAt,
        duration,
        status: 'SCHEDULED',
        paymentStatus: 'PENDING',
        consultType: 'VIDEO',
        amount,
        appointmentType: 'TELEMEDICINE',
        isOnDuty: Boolean(input?.isOnDuty),
        urgencyLevel: input?.isOnDuty
          ? (String(input?.urgencyLevel || 'low').toUpperCase() as any)
          : null,
        partnerSource: input?.partner || null,
      },
    });

    // Create transaction
    const transaction = await db.transaction.create({
      data: {
        appointmentId: appointment.id,
        amount,
        platformFee: 0,
        doctorAmount: amount,
        status: 'PENDING',
        paymentMethod: input.paymentMethod,
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        partnerSource: input?.partner || null,
        doctorId: doctor.id,
      },
    });

    // Pay with Asaas
    const asaas = new AsaasClient();
    const paymentResult = await asaas.createPayment({
      customerData: {
        name: input.customerData.name,
        email: input.customerData.email,
        phone: input.customerData.phone || '',
        cpf: input.customerData.cpf || '',
      },
      paymentMethod: input.paymentMethod,
      creditCard:
        input.paymentMethod === 'CREDIT_CARD'
          ? {
              cardNumber: input.creditCard?.cardNumber || '',
              cardHolder: input.creditCard?.cardHolder || '',
              cardExpiry: input.creditCard?.cardExpiry || '',
              cardCvv: input.creditCard?.cardCvv || '',
              installments: input.creditCard?.installments || 1,
            }
          : undefined,
      totalAmount: Number(amount),
      transactionId: transaction.id,
    });

    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        asaasId: paymentResult?.id || null,
        status: paymentResult?.status === 'CONFIRMED' ? 'PAID' : 'PENDING',
      },
    });
    if (paymentResult?.status === 'CONFIRMED') {
      await db.appointment.update({ where: { id: appointment.id }, data: { paymentStatus: 'PAID' } });
    }

    let pixCode: any = undefined;
    if (input.paymentMethod === 'PIX' && paymentResult?.id) {
      try {
        pixCode = await asaas.getPixQRCode(paymentResult.id);
      } catch {}
    }

    return NextResponse.json({
      success: true,
      transactionId: transaction.id,
      paymentId: paymentResult?.id,
      appointmentId: appointment.id,
      status: paymentResult?.status,
      paymentMethod: input.paymentMethod,
      pixCode,
    });
  } catch (error: any) {
    console.error('[PAY/APPOINTMENT] Error:', error);
    return NextResponse.json({ message: error?.message || 'Erro no processamento' }, { status: 500 });
  }
}


