import { NextRequest, NextResponse } from 'next/server';
import { db } from 'database';
import { AsaasClient } from 'api/modules/asaas/client';
import { ON_DUTY_CONFIG } from 'api/constants/on-duty';
import { getBaseUrl } from 'utils';

export async function POST(req: NextRequest) {
  try {
    let input: any = null;
    try {
      input = await req.json();
    } catch (e: any) {
      console.error('[PAY/PLANTAO] Failed to parse JSON body:', e?.message || e);
      throw new Error('Invalid JSON body');
    }

    console.log('[PAY/PLANTAO] Incoming request:', {
      hasBody: <PERSON><PERSON>an(input),
      paymentMethod: input?.paymentMethod,
      urgencyLevel: input?.urgencyLevel,
      customerDataPresent: Boolean(input?.customerData),
      hasCC: Boolean(input?.creditCard),
    });

    // Basic input normalization/safety
    const customerData = input?.customerData ?? {};
    const paymentMethod: 'CREDIT_CARD' | 'PIX' | 'BOLETO' = input?.paymentMethod;
    const urgencyLevel = String(input?.urgencyLevel || '').toLowerCase();

    if (!customerData?.name || !customerData?.email) {
      return NextResponse.json({ message: 'Nome e email são obrigatórios' }, { status: 400 });
    }
    if (!customerData?.cpf) {
      return NextResponse.json({ message: 'CPF é obrigatório' }, { status: 400 });
    }
    if (!urgencyLevel) {
      return NextResponse.json({ message: 'Nível de urgência é obrigatório' }, { status: 400 });
    }

    // VALIDAÇÃO RIGOROSA DO TELEFONE PARA PLANTÃO
    if (!customerData?.phone) {
      console.error('[PAY/PLANTAO] Telefone não fornecido:', customerData);
      return NextResponse.json({ message: 'Telefone é obrigatório para atendimento de plantão' }, { status: 400 });
    }

    // Limpar e validar formato do telefone
    const cleanedPhone = customerData.phone.replace(/\D/g, '');
    if (cleanedPhone.length < 10 || cleanedPhone.length > 11) {
      console.error('[PAY/PLANTAO] Telefone inválido:', {
        original: customerData.phone,
        cleaned: cleanedPhone,
        length: cleanedPhone.length
      });
      return NextResponse.json({
        message: 'Telefone inválido. Deve ter 10 ou 11 dígitos (DDD + número)'
      }, { status: 400 });
    }

    console.log('[PAY/PLANTAO] Telefone validado:', {
      original: customerData.phone,
      cleaned: cleanedPhone,
      length: cleanedPhone.length
    });

    const level = String(urgencyLevel).toUpperCase() as keyof typeof ON_DUTY_CONFIG;
    const cfg = ON_DUTY_CONFIG[level];
    if (!cfg) {
      console.error('[PAY/PLANTAO] Invalid urgency level:', urgencyLevel);
      return NextResponse.json({ message: 'Nível de urgência inválido' }, { status: 400 });
    }
    const amount = cfg.price;
    const duration = cfg.duration;
    console.log('[PAY/PLANTAO] Normalized params:', { level, amount, duration });

    // Create/find user/patient
    let user = await db.user.findUnique({ where: { email: customerData.email }, include: { patient: true } });
    if (!user) {
      user = await db.user.create({
        data: {
          email: customerData.email,
          name: customerData.name,
          phone: cleanedPhone, // Usar telefone limpo e validado
          role: 'PATIENT',
        },
        include: { patient: true },
      });
      console.log('[PAY/PLANTAO] Usuário criado com telefone:', {
        userId: user.id,
        phone: cleanedPhone,
        phoneLength: cleanedPhone.length
      });
    } else {
      // Se usuário já existe, atualizar telefone se necessário
      if (user.phone !== cleanedPhone) {
        await db.user.update({
          where: { id: user.id },
          data: { phone: cleanedPhone }
        });
        console.log('[PAY/PLANTAO] Telefone do usuário atualizado:', {
          userId: user.id,
          oldPhone: user.phone,
          newPhone: cleanedPhone
        });
      }
    }
    console.log('[PAY/PLANTAO] User resolved:', { userId: user.id, hasPatient: Boolean(user.patient) });
    let patient = user.patient;
    if (!patient) {
      patient = await db.patient.create({
        data: {
          userId: user.id,
          cpf: String(customerData.cpf).replace(/\D/g, ''),
          birthDate: new Date(),
          gender: 'N',
          address: {},
          allergies: [],
          chronicConditions: [],
        },
      });
    }
    console.log('[PAY/PLANTAO] Patient resolved:', { patientId: patient.id });

    // Create appointment without doctor
    const appointment = await db.appointment.create({
      data: {
        patientId: patient.id,
        scheduledAt: new Date(),
        duration,
        status: 'SCHEDULED',
        paymentStatus: 'PENDING',
        consultType: 'CHAT',
        amount,
        appointmentType: 'TELEMEDICINE',
        isOnDuty: true,
        urgencyLevel: level as any,
        partnerSource: input?.partner || null,
        symptoms: input?.symptoms || `Atendimento de plantão - ${cfg.label}`,
      },
    });
    console.log('[PAY/PLANTAO] Appointment created:', { appointmentId: appointment.id });

    const transaction = await db.transaction.create({
      data: {
        appointmentId: appointment.id,
        amount,
        platformFee: 0,
        doctorAmount: amount,
        status: 'PENDING',
        paymentMethod,
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        partnerSource: input?.partner || null,
      },
    });
    console.log('[PAY/PLANTAO] Transaction created:', { transactionId: transaction.id, paymentMethod });

    const asaas = new AsaasClient();
    let paymentResult: any;
    try {
      console.log('[PAY/PLANTAO] Creating payment at Asaas...', {
        totalAmount: Number(amount),
        transactionId: transaction.id,
        method: paymentMethod,
        hasCC: paymentMethod === 'CREDIT_CARD',
      });
      paymentResult = await asaas.createPayment({
        customerData: {
          name: customerData.name,
          email: customerData.email,
          phone: cleanedPhone, // Usar telefone limpo e validado
          cpf: String(customerData.cpf).replace(/\D/g, ''),
        },
        paymentMethod,
        creditCard:
          paymentMethod === 'CREDIT_CARD'
            ? {
                cardNumber: input?.creditCard?.cardNumber || '',
                cardHolder: input?.creditCard?.cardHolder || '',
                cardExpiry: input?.creditCard?.cardExpiry || '',
                cardCvv: input?.creditCard?.cardCvv || '',
                installments: input?.creditCard?.installments || 1,
              }
            : undefined,
        totalAmount: Number(amount),
        transactionId: transaction.id,
      });
      console.log('[PAY/PLANTAO] Payment created:', {
        asaasId: paymentResult?.id,
        status: paymentResult?.status,
      });
    } catch (e: any) {
      console.error('[PAY/PLANTAO] createPayment failed:', e?.message || e);
      throw e;
    }

    await db.transaction.update({
      where: { id: transaction.id },
      data: {
        asaasId: paymentResult?.id || null,
        status: paymentResult?.status === 'CONFIRMED' ? 'PAID' : 'PENDING',
      },
    });

    // Se pagamento confirmado instantaneamente (cartão), atualizar appointment e disparar notificações
    if (paymentResult?.status === 'CONFIRMED') {
      await db.transaction.update({
        where: { id: transaction.id },
        data: { status: 'PAID' }
      });

      await db.appointment.update({ where: { id: appointment.id }, data: { paymentStatus: 'PAID' } });

      // Disparar notificações de forma assíncrona (não bloquear response)
      console.log('[PAY/PLANTAO] Pagamento confirmado instantaneamente, disparando notificações assíncronas...');

      // Usar setTimeout para não bloquear a resposta do checkout
      setTimeout(async () => {
        try {
          const baseUrl = getBaseUrl();
          const webhookUrl = `${baseUrl}/api/webhooks/plantao-payment-confirmed`;

          const notificationResponse = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              appointmentId: appointment.id,
              transactionId: transaction.id,
              paymentId: paymentResult.id
            })
          });

          if (!notificationResponse.ok) {
            console.error('[PAY/PLANTAO] Erro ao chamar webhook de notificações:', {
              status: notificationResponse.status,
              statusText: notificationResponse.statusText
            });
          } else {
            const result = await notificationResponse.json();
            console.log('[PAY/PLANTAO] Notificações processadas com sucesso:', result);
          }
        } catch (notificationError) {
          console.error('[PAY/PLANTAO] Erro ao processar notificações assíncronas:', notificationError);
        }
      }, 100); // Delay mínimo para não bloquear resposta
    }

    // Para pagamentos PIX, agendar verificação de status e notificações de fallback
    if (paymentMethod === 'PIX' && paymentResult?.id) {
      console.log('[PAY/PLANTAO] Pagamento PIX criado, agendando verificação de status e notificações de fallback...');

      // Agendar verificação de status do PIX após 5 minutos (fallback se webhook falhar)
      setTimeout(async () => {
        try {
          console.log('[PAY/PLANTAO] Executando verificação de fallback para PIX:', paymentResult.id);

          // Verificar se o pagamento já foi confirmado via webhook
          const currentTransaction = await db.transaction.findUnique({
            where: { id: transaction.id },
            include: { appointment: true }
          });

          if (currentTransaction && currentTransaction.status === 'PENDING') {
            console.log('[PAY/PLANTAO] PIX ainda pendente, verificando status no Asaas...');

            // Verificar status no Asaas
            const asaasClient = new AsaasClient();
            const paymentStatus = await asaasClient.getPaymentStatus(paymentResult.id);

            if (paymentStatus?.status === 'CONFIRMED' || paymentStatus?.status === 'RECEIVED') {
              console.log('[PAY/PLANTAO] PIX confirmado via verificação de fallback, atualizando status e enviando notificações...');

              // Atualizar status
              await db.transaction.update({
                where: { id: transaction.id },
                data: { status: 'PAID' }
              });

              await db.appointment.update({
                where: { id: appointment.id },
                data: { paymentStatus: 'PAID' }
              });

              // Enviar notificações via webhook
              const baseUrl = getBaseUrl();
              const webhookUrl = `${baseUrl}/api/webhooks/plantao-payment-confirmed`;

              const notificationResponse = await fetch(webhookUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  appointmentId: appointment.id,
                  transactionId: transaction.id,
                  paymentId: paymentResult.id
                })
              });

              if (!notificationResponse.ok) {
                console.error('[PAY/PLANTAO] Erro ao chamar webhook de notificações (fallback):', {
                  status: notificationResponse.status,
                  statusText: notificationResponse.statusText
                });
              } else {
                const result = await notificationResponse.json();
                console.log('[PAY/PLANTAO] Notificações processadas com sucesso via fallback:', result);
              }

              // 3. Gerar magic link para login automático (igual ao cartão)
              try {
                console.log('[PAY/PLANTAO] Gerando magic link para PIX confirmado via fallback...');

                const magicLinkResponse = await fetch(`${baseUrl}/api/auth/magic-link`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    appointmentId: appointment.id,
                    type: 'PLANTAO'
                  })
                });

                if (magicLinkResponse.ok) {
                  const magicLinkData = await magicLinkResponse.json();
                  console.log('[PAY/PLANTAO] Magic link gerado com sucesso para PIX:', {
                    success: magicLinkData.success,
                    hasMagicUrl: !!magicLinkData.magicUrl
                  });
                } else {
                  console.error('[PAY/PLANTAO] Erro ao gerar magic link para PIX:', {
                    status: magicLinkResponse.status,
                    statusText: magicLinkResponse.statusText
                  });
                }
              } catch (magicLinkError) {
                console.error('[PAY/PLANTAO] Erro ao gerar magic link para PIX via fallback:', magicLinkError);
              }
            } else {
              console.log('[PAY/PLANTAO] PIX ainda pendente no Asaas:', paymentStatus?.status);
            }
          } else if (currentTransaction?.status === 'PAID') {
            console.log('[PAY/PLANTAO] PIX já foi confirmado via webhook, não é necessário fallback');
          }
        } catch (fallbackError) {
          console.error('[PAY/PLANTAO] Erro na verificação de fallback do PIX:', fallbackError);
        }
      }, 5 * 60 * 1000); // 5 minutos
    }

    let pixCode: any = undefined;
    if (paymentMethod === 'PIX' && paymentResult?.id) {
      try {
        console.log('[PAY/PLANTAO] Fetching PIX QR Code for', paymentResult.id);
        pixCode = await asaas.getPixQRCode(paymentResult.id);
        console.log('[PAY/PLANTAO] PIX QR Code retrieved:', {
          hasImage: Boolean(pixCode?.encodedImage),
          hasPayload: Boolean(pixCode?.payload),
        });
      } catch {}
    }

    return NextResponse.json({
      success: true,
      transactionId: transaction.id,
      paymentId: paymentResult?.id,
      appointmentId: appointment.id,
      status: paymentResult?.status,
      paymentMethod,
      pixCode,
      urgencyLevel,
    });
  } catch (error: any) {
    console.error('[PAY/PLANTAO] Error:', error?.stack || error?.message || error);
    return NextResponse.json({ message: error?.message || 'Erro no processamento' }, { status: 500 });
  }
}


