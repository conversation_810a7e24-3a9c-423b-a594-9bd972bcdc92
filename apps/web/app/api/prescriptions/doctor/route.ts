import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const status = searchParams.get("status");
    const search = searchParams.get("search");

    const skip = (page - 1) * limit;

    // Construir filtros
    let whereClause: any = {};

    if (user.role === "DOCTOR") {
      // Médicos só veem suas próprias prescrições
      whereClause.appointment = {
        doctor: {
          userId: user.id
        }
      };
    }

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause.OR = [
        {
          appointment: {
            patient: {
              user: {
                name: {
                  contains: search,
                  mode: 'insensitive'
                }
              }
            }
          }
        },
        {
          id: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Buscar prescrições
    const [prescriptions, total] = await Promise.all([
      db.prescription.findMany({
        where: whereClause,
        include: {
          appointment: {
            include: {
              patient: {
                include: {
                  user: true
                }
              },
              doctor: {
                include: {
                  user: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.prescription.count({
        where: whereClause
      })
    ]);

    // Formatar dados para o frontend
    const formattedPrescriptions = prescriptions.map(prescription => ({
      id: prescription.id,
      appointmentId: prescription.appointmentId,
      patientName: prescription.appointment.patient.user.name,
      patientId: prescription.appointment.patient.id,
      doctorName: prescription.appointment.doctor.user.name,
      createdAt: prescription.createdAt.toISOString(),
      updatedAt: prescription.updatedAt.toISOString(),
      status: prescription.status,
      pdfUrl: prescription.pdfUrl,
      memedId: prescription.memedId,
      content: prescription.content
    }));

    return NextResponse.json({
      prescriptions: formattedPrescriptions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error("Erro ao buscar prescrições:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
