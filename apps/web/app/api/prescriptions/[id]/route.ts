import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const prescriptionId = params.id;

    // Buscar prescrição com todos os detalhes
    const prescription = await db.prescription.findUnique({
      where: { id: prescriptionId },
      include: {
        appointment: {
          include: {
            doctor: {
              include: {
                user: true,
                specialties: true
              }
            },
            patient: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!prescription) {
      return NextResponse.json({ error: "Prescrição não encontrada" }, { status: 404 });
    }

    // Verificar se o usuário tem acesso a esta prescrição
    const isPatient = prescription.appointment.patient.user.id === user.id;
    const isDoctor = prescription.appointment.doctor?.user.id === user.id;
    const isAdmin = user.role === "ADMIN";

    if (!isPatient && !isDoctor && !isAdmin) {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    // Formatar dados para o frontend
    const formattedPrescription = {
      id: prescription.id,
      appointmentId: prescription.appointmentId,
      doctor: {
        name: prescription.appointment.doctor?.user.name || "Médico",
        specialty: prescription.appointment.doctor?.specialties?.[0]?.name || "Especialidade",
        crm: prescription.appointment.doctor?.crm || "",
        crmState: prescription.appointment.doctor?.crmState || "",
        image: prescription.appointment.doctor?.user.avatarUrl
      },
      patient: {
        name: prescription.appointment.patient.user.name,
        email: prescription.appointment.patient.user.email
      },
      consultation: {
        id: prescription.appointment.id,
        scheduledAt: prescription.appointment.scheduledAt.toISOString(),
        type: prescription.appointment.isOnDuty ? "ON_DUTY" : "REGULAR",
        reason: prescription.appointment.symptoms || "Consulta médica",
        duration: prescription.appointment.duration,
        status: prescription.appointment.status
      },
      prescription: {
        status: prescription.status,
        content: prescription.content,
        pdfUrl: prescription.pdfUrl,
        memedId: prescription.memedId,
        createdAt: prescription.createdAt.toISOString(),
        updatedAt: prescription.updatedAt.toISOString()
      }
    };

    return NextResponse.json(formattedPrescription);

  } catch (error) {
    console.error("Erro ao buscar detalhes da prescrição:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
