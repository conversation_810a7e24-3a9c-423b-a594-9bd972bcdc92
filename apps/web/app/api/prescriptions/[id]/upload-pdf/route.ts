import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { uploadPrescriptionPDF } from "../../../../../lib/supabase-storage";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const prescriptionId = params.id;

    // Verificar se a prescrição existe
    const prescription = await db.prescription.findUnique({
      where: { id: prescriptionId },
      include: {
        appointment: {
          include: {
            doctor: true
          }
        }
      }
    });

    if (!prescription) {
      return NextResponse.json({ error: "Prescrição não encontrada" }, { status: 404 });
    }

    // Verificar se o médico é o dono da prescrição (ou se é admin)
    if (user.role === "DOCTOR" && prescription.appointment.doctor?.userId !== user.id) {
      return NextResponse.json({ error: "Acesso negado a esta prescrição" }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get("pdf") as File;

    if (!file) {
      return NextResponse.json({ error: "Arquivo PDF é obrigatório" }, { status: 400 });
    }

    // Verificar se é PDF
    if (file.type !== "application/pdf") {
      return NextResponse.json({ error: "Apenas arquivos PDF são permitidos" }, { status: 400 });
    }

    // Verificar tamanho (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "Arquivo muito grande. Máximo 10MB" }, { status: 400 });
    }

    // Upload para Supabase Storage
    const filename = `prescriptions/${prescriptionId}/${Date.now()}_${file.name}`;

    // Converter File para ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Fazer upload para Supabase Storage
    const uploadResult = await uploadPrescriptionPDF(arrayBuffer, filename, {
      appointmentId: prescription.appointmentId,
      originalName: file.name,
      uploadedAt: new Date().toISOString(),
      uploadedBy: user.id,
    });

    // Atualizar prescrição com URL do PDF
    const updatedPrescription = await db.prescription.update({
      where: { id: prescriptionId },
      data: {
        pdfUrl: uploadResult.url,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      prescription: updatedPrescription,
      pdfUrl: uploadResult.url
    });

  } catch (error) {
    console.error("Erro ao fazer upload do PDF:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const prescriptionId = params.id;

    // Verificar se a prescrição existe
    const prescription = await db.prescription.findUnique({
      where: { id: prescriptionId },
      include: {
        appointment: {
          include: {
            doctor: true
          }
        }
      }
    });

    if (!prescription) {
      return NextResponse.json({ error: "Prescrição não encontrada" }, { status: 404 });
    }

    // Verificar se o médico é o dono da prescrição (ou se é admin)
    if (user.role === "DOCTOR" && prescription.appointment.doctor?.userId !== user.id) {
      return NextResponse.json({ error: "Acesso negado a esta prescrição" }, { status: 403 });
    }

    // Remover URL do PDF
    const updatedPrescription = await db.prescription.update({
      where: { id: prescriptionId },
      data: {
        pdfUrl: null,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      prescription: updatedPrescription
    });

  } catch (error) {
    console.error("Erro ao remover PDF:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
