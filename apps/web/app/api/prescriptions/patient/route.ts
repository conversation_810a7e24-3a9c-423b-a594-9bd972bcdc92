import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é paciente
    if (user.role !== "PATIENT" && !user.patientProfile) {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const status = searchParams.get("status");
    const search = searchParams.get("search");

    const skip = (page - 1) * limit;

    // Buscar perfil do paciente
    const patient = await db.patient.findFirst({
      where: {
        user_id: user.id
      }
    });

    if (!patient) {
      return NextResponse.json({ error: "Perfil de paciente não encontrado" }, { status: 404 });
    }

    // Construir filtros
    let whereClause: any = {
      appointment: {
        patient_id: patient.id
      }
    };

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause.OR = [
        {
          appointment: {
            doctor: {
              user: {
                name: {
                  contains: search,
                  mode: 'insensitive'
                }
              }
            }
          }
        },
        {
          content: {
            path: ['medications'],
            array_contains: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive'
                }
              }
            ]
          }
        }
      ];
    }

    // Buscar prescrições
    const [prescriptions, total] = await Promise.all([
      db.prescription.findMany({
        where: whereClause,
        include: {
          appointment: {
            include: {
              doctor: {
                include: {
                  user: true,
                  specialties: true
                }
              },
              patient: {
                include: {
                  user: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.prescription.count({
        where: whereClause
      })
    ]);

    // Formatar dados para o frontend
    const formattedPrescriptions = prescriptions.map(prescription => ({
      id: prescription.id,
      appointmentId: prescription.appointmentId,
      doctor: {
        name: prescription.appointment.doctor?.user.name || "Médico",
        specialty: prescription.appointment.doctor?.specialties?.[0]?.name || "Especialidade",
        crm: prescription.appointment.doctor?.crm || "",
        image: prescription.appointment.doctor?.user.image
      },
      date: prescription.createdAt.toISOString(),
      consultation: {
        id: prescription.appointment.id,
        type: prescription.appointment.isOnDuty ? "ON_DUTY" : "REGULAR",
        reason: prescription.appointment.symptoms || "Consulta médica"
      },
      medications: prescription.content?.medications || [],
      status: prescription.status.toUpperCase(),
      validUntil: prescription.content?.validUntil || null,
      instructions: prescription.content?.instructions || null,
      digitalSignature: Boolean(prescription.pdfUrl || prescription.memedId),
      pdfUrl: prescription.pdfUrl,
      memedId: prescription.memedId
    }));

    return NextResponse.json({
      prescriptions: formattedPrescriptions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      summary: {
        total,
        active: prescriptions.filter(p => p.status === 'active').length,
        completed: prescriptions.filter(p => p.status === 'completed').length,
        cancelled: prescriptions.filter(p => p.status === 'cancelled').length
      }
    });

  } catch (error) {
    console.error("Erro ao buscar prescrições do paciente:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
