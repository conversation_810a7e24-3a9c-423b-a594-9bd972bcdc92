import { NextRequest, NextResponse } from 'next/server';
import { getSignedUrl as getStorageSignedUrl } from "@lib/storage-utils";

/**
 * API route to generate signed URLs for objects in S3 storage
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');
    const bucket = searchParams.get('bucket');
    const expiresIn = parseInt(searchParams.get('expiresIn') || '3600', 10);

    // Validate required parameters
    if (!path) {
      return NextResponse.json({ error: 'Path parameter is required' }, { status: 400 });
    }
    if (!bucket) {
      return NextResponse.json({ error: 'Bucket parameter is required' }, { status: 400 });
    }

    // Security check to prevent accessing unauthorized buckets
    const allowedBuckets = [
      process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME,
      process.env.NEXT_PUBLIC_UPLOADS_BUCKET_NAME
    ];

    if (!allowedBuckets.includes(bucket)) {
      return NextResponse.json({ error: 'Access to this bucket is not allowed' }, { status: 403 });
    }

    // Generate a signed URL using the storage utility
    const url = await getStorageSignedUrl(path, {
      bucket,
      expiresIn
    });

    // Return the signed URL
    return NextResponse.json({ url });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return NextResponse.json({ error: 'Failed to generate signed URL' }, { status: 500 });
  }
}
