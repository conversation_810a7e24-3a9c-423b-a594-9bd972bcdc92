import { NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";

export async function POST(req: Request) {
  try {
    // Verificar autenticação e permissão
    const session = await currentUser();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Não autorizado" },
        { status: 403 }
      );
    }

    // Obter SQL do corpo da requisição
    const { sql } = await req.json();
    if (!sql) {
      return NextResponse.json(
        { success: false, error: "SQL não fornecido" },
        { status: 400 }
      );
    }

    // Usar o cliente admin do Supabase
    const supabase = createAdminClient();

    // Criar função exec_sql se não existir
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION exec_sql(sql text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$;
    `;

    try {
      // Primeiro criar a função exec_sql (ou ignorar se já existir)
      await supabase.rpc('exec_sql', { sql: createFunctionSql });
    } catch (error) {
      console.log("Função exec_sql já pode existir:", error);
    }

    try {
      // Depois executar o SQL fornecido
      await supabase.rpc('exec_sql', { sql });
    } catch (error) {
      console.error("Erro ao executar SQL:", error);
    }

    try {
      // Forçar atualização do cache consultando a tabela messages
      await supabase.from('messages').select('id').limit(1);
    } catch (error) {
      console.log("Não foi possível consultar a tabela messages");
    }

    // Mesmo com erros, retornar sucesso pois algumas operações podem ter funcionado
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao processar requisição:", error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" },
      { status: 500 }
    );
  }
}
