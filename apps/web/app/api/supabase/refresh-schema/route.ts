import { NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";

export async function POST() {
  try {
    // Verificar autenticação e permissão
    const session = await currentUser();
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Não autorizado" },
        { status: 403 }
      );
    }

    // Usar o cliente admin do Supabase
    const supabase = createAdminClient();

    try {
      // Tentar forçar manualmente uma atualização do cache do schema
      // Método 1: Notificar o pgrst
      await supabase.rpc('exec_sql', {
        sql: "NOTIFY pgrst, 'reload schema';"
      }).catch(err => console.log('Método 1 falhou:', err));

      // Método 2: Acessar a tabela messages
      await supabase
        .from('messages')
        .select('id')
        .limit(1)
        .catch(err => console.log('Método 2 falhou:', err));

      // Método 3: Tentar um método mais agressivo
      const querySQL = `
        SELECT pg_notify('pgrst', 'reload schema');
        SELECT pg_sleep(1);
      `;

      await supabase.rpc('exec_sql', { sql: querySQL })
        .catch(err => console.log('Método 3 falhou:', err));

      return NextResponse.json({
        success: true,
        message: "Cache do schema atualizado com sucesso"
      });
    } catch (error) {
      console.error("Erro ao atualizar cache:", error);
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : "Erro desconhecido"
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Erro ao processar requisição:", error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" },
      { status: 500 }
    );
  }
}
