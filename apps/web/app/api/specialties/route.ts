import { NextResponse } from "next/server";
import { db } from "database";

export async function GET() {
  try {
    const specialties = await db.specialty.findMany({
      orderBy: {
        name: "asc",
      },
      select: {
        id: true,
        name: true,
        description: true,
      },
    });

    // Adicionar ícones baseados no nome da especialidade
    const specialtiesWithIcons = specialties.map(specialty => ({
      ...specialty,
      icon: getSpecialtyIcon(specialty.name),
      description: specialty.description || `Cuidados em ${specialty.name.toLowerCase()}`
    }));

    return NextResponse.json(specialtiesWithIcons);
  } catch (error) {
    console.error("Error fetching specialties:", error);
    return NextResponse.json(
      { error: "Failed to fetch specialties" },
      { status: 500 }
    );
  }
}

function getSpecialtyIcon(name: string): string {
  const icons: Record<string, string> = {
    'Cardiologia': '❤️',
    'Dermatologia': '🧴',
    'Ginecologia': '🌸',
    'Pediatria': '👶',
    'Clínica Geral': '🩺',
    'Clínica': '🩺',
    'Psiquiatria': '🧠',
    'Neurologia': '🧠',
    'Ortopedia': '🦴',
    'Oftalmologia': '👁️',
    'Otorrinolaringologia': '👂',
    'Endocrinologia': '⚡',
    'Gastroenterologia': '🫁',
    'Urologia': '🔵',
    'Oncologia': '🦋',
    'Reumatologia': '🦴',
    'Pneumologia': '🫁',
    'Hematologia': '🩸',
    'Infectologia': '🦠',
    'Nefrologia': '🫀',
  };

  // Buscar por correspondência parcial
  for (const [key, icon] of Object.entries(icons)) {
    if (name.toLowerCase().includes(key.toLowerCase())) {
      return icon;
    }
  }

  return '🩺'; // Ícone padrão
}
