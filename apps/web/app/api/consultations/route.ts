import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from "../../../modules/saas/auth/lib/current-user";
import { db } from "database";
import { UserRole } from "@prisma/client";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const role = searchParams.get('role');

    if (!userId || !role) {
      return NextResponse.json(
        { success: false, error: 'userId e role são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar se o usuário está autenticado
    const session = await currentUser();
    if (!session?.user || session.user.id !== userId) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    console.log(`[API] Buscando consultas para usuário: ${userId}, role: ${role}`);

    let consultations: any[] = [];

    if (role === 'doctor') {
      // Buscar consultas do médico
      const doctor = await db.doctor.findUnique({
        where: { userId: userId },
        select: { id: true }
      });

      if (!doctor) {
        return NextResponse.json(
          { success: false, error: 'Médico não encontrado' },
          { status: 404 }
        );
      }

      const appointments = await db.appointment.findMany({
        where: {
          OR: [
            { doctorId: doctor.id },
            { acceptedByDoctorId: doctor.id } // Para plantões aceitos
          ],
          chatEnabled: true,
          status: {
            in: ["SCHEDULED", "IN_PROGRESS", "COMPLETED"]
          }
        },
        orderBy: [
          { isOnDuty: "desc" }, // Plantões primeiro
          { urgencyLevel: "desc" }, // Urgência alta primeiro
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          isOnDuty: true,
          urgencyLevel: true,
          patient: {
            select: {
              user: {
                select: {
                  name: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          },
          _count: {
            select: {
              messages: {
                where: {
                  createdAt: {
                    gt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
                  }
                }
              }
            }
          }
        }
      });

      consultations = appointments.map(appointment => ({
        id: appointment.id ?? "",
        doctor_name: session.user?.name ?? "Médico",
        patient_name: appointment.patient?.user?.name ?? "Paciente",
        status: (appointment.status as 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED') ?? 'SCHEDULED',
        scheduled_at: appointment.scheduledAt?.toISOString() ?? new Date().toISOString(),
        created_at: appointment.createdAt?.toISOString() ?? new Date().toISOString(),
        updated_at: appointment.updatedAt?.toISOString() ?? new Date().toISOString(),
        unread_count: appointment._count?.messages ?? 0,
        is_on_duty: appointment.isOnDuty ?? false,
        urgency_level: appointment.urgencyLevel ?? null,
        last_message: appointment.messages?.[0] ? {
          content: appointment.messages[0]?.content ?? "",
          type: appointment.messages[0]?.type ?? "TEXT",
          created_at: appointment.messages[0]?.createdAt?.toISOString() ?? new Date().toISOString()
        } : undefined
      }));

    } else if (role === 'patient') {
      // Buscar consultas do paciente
      const patient = await db.patient.findUnique({
        where: { userId: userId },
        select: { id: true }
      });

      if (!patient) {
        return NextResponse.json(
          { success: false, error: 'Paciente não encontrado' },
          { status: 404 }
        );
      }

      const appointments = await db.appointment.findMany({
        where: {
          patientId: patient.id,
          chatEnabled: true,
          status: {
            in: ["SCHEDULED", "IN_PROGRESS", "COMPLETED"]
          }
        },
        orderBy: [
          { isOnDuty: "desc" },
          { urgencyLevel: "desc" },
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          isOnDuty: true,
          urgencyLevel: true,
          doctor: {
            select: {
              user: {
                select: {
                  name: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          },
          _count: {
            select: {
              messages: {
                where: {
                  createdAt: {
                    gt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
                  }
                }
              }
            }
          }
        }
      });

      consultations = appointments.map(appointment => ({
        id: appointment.id ?? "",
        doctor_name: appointment.doctor?.user?.name ?? "Médico",
        patient_name: session.user?.name ?? "Paciente",
        status: (appointment.status as 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED') ?? 'SCHEDULED',
        scheduled_at: appointment.scheduledAt?.toISOString() ?? new Date().toISOString(),
        created_at: appointment.createdAt?.toISOString() ?? new Date().toISOString(),
        updated_at: appointment.updatedAt?.toISOString() ?? new Date().toISOString(),
        unread_count: appointment._count?.messages ?? 0,
        is_on_duty: appointment.isOnDuty ?? false,
        urgency_level: appointment.urgencyLevel ?? null,
        last_message: appointment.messages?.[0] ? {
          content: appointment.messages[0]?.content ?? "",
          type: appointment.messages[0]?.type ?? "TEXT",
          created_at: appointment.messages[0]?.createdAt?.toISOString() ?? new Date().toISOString()
        } : undefined
      }));
    }

    console.log(`[API] ${consultations.length} consultas encontradas`);
    return NextResponse.json({
      success: true,
      consultations,
      total: consultations.length,
      hasMore: false
    });

  } catch (error) {
    console.error('[API] Erro:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao buscar consultas'
      },
      { status: 500 }
    );
  }
}
