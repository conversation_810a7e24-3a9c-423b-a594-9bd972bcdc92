import { NextResponse } from "next/server";

export async function GET() {
  try {
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    // Verificar se as variáveis estão configuradas
    const hasApiKey = !!apiKey && apiKey !== 'your_livekit_api_key_here';
    const hasApiSecret = !!apiSecret && apiSecret !== 'your_livekit_api_secret_here';
    const hasServerUrl = !!serverUrl && serverUrl !== 'wss://your-livekit-server.livekit.cloud';

    // Validar formato da URL se estiver configurada
    let isValidUrl = false;
    if (hasServerUrl) {
      try {
        new URL(serverUrl);
        isValidUrl = true;
      } catch {
        isValidUrl = false;
      }
    }

    return NextResponse.json({
      apiKey: hasApiKey ? '***' + apiKey.slice(-4) : null,
      serverUrl: hasServerUrl && isValidUrl ? serverUrl : null,
      hasApiSecret,
      isValidUrl,
      isConfigured: hasApiKey && hasApiSecret && hasServerUrl && isValidUrl,
      issues: [
        !hasApiKey && 'API Key não configurada',
        !hasApiSecret && 'API Secret não configurada',
        !hasServerUrl && 'Server URL não configurada',
        hasServerUrl && !isValidUrl && 'Server URL em formato inválido'
      ].filter(Boolean)
    });

  } catch (error) {
    console.error("Erro ao verificar configuração LiveKit:", error);
    return NextResponse.json(
      { error: "Erro interno ao verificar configuração" },
      { status: 500 }
    );
  }
}
