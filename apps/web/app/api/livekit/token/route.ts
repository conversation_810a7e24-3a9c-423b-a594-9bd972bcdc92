import { NextRequest, NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";
import { currentUser } from "@saas/auth/lib/current-user";

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { appointmentId, userRole, mode } = await request.json();

    if (!appointmentId || !userRole || !mode) {
      return NextResponse.json(
        { error: "Missing required fields: appointmentId, userRole, mode" },
        { status: 400 }
      );
    }

    // Verificar se as variáveis de ambiente estão definidas
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !serverUrl) {
      console.error("LiveKit environment variables not configured");
      return NextResponse.json(
        { error: "LiveKit not configured" },
        { status: 500 }
      );
    }

    // Criar token de acesso
    const at = new AccessToken(apiKey, apiSecret, {
      identity: `${user.id}-${userRole}`,
      name: user.name || `${userRole} ${user.id}`,
      metadata: JSON.stringify({
        userId: user.id,
        userRole,
        appointmentId,
        mode,
      }),
    });

    // Configurar permissões baseadas no tipo de chamada
    const roomName = `appointment-${appointmentId}`;
    
    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      canUpdateOwnMetadata: true,
      ...(mode === "video" && {
        canPublishSources: ["camera", "microphone"],
      }),
      ...(mode === "audio" && {
        canPublishSources: ["microphone"],
      }),
    });

    const token = at.toJwt();

    // Preparar URL do servidor com protocolo correto
    const formattedServerUrl = serverUrl.startsWith("wss://")
      ? serverUrl
      : `wss://${serverUrl}`;

    console.log(`Token LiveKit gerado para ${userRole} na sala ${roomName} (${mode})`);
    console.log(`Server URL formatada: ${formattedServerUrl}`);

    return NextResponse.json({
      token,
      serverUrl: formattedServerUrl,
      roomName,
      identity: at.identity,
    });

  } catch (error) {
    console.error("Erro ao gerar token LiveKit:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
