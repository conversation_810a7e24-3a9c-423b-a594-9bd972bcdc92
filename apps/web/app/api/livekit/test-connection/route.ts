import { NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";

export async function POST() {
  try {
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !serverUrl) {
      return NextResponse.json(
        { error: "Configuração incompleta do LiveKit" },
        { status: 400 }
      );
    }

    // Testar se conseguimos gerar um token
    try {
      const at = new AccessToken(apiKey, apiSecret, {
        identity: 'test-user',
        name: 'Test User',
        metadata: JSON.stringify({
          test: true,
          timestamp: new Date().toISOString()
        }),
      });

      at.addGrant({
        room: 'test-room',
        roomJoin: true,
        canPublish: false,
        canSubscribe: false,
        canPublishData: false,
      });

      const token = at.toJwt();

      if (!token) {
        throw new Error('Falha ao gerar token');
      }

      // Preparar URL do servidor com protocolo correto
      const formattedServerUrl = serverUrl.startsWith("wss://")
        ? serverUrl
        : `wss://${serverUrl}`;

      return NextResponse.json({
        success: true,
        message: 'Conexão com LiveKit testada com sucesso',
        details: {
          tokenGenerated: true,
          serverUrl: formattedServerUrl,
          originalServerUrl: serverUrl,
          apiKeyConfigured: !!apiKey,
          apiSecretConfigured: !!apiSecret,
          timestamp: new Date().toISOString()
        }
      });

    } catch (tokenError) {
      console.error("Erro ao gerar token de teste:", tokenError);
      return NextResponse.json(
        {
          error: "Falha ao gerar token de teste",
          details: tokenError instanceof Error ? tokenError.message : "Erro desconhecido"
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Erro ao testar conexão LiveKit:", error);
    return NextResponse.json(
      { error: "Erro interno ao testar conexão" },
      { status: 500 }
    );
  }
}


