import { NextResponse } from "next/server";
import { createApiCaller } from "api/trpc/caller";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const apiCaller = await createApiCaller();

    const doctor = await apiCaller.doctors.getById({ id: params.id });

    if (!doctor) {
      return NextResponse.json(
        { error: "Doctor not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(doctor);
  } catch (error) {
    console.error("Error fetching doctor:", error);
    return NextResponse.json(
      { error: "Failed to fetch doctor" },
      { status: 500 }
    );
  }
}
