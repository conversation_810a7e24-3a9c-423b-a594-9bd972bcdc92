import { NextRequest } from "next/server";
import { streamDoctorChat, type DoctorMess<PERSON> } from "@zapvida/ai";

// Use nodejs runtime to avoid edge bundling issues with workspace packages
export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = (await req.json()) as {
      doctorName: string;
      clinicName?: string;
      messages: DoctorM<PERSON><PERSON>[];
    };

    if (!process.env.OPENAI_API_KEY) {
      return new Response("Missing OPENAI_API_KEY", { status: 500 });
    }

    if (!body?.doctorName || !Array.isArray(body?.messages)) {
      return new Response("Invalid payload", { status: 400 });
    }

    const result = await streamDoctorChat({
      doctorName: body.doctorName,
      clinicName: body.clinicName,
      messages: body.messages,
    });

    // AI SDK response helper for App Router
    // Prefer toDataStreamResponse for broad compatibility across versions
    // @ts-ignore - method exists at runtime in AI SDK v3/v4
    if (typeof (result as any).toAIStreamResponse === "function") {
      return (result as any).toAIStreamResponse();
    }
    return (result as any).toDataStreamResponse();
  } catch (error) {
    console.error("/api/doctor-agent error", error);
    return new Response(JSON.stringify({ error: "Failed to process" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}


