import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { AppointmentStatus, Doctor, Hospital, Patient, User, Appointment, ConsultType, Prisma } from "@prisma/client";

// Helper function for consistent responses with improved error handling
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store, max-age=0'
    }
  });
}

// Type for API response that matches Prisma schema
type AppointmentWithRelations = Appointment & {
  doctor: Doctor & { user: User };
  patient: Patient & { user: User };
  hospital: Hospital | null;
};

// Define type for base appointment data
type BaseAppointment = {
  id: string;
  doctorId: string;
  patientId: string;
  hospitalId: string | null;
  scheduledAt: Date;
  consultType: ConsultType;
  duration: number;
  status: AppointmentStatus;
  appointmentType: string;
  amount: Prisma.Decimal | null;
  paymentStatus: string;
  symptoms: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export async function GET(request: NextRequest) {
  console.log("[API Route - GET Appointments] Received request");

  try {
    // 1. Authenticate User
    const { user } = await currentUser();
    if (!user) {
      console.log("[API Route - GET Appointments] Unauthorized access");
      return createResponse({ error: "Não autenticado" }, 401);
    }
    console.log(
      `[API Route - GET Appointments] User authenticated: ${user.id}, Role: ${user.role}`,
    );

    // 2. Get & Validate Query Parameters
    const searchParams = request.nextUrl.searchParams;
    const pageStr = searchParams.get("page") || "1";
    const perPageStr = searchParams.get("perPage") || "9";
    const statusParam = searchParams.get("status");

    // Validate and sanitize parameters
    let page = 1;
    try {
      const parsedPage = parseInt(pageStr, 10);
      if (!isNaN(parsedPage) && parsedPage > 0) {
        page = parsedPage;
      }
    } catch (e) {
      console.warn(`[API Route - GET Appointments] Invalid page parameter: ${pageStr}`);
    }

    let perPage = 9;
    try {
      const parsedPerPage = parseInt(perPageStr, 10);
      if (!isNaN(parsedPerPage) && parsedPerPage > 0 && parsedPerPage <= 50) {
        perPage = parsedPerPage;
      }
    } catch (e) {
      console.warn(`[API Route - GET Appointments] Invalid perPage parameter: ${perPageStr}`);
    }

    let status: AppointmentStatus | undefined = undefined;
    if (statusParam) {
      try {
        if (Object.values(AppointmentStatus).includes(statusParam as AppointmentStatus)) {
          status = statusParam as AppointmentStatus;
        } else {
          console.warn(`[API Route - GET Appointments] Invalid status parameter: ${statusParam}`);
        }
      } catch (e) {
        console.warn(`[API Route - GET Appointments] Error parsing status: ${statusParam}`);
      }
    }

    console.log("[API Route - GET Appointments] Validated Query Params:", {
      page,
      perPage,
      status,
    });

    // 3. Build Prisma 'where' clause based on role and status
    const where: any = {};

    // Role-based filtering
    if (user.role === "DOCTOR") {
      try {
        console.log(`[API Route - GET Appointments] Looking for doctor profile for user ${user.id}`);

        // First check if we can find the doctor
        const doctor = await db.doctor.findUnique({
          where: { userId: user.id },
          select: { id: true },
        });

        console.log(`[API Route - GET Appointments] Doctor lookup result:`, doctor);

        if (!doctor) {
          console.log(
            `[API Route - GET Appointments] No doctor profile found for user ${user.id}`,
          );

          // Debug: Check if there are any doctors in the system
          const anyDoctor = await db.doctor.findFirst({
            select: { id: true, userId: true }
          });
          console.log(`[API Route - GET Appointments] Sample doctor in database:`, anyDoctor);

          // Return an empty dataset instead of error
          return createResponse({
            appointments: [],
            pagination: { total: 0, pages: 0, page, perPage }
          });
        }

        where.doctorId = doctor.id;
        console.log(
          `[API Route - GET Appointments] Filtering by doctorId: ${doctor.id}`,
        );
      } catch (error) {
        console.error("[API Route - GET Appointments] Error finding doctor profile:", error);
        // Return empty list if doctor profile lookup fails
        return createResponse({
          appointments: [],
          pagination: { total: 0, pages: 0, page, perPage }
        });
      }
    } else if (user.role === "PATIENT") {
      try {
        console.log(`[API Route - GET Appointments] Looking for patient profile for user ${user.id}`);

        const patient = await db.patient.findUnique({
          where: { userId: user.id },
          select: { id: true },
        });

        console.log(`[API Route - GET Appointments] Patient lookup result:`, patient);

        if (!patient) {
          console.log(
            `[API Route - GET Appointments] No patient profile found for user ${user.id}`,
          );

          // Debug: Check if there are any patients in the system
          const anyPatient = await db.patient.findFirst({
            select: { id: true, userId: true }
          });
          console.log(`[API Route - GET Appointments] Sample patient in database:`, anyPatient);

          // Return empty list if patient profile doesn't exist
          return createResponse({
            appointments: [],
            pagination: { total: 0, pages: 0, page, perPage }
          });
        }

        where.patientId = patient.id;
        console.log(
          `[API Route - GET Appointments] Filtering by patientId: ${patient.id}`,
        );
      } catch (error) {
        console.error("[API Route - GET Appointments] Error finding patient profile:", error);
        // Return empty list if patient profile lookup fails
        return createResponse({
          appointments: [],
          pagination: { total: 0, pages: 0, page, perPage }
        });
      }
    }
    // No specific ID filter for ADMIN or other roles

    // Status filtering
    if (status) {
      where.status = status;
      console.log(
        `[API Route - GET Appointments] Filtering by status: ${status}`,
      );
    }

    // 4. Pagination Calculation
    const skip = (page - 1) * perPage;
    const take = perPage;

    // 5. Fetch Data with error handling
    console.log(
      `[API Route - GET Appointments] Fetching data: skip=${skip}, take=${take}`,
    );

    let total = 0;
    let baseAppointments: BaseAppointment[] = [];

    try {
      // Fetch total count
      console.log(`[API Route - GET Appointments] Counting appointments with where:`, where);
      total = await db.appointment.count({ where });
      console.log(`[API Route - GET Appointments] Total count: ${total}`);

      // If no appointments found, return early with empty array
      if (total === 0) {
        console.log("[API Route - GET Appointments] No appointments found for the given filters");
        return createResponse({
          appointments: [],
          pagination: { total: 0, pages: 0, page, perPage }
        });
      }

      // Fetch appointments with pagination
      console.log(`[API Route - GET Appointments] Fetching appointments with skip=${skip}, take=${take}, where:`, where);
      baseAppointments = await db.appointment.findMany({
        where,
        skip,
        take,
        orderBy: [{ scheduledAt: "desc" }],
        select: {
          id: true,
          doctorId: true,
          patientId: true,
          hospitalId: true,
          scheduledAt: true,
          consultType: true,
          duration: true,
          status: true,
          appointmentType: true,
          amount: true,
          paymentStatus: true,
          symptoms: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      console.log(`[API Route - GET Appointments] Fetched ${baseAppointments.length} appointments`);
    } catch (error) {
      console.error("[API Route - GET Appointments] Database error fetching appointments:", error);
      return createResponse({
        error: "Erro ao buscar consultas",
        details: "Ocorreu um erro ao consultar o banco de dados",
        appointments: [],
        pagination: { total: 0, pages: 0, page, perPage }
      }, 500);
    }

    // If no appointments after query, return empty results
    if (baseAppointments.length === 0) {
      return createResponse({
        appointments: [],
        pagination: {
          total,
          pages: Math.max(1, Math.ceil(total / perPage)),
          page,
          perPage
        }
      });
    }

    // 6. Process related data with proper error handling
    try {
      // Extract unique IDs for related data
      const doctorIds = Array.from(
        new Set(baseAppointments.map(a => a.doctorId).filter(Boolean))
      );

      const patientIds = Array.from(
        new Set(baseAppointments.map(a => a.patientId).filter(Boolean))
      );

      const hospitalIds = Array.from(
        new Set(baseAppointments.map(a => a.hospitalId).filter(Boolean))
      );

      // Prepare result arrays and define default data structures
      const doctorsData: Array<any> = [];
      const patientsData: Array<any> = [];
      const hospitalsData: Array<any> = [];

      // Default/Fallback data with correct structure
      const unknownUser = { id: "unknown", name: "Usuário Desconhecido", email: null, avatarUrl: null };

      // Fetch related data with error handling for each relation
      if (doctorIds.length > 0) {
        try {
          const doctors = await db.doctor.findMany({
            where: { id: { in: doctorIds } },
            select: {
              id: true,
              user: { select: { id: true, name: true, email: true, avatarUrl: true } },
            },
          });
          doctors.forEach(doctor => {
            doctorsData.push({
              id: doctor.id,
              user: doctor.user || unknownUser
            });
          });
        } catch (error) {
          console.error("[API Route - GET Appointments] Error fetching doctor data:", error);
          // Continue with empty array if doctor lookup fails
        }
      }

      if (patientIds.length > 0) {
        try {
          const patients = await db.patient.findMany({
            where: { id: { in: patientIds } },
            select: {
              id: true,
              user: { select: { id: true, name: true, email: true, avatarUrl: true } },
            },
          });
          patients.forEach(patient => {
            patientsData.push({
              id: patient.id,
              user: patient.user || unknownUser
            });
          });
        } catch (error) {
          console.error("[API Route - GET Appointments] Error fetching patient data:", error);
          // Continue with empty array if patient lookup fails
        }
      }

      if (hospitalIds.length > 0) {
        try {
          const hospitals = await db.hospital.findMany({
            where: { id: { in: hospitalIds as string[] } },
            select: { id: true, name: true, logoUrl: true },
          });
          hospitalsData.push(...hospitals);
        } catch (error) {
          console.error("[API Route - GET Appointments] Error fetching hospital data:", error);
          // Continue with empty array if hospital lookup fails
        }
      }

      // Create lookup maps
      const doctorsMap = new Map(doctorsData.map(d => [d.id, d]));
      const patientsMap = new Map(patientsData.map(p => [p.id, p]));
      const hospitalsMap = new Map(hospitalsData.map(h => [h.id, h]));

      // Default entities
      const unknownDoctor = { id: "unknown", user: unknownUser };
      const unknownPatient = { id: "unknown", user: unknownUser };
      const unknownHospital = { id: "unknown", name: "Hospital Desconhecido", logoUrl: null };

      // 7. Assemble appointments with related data
      const appointments = baseAppointments.map(appointment => {
        // Safe lookups with fallbacks
        const doctorData = appointment.doctorId ? doctorsMap.get(appointment.doctorId) : null;
        const patientData = appointment.patientId ? patientsMap.get(appointment.patientId) : null;
        const hospitalData = appointment.hospitalId ? hospitalsMap.get(appointment.hospitalId) : null;

        // Ensure complete doctor data with user
        const doctor = doctorData ? {
          ...doctorData,
          user: doctorData.user || { ...unknownUser, id: doctorData.user?.id || "unknown" }
        } : {
          ...unknownDoctor,
          id: appointment.doctorId || "unknown"
        };

        // Ensure complete patient data with user
        const patient = patientData ? {
          ...patientData,
          user: patientData.user || { ...unknownUser, id: patientData.user?.id || "unknown" }
        } : {
          ...unknownPatient,
          id: appointment.patientId || "unknown"
        };

        // Ensure complete hospital data
        const hospital = appointment.hospitalId
          ? (hospitalData || { ...unknownHospital, id: appointment.hospitalId })
          : null;

        // Warnings about missing data
        if (appointment.doctorId && !doctorData) {
          console.warn(
            `[API Route - GET Appointments] Doctor record not found for doctorId ${appointment.doctorId} in appointment ${appointment.id}`
          );
        }

        if (appointment.patientId && !patientData) {
          console.warn(
            `[API Route - GET Appointments] Patient record not found for patientId ${appointment.patientId} in appointment ${appointment.id}`
          );
        }

        if (appointment.hospitalId && !hospitalData) {
          console.warn(
            `[API Route - GET Appointments] Hospital record not found for hospitalId ${appointment.hospitalId} in appointment ${appointment.id}`
          );
        }

        return {
          ...appointment,
          amount: appointment.amount !== null ? Number(appointment.amount) : null,
          doctor,
          patient,
          hospital,
        };
      });

      // 8. Return Response
      const responsePayload = {
        appointments,
        pagination: {
          total,
          pages: Math.max(1, Math.ceil(total / perPage)),
          page,
          perPage,
        },
      };

      console.log(`[API Route - GET Appointments] Sending ${appointments.length} appointments`);
      return createResponse(responsePayload);

    } catch (error) {
      console.error("[API Route - GET Appointments] Error processing appointment data:", error);
      return createResponse({
        error: "Erro ao processar dados das consultas",
        details: error instanceof Error ? error.message : "Erro desconhecido"
      }, 500);
    }

  } catch (error) {
    console.error("[API Route - GET Appointments] Unhandled error:", error);
    const message = error instanceof Error
      ? error.message
      : "Erro desconhecido ao buscar consultas";

    return createResponse({
      error: "Erro interno do servidor",
      details: message
    }, 500);
  }
}
