import { NextResponse } from 'next/server';
import { db } from 'database';

export async function GET() {
  try {
    console.log('[LIST_APPOINTMENTS] Fetching appointments...');

    // Get recent appointments, especially plantão ones
    const appointments = await db.appointment.findMany({
      where: {
        isOnDuty: true, // Focus on plantão appointments
      },
      include: {
        patient: {
          include: {
            user: {
              select: {
                name: true,
                phone: true,
                email: true
              }
            }
          }
        },
        doctor: {
          include: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    console.log('[LIST_APPOINTMENTS] Found appointments:', appointments.length);

    // Format the response to hide sensitive data
    const formattedAppointments = appointments.map(apt => ({
      id: apt.id,
      status: apt.status,
      paymentStatus: apt.paymentStatus,
      urgencyLevel: apt.urgencyLevel,
      isOnDuty: apt.isOnDuty,
      createdAt: apt.createdAt,
      patient: {
        name: apt.patient.user.name,
        hasPhone: !!apt.patient.user.phone,
        phoneLastDigits: apt.patient.user.phone ? '***' + apt.patient.user.phone.slice(-4) : null
      },
      doctor: apt.doctor ? {
        name: apt.doctor.user.name
      } : null,
      acceptedAt: apt.acceptedAt,
      acceptedByDoctorId: apt.acceptedByDoctorId
    }));

    return NextResponse.json({
      success: true,
      count: appointments.length,
      appointments: formattedAppointments
    });

  } catch (error) {
    console.error('[LIST_APPOINTMENTS] Error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
