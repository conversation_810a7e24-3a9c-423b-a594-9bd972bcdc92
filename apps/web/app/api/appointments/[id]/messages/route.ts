import { createClient } from '@shared/lib/supabase/client';
import { NextResponse } from 'next/server';

export async function GET(req: Request, { params }: { params: { appointmentId: string } }) {
  try {
    console.log('[API] Fetching messages for appointment:', params.appointmentId);

    const supabase = createClient();

    // Get messages for the appointment
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select(`
        id,
        appointment_id,
        sender_id,
        content,
        type,
        metadata,
        created_at,
        updated_at
      `)
      .eq('appointment_id', params.appointmentId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      throw messagesError;
    }

    console.log('[API] Successfully fetched messages:', messages?.length ?? 0);

    return NextResponse.json({ messages: messages || [] });
  } catch (error) {
    console.error('[API] Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}
