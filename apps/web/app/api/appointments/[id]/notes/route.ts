import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { NextRequest, NextResponse } from "next/server";

// GET /api/appointments/[id]/notes
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const appointmentId = params.id;

    // Verify user has access to this appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: true
          }
        },
        patient: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return new NextResponse("Appointment not found", { status: 404 });
    }

    // Check if user is the doctor or patient of this appointment
    const isDoctor = appointment.doctor.user.id === user.user?.id;
    const isPatient = appointment.patient.user.id === user.user?.id;

    if (!isDoctor && !isPatient) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Fetch notes for this appointment
    const notes = await db.appointmentNote.findMany({
      where: { appointmentId },
      orderBy: { createdAt: "desc" },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({ notes });
  } catch (error) {
    console.error("Error fetching notes:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// POST /api/appointments/[id]/notes
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user || !user.user) {
      console.log("Unauthorized - No user found");
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Add debugging
    console.log("Current user:", {
      id: user.user.id,
      role: user.user.role,
      email: user.user.email
    });

    // Only doctors can create notes
    if (user.user.role !== "DOCTOR") {
      console.log("Access denied: User role is not DOCTOR, current role:", user.user.role);
      return new NextResponse("Forbidden - Only doctors can create notes", { status: 403 });
    }

    const appointmentId = params.id;
    const body = await request.json();
    const { content } = body;

    if (!content || typeof content !== "string") {
      return new NextResponse("Content is required", { status: 400 });
    }

    // Verify appointment exists and user is the doctor
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return new NextResponse("Appointment not found", { status: 404 });
    }

    // Add debugging
    console.log("Appointment doctor:", {
      doctorId: appointment.doctor.id,
      doctorUserId: appointment.doctor.user.id,
      currentUserId: user.user.id
    });

    // Check if user is the doctor of this appointment
    if (appointment.doctor.user.id !== user.user.id) {
      console.log("Access denied: User is not the appointment doctor");
      return new NextResponse("Forbidden - Only the appointment doctor can create notes", { status: 403 });
    }

    // Create the note
    try {
      const note = await db.appointmentNote.create({
        data: {
          content,
          appointmentId,
          doctorId: appointment.doctor.id
        },
        include: {
          doctor: {
            include: {
              user: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      });

      console.log("Note created successfully:", note);
      return NextResponse.json(note);
    } catch (createError) {
      console.error("Error in Prisma create operation:", createError);
      return new NextResponse(`Create operation failed: ${createError.message}`, { status: 500 });
    }
  } catch (error) {
    console.error("Error creating note:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// DELETE /api/appointments/[id]/notes?noteId=xyz
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await currentUser();
    if (!user || !user.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Only doctors can delete notes
    if (user.user.role !== "DOCTOR") {
      return new NextResponse("Forbidden", { status: 403 });
    }

    const appointmentId = params.id;
    const url = new URL(request.url);
    const noteId = url.searchParams.get("noteId");

    if (!noteId) {
      return new NextResponse("Note ID is required", { status: 400 });
    }

    // Verify the note exists
    const note = await db.appointmentNote.findUnique({
      where: { id: noteId },
      include: {
        doctor: {
          include: {
            user: true
          }
        }
      }
    });

    if (!note) {
      return new NextResponse("Note not found", { status: 404 });
    }

    // Verify the note belongs to the appointment
    if (note.appointmentId !== appointmentId) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Verify the user is the doctor who created the note
    if (note.doctor.user.id !== user.user.id) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Delete the note
    await db.appointmentNote.delete({
      where: { id: noteId }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting note:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
