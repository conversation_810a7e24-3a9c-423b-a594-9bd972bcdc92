import { NextRequest, NextResponse } from "next/server";
import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";


export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appointmentId = params.id;

    if (!appointmentId) {
      return NextResponse.json(
        { error: "Appointment ID is required" },
        { status: 400 }
      );
    }

    console.log(`[APPOINTMENT_STATUS] Checking status for appointment: ${appointmentId}`);

    // Buscar appointment com informações relevantes
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        id: true,
        status: true,
        isOnDuty: true,
        paymentStatus: true,
        scheduledAt: true,
        acceptedAt: true,
        doctorId: true,
        updatedAt: true,
        doctor: {
          select: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!appointment) {
      return NextResponse.json(
        { error: "Appointment not found" },
        { status: 404 }
      );
    }

    // Apenas retornar status para plantões pagos
    if (!appointment.isOnDuty || appointment.paymentStatus !== 'PAID') {
      return NextResponse.json(
        { error: "Invalid appointment type" },
        { status: 403 }
      );
    }

    const responseData = {
      id: appointment.id,
      status: appointment.status,
      hasDoctor: !!appointment.doctorId,
      doctorName: appointment.doctor?.user?.name || null,
      acceptedAt: appointment.acceptedAt,
      isActive: appointment.status === 'IN_PROGRESS',
      isCompleted: appointment.status === 'COMPLETED',
      updatedAt: appointment.updatedAt,
      timestamp: new Date().toISOString()
    };

    console.log(`[APPOINTMENT_STATUS] Status retrieved:`, {
      appointmentId,
      status: appointment.status,
      hasDoctor: !!appointment.doctorId
    });

    return NextResponse.json(responseData, {
      headers: {
        "Cache-Control": "no-store, no-cache, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    });

  } catch (error) {
    console.error("[APPOINTMENT_STATUS] Error:", error);

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
        status: null,
        isActive: false,
        isCompleted: false
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const appointmentId = params.id;
    const { status, userId } = await request.json();

    if (!appointmentId || !status) {
      return NextResponse.json(
        { success: false, error: "Appointment ID and status are required" },
        { status: 400 }
      );
    }

    console.log(`[APPOINTMENT_STATUS] Updating appointment ${appointmentId} to status ${status}`);

    // Verificar se o usuário está autenticado
    const session = await currentUser();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Atualizar o status
    const updatedAppointment = await db.appointment.update({
      where: { id: appointmentId },
      data: {
        status,
        updatedAt: new Date()
      }
    });

    console.log(`[APPOINTMENT_STATUS] Status updated successfully to ${status}`);

    // Criar mensagem de sistema se necessário
    if (userId) {
      try {
        if (status === 'IN_PROGRESS') {
          await db.message.create({
            data: {
              appointmentId,
              senderId: userId,
              type: "SYSTEM",
              content: 'Consulta iniciada',
              createdAt: new Date()
            }
          });
        } else if (status === 'COMPLETED') {
          await db.message.create({
            data: {
              appointmentId,
              senderId: userId,
              type: "SYSTEM",
              content: 'Consulta finalizada',
              createdAt: new Date()
            }
          });
        }
      } catch (messageError) {
        console.warn("[APPOINTMENT_STATUS] Could not create system message:", messageError);
        // Não falhar se não conseguir criar a mensagem
      }
    }

    return NextResponse.json({
      success: true,
      appointment: updatedAppointment
    });

  } catch (error) {
    console.error("[APPOINTMENT_STATUS] Error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Erro ao atualizar status da consulta"
      },
      { status: 500 }
    );
  }
}
