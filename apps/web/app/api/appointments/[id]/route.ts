import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

// Helper function for consistent JSON responses
function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store, max-age=0'
    }
  });
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  console.log(`[API] Fetching details for appointment: ${params.id}`);

  try {
    // 1. Authenticate User
    const { user } = await currentUser();
    if (!user) {
      return createResponse({ error: "Não autenticado" }, 401);
    }

    const appointmentId = params.id;

    // 2. Fetch appointment with related data
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            specialty: {
              select: {
                name: true
              }
            }
          }
        },
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          }
        }
      }
    });

    if (!appointment) {
      return createResponse({ error: "Consulta não encontrada" }, 404);
    }

    // 3. Check if user has access to this appointment
    const isDoctor = appointment.doctor?.user?.id === user.id;
    const isPatient = appointment.patient?.user?.id === user.id;

    if (!isDoctor && !isPatient) {
      return createResponse({ error: "Não autorizado" }, 403);
    }

    // 4. Format and return appointment data
    const formattedAppointment = {
      id: appointment.id,
      scheduledAt: appointment.scheduledAt,
      status: appointment.status,
      doctor: {
        user: {
          id: appointment.doctor?.user?.id,
          name: appointment.doctor?.user?.name,
          image: appointment.doctor?.user?.image
        },
        specialty: appointment.doctor?.specialty?.name
      },
      patient: {
        user: {
          id: appointment.patient?.user?.id,
          name: appointment.patient?.user?.name,
          image: appointment.patient?.user?.image
        }
      }
    };

    return createResponse(formattedAppointment);

  } catch (error) {
    console.error("[API] Error fetching appointment details:", error);
    return createResponse({ error: "Erro ao buscar detalhes da consulta" }, 500);
  }
}
