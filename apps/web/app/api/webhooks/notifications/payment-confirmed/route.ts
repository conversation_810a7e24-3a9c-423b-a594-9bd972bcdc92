import { NextResponse } from "next/server";

// Rota para processar notificações de pagamento confirmado
export async function POST(req: Request) {
  try {
    console.log("[PAYMENT_CONFIRMED_WEBHOOK] Received request");

    // Parse the request body
    let payload;
    try {
      payload = await req.json();
      console.log("[PAYMENT_CONFIRMED_WEBHOOK] Request payload:", JSON.stringify(payload, null, 2));
    } catch (error) {
      console.error("[PAYMENT_CONFIRMED_WEBHOOK] Failed to parse JSON:", error);
      return NextResponse.json(
        { success: false, message: "Invalid JSON payload" },
        { status: 400 }
      );
    }

    // Validate the payload
    if (!payload?.appointment) {
      console.error("[PAYMENT_CONFIRMED_WEBHOOK] Missing appointment data in payload");
      return NextResponse.json(
        { success: false, message: "Missing appointment data" },
        { status: 400 }
      );
    }

    const { appointment } = payload;

    // Additional validation for required fields
    if (!appointment.id || !appointment.scheduledAt) {
      console.error("[PAYMENT_CONFIRMED_WEBHOOK] Missing required appointment fields:", appointment);
      return NextResponse.json(
        { success: false, message: "Missing required appointment fields" },
        { status: 400 }
      );
    }

    if (!appointment.patient?.user?.phone) {
      console.error("[PAYMENT_CONFIRMED_WEBHOOK] Missing patient phone number");
      return NextResponse.json(
        { success: false, message: "Missing patient phone number" },
        { status: 400 }
      );
    }

    // Format phone number (remove non-digit characters)
    const phone = appointment.patient.user.phone.replace(/\D/g, '');

    // Format date
    let formattedDate = "Data agendada";
    try {
      const date = new Date(appointment.scheduledAt);
      formattedDate = date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error("[PAYMENT_CONFIRMED_WEBHOOK] Error formatting date:", error);
    }

    // Prepare message
    const message = `Olá ${appointment.patient.user.name || "paciente"},\n\nSua consulta com ${appointment.doctor.user.name || "Dr."} foi agendada para ${formattedDate}.\n\nVocê pode acessar os detalhes em: ${appointment.url || "https://zapvida.com"}\n\nAtenciosamente,\nEquipe ZapVida`;

    console.log("[PAYMENT_CONFIRMED_WEBHOOK] Sending WhatsApp to:", {
      phone,
      message: message.substring(0, 50) + "..." // Log truncated message for security
    });

    // Here you would actually send the WhatsApp message
    // This is a placeholder that simulates successful sending
    // In a real implementation, you would call your WhatsApp API service

    // Simulate successful sending
    const whatsappResult = {
      success: true,
      messageId: `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      recipient: phone
    };

    console.log("[PAYMENT_CONFIRMED_WEBHOOK] WhatsApp sent successfully:", whatsappResult);

    return NextResponse.json({
      success: true,
      message: "WhatsApp notification sent successfully",
      data: whatsappResult
    });
  } catch (error) {
    console.error("[PAYMENT_CONFIRMED_WEBHOOK] Error processing request:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
