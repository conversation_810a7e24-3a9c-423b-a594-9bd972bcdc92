// src/app/api/webhooks/asaas/route.ts
import { NextResponse } from "next/server";
import { validateAsaasSignature } from "api/modules/asaas/client";
import { AppointmentStatus, PaymentStatus, AppointmentType } from "@prisma/client";

import { prisma } from "../../../../lib/db";

import { sendAppointmentNotifications } from "../../../../actions/checkout/notifications/send-notifications";
import { sendPlantaoPaymentConfirmedNotification } from "../../../../actions/appointments/send-plantao-notifications";
import { getBaseUrl } from 'utils';

export async function POST(req: Request) {
  try {
    // Validate signature before accepting
    const signature =
      req.headers.get("x-hook-signature") ||
      req.headers.get("x-hub-signature") ||
      req.headers.get("x-asaas-signature");

    let payloadForValidation: unknown = null;
    try {
      payloadForValidation = await req.clone().json();
    } catch {
      // ignore — we'll parse inside async too; just fail validation if needed
    }

    // Log detalhado para debug da validação
    console.log("[ASAAS_WEBHOOK] Headers recebidos:", {
      xHookSignature: req.headers.get("x-hook-signature"),
      xHubSignature: req.headers.get("x-hub-signature"),
      xAsaasSignature: req.headers.get("x-asaas-signature"),
      contentType: req.headers.get("content-type"),
      userAgent: req.headers.get("user-agent")
    });

    console.log("[ASAAS_WEBHOOK] Variáveis de ambiente:", {
      hasWebhookToken: !!process.env.ASAAS_WEBHOOK_TOKEN,
      webhookTokenLength: process.env.ASAAS_WEBHOOK_TOKEN?.length || 0,
      webhookTokenStart: process.env.ASAAS_WEBHOOK_TOKEN?.substring(0, 10) || 'não definido'
    });

    // Validar assinatura
    const isValidSignature = validateAsaasSignature(signature, payloadForValidation);
    console.log("[ASAAS_WEBHOOK] Validação de assinatura:", {
      signature: signature ? `${signature.substring(0, 20)}...` : 'não encontrada',
      isValid: isValidSignature,
      hasPayload: !!payloadForValidation
    });

    // Em desenvolvimento, permitir processamento mesmo com assinatura inválida
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';

    // if (!isValidSignature) {
    //   if (isDevelopment) {
    //     console.warn("[ASAAS_WEBHOOK] Assinatura inválida em desenvolvimento, continuando processamento para debug");
    //   } else {
    //     console.warn("[ASAAS_WEBHOOK] Assinatura inválida em produção, rejeitando webhook");
    //     return NextResponse.json({ error: "invalid signature" }, { status: 401 });
    //   }
    // }

    // Clone the request to read it in the background processor
    const clonedReq = req.clone();

    // Start processing in the background
    processWebhookAsync(clonedReq).catch(error => {
      console.error("[ASAAS_WEBHOOK_ASYNC] Background processing error:", error);
    });

    // Respond immediately to prevent timeout
    return NextResponse.json({ status: "success" });
  } catch (e) {
    console.error("[ASAAS_WEBHOOK] POST handler error:", e);
    return NextResponse.json({ error: "internal error" }, { status: 500 });
  }
}

// Separate async function to process the webhook without blocking the response
async function processWebhookAsync(req: Request) {
  try {
    console.log("[ASAAS_WEBHOOK] Received webhook request");

    // Ler o payload com segurança
    let payload;
    try {
      payload = await req.json();
      if (!payload) {
        console.error("[ASAAS_WEBHOOK] Empty payload");
        return;
      }
      console.log("[ASAAS_WEBHOOK] Payload:", JSON.stringify(payload, null, 2));
    } catch (error) {
      console.error("[ASAAS_WEBHOOK] Failed to parse payload:", error);
      return;
    }

    // Buscar transação pelo ID do pagamento Asaas
    if (!payload.payment || !payload.payment.id) {
      console.warn("[ASAAS_WEBHOOK] Missing payment ID in payload");
      return;
    }

    // Log detalhado do payload para debug
    console.log("[ASAAS_WEBHOOK] Payload completo recebido:", {
      eventId: payload.id,
      event: payload.event,
      paymentId: payload.payment.id,
      paymentStatus: payload.payment.status,
      billingType: payload.payment.billingType,
      externalReference: payload.payment.externalReference,
      value: payload.payment.value,
      confirmedDate: payload.payment.confirmedDate,
      paymentDate: payload.payment.paymentDate
    });

    // Buscar transação pelo ID do pagamento Asaas
    let transaction = await prisma.transaction.findFirst({
      where: { asaasId: payload.payment.id },
      include: {
        appointment: {
          include: {
            doctor: { include: { user: true } },
            patient: { include: { user: true } },
          },
        },
        patientSubscription: {
          include: {
            patient: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    if (!transaction) {
      console.warn(
        "[ASAAS_WEBHOOK] Transaction not found by asaasId:",
        payload.payment.id
      );

      // Tentar buscar pelo externalReference se disponível
      if (payload.payment.externalReference) {
        console.log("[ASAAS_WEBHOOK] Tentando buscar por externalReference:", payload.payment.externalReference);

        const transactionByRef = await prisma.transaction.findFirst({
          where: {
            appointment: {
              id: payload.payment.externalReference
            }
          },
          include: {
            appointment: {
              include: {
                doctor: { include: { user: true } },
                patient: { include: { user: true } },
              },
            },
            patientSubscription: {
              include: {
                patient: {
                  include: {
                    user: true,
                  },
                },
              },
            },
          },
        });

        if (transactionByRef) {
          console.log("[ASAAS_WEBHOOK] Transaction encontrada por externalReference:", {
            transactionId: transactionByRef.id,
            appointmentId: transactionByRef.appointmentId
          });

          // Atualizar o asaasId da transação
          await prisma.transaction.update({
            where: { id: transactionByRef.id },
            data: { asaasId: payload.payment.id }
          });

          // Usar a transação encontrada
          transaction = transactionByRef;
        } else {
          console.error("[ASAAS_WEBHOOK] Transaction não encontrada nem por asaasId nem por externalReference");
          return;
        }
      } else {
        return;
      }
    }

    if (!transaction) {
      console.warn(
        "[ASAAS_WEBHOOK] Transaction not found:",
        payload.payment.id,
      );
      return;
    }

    // Verificar se é transação de subscription
    if (transaction.patientSubscriptionId) {
      console.log("[ASAAS_WEBHOOK] Processing subscription payment:", {
        subscriptionId: transaction.patientSubscriptionId,
        paymentEvent: payload.event,
      });
      await processSubscriptionPayment(transaction, payload);
      return;
    }

    // Verificar se transaction tem appointment (fluxo original)
    if (!transaction.appointment) {
      console.warn(
        "[ASAAS_WEBHOOK] Transaction without appointment:",
        payload.payment.id,
      );
      return;
    }

    if (!transaction.appointmentId) {
      console.warn("[ASAAS_WEBHOOK] Transaction without appointmentId:", transaction.id);
      return;
    }

    const appointmentIdStrict = transaction.appointmentId as string;

    console.log("[ASAAS_WEBHOOK] Found transaction:", {
      id: transaction.id,
      appointmentId: transaction.appointmentId,
      status: transaction.status,
      isOnDuty: transaction.appointment?.isOnDuty || false,
      paymentMethod: transaction.paymentMethod,
      asaasId: transaction.asaasId
    });

    // Mapear status do Asaas para nosso PaymentStatus
    let paymentStatus: PaymentStatus;
    let appointmentStatus: AppointmentStatus;

    // Log específico para PIX
    if (transaction.paymentMethod === 'PIX') {
      console.log("[ASAAS_WEBHOOK] Processando evento PIX:", {
        event: payload.event,
        paymentStatus: payload.payment.status,
        confirmedDate: payload.payment.confirmedDate,
        paymentDate: payload.payment.paymentDate
      });
    }

    switch (payload.event) {
      case "PAYMENT_CONFIRMED":
      case "PAYMENT_RECEIVED":
        paymentStatus = PaymentStatus.PAID;
        appointmentStatus = AppointmentStatus.SCHEDULED;

        // Para PIX, verificar se tem data de confirmação
        if (transaction.paymentMethod === 'PIX' && payload.payment.confirmedDate) {
          console.log("[ASAAS_WEBHOOK] PIX confirmado com data:", payload.payment.confirmedDate);
        }
        break;
      case "PAYMENT_REFUNDED":
        paymentStatus = PaymentStatus.REFUNDED;
        appointmentStatus = AppointmentStatus.CANCELED;
        break;
      case "PAYMENT_OVERDUE":
      case "PAYMENT_DELETED":
      case "PAYMENT_REJECTED":
        paymentStatus = PaymentStatus.FAILED;
        appointmentStatus = AppointmentStatus.CANCELED;
        break;
      default:
        paymentStatus = PaymentStatus.PENDING;
        appointmentStatus = AppointmentStatus.SCHEDULED;
    }

    console.log("[ASAAS_WEBHOOK] Updating statuses:", {
      payment: paymentStatus,
      appointment: appointmentStatus,
    });

    // Atualizar em transação atômica
    await prisma.$transaction([
      prisma.transaction.update({
        where: { id: transaction.id },
        data: {
          status: paymentStatus,
          paidAt: paymentStatus === PaymentStatus.PAID ? (new Date() as unknown as Date | undefined) : undefined,
          refundedAt:
            paymentStatus === PaymentStatus.REFUNDED ? (new Date() as unknown as Date | undefined) : undefined,
        },
      }),
      prisma.appointment.update({
        where: { id: appointmentIdStrict },
        data: {
          status: appointmentStatus,
          paymentStatus: paymentStatus,
        },
      }),
    ]);

    // Verificar se o pagamento foi confirmado
    if (paymentStatus === PaymentStatus.PAID && (payload.event === "PAYMENT_CONFIRMED" || payload.event === "PAYMENT_RECEIVED")) {
      try {
        // Verificar se é um appointment de plantão ou regular
        if (transaction.appointment.isOnDuty) {
          // PLANTÃO: Redirecionar para o webhook específico de plantão
          console.log("[ASAAS_WEBHOOK] Detectado pagamento de PLANTÃO confirmado. Redirecionando para webhook específico:", {
            appointmentId: transaction.appointmentId,
            transactionId: transaction.id,
            paymentId: payload.payment.id
          });

          // Chamar o webhook de plantão para processar as notificações específicas
          await callPlantaoWebhook(appointmentIdStrict, transaction.id, payload.payment.id as string);
        } else {
          // CONSULTA REGULAR: Enviar notificações padrão (email + WhatsApp)
          console.log("[ASAAS_WEBHOOK] Enviando notificações para pagamento de CONSULTA REGULAR confirmado:", {
            transactionId: transaction.id,
            appointmentId: transaction.appointment?.id,
            paymentMethod: transaction.paymentMethod,
            event: payload.event
          });

          // Enviar notificações usando o serviço unificado
          const notificationResult = await sendAppointmentNotifications(
            // Dados da consulta
            {
              id: transaction.appointment!.id,
              scheduledAt: transaction.appointment!.scheduledAt as any,
              type: transaction.appointment!.appointmentType as any,
            },
            // Dados do paciente
            {
              id: transaction.appointment!.patient!.id,
              user: {
                id: transaction.appointment!.patient!.user!.id,
                name: transaction.appointment!.patient!.user!.name || "",
                email: transaction.appointment!.patient!.user!.email || "",
                phone: (transaction.appointment!.patient!.user!.phone as string | null) || undefined,
              },
            },
            // Dados do médico
            {
              id: transaction.appointment!.doctor!.id,
              user: {
                id: transaction.appointment!.doctor!.user!.id,
                name: transaction.appointment!.doctor!.user!.name || "Médico",
                email: transaction.appointment!.doctor!.user!.email || "",
                phone: (transaction.appointment!.doctor!.user!.phone as string | null) || undefined,
              },
            },
            // Opções
            {
              sendEmail: true,
              sendWhatsApp: true,
              useDirectLinks: true
            }
          );

          if (!notificationResult?.success) {
            console.error("[ASAAS_WEBHOOK_NOTIFICATIONS_ERROR]", {
              error: notificationResult?.error,
              emailError: notificationResult?.email?.error,
              whatsAppError: notificationResult?.whatsApp?.error,
              appointmentId: transaction.appointment.id,
              transactionId: transaction.id
            });
          } else {
            console.log("[ASAAS_WEBHOOK_NOTIFICATIONS_SUCCESS]", {
              emailSent: notificationResult.email?.sent,
              whatsAppSent: notificationResult.whatsApp?.sent,
              messageCount: notificationResult.whatsApp?.messageCount,
              appointmentId: transaction.appointment.id,
              transactionId: transaction.id
            });
          }
        }
      } catch (notificationError) {
        // Capturar e registrar qualquer erro, mas continuar o processamento do webhook
            console.error("[ASAAS_WEBHOOK_NOTIFICATIONS_CRITICAL_ERROR]", {
          error: notificationError instanceof Error ? notificationError.message : String(notificationError),
          errorType: notificationError instanceof Error ? notificationError.constructor.name : 'Unknown',
          stack: notificationError instanceof Error ? notificationError.stack : undefined,
          appointmentId: transaction.appointment!.id,
          transactionId: transaction.id,
          event: payload.event
        });
        // Continuar processando o webhook mesmo em caso de erro nas notificações
      }
    }

    console.log("[ASAAS_WEBHOOK] Successfully processed webhook");
  } catch (error) {
    console.error("[ASAAS_WEBHOOK] Error processing webhook:", error);
  }
}

// Função para processar pagamento de subscription
async function processSubscriptionPayment(transaction: any, payload: any) {
  try {
    const { patientSubscription } = transaction;

    if (!patientSubscription) {
      console.warn("[ASAAS_WEBHOOK] Transaction has subscriptionId but no subscription data");
      return;
    }

    console.log("[ASAAS_WEBHOOK] Processing subscription payment:", {
      subscriptionId: patientSubscription.id,
      currentStatus: patientSubscription.status,
      event: payload.event,
    });

    // Determinar novo status da transaction e subscription baseado no evento
    let transactionStatus: PaymentStatus;
    let subscriptionStatus: string;

    switch (payload.event) {
      case "PAYMENT_CONFIRMED":
      case "PAYMENT_RECEIVED":
        transactionStatus = PaymentStatus.PAID;
        subscriptionStatus = "ACTIVE";
        break;
      case "PAYMENT_REFUNDED":
        transactionStatus = PaymentStatus.REFUNDED;
        subscriptionStatus = "CANCELED";
        break;
      case "PAYMENT_OVERDUE":
      case "PAYMENT_DELETED":
      case "PAYMENT_REJECTED":
        transactionStatus = PaymentStatus.FAILED;
        subscriptionStatus = "PAST_DUE";
        break;
      default:
        transactionStatus = PaymentStatus.PENDING;
        subscriptionStatus = patientSubscription.status; // Keep current status
    }

    // Atualizar transação e subscription em transação atômica
    await prisma.$transaction([
      prisma.transaction.update({
        where: { id: transaction.id },
        data: {
          status: transactionStatus,
          paidAt: transactionStatus === PaymentStatus.PAID ? new Date() : undefined,
          refundedAt: transactionStatus === PaymentStatus.REFUNDED ? new Date() : undefined,
        },
      }),
      prisma.patientSubscription.update({
        where: { id: patientSubscription.id },
        data: {
          status: subscriptionStatus as any,
          lastPaymentStatus: transactionStatus,
          lastPaymentAt: transactionStatus === PaymentStatus.PAID ? new Date() : undefined,
          failedPaymentAt: transactionStatus === PaymentStatus.FAILED ? new Date() : undefined,
        },
      }),
    ]);

    // Se pagamento confirmado, ativar hasActiveSubscription do paciente e enviar notificações
    if (transactionStatus === PaymentStatus.PAID && subscriptionStatus === "ACTIVE") {
      await prisma.patient.update({
        where: { id: patientSubscription.patientId },
        data: { hasActiveSubscription: true },
      });

      // Enviar notificações de subscription ativada
      try {
        const user = patientSubscription.patient.user;

        // Email de confirmação
        const { sendEmail } = await import('mail');
        await sendEmail({
          to: user.email,
          templateId: "subscriptionCreated" as any,
          context: {
            recipientName: user.name || 'Paciente',
            planName: patientSubscription.planName,
            planPrice: Number(patientSubscription.planPrice),
            nextBillingDate: patientSubscription.nextBillingDate,
            manageUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com'}/app`,
          } as any,
          locale: 'pt' as any,
        });

        // WhatsApp (se tiver telefone)
        if (user.phone) {
          const { EvolutionService } = await import('../../../../actions/checkout/integrations/evolution/evolution.service');
          const { formatPhoneForWhatsApp } = await import('../../../../lib/utils/format-phone');

          const evolution = new EvolutionService();
          const phone = formatPhoneForWhatsApp(user.phone);
          const messages: string[] = [
            `Olá ${user.name || 'Paciente'}! Sua assinatura ${patientSubscription.planName} foi ativada com sucesso. ✅`,
            `Valor do plano: R$ ${Number(patientSubscription.planPrice).toFixed(2)}${patientSubscription.nextBillingDate ? `\nPróxima cobrança: ${new Date(patientSubscription.nextBillingDate).toLocaleDateString('pt-BR')}` : ''}`,
            `Para gerenciar sua assinatura, acesse: ${process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com'}/app`
          ];
          await evolution.sendMessagesWithDelay(messages, phone);
        }
      } catch (notifError) {
        console.error('[ASAAS_WEBHOOK] Erro ao enviar notificações de subscription:', notifError);
      }
    }

    console.log("[ASAAS_WEBHOOK] Successfully processed subscription payment:", {
      subscriptionId: patientSubscription.id,
      newStatus: subscriptionStatus,
      transactionStatus,
    });

  } catch (error) {
    console.error("[ASAAS_WEBHOOK] Error processing subscription payment:", error);
  }
}

// Função auxiliar para chamar o webhook de plantão
async function callPlantaoWebhook(appointmentId: string, transactionId: string, paymentId: string) {
  try {
          const baseUrl = getBaseUrl();
    const webhookUrl = `${baseUrl}/api/webhooks/plantao-payment-confirmed`;

    console.log("[ASAAS_WEBHOOK] Chamando webhook de plantão:", {
      url: webhookUrl,
      appointmentId,
      transactionId,
      paymentId
    });

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        appointmentId,
        transactionId,
        paymentId
      })
    });

    const result = await response.json();

    console.log("[ASAAS_WEBHOOK] Resposta do webhook de plantão:", {
      status: response.status,
      success: result.success,
      message: result.message
    });

    return result;
  } catch (error) {
    console.error("[ASAAS_WEBHOOK] Erro ao chamar webhook de plantão:", error);
    return { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" };
  }
}
