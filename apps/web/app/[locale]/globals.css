@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	* {
		@apply border-border;
	}
}

@layer utilities {
	.no-scrollbar::-webkit-scrollbar {
		display: none;
	}

	.no-scrollbar {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
}

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}



@layer base {
	:root {
		--colors-border: #d8dee6;
		--colors-input: #c7ced8;
		--colors-ring: #29CEFC;
		--colors-background: #f5f5f8;
		--colors-foreground: #292b35;
		--colors-primary: #184F70;
		--colors-primary-foreground: #ffffff;
		--colors-secondary: #001D29;
		--colors-secondary-foreground: #ffffff;
		--colors-destructive: #ef4444;
		--colors-destructive-foreground: #ffffff;
		--colors-success: #39a561;
		--colors-success-foreground: #ffffff;
		--colors-muted: #f8fafc;
		--colors-muted-foreground: #64748b;
		--colors-accent: #E0F2F7;
		--colors-accent-foreground: #292b35;
		--colors-popover: #ffffff;
		--colors-popover-foreground: #292b35;
		--colors-card: #ffffff;
		--colors-card-foreground: #292b35;
		--colors-highlight: #184F70;
		--colors-highlight-foreground: #ffffff;
	}

	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
		overflow-x: hidden;
		overflow-y: auto;
	}

	html {
		overflow-x: hidden;
		overflow-y: auto;
	}
}
