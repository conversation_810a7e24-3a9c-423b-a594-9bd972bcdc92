'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Check<PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON>gle, User, CalendarDays, Video, CreditCard, CalendarCheck } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { useMediaQuery } from '@ui/hooks/use-media-query';
import { toast } from '@ui/hooks/use-toast';
import { PixQRCode } from '../components/pix-qrcode';
import { DoctorAvatar } from '../components/doctor-avatar';
import { CheckoutSteps } from '../components/checkout-steps';

interface Doctor {
	id: string;
	name: string;
	profileImage?: string | null;
	specialty?: string;
	crm?: string;
	crmState?: string;
}

interface SafeOrder {
	id: string;
	amount: number;
	status: string;
	createdAt: Date;
	updatedAt: Date;
	gatewayId: string | null;
	product: {
		title: string;
		description: string | null;
		thumbnail: string | null;
		scheduledAt?: Date;
		duration?: number;
		isOnline?: boolean;
		doctor?: Doctor;
	};
	user: {
		name: string | null;
		email: string;
	};
}

interface PixPageProps {
	data: {
		order: SafeOrder;
		pixPayload: string;
		qrCode: string;
	};
}

export function PixPage({ data }: PixPageProps) {
	const [copied, setCopied] = useState(false);
	const [isChecking, setIsChecking] = useState(false);
	const [pollCount, setPollCount] = useState(0);
	const [paymentStatus, setPaymentStatus] = useState<'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED'>('PENDING');
	const [isManuallyChecking, setIsManuallyChecking] = useState(false);
	const router = useRouter();
	const { isMobile } = useMediaQuery();

	const { order, pixPayload, qrCode } = data;
	const doctor = order.product.doctor;
	const scheduledAt = order.product.scheduledAt;
	const duration = order.product.duration || 30;

	const checkPaymentStatus = async () => {
		try {
			setIsChecking(true);


			// Primeiramente, tente o endpoint principal
			let response;
			let responseText;
			let endpoint = `/api/payments/status?transactionId=${order.id}`;

			try {

				response = await fetch(endpoint);

				// Se o primeiro endpoint falhar, tente o alternativo
				if (!response.ok) {
					endpoint = `/api/payments?transactionId=${order.id}`;
					response = await fetch(endpoint);
				}
			} catch (fetchError) {
				// Se acontecer um erro de rede no primeiro endpoint, tente o alternativo
				console.error('[PAYMENT_STATUS] Network error with primary endpoint:', fetchError);
				endpoint = `/api/payments?transactionId=${order.id}`;

				response = await fetch(endpoint);
			}

			if (!response.ok) {
				console.error('[PAYMENT_STATUS] All API requests failed. Last response:', response.status, response.statusText);
				toast({
					title: 'Erro ao verificar pagamento',
					description: 'Não foi possível verificar o status do pagamento. Tente novamente em alguns instantes.',
					variant: 'error',
				});
				throw new Error(`API status check failed: ${response.status} ${response.statusText}`);
			}

			// Get response text
			responseText = await response.text();

			let data;
			try {
				data = JSON.parse(responseText);
			} catch (parseError) {
				console.error('[PAYMENT_STATUS] Error parsing response:', parseError);
				throw new Error('Falha ao processar resposta da API');
			}



			if (data.status === 'PAID') {
				setPaymentStatus('PAID');
				toast({
					title: 'Pagamento confirmado!',
					description:
						'Você receberá um email com as instruções de acesso à consulta médica',
					variant: 'success',
				});

                // Navigate to success page after 2 seconds
                setTimeout(() => {
                    // Para plantão e agendamento, usamos páginas distintas: plantão vai para /checkout/success/plantao
                    if (data?.isOnDuty) {
                        router.push(`/checkout/success/plantao?transactionId=${order.id}`);
                    } else {
                        router.push(`/checkout/success?appointmentId=${data.appointmentId}`);
                    }
                }, 2000);

				return true; // Payment confirmed
			} else if (data.status === 'FAILED') {
				setPaymentStatus('FAILED');
				toast({
					title: 'Pagamento falhou',
					description: 'Houve um problema com seu pagamento. Por favor, tente novamente.',
					variant: 'error',
				});
				return true; // Payment failed, stop polling
			} else if (data.status === 'REFUNDED') {
				setPaymentStatus('REFUNDED');
				toast({
					title: 'Pagamento reembolsado',
					description: 'Seu pagamento foi reembolsado.',
					variant: 'error',
				});
				return true; // Payment refunded, stop polling
			}

			return false; // Payment still pending
		} catch (error) {
			console.error('[PAYMENT_STATUS] Error checking payment:', error);
			return false;
		} finally {
			setIsChecking(false);
		}
	};

	useEffect(() => {
		let interval: any;
		if (order?.id) {
			// Delay inicial para garantir que a transação tenha sido criada no banco de dados
			const initialDelay = setTimeout(() => {
				// Initial check depois de um breve delay
				checkPaymentStatus();

				// Setup interval for polling
				interval = setInterval(async () => {
					setPollCount(prevCount => prevCount + 1);
					const shouldStop = await checkPaymentStatus();

					// If payment is confirmed/failed/refunded, stop polling
					if (shouldStop) {
						clearInterval(interval);
					}

					// Stop polling after 20 attempts (5 minutes at 15-second intervals)
					if (pollCount >= 20) {
						clearInterval(interval);

					}
				}, 15000); // Check every 15 seconds
			}, 3000); // Aguardar 3 segundos antes da primeira verificação

			return () => {
				clearTimeout(initialDelay);
				interval && clearInterval(interval);
			};
		}
		return () => interval && clearInterval(interval);
	}, [order?.id, pollCount]);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(pixPayload);
			setCopied(true);

			toast({
				title: 'Código PIX copiado!',
				description:
					'Você pode usar este código para pagar no app do seu banco',
				variant: 'success',
			});
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			toast({
				title: 'Erro ao copiar código',
				description: 'Tente copiar o código manualmente',
				variant: 'error',
			});
		}
	};

	const handleManualCheck = async () => {
		setIsManuallyChecking(true);
		try {
			await checkPaymentStatus();
		} finally {
			setIsManuallyChecking(false);
		}
	};

	const estimatedWaitTime = "15-20 minutos após confirmação";

	// Format date if available
	const formattedDate = scheduledAt
		? new Date(scheduledAt).toLocaleDateString('pt-BR', {
				weekday: 'long',
				day: '2-digit',
				month: 'long',
				year: 'numeric'
			})
		: null;

	// Format time if available
	const formattedTime = scheduledAt
		? new Date(scheduledAt).toLocaleTimeString('pt-BR', {
				hour: '2-digit',
				minute: '2-digit'
			})
		: null;

	return (
		<div className="max-w-4xl mx-auto">
			<div className="max-w-xl mx-auto mb-8">
				<CheckoutSteps currentStep="data" />
			</div>

			<Card className="mb-8 border-blue-100 bg-gradient-to-br from-blue-50/60 to-white">
				<CardHeader className='text-center'>
					<div className='mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-primary/10'>
						<QrCode className='size-6 text-primary' />
					</div>
					<CardTitle className='text-2xl'>Pagamento via PIX</CardTitle>
					<p className='mt-2 text-muted-foreground'>
						Realize o pagamento para confirmar sua consulta
					</p>
				</CardHeader>

				<CardContent className='space-y-6'>
					{/* Status with medical context */}
					<div className='flex flex-col items-center justify-center gap-2'>
						<Badge className={`flex items-center gap-2 bg-secondary px-4 py-1 text-white`}>
							{paymentStatus === 'PENDING' && isChecking
								? <Loader2 className='size-3 animate-spin' />
								: paymentStatus === 'PAID'
									? <CheckCircle2 className='size-3' />
									: paymentStatus === 'FAILED' || paymentStatus === 'REFUNDED'
										? <AlertTriangle className='size-3' />
										: <Clock className='size-3' />
						}
						{paymentStatus === 'PAID'
							? 'Pagamento confirmado'
							: paymentStatus === 'FAILED'
								? 'Pagamento falhou'
								: paymentStatus === 'REFUNDED'
									? 'Pagamento reembolsado'
									: 'Aguardando pagamento'
						}
					</Badge>

				</div>

				{/* Resumo do Pagamento - Movido para cima */}
				<div className='rounded-lg border border-blue-100 p-4 bg-white shadow-sm'>
					<h3 className='mb-4 font-medium text-blue-800'>Resumo do Pagamento</h3>
					<div className='space-y-2 text-sm'>
						<div className='flex justify-between'>
							<span className='text-muted-foreground'>Serviço</span>
							<span className='font-medium'>Consulta Médica Online</span>
						</div>
						{doctor && (
							<div className='flex justify-between'>
								<span className='text-muted-foreground'>Médico</span>
								<span className='font-medium'>{doctor.name}</span>
							</div>
						)}
						{scheduledAt && (
							<div className='flex justify-between'>
								<span className='text-muted-foreground'>Data/Hora</span>
								<span className='font-medium'>{formattedDate}, {formattedTime}</span>
							</div>
						)}
						<Separator className='my-2' />
						<div className='flex justify-between items-center pt-2'>
							<span className='text-gray-700 font-medium flex items-center gap-1.5'>
								<CreditCard className='h-4 w-4 text-primary' />
								Total
							</span>
							<span className='text-lg font-bold text-primary'>
								R$ {Number(order?.amount).toFixed(2)}
							</span>
						</div>
					</div>
				</div>

				{/* Conditional rendering based on device type */}
				{isMobile ? (
					<>
						{/* Código PIX - Shown first on mobile */}
						<div className='rounded-lg border-2 border-primary p-4 bg-white shadow-sm'>
							<h4 className='mb-2 font-medium text-blue-800 flex items-center gap-2'>
								<Copy className='size-4' />
								Código PIX
							</h4>
							<p className='mb-4 text-sm text-muted-foreground'>
								Copie o código e pague no app do seu banco
							</p>

							{pixPayload ? (
								<>
									<div className='p-3 bg-gray-50 rounded-lg mb-4 font-mono text-sm break-all'>
										{pixPayload}
									</div>
									<Button
										variant='default'
										onClick={handleCopy}
										className='w-full justify-center gap-2 text-white font-medium'
										disabled={paymentStatus === 'PAID'}
									>
										<Copy className='size-4' />
										{copied ? 'Copiado!' : 'Copiar código PIX'}
									</Button>
								</>
							) : (
								<div className="bg-amber-50 p-3 rounded-lg text-amber-700 text-sm border border-amber-100">
									<div className="flex items-center gap-2 mb-1">
										<AlertTriangle className="h-4 w-4" />
										<span className="font-medium">Código PIX não disponível</span>
									</div>
									<p>Houve um problema ao gerar o código PIX. Por favor, tente verificar o pagamento ou entre em contato com o suporte.</p>
								</div>
							)}
						</div>

						{/* QR Code - Shown second on mobile */}
						<div className='flex flex-col items-center space-y-4 rounded-xl border bg-white p-6 shadow-sm'>
							<h4 className='font-medium text-blue-800 mb-2'>QR Code PIX</h4>
							{qrCode ? (
								<>
									<PixQRCode qrCodeBase64={qrCode} size={isMobile ? 150 : 200} />
									<span className='text-sm text-muted-foreground'>
										Escaneie o QR Code com outro celular
									</span>
									{/* Botão de verificação manual */}
									{paymentStatus === 'PENDING' && (
										<Button
											variant='secondary'
											onClick={handleManualCheck}
											className='justify-center gap-2'
											disabled={isManuallyChecking}
										>
											{isManuallyChecking ? (
												<>
													<Loader2 className='size-4 animate-spin' />
													Verificando...
												</>
											) : (
												<>
													<CheckCircle2 className='size-4' />
													Verificar pagamento
												</>
											)}
										</Button>
									)}
								</>
							) : (
								<div className="flex flex-col items-center justify-center h-[150px] w-[150px] bg-gray-50 rounded-lg border border-gray-200">
									<AlertTriangle className="h-8 w-8 text-amber-500 mb-2" />
									<p className="text-sm text-gray-600 text-center">
										QR Code não disponível
									</p>
									<p className="text-xs text-gray-500 mt-2 text-center px-4">
										Por favor, utilize o código PIX acima para pagamento
									</p>
								</div>
							)}
						</div>
					</>
				) : (
					<>
						{/* QR Code - Shown first on desktop */}
						<div className='flex flex-col items-center space-y-4 rounded-xl border bg-white p-6 shadow-sm'>
							<h4 className='font-medium text-blue-800 mb-2'>QR Code PIX</h4>
							{qrCode ? (
								<>
									<PixQRCode qrCodeBase64={qrCode} size={isMobile ? 150 : 200} />
									<span className='text-sm text-muted-foreground'>
										Escaneie o QR Code com outro celular
									</span>
									{/* Botão de verificação manual */}
									{paymentStatus === 'PENDING' && (
										<Button
											variant='secondary'
											onClick={handleManualCheck}
											className='justify-center gap-2'
											disabled={isManuallyChecking}
										>
											{isManuallyChecking ? (
												<>
													<Loader2 className='size-4 animate-spin' />
													Verificando...
												</>
											) : (
												<>
													<CheckCircle2 className='size-4' />
													Verificar pagamento
												</>
											)}
										</Button>
									)}
								</>
							) : (
								<div className="flex flex-col items-center justify-center h-[150px] w-[150px] bg-gray-50 rounded-lg border border-gray-200">
									<AlertTriangle className="h-8 w-8 text-amber-500 mb-2" />
									<p className="text-sm text-gray-600 text-center">
										QR Code não disponível
									</p>
									<p className="text-xs text-gray-500 mt-2 text-center px-4">
										Por favor, utilize o código PIX acima para pagamento
									</p>
								</div>
							)}
						</div>

						{/* Código PIX - Shown second on desktop */}
						<div className='rounded-lg border-2 border-primary p-4 bg-white shadow-sm'>
							<h4 className='mb-2 font-medium text-blue-800 flex items-center gap-2'>
								<Copy className='size-4' />
								Código PIX
							</h4>
							<p className='mb-4 text-sm text-muted-foreground'>
								Copie o código e pague no app do seu banco
							</p>

							{pixPayload ? (
								<>
									<div className='p-3 bg-gray-50 rounded-lg mb-4 font-mono text-sm break-all'>
										{pixPayload}
									</div>
									<Button
										variant='default'
										onClick={handleCopy}
										className='w-full justify-center gap-2 text-white font-medium'
										disabled={paymentStatus === 'PAID'}
									>
										<Copy className='size-4' />
										{copied ? 'Copiado!' : 'Copiar código PIX'}
									</Button>
								</>
							) : (
								<div className="bg-amber-50 p-3 rounded-lg text-amber-700 text-sm border border-amber-100">
									<div className="flex items-center gap-2 mb-1">
										<AlertTriangle className="h-4 w-4" />
										<span className="font-medium">Código PIX não disponível</span>
									</div>
									<p>Houve um problema ao gerar o código PIX. Por favor, tente verificar o pagamento ou entre em contato com o suporte.</p>
								</div>
							)}
						</div>
					</>
				)}

				{/* Como pagar - Simplificado */}
				<div className='rounded-lg bg-blue-50 p-4 text-sm border border-blue-100'>
					<h4 className='mb-2 font-medium text-blue-800'>Como pagar</h4>
					<ol className='list-decimal space-y-2 pl-4 text-gray-700'>
						<li>Abra o app do seu banco</li>
						<li>Escolha pagar via PIX</li>
						<li>Cole o código copiado ou escaneie o QR code</li>
						<li>Confirme o pagamento</li>
					</ol>
				</div>

				{/* Informações importantes - Simplificadas */}
				<div className="text-sm text-amber-700 bg-amber-50 p-3 rounded-lg border border-amber-100">
					<p className="flex items-center gap-1 justify-center mb-1">
						<AlertTriangle className="h-3.5 w-3.5" />
						<span className="font-medium">Importante</span>
					</p>
					<p>O seu atendimento só será confirmado após a confirmação do pagamento. Você receberá as instruções de acesso por WhatsApp e email.</p>
				</div>
			</CardContent>

			<CardFooter className='flex-col space-y-4 text-center p-6 bg-gradient-to-t from-blue-50/60 to-transparent rounded-b-lg'>
				<Button
					variant='link'
					className='text-blue-600 hover:text-blue-800'
					onClick={() => router.push('/')}
				>
					Voltar para a página inicial
				</Button>
			</CardFooter>
		</Card>
	</div>
);
}
