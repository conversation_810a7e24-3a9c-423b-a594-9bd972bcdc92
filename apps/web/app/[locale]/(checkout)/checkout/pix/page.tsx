import { redirect } from 'next/navigation';
import { PixPage } from './page-client';
import { db } from 'database';
import { Appointment } from '@prisma/client';
import { AlertTriangle } from 'lucide-react';
import { Button } from '@ui/components/button';
import Link from 'next/link';
import { AsaasClient } from 'api/modules/asaas/client';

// Define more precise types
type AppointmentWithDoctor = Appointment & {
	doctor: {
		id: string;
		user: {
			name: string | null;
			avatarUrl: string | null;
		};
		specialties: Array<{ name: string }>;
		crm: string | null;
		crmState: string | null;
	};
};

type DoctorData = {
	id: string;
	name: string;
	profileImage: string | null;
	specialty: string;
	crm: string;
	crmState: string;
};

interface PixPageProps {
	params: {
		locale: string;
	};
	searchParams: {
		transactionId?: string;
    paymentId?: string;
		appointmentId?: string;
	};
}

export default async function CheckoutPixPage({
	params,
	searchParams,
}: PixPageProps) {
  const { transactionId, appointmentId, paymentId } = await searchParams;



  if (!transactionId && !paymentId) {
    console.error('Missing transaction or payment ID');
		// Instead of redirecting, render an error component
		return (
			<div className='min-h-screen bg-gradient-to-br from-blue-50/30 to-white/90 flex items-center justify-center'>
				<div className='container max-w-lg p-8 bg-white rounded-lg shadow-sm border border-red-100'>
					<div className='flex flex-col items-center gap-4 text-center'>
						<AlertTriangle className='h-12 w-12 text-red-500' />
            <h1 className='text-xl font-semibold'>Erro no processamento do pagamento</h1>
						<p className='text-gray-600'>
              Não foi possível carregar as informações do pagamento. Identificador ausente.
						</p>
						<Button asChild className='mt-4'>
							<Link href='/'>Voltar para a página inicial</Link>
						</Button>
					</div>
				</div>
			</div>
		);
	}

  try {
		// Tentar buscar dados reais da transação e consulta do banco de dados
		let doctorData: DoctorData | undefined = undefined;
		let appointmentData: AppointmentWithDoctor | undefined = undefined;
		let transactionAmount = 100; // valor padrão se não conseguir buscar
		let pixData = {
			payload: '',
			encodedImage: '',
		};

    // Caso seja pagamento de assinatura (paymentId), buscar QR Code diretamente no Asaas
    if (paymentId) {
      try {
        const asaas = new AsaasClient();
        const qrCodeData = await asaas.getPixQRCode(paymentId);
        if (qrCodeData && qrCodeData.encodedImage && qrCodeData.payload) {
          pixData = { payload: qrCodeData.payload, encodedImage: qrCodeData.encodedImage };
        } else {
          throw new Error('Invalid QR code data received from Asaas');
        }
      } catch (err) {
        console.error('Error fetching PIX QR code by paymentId:', err);
        throw err;
      }
    }

    // Tentar buscar a transação do banco de dados (fluxo pagamento único)
    if (!pixData.payload && transactionId) {
			try {
				const transaction = await db.transaction.findUnique({
					where: { id: transactionId },
					include: {
						appointment: {
							include: {
								doctor: {
									include: {
										user: true,
										specialties: true,
									},
								},
							},
						},
					},
				});

				if (transaction?.appointment) {
					transactionAmount = Number(transaction.amount);
					appointmentData =
						transaction.appointment as unknown as AppointmentWithDoctor;

					if (appointmentData.doctor?.user) {
						doctorData = {
							id: appointmentData.doctor.id,
							name: appointmentData.doctor.user.name || 'Médico',
							profileImage: appointmentData.doctor.user.avatarUrl,
							specialty:
								appointmentData.doctor.specialties[0]?.name || 'Clínico Geral',
							crm: appointmentData.doctor.crm || '',
							crmState: appointmentData.doctor.crmState || '',
						};
					}

					// Se tiver um asaasId, tentar buscar o QR code do PIX
          if (transaction.asaasId) {
						try {
						const asaas = new AsaasClient();
						const qrCodeData = await asaas.getPixQRCode(transaction.asaasId);

						if (qrCodeData && qrCodeData.encodedImage && qrCodeData.payload) {
							pixData = {
								payload: qrCodeData.payload,
								encodedImage: qrCodeData.encodedImage,
							};
							} else {
								console.error('Invalid QR code data received from Asaas');
								throw new Error('Invalid QR code data received from Asaas');
							}
						} catch (pixError) {
							console.error('Error fetching PIX QR code:', pixError);
							throw pixError;
						}
          } else {
						console.error('No Asaas ID found for transaction:', transactionId);
						throw new Error(
							'No Asaas payment ID associated with this transaction'
						);
					}
				}
			} catch (dbError) {
				console.error('Error fetching transaction data:', dbError);
				// Continue com dados simulados em caso de erro
			}
		}

		// Se não conseguiu buscar a transação mas tem appointmentId, tentar buscar dados da consulta
		if (!doctorData && appointmentId) {
			try {
				const appointment = await db.appointment.findUnique({
					where: { id: appointmentId },
					include: {
						doctor: {
							include: {
								user: true,
								specialties: true,
							},
						},
					},
				});

				if (appointment) {
					appointmentData = appointment as unknown as AppointmentWithDoctor;
					transactionAmount = Number(appointment.amount);

					if (appointment.doctor?.user) {
						doctorData = {
							id: appointment.doctor.id,
							name: appointment.doctor.user.name || 'Médico',
							profileImage: appointment.doctor.user.avatarUrl,
							specialty:
								appointment.doctor.specialties[0]?.name || 'Clínico Geral',
							crm: appointment.doctor.crm || '',
							crmState: appointment.doctor.crmState || '',
						};
					}
				}
			} catch (dbError) {
				console.error('Error fetching appointment data:', dbError);
				// Continue com dados simulados em caso de erro
			}
		}

		// Usar QR code padrão apenas se não conseguiu buscar do Asaas
		if (!pixData.encodedImage || !pixData.payload) {
			pixData = {
				payload:
					'00020101021226990014br.gov.bcb.pix2572pix.zapvida.com.br52040000530398654046.005802BR5924ZAP VIDA SERVICOS MEDICOS6009SAO PAULO62080504test63044E8E',
				encodedImage:
					'iVBORw0KGgoAAAANSUhEUgAAAHQAAAB0CAYAAABUmhYnAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAdKADAAQAAAABAAAAdAAAAACyeR7RAAAE0ElEQVR4Ae2cUW4UMRBEeYe9xKLcgDvsBbinuQGIE8ANkrshDoDEgffvQo8YtVPtnrGdTLrftPSr2I47r2vGcWZWh8Oh03Ec/3adLKTuZJ7BNjCQeQZzH2gDhNpAqVcDlKHUzAGlUmpyjxChUKkRCpUaoVCpEQqVGqFQqREKlRqhUKkRCpUaoVCpEQqVGqFQqREKlRqhUKkRCpUaoVCpEQqVGqFQqREKlRqhUKkRCpUaoVCpEQqVGqFQqREKlRqhUKkRCpXaJUIPHz9+67rLy+77Nft73bOHDw6fP93V30b4bx8+/Okurx5013fWO31v/Prr9w/dpzev78C+e/jjm40nL593b165iN2EVnKud39bfT+RFt1p5RGaA0YoVGqEQqVGKFRqVoTev39y/PXjdbeX9+7p09Nx2d88e1Z/rJtaez5+fHms67a47pxc1tqfH1lbEVrJdLf/9evXSu7J13+8fXsSMzd+aDtcnA95Wd/9s7iF0HxyhEKlRihUaoRCpTYjNI+V1PZWXlaIvs29/r47vlb+Mu/pzn1m55eHvUgmMo+qiO2O8pYS/LxzyNvq+JIXoflGIxQqNUKhUiMUKjUrQuuxUi31dn7P0Z5j/vTM3jk/x+T4XL+G389v+Xpkrgjlw5J6cNQbSi0PFvcRXt9hbH2WrfUztx6Z/O86yTcWoVCpEQqVGqFQqdkQOvXnZ6ZCHzqe+f3zc5/XU8d8bPT4+eP+z+/tz7GW149bJ1RifMxbHyIUKjVCoVIjFCo1G0LrsVJ9/iyGUuOxVS3/XF6Op5bP8OVHaP6GIhQqNUKhUiMUKrUVodOxUm2VObT62eOxnK+3x0pebxbzevJzXkteq+vL8Xn9OU/r87xeZHpC+fiJeiHUI7H5vXmtz++5/hb3ef1bE8oYGh2FCoZKjVCo1AiFSs2K0BzzWCnnn1t/7vizVj7Hn9vbz+WXa/iVj5x3Wl/veYKPzyGPn7/n8/fXn+tGKFRqhEKlRihUajaE1nNfPdCZji3q+KiOp+pYqM5TPR5rRdajntY3re9cXtaX8/a6viI0fzMRCpUaoVCpEQqVmhWheazkY6Vz49Dp+bP65+v29mjPE3g856/ybz1W27v++uK9e+b9tVBvL5/P+fTp8/3cOe+5z/Pnn9U35d/lRWhJ1ltEKFRqhEKlRihUalaE+ljJx0q51NtKVJnv9/3qrPLm9yJT/jqvrvKzfvnYyI+fvA9t5fffvMOb0JVYbz1CoVIjFCo1QqFSWxE6HSvV9lv/zV8OZcXk1iOUKqvXFaFQqREKlRqhUKkRCpXatYTeBrJ3s0Z/AbWCaRFYqvr5AAAAAElFTkSuQmCC',
			};
		}

		// Create order data for the client component with medical context
		const orderData = {
			id: transactionId,
			amount: transactionAmount,
			status: 'PENDING',
			createdAt: new Date(),
			updatedAt: new Date(),
			gatewayId: transactionId,
			product: {
				title: appointmentData
					? String(appointmentData.consultType)
					: 'Teleconsulta Médica',
				description: 'Atendimento médico online via videoconferência',
				thumbnail: null,
				scheduledAt: appointmentData?.scheduledAt,
				duration: appointmentData?.duration || 30,
				isOnline: appointmentData?.appointmentType === 'TELEMEDICINE',
				doctor: doctorData || {
					id: 'mock-doctor-id',
					name: 'Dr. Médico',
					profileImage: null,
					specialty: 'Clínico Geral',
					crm: '12345',
					crmState: 'SP',
				},
			},
			user: {
				name: 'Paciente',
				email: '<EMAIL>',
			},
		};

		return (
			<div className='min-h-screen bg-gradient-to-br from-blue-50/30 to-white/90'>
				<div className='container max-w-5xl py-8 lg:py-12'>
					<PixPage
						data={{
							order: orderData,
							pixPayload: pixData.payload,
							qrCode: pixData.encodedImage,
						}}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading PIX page:', error);
		// Instead of redirecting, render an error component with error details
		return (
			<div className='min-h-screen bg-gradient-to-br from-blue-50/30 to-white/90 flex items-center justify-center'>
				<div className='container max-w-lg p-8 bg-white rounded-lg shadow-sm border border-red-100'>
					<div className='flex flex-col items-center gap-4 text-center'>
						<AlertTriangle className='h-12 w-12 text-red-500' />
						<h1 className='text-xl font-semibold'>
							Erro no processamento do pagamento
						</h1>
						<p className='text-gray-600'>
							Ocorreu um erro ao carregar os dados do pagamento. Por favor,
							tente novamente ou entre em contato com o suporte.
						</p>
						<div className='bg-red-50 p-3 rounded text-xs text-red-700 font-mono w-full overflow-auto mt-2 text-left'>
							{error instanceof Error ? error.message : String(error)}
						</div>
						<Button asChild className='mt-4'>
							<Link href='/'>Voltar para a página inicial</Link>
						</Button>
					</div>
				</div>
			</div>
		);
	}
}
