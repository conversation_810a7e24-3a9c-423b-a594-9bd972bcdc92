import { cn } from '@ui/lib';
import Cleave from 'cleave.js/react';
// components/phone-input.tsx
import { forwardRef } from 'react';

interface BRPhoneInputProps
	extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
	value: string;
	onChange?: (value: string) => void;
	error?: boolean;
}

export const BRPhoneInput = forwardRef<HTMLInputElement, BRPhoneInputProps>(
	({ className, value, onChange, error, ...props }, ref) => {
		return (
			<div className='relative'>
				<Cleave
					{...props}
					style={{ fontSize: '15px' }}
					value={value}
					onChange={(e: any) => {
						const rawValue = e.target.rawValue;
						onChange?.(rawValue);
					}}
					options={{
						delimiters: ['(', ') ', ' ', '-'],
						blocks: [0, 2, 5, 4],
						numericOnly: true,
					}}
					className={cn(
						'flex h-10 w-full rounded-lg border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:border-primary focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
						error && 'border-destructive',
						className
					)}
					ref={ref}
				/>

				<div className='pointer-events-none absolute inset-y-0 right-1 flex items-center pr-3'>
					{value?.length >= 11 && !error && (
						<svg
							className='text-success size-4'
							fill='none'
							strokeWidth='2'
							stroke='currentColor'
							viewBox='0 0 24 24'
							xmlns='http://www.w3.org/2000/svg'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								d='M5 13l4 4L19 7'
							/>
						</svg>
					)}
					{error && (
						<svg
							className='size-4 text-destructive'
							fill='none'
							strokeWidth='2'
							stroke='currentColor'
							viewBox='0 0 24 24'
							xmlns='http://www.w3.org/2000/svg'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					)}
				</div>
			</div>
		);
	}
);

BRPhoneInput.displayName = 'BRPhoneInput';
