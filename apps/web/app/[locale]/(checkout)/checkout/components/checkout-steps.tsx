'use client';

import { Fragment, ReactNode } from 'react';

export type CheckoutStep = 'urgency' | 'data' | 'payment';

interface CheckoutStepsProps {
  currentStep: CheckoutStep;
}

export function CheckoutSteps({ currentStep }: CheckoutStepsProps) {
  // Define the steps with more generic labels
  const steps: Array<{ id: CheckoutStep; label: string }> = [
    { id: 'urgency', label: 'Escolha' },
    { id: 'data', label: 'Informações' },
    { id: 'payment', label: 'Consulta' },
  ];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between w-full mb-2">
        {steps.map((step, index) => (
          <Fragment key={step.id}>
            <div className="flex flex-col items-center">
              <div
                className={`${
                  currentStep === step.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 text-gray-500'
                } w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold`}
              >
                {index + 1}
              </div>
              <span
                className={`text-xs font-medium mt-1 ${
                  currentStep === step.id ? 'text-primary' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className="flex-1 mx-2 border-t-2 border-dashed border-gray-300"></div>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
}
