'use client';

import { <PERSON><PERSON> } from '@ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Alert, AlertDescription, AlertTitle } from '@ui/components/alert';
import {
	ArrowLeft,
	Calendar,
	Video,
	Phone,
	MessageSquare,
	Info,
	AlertCircle,
	CheckCircle2,
	Clock,
} from 'lucide-react';
import Link from 'next/link';
import { ConsultType } from '@prisma/client';

interface ProductAccessProps {
	appointment: {
		id: string;
		scheduledAt: Date;
		consultType: ConsultType;
		status: string;
		roomId?: string | null;
		doctor: {
			user: {
				name?: string | null;
			};
		};
	};
	isPaid?: boolean;
	formattedDate: string;
	formattedTime: string;
}

export function ProductAccess({ appointment, isPaid = true, formattedDate, formattedTime }: ProductAccessProps) {
	const getConsultIcon = () => {
		switch (appointment.consultType) {
			case 'VIDEO':
				return <Video className='h-5 w-5' />;
			case 'AUDIO':
				return <Phone className='h-5 w-5' />;
			case 'CHAT':
				return <MessageSquare className='h-5 w-5' />;
			default:
				return <Calendar className='h-5 w-5' />;
		}
	};

	const getConsultTypeText = () => {
		switch (appointment.consultType) {
			case 'VIDEO':
				return 'Consulta por vídeo';
			case 'AUDIO':
				return 'Consulta por áudio';
			case 'CHAT':
				return 'Consulta por chat';
			default:
				return 'Consulta';
		}
	};

	const getAccessInstructions = () => {
		return {
			title: 'Acesse sua consulta',
			description: isPaid
				? `Sua consulta está agendada para ${formattedDate} às ${formattedTime}`
				: 'Sua consulta será confirmada assim que recebermos a confirmação do pagamento',
			buttonText: 'Acessar minhas consultas',
			link: `/account/appointments`,
			appointmentLink: `/account/appointment/${appointment.id}`,
		};
	};

	const instructions = getAccessInstructions();

	return (
		<Card className='mb-6'>
			<CardHeader className='pb-3'>
				<div className='flex items-center gap-2'>
					<Calendar className='h-5 w-5' />
					<CardTitle>{instructions.title}</CardTitle>
				</div>
			</CardHeader>

			<CardContent>
				<div className='space-y-4'>
					{!isPaid && (
						<Alert variant='default' className='mb-4'>
							<AlertCircle className='h-4 w-4' />
							<AlertTitle>Aguardando confirmação de pagamento</AlertTitle>
							<AlertDescription>
								Estamos processando seu pagamento. Assim que confirmado, você
								receberá um email e poderá visualizar todos os detalhes da sua consulta.
							</AlertDescription>
						</Alert>
					)}

					{isPaid && (
						<Alert variant='success' className='mb-4'>
							<CheckCircle2 className='h-4 w-4' />
							<AlertTitle>Consulta agendada!</AlertTitle>
							<AlertDescription>
								Sua consulta está confirmada. Enviamos também todas as informações para seu email.
							</AlertDescription>
						</Alert>
					)}

					<div className='flex items-center gap-4'>
						<div className='flex h-16 w-16 items-center justify-center rounded-lg bg-primary/10'>
							{getConsultIcon()}
						</div>
						<div>
							<h3 className='font-medium'>{getConsultTypeText()}</h3>
							<p className='text-sm text-muted-foreground'>
								{instructions.description}
							</p>
						</div>
					</div>

					<div className='flex items-center gap-4 bg-muted p-4 rounded-lg'>
						<Clock className='h-5 w-5 text-primary' />
						<div>
							<h3 className='font-medium text-sm'>Horário da consulta</h3>
							<p className='text-sm'>{formattedDate} às {formattedTime}</p>
						</div>
					</div>

					<Button asChild className='w-full' disabled={!isPaid}>
						<Link href={instructions.link}>{instructions.buttonText}</Link>
					</Button>

					{isPaid && instructions.appointmentLink && (
						<Button asChild variant='outline' className='w-full mt-2'>
							<Link href={instructions.appointmentLink}>
								Acessar esta consulta
							</Link>
						</Button>
					)}

					<div className='bg-muted p-4 rounded-lg'>
						<div className='flex gap-2 mb-2'>
							<Info className='h-4 w-4 text-primary' />
							<h4 className='text-sm font-medium'>Como acessar sua consulta:</h4>
						</div>
						<ol className='text-sm space-y-1 list-decimal list-inside text-muted-foreground'>
							<li>Faça login na plataforma com seu email e senha</li>
							<li>Acesse a área "Minhas Consultas" no menu superior</li>
							<li>
								Encontre sua consulta na lista de consultas agendadas
							</li>
							<li>
								No dia e horário da consulta, clique em "Entrar na sala" para iniciar seu atendimento
							</li>
						</ol>
					</div>

					<hr />

					<div className='flex justify-center text-sm'>
						<Link href='/' className='flex items-center gap-2'>
							<ArrowLeft className='h-4 w-4' />
							Voltar para a página inicial
						</Link>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
