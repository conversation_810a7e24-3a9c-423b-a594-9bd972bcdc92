import { Card, CardContent, CardHeader } from '@ui/components/card';
// components/success-header.tsx
import { CheckCircle2 } from 'lucide-react';

import { PaymentStatus } from '@prisma/client';
import { formatCurrency } from '@lib/utils';

interface SuccessHeaderProps {
	transaction: {
		id: string;
		amount: number | string;
		paymentMethod: string;
		status: PaymentStatus;
	};
	appointment: {
		id: string;
		patient: {
			user: {
				email: string;
				name?: string | null;
			};
		};
		doctor: {
			user: {
				name?: string | null;
			};
		};
	};
	formattedDate: string;
	formattedTime: string;
}

export function SuccessHeader({ transaction, appointment, formattedDate, formattedTime }: SuccessHeaderProps) {
	// Garantir que o valor seja numérico para formatação
	const amount = typeof transaction.amount === 'string'
		? parseFloat(transaction.amount)
		: transaction.amount;

	return (
		<Card className='mb-8'>
			<CardHeader className='text-center pb-6'>
				<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
					<CheckCircle2 className='h-6 w-6 text-primary' />
				</div>
				<h1 className='text-2xl font-semibold'>Agendamento Confirmado!</h1>
				<p className='text-muted-foreground'>
					{transaction.paymentMethod === 'CREDIT_CARD'
						? 'Seu pagamento foi processado com sucesso'
						: 'Recebemos a confirmação do seu pagamento'}
				</p>
			</CardHeader>

			<CardContent className='border-t pt-6'>
				<div className='space-y-4'>
					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Número da consulta</span>
						<span className='font-medium'>{appointment.id}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Data e horário</span>
						<span className='font-medium'>{formattedDate} às {formattedTime}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Médico</span>
						<span className='font-medium'>{appointment.doctor.user.name || 'Médico'}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Total pago</span>
						<span className='font-medium'>{formatCurrency(amount)}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Email</span>
						<span className='font-medium'>{appointment.patient.user.email}</span>
					</div>

					<div className='rounded-lg bg-muted p-4 text-sm text-center text-muted-foreground'>
						Enviamos um email para você com todos os detalhes da sua consulta
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
