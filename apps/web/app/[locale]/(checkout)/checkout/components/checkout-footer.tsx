import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export function CheckoutFooter() {
	return (
		<div>
			{/* Termos e Segurança */}
			<div className='mt-8 space-y-6 text-center'>
				<div className='space-y-2'>
					<p className='mb-2 pt-3 text-xs text-muted-foreground'>
						Ao confirmar o pagamento, você concorda com nossos{' '}
						<Link href='/legal/terms' target='_blank' className='text-primary hover:underline'>
							Termos de Uso
						</Link>{' '}
						e{' '}
						<Link href='/legal/privacy' target='_blank' className='text-primary hover:underline'>
							Política de Privacidade
						</Link>
					</p>
					<span className='text-xs text-muted-foreground'>
						Este site está protegido pelo Google reCAPTCHA.{' '}
						<a
							href='https://policies.google.com/privacy'
							target='_blank'
							rel='noreferrer'
						>
							Política de Privacidade
						</a>{' '}
						e{' '}
						<a
							href='https://policies.google.com/terms'
							target='_blank'
							rel='noreferrer'
						>
							Termos de Serviço
						</a>
						.
					</span>
					{/* <div className='flex flex-col items-center justify-center gap-3 px-8'>
						<span className='text-muted-foreground text-xs lg:px-16 flex items-center gap-2'>
							<ShieldCheck className='h-4 w-4' />
							Este pagamento será processado de forma segura pela nossa plataforma. Seus dados são criptografados e não são armazenados em nossos servidores.
						</span>
					</div> */}
				</div>

				<div className='flex items-center justify-center gap-6'>
					<Image
						src='/images/payments/ssl.svg'
						alt='SSL Security'
						width={32}
						height={32}
						className='opacity-50'
					/>
					<Image
						src='/images/payments/pci.svg'
						alt='PCI Compliant'
						width={38}
						height={38}
						className='opacity-50'
					/>
				</div>
			</div>
		</div>
	);
}
