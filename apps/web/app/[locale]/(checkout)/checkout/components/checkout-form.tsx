// apps/web/(checkout)/checkout/components/checkout-form.tsx

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { FormProvider, useForm } from 'react-hook-form';
import { CheckoutFormData, checkoutFormSchema } from './types';
import { CustomerForm } from './customer-form';
import { PaymentForm } from './payment-form';
import { useToast } from '@ui/hooks/use-toast';
import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';

// Define CheckoutFormProps interface
interface CheckoutFormProps {
	product: {
		id: string;
		title: string;
		description?: string | null;
		type: 'COURSE' | 'MENTORING' | 'EBOOK' | 'MEDICAL_CONSULTATION' | 'ON_DUTY_CONSULTATION';
		price: number;
		installmentsLimit: number;
		enableInstallments?: boolean;
		thumbnail?: string | null;
		checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL' | 'PLANTAO';
		acceptedPayments?: string[];
		checkoutSettings?: any;
		customCheckoutUrl?: string | null;
		successUrl?: string | null;
		cancelUrl?: string | null;
		termsUrl?: string | null;
		offers?: {
			id: string;
			title: string;
			description?: string | null;
			price: number;
			type: string;
		}[];
		// Campos específicos para consultas médicas
		scheduledAt?: Date;
		duration?: number;
		doctor?: {
			id: string;
			name: string;
			profileImage?: string;
			specialty?: string;
			crm?: string;
			crmState?: string;
		};
		serviceFee?: number;
		// Adicionando campos para plantão
		isOnDuty?: boolean;
		urgencyLevel?: 'high' | 'medium' | 'low';
		// Adicionando campo para parceiros
		partner?: string;
	};
}

export function CheckoutForm({ product }: CheckoutFormProps) {
	const router = useRouter();
	const { toast } = useToast();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Use tRPC mutation hooks
	const processPaymentMutation =
		apiClient.checkout.processPayment.useMutation();
	const processPlantaoPaymentMutation =
		apiClient.checkout.processPlantaoPayment.useMutation();



	// Use default payment method from accepted payments
	const defaultPaymentMethod = product.acceptedPayments?.includes('CREDIT_CARD')
		? 'CREDIT_CARD'
		: product.acceptedPayments?.includes('PIX')
		? 'PIX'
		: product.acceptedPayments?.includes('BOLETO')
		? 'BOLETO'
		: 'CREDIT_CARD';

	const methods = useForm<CheckoutFormData>({
		resolver: zodResolver(checkoutFormSchema),
		defaultValues: {
			customerData: {
				name: '',
				email: '',
				phone: '',
				cpf: '',
			},
			// Para plantão, usar um ID genérico; para consultas normais, usar o ID do médico
			doctorId: product.checkoutType === 'PLANTAO' ? 'plantao-service' : product.id,
			scheduledAt: product.scheduledAt,
			duration: product.duration,
			paymentMethod: defaultPaymentMethod,
			creditCard: {
				cardNumber: '',
				cardHolder: '',
				cardExpiry: '',
				cardCvv: '',
				installments: 1,
			},
			// Adicionando campos para plantão
			isOnDuty: product.isOnDuty,
			urgencyLevel: product.urgencyLevel,
			// Adicionando campo para parceiros
			...(product.partner && { partner: product.partner }),
		},
	});

	const onSubmit = async (data: CheckoutFormData) => {
		try {

			setIsSubmitting(true);

			// Validate base required fields - para plantão, não precisamos de doctorId específico
			if (!data.doctorId && product.checkoutType !== 'PLANTAO' && !product.isOnDuty) {
				throw new Error('ID do médico não informado');
			}

			// Ensure we have customer data
			if (!data.customerData || typeof data.customerData !== 'object') {
				throw new Error('Dados do cliente não informados');
			}

			// Create safe customer data object
			const safeCustomerData = {
				name: data.customerData.name || '',
				email: data.customerData.email || '',
				phone: data.customerData.phone || '',
				cpf: data.customerData.cpf || '',
			};

			// Validate required customer fields
			if (!safeCustomerData.name || !safeCustomerData.email) {
				throw new Error('Nome e email são obrigatórios');
			}

			// VALIDAÇÃO ESPECIAL PARA PLANTÃO - TELEFONE OBRIGATÓRIO
			if (product.checkoutType === 'PLANTAO' || product.isOnDuty) {
				if (!safeCustomerData.phone || safeCustomerData.phone.replace(/\D/g, '').length < 10) {
					throw new Error('Telefone é obrigatório para atendimento de plantão');
				}
				console.log('[CHECKOUT_FORM] Telefone validado para plantão:', {
					phone: safeCustomerData.phone,
					cleanedLength: safeCustomerData.phone.replace(/\D/g, '').length
				});
			}

			// Criar uma data para dois dias no futuro
			const futureDateForSchedule = new Date();
			futureDateForSchedule.setDate(futureDateForSchedule.getDate() + 2);

			// Display loading state
			toast({
				title: 'Processando pagamento',
				description: 'Aguarde enquanto processamos seu pagamento...',
				variant: 'default',
			});

			// Choose the appropriate payment processing method based on checkout type
			let result;

			if (product.checkoutType === 'PLANTAO' || product.isOnDuty) {
				// Use the specific plantão payment procedure
				result = await processPlantaoPaymentMutation.mutateAsync({
					customerData: safeCustomerData,
					paymentMethod: data.paymentMethod,
					creditCard:
						data.paymentMethod === 'CREDIT_CARD'
							? {
									cardNumber:
										data.creditCard?.cardNumber.replace(/\D/g, '') || '',
									cardHolder: data.creditCard?.cardHolder || '',
									cardExpiry: data.creditCard?.cardExpiry || '',
									cardCvv: data.creditCard?.cardCvv || '',
									installments: data.creditCard?.installments || 1,
							  }
							: undefined,
					urgencyLevel: product.urgencyLevel as 'high' | 'medium' | 'low',
					symptoms: undefined, // Could be added to the form later
					partner: product.partner,
				});
			} else {
				// Use the regular appointment payment procedure
				result = await processPaymentMutation.mutateAsync({
					customerData: safeCustomerData,
					doctorId: data.doctorId,
					paymentMethod: data.paymentMethod,
					scheduledAt:
						parseScheduledDate(data.scheduledAt) || futureDateForSchedule,
					duration: data.duration || undefined,
					creditCard:
						data.paymentMethod === 'CREDIT_CARD'
							? {
									cardNumber:
										data.creditCard?.cardNumber.replace(/\D/g, '') || '',
									cardHolder: data.creditCard?.cardHolder || '',
									cardExpiry: data.creditCard?.cardExpiry || '',
									cardCvv: data.creditCard?.cardCvv || '',
									installments: data.creditCard?.installments || 1,
							  }
							: undefined,
					// IMPORTANTE: Incluir os parâmetros de plantão para o fluxo regular
					isOnDuty: product.isOnDuty,
					urgencyLevel: product.urgencyLevel,
					// Adicionar o parâmetro de parceiro
					...(product.partner && { partner: product.partner }),
				});
			}



			// Resto do código de processamento de resposta...
			// ...

			// Validate result
			if (!result || typeof result !== 'object') {
				throw new Error('Resposta inválida do servidor');
			}

			if (!result.transactionId) {
				throw new Error('ID da transação não retornado');
			}

			// Handle PIX payments
				if (data.paymentMethod === 'PIX') {
					// Ensure PIX code was generated
					if (!result.pixCode && result.paymentMethod === 'PIX') {
						throw new Error('Dados do PIX não foram gerados corretamente');
					}

					try {
						await router.push(
							`/checkout/pix?transactionId=${result.transactionId}`
						);
					} catch (routerError) {
						window.location.href = `/checkout/pix?transactionId=${result.transactionId}`;
					}
					return;
				}

			// Handle other payment methods
			if (product.successUrl && result.paymentMethod !== 'BOLETO') {
				toast({
					title: 'Pagamento processado com sucesso!',
					description: 'Você será redirecionado para a página de confirmação.',
					variant: 'success',
				});

				setTimeout(() => {
					router.push(
						`${product.successUrl}?appointmentId=${result.appointmentId}`
					);
				}, 1000);
			} else if (result.paymentMethod === 'BOLETO') {
				router.push(`/checkout/boleto?transactionId=${result.transactionId}`);
			} else {
				toast({
					title: 'Pagamento processado com sucesso!',
					description: 'Você será redirecionado para a página de confirmação.',
					variant: 'success',
				});

				setTimeout(() => {
					router.push(
						`/checkout/success?appointmentId=${result.appointmentId}`
					);
				}, 1000);
			}
		} catch (error) {
			// Enhanced error logging
			if (error instanceof Error && error.name === 'TRPCClientError') {

			} else if (error instanceof Error) {

			}

			// Show error toast with more helpful message
			toast({
				title: 'Erro ao processar pagamento',
				description:
					error instanceof Error
						? String(
								'data' in error &&
									error.data &&
									typeof error.data === 'object' &&
									'message' in error.data
									? error.data.message
									: error.message
						  )
						: 'Erro desconhecido no processamento',
				variant: 'error',
			});

			if (product.cancelUrl) {
				setTimeout(() => {
					const message =
						error instanceof Error ? error.message.toLowerCase() : '';
					const isCriticalError =
						message.includes('payload') ||
						message.includes('null') ||
						message.includes('pix');

					if (isCriticalError) {

						return;
					}


					// router.push(product.cancelUrl!);
				}, 3000);
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	// Função para processar o scheduledAt corretamente
	const parseScheduledDate = (scheduledAt: any): Date | undefined => {
		if (!scheduledAt) {
			// Se não tiver data agendada, use 2 dias no futuro
			const futureDate = new Date();
			futureDate.setDate(futureDate.getDate() + 2);
			return futureDate;
		}

		// Verificar se é uma string com formato $D...
		if (typeof scheduledAt === 'string' && scheduledAt.startsWith('$D')) {
			try {
				// Extrair a data depois do prefixo $D
				return new Date(scheduledAt.substring(2));
			} catch (error) {

				// Em caso de erro, use 2 dias no futuro
				const futureDate = new Date();
				futureDate.setDate(futureDate.getDate() + 2);
				return futureDate;
			}
		}

		// Se já for uma instância de Date, use-a diretamente
		if (scheduledAt instanceof Date) {
			return scheduledAt;
		}

		// Se for uma string normal de data ou timestamp, tente convertê-la
		try {
			return new Date(scheduledAt);
		} catch (error) {

			// Em caso de erro, use 2 dias no futuro
			const futureDate = new Date();
			futureDate.setDate(futureDate.getDate() + 2);
			return futureDate;
		}
	};

	// Apply custom checkout styles from product settings if available
	const checkoutStyles = product.checkoutSettings?.styles || {};

	return (
		<FormProvider {...methods}>
			<form
				onSubmit={methods.handleSubmit(onSubmit)}
				className='container max-w-6xl mx-auto px-0 lg:px-4'
				style={checkoutStyles.formContainer || {}}
			>
				<div className='lg:col-span-8 space-y-6'>
					<CustomerForm />
					<PaymentForm
						totalAmount={calculateTotal(product.price, product.serviceFee)}
						installmentsLimit={product.installmentsLimit}
						enableInstallments={product.enableInstallments}
						acceptedPayments={product.acceptedPayments}
						loading={isSubmitting || processPaymentMutation.isPending}
						onPixClick={async () => {
						// Get current form data
						const currentData = methods.getValues();

							// Show loading
							setIsSubmitting(true);

							try {
								// Validate required fields
								if (
									!currentData.customerData?.name ||
									!currentData.customerData?.email
								) {
									throw new Error(
										'Por favor, preencha nome e email antes de continuar.'
									);
								}

								// Para plantão, não precisamos validar doctorId específico
								if (!currentData.doctorId && product.checkoutType !== 'PLANTAO' && !product.isOnDuty) {
									throw new Error('ID do médico não informado');
								}

								// Criar uma data para dois dias no futuro
								const futureDateForSchedule = new Date();
								futureDateForSchedule.setDate(
									futureDateForSchedule.getDate() + 2
								);

								// Display loading state
								toast({
									title: 'Processando pagamento PIX',
									description: 'Aguarde enquanto geramos seu código PIX...',
									variant: 'default',
								});

								// Process payment using appropriate procedure based on checkout type
								let result;

								if (product.checkoutType === 'PLANTAO' || product.isOnDuty) {
									// Use plantão payment procedure for PIX
									result = await processPlantaoPaymentMutation.mutateAsync({
										customerData: {
											name: currentData.customerData.name || '',
											email: currentData.customerData.email || '',
											phone: currentData.customerData.phone || '',
											cpf: currentData.customerData.cpf || '',
										},
										paymentMethod: 'PIX',
										urgencyLevel: product.urgencyLevel as 'high' | 'medium' | 'low',
										symptoms: undefined,
										partner: product.partner,
									});
								} else {
									// Use regular payment procedure for PIX
									result = await processPaymentMutation.mutateAsync({
										customerData: {
											name: currentData.customerData.name || '',
											email: currentData.customerData.email || '',
											phone: currentData.customerData.phone || '',
											cpf: currentData.customerData.cpf || '',
										},
										doctorId: currentData.doctorId || product.id,
										paymentMethod: 'PIX',
										scheduledAt:
											parseScheduledDate(currentData.scheduledAt) ||
											futureDateForSchedule,
										duration: currentData.duration || undefined,
										// IMPORTANTE: Incluir os parâmetros de plantão no fluxo de PIX
										isOnDuty: product.isOnDuty,
										urgencyLevel: product.urgencyLevel,
										// Adicionar o parâmetro de parceiro
										...(product.partner && { partner: product.partner }),
									});
								}



								// Validate result
								if (!result || typeof result !== 'object') {
									throw new Error('Resposta inválida do servidor');
								}

								if (!result.transactionId) {
									throw new Error('ID da transação não retornado');
								}

								// Ensure PIX code was generated for PIX payments
								if (!result.pixCode && result.paymentMethod === 'PIX') {
									throw new Error(
										'Dados do PIX não foram gerados corretamente'
									);
								}

								// Navigate to PIX page
								try {
									await router.push(
										`/checkout/pix?transactionId=${result.transactionId}`
									);
								} catch (routerError) {
									window.location.href = `/checkout/pix?transactionId=${result.transactionId}`;
								}
							} catch (error) {
								// Enhanced error logging
								if (
									error instanceof Error &&
									error.name === 'TRPCClientError'
								) {
									console.error('TRPC Error details:', error);
								}

								// Show error toast
								toast({
									title: 'Erro no processamento PIX',
									description:
										error instanceof Error
											? error.message
											: 'Erro desconhecido no processamento PIX',
									variant: 'error',
								});
							} finally {
								setIsSubmitting(false);
							}
						}}
					/>
				</div>
			</form>
		</FormProvider>
	);
}

function calculateTotal(basePrice: number, serviceFee?: number): number {
	let total = Number(basePrice);

	if (serviceFee) {
		total += serviceFee;
	}

	return total;
}
