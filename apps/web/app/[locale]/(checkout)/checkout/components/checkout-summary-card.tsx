'use client';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import {
	AlertTriangle,
	CalendarDays,
	ChevronDown,
	Clock,
	FileText,
	CreditCard,
	Info,
	MapPin,
	Video,
	User,
	Calendar1Icon,
} from 'lucide-react';
import Image from 'next/image';

import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from '@ui/components/collapsible';
import { cn } from '@ui/lib';
import { useState } from 'react';
import { DoctorAvatar } from './doctor-avatar';

import { ON_DUTY_CONFIG, UrgencyLevel } from 'api/constants/on-duty';

interface Product {
	id: string;
	title: string;
	description?: string | null;
	type: 'COURSE' | 'MENTORING' | 'EBOOK' | 'MEDICAL_CONSULTATION';
	price: number;
	thumbnail?: string | null;
	offers?: Array<{ id: string; price: number }>;
	scheduledAt?: Date;
	duration?: number;
	isOnline?: boolean;
	serviceFee?: number;
	isOnDuty?: boolean; // Indica se é plantão
	urgencyLevel?: 'high' | 'medium' | 'low'; // Nível de urgência para plantão
	doctor?: {
		id: string;
		name: string;
		profileImage?: string | null;
		specialty?: string;
		crm?: string;
		crmState?: string;
	};
}

interface ProductSummaryProps {
	product: Product;
	isMobile: boolean;
}

export function ProductSummaryCard({ product, isMobile }: ProductSummaryProps) {
	const [isOpen, setIsOpen] = useState(false);

	const calculateTotal = () => {
		let total = product.price;
		// Adicionar taxa de serviço quando existir
		if (product.serviceFee) {
			total += product.serviceFee;
		}
		return total;
	};

	// Função para retornar o nome do nível de urgência e classe de cor
	const getUrgencyInfo = (level?: string) => {
		if (!level)
			return { name: 'Normal', color: 'bg-blue-500 text-white', waitTime: '' };

		const config = ON_DUTY_CONFIG[level.toUpperCase() as UrgencyLevel];
		return {
			name: config.label,
			color:
				level === 'high'
					? 'bg-red-500 text-white'
					: level === 'medium'
					? 'bg-amber-500 text-white'
					: 'bg-green-500 text-white',
			waitTime: `+ ${config.waitTime} min`,
		};
	};

	const urgencyInfo = getUrgencyInfo(product.urgencyLevel);

	const MobileHeader = () => (
		<div className='flex items-center justify-between w-full'>
			<div className='flex items-center gap-3'>
				{product.type === 'MEDICAL_CONSULTATION' && product.doctor ? (
					<DoctorAvatar
						name={product.doctor.name}
						avatarUrl={product.doctor.profileImage || product.thumbnail}
						size='sm'
					/>
				) : product.thumbnail ? (
					<Image
						src={product.thumbnail}
						alt={product.title}
						width={55}
						height={55}
						className='w-10 h-10 object-cover'
					/>
				) : (
					<div className='h-12 w-12 rounded-md bg-muted flex items-center justify-center'>
						<FileText className='h-6 w-6 text-muted-foreground' />
					</div>
				)}
				<div className='py-3 text-left'>
					<h3 className='font-medium text-sm line-clamp-1'>{product.title}</h3>
					<div className='text-lg font-bold text-primary'>
						R$ {calculateTotal().toFixed(2)}
					</div>
				</div>
			</div>
			<ChevronDown
				className={cn('h-5 w-5 text-muted-foreground transition-transform', {
					'transform rotate-180': isOpen,
				})}
			/>
		</div>
	);

	const Content = () => (
		<div className='space-y-5'>
			{!isMobile &&
				product.type === 'MEDICAL_CONSULTATION' &&
				product.doctor && (
					<div className='flex flex-col'>
						<div className='flex items-start gap-4 mb-3'>
							<DoctorAvatar
								name={product.doctor.name}
								avatarUrl={product.doctor.profileImage || product.thumbnail}
								size='lg'
								className='rounded-full'
							/>
							<div className='flex-1'>
								<h3 className='font-semibold text-lg'>{product.doctor.name}</h3>
								{product.doctor?.specialty && (
									<div className='text-gray-600 text-sm'>
										{product.doctor.specialty}
									</div>
								)}
								{product.doctor?.crm && product.doctor?.crmState && (
									<div className='text-xs text-gray-500 mt-1'>
										CRM {product.doctor.crmState} {product.doctor.crm}
									</div>
								)}
							</div>
						</div>
					</div>
				)}

			{/* Detalhes da consulta - seção melhorada */}
			{product.type === 'MEDICAL_CONSULTATION' && (
				<div className='bg-blue-50 rounded-lg p-4 border border-blue-100'>
					<h4 className='text-sm font-semibold text-blue-800 mb-3 flex items-center gap-1.5'>
						<Info className='h-4 w-4' />
						{product.isOnDuty ? 'Detalhes do Plantão' : 'Detalhes da Consulta'}
					</h4>

					<div className='space-y-3'>
						{/* Se for plantão, mostrar nível de urgência */}
						{product.isOnDuty && (
							<div className='flex items-center gap-2 pb-1'>
								<AlertTriangle className='h-4 w-4 text-blue-700 flex-shrink-0' />
								<div className='flex-1'>
									<div className='text-xs font-medium text-blue-800 mb-1'>
										Nível de Urgência
									</div>
									<div
										className={`text-sm font-semibold px-2 py-1 rounded-md inline-block ${urgencyInfo.color}`}
									>
										{urgencyInfo.name}{' '}
										<span className='opacity-90 font-normal'>
											{urgencyInfo.waitTime}
										</span>
									</div>
								</div>
							</div>
						)}

						{/* Se for consulta agendada, mostrar data e hora */}
						{!product.isOnDuty && product.scheduledAt && (
							<>
								<div className='flex items-center gap-2'>
									<CalendarDays className='h-4 w-4 text-blue-700 flex-shrink-0' />
									<div>
										<div className='text-xs font-medium text-blue-800'>
											Data
										</div>
										<div className='text-sm text-gray-700'>
											{new Date(product.scheduledAt).toLocaleDateString(
												'pt-BR',
												{
													weekday: 'long',
													day: '2-digit',
													month: 'long',
													year: 'numeric',
												}
											)}
										</div>
									</div>
								</div>

								<div className='flex items-center gap-2'>
									<Clock className='h-4 w-4 text-blue-700 flex-shrink-0' />
									<div>
										<div className='text-xs font-medium text-blue-800'>
											Horário
										</div>
										<div className='text-sm text-gray-700'>
											{new Date(product.scheduledAt).toLocaleTimeString(
												'pt-BR',
												{
													hour: '2-digit',
													minute: '2-digit',
												}
											)}
										</div>
									</div>
								</div>
							</>
						)}

						{/* Exibir sempre a duração */}
						{product.duration && (
							<div className='flex items-center gap-2'>
								<Clock className='h-4 w-4 text-blue-700 flex-shrink-0' />
								<div>
									<div className='text-xs font-medium text-blue-800'>
										{product.isOnDuty ? 'Tempo Estimado de Espera' : 'Duração'}
									</div>
									<div className='text-sm text-gray-700'>
										{product.isOnDuty
											? urgencyInfo.waitTime
											: `${product.duration} minutos`}
									</div>
								</div>
							</div>
						)}

						{/* Tipo de Consulta (Online/Presencial) */}
						<div className='flex items-center gap-2'>
							<Calendar1Icon className='h-4 w-4 text-blue-700 flex-shrink-0' />
							<div>
								<div className='text-xs font-medium text-blue-800'>
									Tipo de Consulta
								</div>
								<div className='text-sm text-gray-700'>
									{product.isOnline !== false
										? 'Consulta Online'
										: 'Consulta Presencial'}
								</div>
							</div>
						</div>
					</div>
				</div>
			)}

			<div className='rounded-lg bg-gray-50 p-4 text-sm text-gray-600 border border-gray-100'>
				<h4 className='text-sm font-semibold text-gray-700 mb-2 flex items-center gap-1.5'>
					<Info className='h-4 w-4' />
					Informações Importantes
				</h4>
				{product.type === 'COURSE' && (
					<p>Acesso imediato após a confirmação do pagamento</p>
				)}
				{product.type === 'MENTORING' && (
					<p>
						Você receberá as instruções de agendamento no seu Email e WhatsApp
					</p>
				)}
				{product.type === 'EBOOK' && (
					<p>Download disponível após a confirmação do pagamento</p>
				)}
				{product.type === 'MEDICAL_CONSULTATION' && product.isOnDuty && (
					<p>
						Após o pagamento o link da consulta será enviado para o seu Whatsapp
						e você poderá acompanhar o atendimento em tempo real.
					</p>
				)}
				{product.type === 'MEDICAL_CONSULTATION' &&
					!product.isOnDuty &&
					product.isOnline !== false && (
						<p>
							O link para a consulta será enviado por email após a confirmação
							do pagamento.
						</p>
					)}
				{product.type === 'MEDICAL_CONSULTATION' &&
					!product.isOnDuty &&
					product.isOnline === false && (
						<p>
							Você receberá as instruções de acesso à consulta no seu Email e
							WhatsApp após a confirmação do pagamento.
						</p>
					)}
			</div>

			{/* Resumo do valor com detalhamento do preço */}
			<div className='space-y-3 mt-4'>
				<Separator />

				{/* Exibir preço da consulta */}
				<div className='flex justify-between items-center text-sm text-gray-700'>
					<span>{product.isOnDuty ? 'Plantão' : 'Consulta'}</span>
					<span>R$ {product.price.toFixed(2)}</span>
				</div>

				{/* Exibir taxa de serviço se existir */}
				{product.serviceFee !== undefined && (
					<div className='flex justify-between items-center text-sm text-gray-700'>
						<span>Taxa de serviço</span>
						<span>
							{product.serviceFee === 0
								? 'Grátis'
								: `R$ ${product.serviceFee.toFixed(2)}`}
						</span>
					</div>
				)}

				{/* Exibir total */}
				<div className='flex justify-between items-center pt-2 border-t border-gray-100 mt-2'>
					<span className='text-gray-700 font-medium flex items-center gap-1.5'>
						<CreditCard className='h-4 w-4 text-primary' />
						Total
					</span>
					<span className='text-lg font-bold text-primary'>
						R$ {calculateTotal().toFixed(2)}
					</span>
				</div>
			</div>
		</div>
	);

	if (isMobile) {
		return (
			<Card className='bg-white shadow-sm border-gray-100'>
				<Collapsible open={isOpen} onOpenChange={setIsOpen}>
					<CollapsibleTrigger asChild>
						<div className='w-full px-3 py-2 rounded-none cursor-pointer'>
							<MobileHeader />
						</div>
					</CollapsibleTrigger>
					<CollapsibleContent>
						<CardContent className='px-4 pb-4 pt-0'>
							<Content />
						</CardContent>
					</CollapsibleContent>
				</Collapsible>
			</Card>
		);
	}

	return (
		<Card className='border-gray-100 shadow-sm'>
			<CardHeader className='p-5 border-b border-gray-100 bg-gray-50/80'>
				<CardTitle className='text-lg flex items-center gap-1.5'>
					<Info className='h-5 w-5 text-primary' />
					{product.isOnDuty ? 'Resumo do Plantão' : 'Resumo da Consulta'}
				</CardTitle>
			</CardHeader>
			<CardContent className='p-5'>
				<Content />
			</CardContent>
		</Card>
	);
}
