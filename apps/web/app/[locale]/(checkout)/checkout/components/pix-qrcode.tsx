// components/pix-qrcode.tsx
import Image from 'next/image';
import { Card } from '@ui/components/card';
import { AlertTriangle } from 'lucide-react';

interface PixQRCodeProps {
	qrCodeBase64: string;
	size?: number;
}

export function PixQRCode({ qrCodeBase64, size = 200 }: PixQRCodeProps) {
	if (!qrCodeBase64) {
		return (
			<Card className='flex h-[200px] w-[200px] items-center justify-center bg-gray-50'>
				<div className="flex flex-col items-center gap-2 p-4 text-center">
					<AlertTriangle className="h-6 w-6 text-amber-500" />
					<span className='text-sm text-muted-foreground'>
						Erro ao carregar QR Code
					</span>
				</div>
			</Card>
		);
	}

	try {
		return (
			<div className="border-4 border-primary/10 rounded-lg p-2 bg-white shadow-sm">
				<Image
					src={`data:image/png;base64,${qrCodeBase64}`}
					alt='QR Code PIX'
					width={size}
					height={size}
					className='rounded-lg'
					priority
				/>
			</div>
		);
	} catch (error) {
		console.error('Error rendering QR code:', error);
		return (
			<Card className='flex h-[200px] w-[200px] items-center justify-center bg-gray-50'>
				<div className="flex flex-col items-center gap-2 p-4 text-center">
					<AlertTriangle className="h-6 w-6 text-amber-500" />
					<span className='text-sm text-muted-foreground'>
						Erro ao exibir QR Code
					</span>
				</div>
			</Card>
		);
	}
}
