// components/payment-form.tsx
import { ArrowR<PERSON>, CreditCard, Loader2, Lock } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';

import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { RadioGroup } from '@ui/components/radio-group';
import { PaymentFormPixInfo } from './payment-form-pix-info';
import type { CheckoutFormData } from './types';

import { PaymentOption } from './payment-option';

import { CreditCardForm } from './checkout-credit-card-form';
import { CheckoutFooter } from './checkout-footer';
import { CheckoutSteps } from './checkout-steps';

interface PaymentFormProps {
	totalAmount: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
	acceptedPayments?: string[];
	loading?: boolean;
	onPixClick?: () => void;
	confirmCta?: string;
	pixCta?: string;
}

// Definição padrão de métodos de pagamento para referência
const PAYMENT_METHODS = {
	CREDIT_CARD: {
		value: 'CREDIT_CARD',
		title: 'Cartão',
		description: '',
		icon: 'credit-card' as const,
	},
	PIX: {
		value: 'PIX',
		title: 'Pix',
		description: 'Pagamento instantâneo',
		icon: 'pix' as const,
	},
	BOLETO: {
		value: 'BOLETO',
		title: 'Boleto',
		description: 'Confirmação em até 24 horas',
		icon: 'boleto' as const,
	},
};

const brands = ['visa', 'mastercard', 'amex', 'elo'];

export function PaymentForm({
	totalAmount,
	installmentsLimit,
	enableInstallments = false,
	acceptedPayments = ['CREDIT_CARD', 'PIX', 'BOLETO'],
	loading = false,
	onPixClick,
	confirmCta,
	pixCta,
}: PaymentFormProps) {
	const methods = useFormContext<CheckoutFormData>();

	// Watch the payment method from the form
	const selectedPaymentMethod = methods.watch('paymentMethod');

	// Filtrar apenas métodos de pagamento permitidos pelo produto
	const allowedPaymentMethods = Object.values(PAYMENT_METHODS).filter(
		(method) => acceptedPayments.includes(method.value)
	);

	const renderPaymentMethod = () => {
		switch (selectedPaymentMethod) {
			case 'CREDIT_CARD':
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
			case 'PIX':
				return <PaymentFormPixInfo />;
			default:
				return (
					<CreditCardForm
						price={totalAmount}
						installmentsLimit={installmentsLimit}
						enableInstallments={enableInstallments}
					/>
				);
		}
	};

	const handleButtonClick = (e: React.MouseEvent) => {
		// If payment method is PIX and we have a direct handler, use it
		if (selectedPaymentMethod === 'PIX' && onPixClick) {
			e.preventDefault();

			onPixClick();
		}
		// Otherwise normal form submission will occur
	};

	const buttonLabel = selectedPaymentMethod === 'PIX' ? (pixCta || 'Gerar QR Code PIX') : (confirmCta || 'Confirmar Consulta');

	return (
		<Card className='bg-white shadow-sm'>
			<CardHeader className='p-6 space-y-1 '>
				<div className='flex items-center justify-between'>
					<div>
						<div className='flex items-center gap-2'>
							<CreditCard className='h-5 w-5 text-muted-foreground' />
							<CardTitle className='text-lg'>Pagamento</CardTitle>
						</div>
						<p className='text-sm text-muted-foreground hidden'>
							Escolha a forma de pagamento
						</p>
					</div>

					<div className='brand-icons  flex  items-center gap-1 md:gap-3    '>
						{brands.map((brand) => (
							<img
								key={brand}
								src={`/images/payments/card/${brand}.svg`}
								alt={brand}
								className='h-4 md:h-6 w-auto border rounded-[3px]'
							/>
						))}
					</div>
				</div>
			</CardHeader>

			<CardContent className='p-6 pt-0 space-y-6'>
				{allowedPaymentMethods.length > 0 && (
					<Controller
						name='paymentMethod'
						control={methods.control}
						render={({ field }) => (
							<RadioGroup
								value={field.value}
								onValueChange={(value) => {
									// Garantir que o tipo seja compatível
									const paymentMethod = value as 'CREDIT_CARD' | 'PIX' | 'BOLETO';
									field.onChange(paymentMethod);
									if (paymentMethod !== 'CREDIT_CARD') {
										// Clear credit card data when switching to non-card payment method
										methods.setValue('creditCard', undefined);
									} else {
										// Initialize credit card data if empty
										if (!methods.getValues('creditCard')) {
											methods.setValue('creditCard', {
												cardNumber: '',
												cardHolder: '',
												cardExpiry: '',
												cardCvv: '',
												installments: 1,
											});
										}
									}
								}}
								className='grid grid-cols-3 gap-4'
							>
								{allowedPaymentMethods.map((method) => (
									<PaymentOption
										key={method.value}
										value={method.value}
										title={method.title}
										description={method.description}
										icon={method.icon}
										selected={selectedPaymentMethod === method.value}
									/>
								))}
							</RadioGroup>
						)}
					/>
				)}

				{renderPaymentMethod()}

				<Button
					type='submit'
					variant='secondary'
					className='w-full h-12'
					disabled={loading}
					onClick={handleButtonClick}
				>
					{loading ? (
						<span className='flex items-center gap-2'>
							<Loader2 className='h-4 w-4 animate-spin' />
							Processando...
						</span>
					) : (
						<span className='flex items-center gap-2'>
							{buttonLabel}
							<ArrowRight className='h-4 w-4' />
						</span>
					)}
				</Button>

				<CheckoutFooter />
			</CardContent>
		</Card>
	);
}
