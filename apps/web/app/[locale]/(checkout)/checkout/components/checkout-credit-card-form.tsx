// apps/web/modules/saas/checkout/components/checkout-credit-card-form.tsx

import { useState, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { CreditCard } from './credit-card';
import { CreditCardInput } from './credit-card-input';

import { Input } from '@ui/components/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { CheckoutFormData } from './types';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from '@ui/components/form';

interface CreditCardFormProps {
	price: number;
	installmentsLimit: number;
	enableInstallments?: boolean;
}

export function CreditCardForm({
	price,
	installmentsLimit,
	enableInstallments = false,
}: CreditCardFormProps) {
	const { control, watch, setValue } = useFormContext<CheckoutFormData>();

	const [cardState, setCardState] = useState({
		number: '',
		holder: '',
		expiry: '',
		cvv: '',
		focused: null as 'number' | 'name' | 'expiry' | 'cvc' | null,
		cardType: '',
	});

	// Calcular opções de parcelamento
	const calculateInstallments = () => {
		// Se parcelamento não está habilitado, retorne apenas uma opção
		if (!enableInstallments) {
			return [
				{
					number: 1,
					value: Number(price),
					total: Number(price),
					text: `1x de ${new Intl.NumberFormat('pt-BR', {
						style: 'currency',
						currency: 'BRL',
					}).format(Number(price))} sem juros`,
				},
			];
		}

		// Garantir que o preço é um número válido
		const basePrice = Number(price);
		if (isNaN(basePrice) || basePrice <= 0) {
			console.error('Preço inválido:', price);
			return [];
		}

		const maxInstallments = Math.min(12, installmentsLimit);

		return Array.from({ length: maxInstallments }, (_, index) => {
			const installmentNumber = index + 1;
			const hasInterest = installmentNumber > 1;
			const interestRate = hasInterest ? 0.0199 : 0; // 1.99% ao mês

			const baseInstallmentValue = basePrice / installmentNumber;
			const installmentValue = hasInterest
				? baseInstallmentValue * (1 + interestRate)
				: baseInstallmentValue;

			const totalValue = installmentValue * installmentNumber;

			return {
				number: installmentNumber,
				value: installmentValue,
				total: totalValue,
				text: `${installmentNumber}x de ${new Intl.NumberFormat('pt-BR', {
					style: 'currency',
					currency: 'BRL',
				}).format(installmentValue)}${
					hasInterest ? ' com juros' : ' sem juros'
				}`,
			};
		});
	};

	const installmentOptions = calculateInstallments();

	// Ensure single installment is selected when installments are disabled
	useEffect(() => {
		if (!enableInstallments) {
			setValue('creditCard.installments', 1);
		}
	}, [enableInstallments, setValue]);

	return (
		<div className='grid gap-8 lg:grid-cols-2'>
			<div className='space-y-4'>
				<FormField
					control={control}
					name='creditCard.cardHolder'
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nome no cartão</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder='Nome como está no cartão'
									onChange={(e) => {
										field.onChange(e.target.value);
										setCardState((prev) => ({
											...prev,
											holder: e.target.value,
										}));
									}}
									onFocus={() =>
										setCardState((prev) => ({ ...prev, focused: 'name' }))
									}
									onBlur={() =>
										setCardState((prev) => ({ ...prev, focused: null }))
									}
								/>
							</FormControl>
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name='creditCard.cardNumber'
					render={({ field }) => (
						<div>
							<FormLabel>Número do cartão</FormLabel>

							<CreditCardInput
								value={field.value}
								onChange={(value) => {
									field.onChange(value);
									setCardState((prev) => ({ ...prev, number: value }));
								}}
								onFocus={() =>
									setCardState((prev) => ({ ...prev, focused: 'number' }))
								}
								onBlur={() =>
									setCardState((prev) => ({ ...prev, focused: null }))
								}
								onCardTypeChange={(type) =>
									setCardState((prev) => ({ ...prev, cardType: type }))
								}
							/>
						</div>
					)}
				/>

				<div className='grid grid-cols-2 gap-4'>
					<FormField
						control={control}
						name='creditCard.cardExpiry'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Validade</FormLabel>
								<FormControl>
									<Input
										{...field}
										placeholder='MM/AA'
										maxLength={5}
										onChange={(e) => {
											const value = e.target.value.replace(/\D/g, '');
											let formatted = value;
											if (value.length >= 2) {
												const month = value.substring(0, 2);
												const year = value.substring(2, 4);
												formatted = `${month}/${year}`;
											}
											field.onChange(formatted);
											setCardState((prev) => ({ ...prev, expiry: formatted }));
										}}
										onFocus={() =>
											setCardState((prev) => ({ ...prev, focused: 'expiry' }))
										}
										onBlur={() =>
											setCardState((prev) => ({ ...prev, focused: null }))
										}
									/>
								</FormControl>
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='creditCard.cardCvv'
						render={({ field }) => (
							<FormItem>
								<FormLabel>CVV</FormLabel>
								<FormControl>
									<Input
										{...field}
										type='text'
										maxLength={4}
										placeholder='123'
										onChange={(e) => {
											const value = e.target.value.replace(/\D/g, '');
											field.onChange(value);
											setCardState((prev) => ({ ...prev, cvv: value }));
										}}
										onFocus={() =>
											setCardState((prev) => ({ ...prev, focused: 'cvc' }))
										}
										onBlur={() =>
											setCardState((prev) => ({ ...prev, focused: null }))
										}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</div>

				{enableInstallments && installmentOptions.length > 1 && (
					<FormField
						control={control}
						name='creditCard.installments'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Parcelas</FormLabel>
								<Select
									value={String(field.value || 1)}
									onValueChange={(value) => field.onChange(Number(value))}
								>
									<SelectTrigger>
										<SelectValue placeholder='Selecione o número de parcelas' />
									</SelectTrigger>
									<SelectContent>
										{installmentOptions.map((option) => (
											<SelectItem
												key={option.number}
												value={String(option.number)}
											>
												{option.text}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</FormItem>
						)}
					/>
				)}
			</div>

			<div className='hidden lg:block'>
				<CreditCard
					number={cardState.number}
					name={cardState.holder}
					expiry={cardState.expiry}
					cvc={cardState.cvv}
					focused={cardState.focused}
					type={cardState.cardType}
				/>
			</div>
		</div>
	);
}
