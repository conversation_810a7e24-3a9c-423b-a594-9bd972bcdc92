// components/payment-form-pix-info.tsx
import { Info, QrCode } from 'lucide-react';

export function PaymentFormPixInfo() {
	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
				<div className='rounded-lg border bg-card p-4'>
					<div className='flex flex-col items-center space-y-2 text-center'>
						<Info className='h-8 w-8 text-muted-foreground' />
						<h3 className='font-medium'>Como funciona?</h3>
						<p className='text-sm text-muted-foreground'>
							Clique em <b>"Gerar QR Code"</b>. É simples e o processamento é
							instantâneo.
						</p>
					</div>
				</div>

				<div className='rounded-lg border bg-card p-4'>
					<div className='flex flex-col items-center space-y-2 text-center'>
						<QrCode className='h-8 w-8 text-muted-foreground' />
						<h3 className='font-medium'>Finalize sua compra com facilidade</h3>
						<p className='text-sm text-muted-foreground'>
							Abra o app do seu banco, acesse o PIX e escaneie o QR code ou
							copie o código
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
