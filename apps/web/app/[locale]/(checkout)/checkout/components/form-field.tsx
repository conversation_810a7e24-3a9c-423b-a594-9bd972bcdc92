// components/form-field.tsx
import { ReactNode } from 'react';
import { Label } from '@ui/components/label';
import { FormMessage } from '@ui/components/form';
import { Control, useController } from 'react-hook-form';

interface FormFieldProps {
	label: string;
	name: string;
	control?: Control<any>;
	error?: string;
	children?: ReactNode;
	render?: (props: any) => ReactNode;
	required?: boolean;
	helpText?: string;
}

export function FormField({
	label,
	name,
	control,
	error,
	children,
	render,
	required,
	helpText,
}: FormFieldProps) {
	// Se tiver control e render, usa o controller
	if (control && render) {
		const { field, fieldState } = useController({
			name,
			control,
		});

		return (
			<div className='space-y-2'>
				<Label htmlFor={name} className='text-sm font-medium'>
					{label}
					{required && <span className='text-destructive ml-1'>*</span>}
				</Label>

				{render({ ...field, error: fieldState.error?.message })}

				{helpText && (
					<p className='text-xs text-muted-foreground'>{helpText}</p>
				)}

				{fieldState.error?.message && (
					<FormMessage>{fieldState.error.message}</FormMessage>
				)}
			</div>
		);
	}

	// Caso contrário, renderiza children direto
	return (
		<div className='space-y-2'>
			<Label htmlFor={name} className='text-sm font-medium'>
				{label}
				{required && <span className='text-destructive ml-1'>*</span>}
			</Label>

			{children}

			{helpText && <p className='text-xs text-muted-foreground'>{helpText}</p>}

			{error && <FormMessage>{error}</FormMessage>}
		</div>
	);
}
