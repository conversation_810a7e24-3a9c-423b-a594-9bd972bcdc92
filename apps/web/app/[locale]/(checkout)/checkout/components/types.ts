// apps/web/(checkout)/checkout/types.ts
import { z } from "zod";

// Schemas de Validação
export const customerDataSchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido"),
  cpf: z.string().min(11, "CPF inválido"),
});

export const creditCardSchema = z.object({
  cardNumber: z.string().min(13, "Número do cartão inválido"),
  cardHolder: z.string().min(2, "Nome do portador é obrigatório"),
  cardExpiry: z.string().min(5, "Data de validade inválida"),
  cardCvv: z.string().min(3, "CVV inválido"),
  installments: z.number().min(1).max(12),
});

export const checkoutFormSchema = z.object({
  customerData: z.object({
    name: z.string().min(1, 'Nome é obrigatório'),
    email: z.string().email('Email inválido'),
    phone: z.string().min(10, 'Telefone é obrigatório'), // Corrigido: telefone obrigatório
    cpf: z.string().min(11, 'CPF inválido'), // Corrigido: CPF obrigatório
  }),
  doctorId: z.string().optional(), // Opcional para plantão
  scheduledAt: z.any().optional(), // Data da consulta (opcional)
  duration: z.number().optional(), // Duração da consulta em minutos
  paymentMethod: z.enum(['CREDIT_CARD', 'PIX', 'BOLETO']),
  creditCard: z.object({
    cardNumber: z.string().min(1, 'Número do cartão é obrigatório').optional(),
    cardHolder: z.string().min(1, 'Nome do portador é obrigatório').optional(),
    cardExpiry: z.string().min(1, 'Data de validade é obrigatória').optional(),
    cardCvv: z.string().min(1, 'CVV é obrigatório').optional(),
    installments: z.number().min(1).default(1),
  }).optional(),
  // Campos para plantão
  isOnDuty: z.boolean().optional(),
  urgencyLevel: z.enum(['high', 'medium', 'low']).optional(),
  // Campo para parceiros
  partner: z.string().optional(),
});

// Types inferidos dos schemas
export type CustomerData = z.infer<typeof customerDataSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

// Types específicos para os componentes
export interface CheckoutProduct {
  id: string;
  title: string;
  description?: string | null;
  type: 'COURSE' | 'MENTORING' | 'EBOOK' | 'MEDICAL_CONSULTATION' | 'SUBSCRIPTION';
  price: number;
  installmentsLimit: number;
  thumbnail?: string | null;
  offers?: {
    id: string;
    title: string;
    description?: string | null;
    price: number;
    type: string;
  }[];
  creator?: {
    id: string;
    name: string;
  };
  // Campos específicos para consultas médicas
  scheduledAt?: Date;
  duration?: number;
  doctor?: {
    id: string;
    name: string;
    profileImage?: string;
    specialty?: string;
    crm?: string;
    crmState?: string;
  };
}

export interface Offer {
  id: string;
  title: string;
  description: string | null;
  price: number;
  type: 'ORDER_BUMP' | 'UPSELL' | 'DOWNSELL';
  thumbnail?: string | null;
}

export interface ProductOffer {
  id: string;
  title: string;
  description?: string;
  price: number;
  type: "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  thumbnail?: string;
  originalPrice?: number; // Para mostrar o desconto
}

export interface PaymentOption {
  method: "CREDIT_CARD" | "PIX";
  title: string;
  description: string;
  icon: React.ReactNode;
}

// Types para os estados do pedido
export type OrderStatus =
  | "PENDING"
  | "PROCESSING"
  | "PAID"
  | "FAILED"
  | "REFUNDED"
  | "CANCELLED";

export interface Order {
  id: string;
  status: OrderStatus;
  amount: number;
  productId: string;
  customerData: CustomerData;
  paymentMethod: "CREDIT_CARD" | "PIX";
  paymentId?: string;
  orderBumps?: ProductOffer[];
  createdAt: Date;
  updatedAt: Date;
}

// Types para integrações
export interface AsaasPaymentResponse {
  id: string;
  status: string;
  paymentLink?: string;
  pixQrCode?: {
    encodedImage: string;
    payload: string;
  };
}

export interface PaymentProcessingResult {
  orderId: string;
  status: OrderStatus;
  paymentMethod: "CREDIT_CARD" | "PIX";
  pixCode?: {
    qrCode: string;
    payload: string;
  };
  successUrl?: string;
  errorUrl?: string;
}

// Props interfaces para os componentes
export interface CreditCardDisplayProps {
  number?: string;
  name?: string;
  expiry?: string;
  cvc?: string;
  focused?: "number" | "name" | "expiry" | "cvc" | null;
  className?: string;
}

export type CardType = "visa" | "mastercard" | "amex" | "elo" | "";

// Renomear o parâmetro onChange para onValueChange para evitar conflito com o tipo React.InputHTMLAttributes
export interface CreditCardInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  error?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  onCardTypeChange?: (type: CardType) => void;
}

export interface FormFieldProps {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  helpText?: string;
}

export interface PaymentFormProps {
  totalAmount: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
  acceptedPayments?: string[];
  loading?: boolean;
}
