// src/features/checkout/components/cpf-input.tsx
import { Input, InputProps } from '@ui/components/input';
import { cn } from '@ui/lib';
import { forwardRef } from 'react';

interface CPFInputProps extends Omit<InputProps, 'value' | 'onChange'> {
	error?: string;
	value?: string;
	onChange?: (value: string) => void;
}

export const CPFInput = forwardRef<HTMLInputElement, CPFInputProps>(
	({ className, error, value = '', onChange, ...props }, ref) => {
		// Função para formatar CPF
		const formatCPF = (value: string) => {
			const numbers = value.replace(/\D/g, '');
			if (numbers.length <= 3) return numbers;
			if (numbers.length <= 6)
				return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
			if (numbers.length <= 9)
				return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(
					6
				)}`;
			return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(
				6,
				9
			)}-${numbers.slice(9, 11)}`;
		};

		// Handler para mudanças no input
		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const newValue = formatCPF(e.target.value);
			onChange?.(newValue);
		};

		return (
			<Input
				ref={ref}
				value={formatCPF(value)}
				onChange={handleChange}
				maxLength={14}
				className={cn(error && 'border-destructive', className)}
				{...props}
			/>
		);
	}
);

CPFInput.displayName = 'CPFInput';
