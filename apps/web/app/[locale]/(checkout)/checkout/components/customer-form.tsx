// components/customer-form.tsx
import { User } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';

import { CPFInput } from './cpf-input';
import { FormField } from './form-field';
import { BRPhoneInput } from './phone-input';
import { CheckoutFormData } from './types';

export function CustomerForm() {
	const {
		control,
		formState: { errors },
	} = useFormContext<CheckoutFormData>();

	return (
		<Card className='bg-white shadow-sm  '>
			<CardHeader className='p-6 space-y-1'>
				<div className='flex items-center gap-2'>
					<User className='h-5 w-5 text-muted-foreground' />
					<CardTitle className='text-lg'>Informações do Paciente</CardTitle>
				</div>
				<p className='text-sm text-muted-foreground'>
					Preencha seus dados para continuar
				</p>
			</CardHeader>

			<CardContent className='p-6 pt-0 space-y-4'>
				<div className='grid gap-4 md:grid-cols-2'>
					<FormField
						name='customerData.name'
						label='Nome completo'
						error={errors.customerData?.name?.message}
					>
						<Controller
							name='customerData.name'
							control={control}
							render={({ field }) => (
								<Input {...field} placeholder='Digite seu nome completo' />
							)}
						/>
					</FormField>

					<FormField
						name='customerData.email'
						label='E-mail'
						error={errors.customerData?.email?.message}
					>
						<Controller
							name='customerData.email'
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									type='email'
									placeholder='<EMAIL>'
									onChange={(e) => field.onChange(e.target.value)}
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.cpf'
						label='CPF'
						error={errors.customerData?.cpf?.message}
					>
						<Controller
							name='customerData.cpf'
							control={control}
							render={({ field }) => (
								<CPFInput
									value={field.value}
									onChange={field.onChange}
									onBlur={field.onBlur}
									placeholder='000.000.000-00'
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.phone'
						label='Celular'
						error={errors.customerData?.phone?.message}
					>
						<Controller
							name='customerData.phone'
							control={control}
							render={({ field }) => (
								<BRPhoneInput
									value={field.value}
									onChange={field.onChange}
									placeholder='(00) 00000-0000'
									type='tel'
									className='input-tel-no-zoom'
								/>
							)}
						/>
					</FormField>
				</div>
			</CardContent>
		</Card>
	);
}
