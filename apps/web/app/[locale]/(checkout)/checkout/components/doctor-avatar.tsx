"use client";

import { UserAvatar } from "../../../../../modules/shared/components/UserAvatar";

interface DoctorAvatarProps {
  name: string;
  avatarUrl?: string | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg'; // Tamanhos padronizados
}

export function DoctorAvatar({ name, avatarUrl, className, size = 'md' }: DoctorAvatarProps) {
  // Mapear tamanhos para classes específicas
  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-20 w-20'
  };

  const sizeClass = sizeClasses[size];

  return (
    <UserAvatar
      name={name}
      avatarUrl={avatarUrl}
      className={`${sizeClass} ${className || ''}`}
    />
  );
}
