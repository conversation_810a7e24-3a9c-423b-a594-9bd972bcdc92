Cobranças via boleto
Comece a aceitar pagamentos de boletos online com o Asaas.

Suggest Edits
As cobranças são a principal forma de receber dinheiro em sua conta no Asaas. Com elas você pode receber pagamentos por Boleto, Cartão de crédito, Cartão de débito e Pix. Este primeiro guia irá te mostrar como criar um fluxo para boletos. Conheça mais.


Criando uma cobrança por boleto
Ao criar um cobrança, automaticamente um boleto será criado. Lembrando que a taxa referente ao pagamento de um boleto só é descontada da sua conta em caso de pagamento do mesmo.

POST /v3/payments
Confira a referência completa deste endpoint

JSON

{
      "customer": "cus_000005219613",
      "billingType": "BOLETO",
      "value": 100.00,
      "dueDate": "2023-07-21"
}
Olhando para o objeto retornado, temos acesso a propriedade bankSlipUrl que é o arquivo PDF do boleto que acabou de ser gerado.

Cobrança parcelada
Você também pode facilmente criar uma cobrança parcelada e recuperar o carnê desta cobrança com todos os boletos do parcelamento.

Primeiro, vamos criar nossa cobrança parcelada em 10 vezes.

POST /v3/payments
Confira a referência completa deste endpoint

JSON

{
  "customer": "cus_000005219613",
  "billingType": "BOLETO",
  "value": 2000.00,
  "dueDate": "2023-07-21",
  "installmentCount": 10,
  "installmentValue": 200.00
}
No retorno feito pela API já podemos ver que o campo installment veio preenchido com o ID do parcelamento: 24ef7e81-7961-41b7-bd28-90e25ad2c3d7.

Carnê de pagamentos
Para gerar o carnê você só precisa fazer uma chamada GET para o seguinte endpoint:

GET /v3/installments/24ef7e81-7961-41b7-bd28-90e25ad2c3d7/paymentBook

Confira a referência completa deste endpoint

Note que foi usado o ID do parcelamento que acabamos de receber ao criar o mesmo, este endpoint retorna um arquivo em PDF com todos os boletos gerados.

Boleto com descontos para pagamento antecipado
Para que o Asaas cobre juros e multa na hora que um boleto for pago em atraso, você deve informar isso na criação da cobrança. Por exemplo, se você desejar dar um desconto de 10% para quem pagar 5 dias antes do vencimento, basta enviar a criação da cobrança dessa forma:

POST /v3/payments
Confira a referência completa deste endpoint

JSON

{
  "customer": "cus_000005219613",
  "billingType": "BOLETO",
  "value": 2000.00,
  "dueDate": "2023-07-21",
  "discount": {
     "value": 10,
     "dueDateLimitDays": 5,
     "type": "PERCENTAGE"
}
Após a cobrança ser paga, se você fizer uma busca pela mesma, poderá ver que existirá um campo originalValue, indicando que o campo value está diferente do valor definido originalmente. Essa informação também estará presente no retorno do Webhook.

Boleto com juros e multas
Da mesma forma que você pode adicionar descontos para pagamentos antecipados, você pode definir juros e multas para pagamentos em atraso.

POST /v3/payments
Confira a referência completa deste endpoint

JSON

{
  "customer": "cus_000005219613",
  "billingType": "BOLETO",
  "value": 2000.00,
  "dueDate": "2023-07-21",
  "interest": {
     "value": 1,
  },
  "fine": {
     "value": 2,
  },
}
Isso irá adicionar 1% de juros ao mês e 2% de multa em caso de atraso. A mesma informação sobre o originalValue se encaixa nesse formato também.

📘
Após o boleto ser pago, no retorno do Webhook você terá acesso ao campo interestValue, que mostra a soma dos juros e multa que foram aplicadas na cobrança.

Obter linha digitável do boleto
Se você precisar da linha digitável para exibir na tela ao seu cliente, é necessário fazer uma nova chamada na API.

GET /v3/payments/{id}/identificationField
Confira a referência completa deste endpoint

JSON

{
  "identificationField": "00190000090275928800021932978170187890000005000",
  "nossoNumero": "6543",
  "barCode": "00191878900000050000000002759288002193297817"
}



------------------------------

Criar nova cobrança
post
https://api-sandbox.asaas.com/v3/payments
É possível escolher entre as formas de pagamento com boleto, cartão de crédito, Pix ou permitir que o cliente escolha a forma que desejar.

Guia de cobranças
Confira o guia de cobranças para mais informações.

Criar cobrança parcelada
Confira o guia para criar cobranças parceladas.

📘
Não é possível gerar uma cobrança com dois billingTypes diferentes (PIX e CREDIT_CARD, por exemplo).

Caso não queira receber pagamento em Pix ou em Cartão de débito, é possível desabilitar dentro de sua interface em Minha Conta > Configuração > Configurações do Sistema.

Caso queira desabilitar em subcontas white label, entre em contato com o nosso time de integração.

🚧
Status CONFIRMED em cobranças Pix para pessoas físicas

O status CONFIRMED pode ficar disponível em cobranças Pix de contas de pessoas físicas em caso de cobranças que sofram bloqueio cautelar e que precisam de análise da área de prevenção. O prazo máximo de bloqueio é de 72h e a cobrança mudará para o status RECEIVED se recebida ou REFUNDED caso negada.

❗️
Importante

Para cobranças avulsas (1x) não deve-se usar os atributos do parcelamento: installmentCount, installmentValue e totalValue. Se for uma cobrança em 1x, usa-se apenas o value.

Somente cobranças com 2 ou mais parcelas usa-se os atributos do parcelamento.

Body Params
customer
string
required
Identificador único do cliente no Asaas

billingType
string
required
Forma de pagamento


BOLETO
value
number
required
Valor da cobrança

dueDate
date
required
Data de vencimento da cobrança

description
string
Descrição da cobrança (máx. 500 caracteres)

daysAfterDueDateToRegistrationCancellation
int32
Dias após o vencimento para cancelamento do registro (somente para boleto bancário)

externalReference
string
Campo livre para busca

installmentCount
int32
Número de parcelas (somente no caso de cobrança parcelada)

totalValue
number
Informe o valor total de uma cobrança que será parcelada (somente no caso de cobrança parcelada). Caso enviado este campo o installmentValue não é necessário, o cálculo por parcela será automático.

installmentValue
number
Valor de cada parcela (somente no caso de cobrança parcelada). Envie este campo em caso de querer definir o valor de cada parcela.

discount
object
Informações de desconto


discount object
interest
object
Informações de juros para pagamento após o vencimento


interest object
fine
object
Informações de multa para pagamento após o vencimento


fine object
postalService
boolean
Define se a cobrança será enviada via Correios


split
array of objects
Configurações do split


ADD object
callback
object
Informações de redirecionamento automático após pagamento do link de pagamento


callback object
Responses

200
OK


400
Bad Request

401
Unauthorized


