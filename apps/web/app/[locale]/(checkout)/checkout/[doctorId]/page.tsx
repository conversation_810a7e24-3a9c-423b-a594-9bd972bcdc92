// app/[locale]/checkout/[doctorId]/page.tsx

import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { AlertTriangle, Clock, ArrowRight, CheckCircle2 } from 'lucide-react';
import { Suspense } from 'react';

import { getCheckoutInitialData } from '../../../../../actions/checkout/get-initial-data';
import { CheckoutSteps } from '../components/checkout-steps';
import { ON_DUTY_CONFIG } from 'api/constants/on-duty';

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
	return {
		title: 'Checkout',
		description: 'Página de pagamento',
	};
}

interface CheckoutPageProps {
	params: {
		locale: string;
		doctorId?: string;
	};
	searchParams: {
		date?: string;
		time?: string;
		isOnDuty?: string; // "true" para plantão
		urgencyLevel?: 'high' | 'medium' | 'low'; // para plantão
		partner?: string; // Identificador do parceiro
	};
}

export default async function CheckoutPage({
	params,
	searchParams,
}: CheckoutPageProps) {
	// Aguardar params conforme Next.js 15
	const { doctorId } = await params;

	// Verificar se o ID do médico foi fornecido
	if (!doctorId) {
		return redirect('/');
	}

	// Aguardar searchParams conforme Next.js 15
	const {
		date,
		time,
		isOnDuty: isOnDutyParam,
		urgencyLevel,
		partner,
	} = await searchParams;

	// Filtrar parâmetros undefined ou vazios
	const safeSearchParams = Object.fromEntries(
		Object.entries({
			date,
			time,
			isOnDuty: isOnDutyParam,
			urgencyLevel,
			partner,
		}).filter(
			([_, value]) =>
				value !== undefined && value !== 'undefined' && value !== ''
		)
	);

	// Parse isOnDuty flag
	const isOnDuty = safeSearchParams.isOnDuty === 'true';

	// Para parceiros, definimos um nível de urgência padrão (baixo) se não estiver definido
	// Isso garante que o fluxo funcione mesmo sem um nível de urgência explícito
	let parsedUrgencyLevel = isOnDuty
		? (safeSearchParams.urgencyLevel as 'high' | 'medium' | 'low' | undefined)
		: undefined;

	// Se for parceiro e não tiver urgência definida, use 'low' como padrão
	if (isOnDuty && safeSearchParams.partner && !parsedUrgencyLevel) {
		parsedUrgencyLevel = 'low';
	}

	const initialData = await getCheckoutInitialData({
		doctorId,
		...(safeSearchParams.date && { date: safeSearchParams.date }),
		...(safeSearchParams.time && { time: safeSearchParams.time }),
		isOnDuty,
		...(parsedUrgencyLevel && { urgencyLevel: parsedUrgencyLevel }),
		...(safeSearchParams.partner && { partner: safeSearchParams.partner }),
	});

	// Se for plantão e não tiver urgência selecionada E não vier de um parceiro, renderizar a interface de seleção
	// Para parceiros, não precisamos mostrar a seleção de urgência pois o preço é fixo
	const renderUrgencySelection =
		isOnDuty && !urgencyLevel && !safeSearchParams.partner;

	// Informações dos níveis de urgência
	const urgencyLevels = [
		{
			id: 'low',
			name: ON_DUTY_CONFIG.LOW.label,
			color: 'bg-green-500',
			price: ON_DUTY_CONFIG.LOW.price,
			waitTime: `Aproximadamente ${ON_DUTY_CONFIG.LOW.waitTime} minutos`,
			description:
				'Para consultas de rotina, acompanhamento médico ou sintomas leves que não apresentam risco imediato.',
		},
		{
			id: 'medium',
			name: ON_DUTY_CONFIG.MEDIUM.label,
			color: 'bg-amber-500',
			price: ON_DUTY_CONFIG.MEDIUM.price,
			waitTime: `Aproximadamente ${ON_DUTY_CONFIG.MEDIUM.waitTime} minutos`,
			description:
				'Para condições que necessitam de atenção, mas não são emergenciais. Sintomas moderados ou problemas de saúde que precisam ser avaliados em breve.',
		},
		{
			id: 'high',
			name: ON_DUTY_CONFIG.HIGH.label,
			color: 'bg-red-500',
			price: ON_DUTY_CONFIG.HIGH.price,
			waitTime: `Aproximadamente ${ON_DUTY_CONFIG.HIGH.waitTime} minutos`,
			description:
				'Para situações que requerem atenção médica imediata, como sintomas intensos ou preocupações graves.',
		},
	];

	if (renderUrgencySelection) {
		return (
			<div className='container max-w-xl my-8 px-4'>
				{/* Steps Indicator for urgency selection */}
				<CheckoutSteps currentStep='urgency' />

				<h1 className='text-2xl font-bold mb-6 text-center'>
					Selecione o nível de urgência para seu atendimento
				</h1>

				<p className='text-gray-600 mb-8 text-center'>
					O tempo de espera estimado e o valor variam de acordo com a urgência
					selecionada.
				</p>
				{/* <p className='text-gray-600 mb-8 text-center'>
					Escolha o nível de urgência adequado para sua consulta. O tempo de
					espera estimado e o valor variam de acordo com a urgência selecionada.
				</p> */}

				<div className='space-y-4'>
					{urgencyLevels.map((level) => (
						<a
							key={level.id}
							href={`/${
								params.locale
							}/checkout/${doctorId}?${new URLSearchParams(
								Object.fromEntries(
									Object.entries({
										isOnDuty: 'true',
										urgencyLevel: level.id,
									}).filter(([_, value]) => value !== undefined && value !== '')
								)
							)}`}
							className='block'
						>
							<div
								className={`border rounded-lg p-4 hover:border-primary hover:shadow-md transition-all cursor-pointer flex flex-col relative overflow-hidden group`}
							>
								{/* Overlay indicator on hover */}
								<div className='absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity'></div>

								<div className='flex items-center gap-3 mb-2'>
									<div
										className={`${level.color} text-white text-sm font-semibold py-1 px-3 rounded-full flex gap-1 items-center`}
									>
										{level.id === 'high' && (
											<AlertTriangle className='h-3.5 w-3.5' />
										)}
										{level.name}
									</div>
									<div className='text-lg font-bold ml-auto'>
										R$ {level.price.toFixed(2)}
									</div>
								</div>

								{/* <p className='text-gray-600 mb-3'>{level.description}</p> */}

								<div className='flex items-center justify-between'>
									<div className='flex items-center gap-2 text-gray-500 text-sm'>
										<Clock className='h-5 w-5' />
										<span className=''>
											Tempo de espera:
											<br />
											{level.waitTime}
										</span>
									</div>

									{/* Select button/icon */}
									<div className='text-primary flex items-center gap-1 font-medium text-sm group-hover:translate-x-1 transition-transform'>
										Selecionar
										<ArrowRight className='h-4 w-4' />
									</div>
								</div>
							</div>
						</a>
					))}
				</div>

				<div className='mt-8 text-sm text-gray-500 p-4 bg-gray-50 rounded-lg'>
					<p className='mb-2 font-semibold'>Observações importantes:</p>
					<ul className='list-disc pl-5 space-y-1'>
						<li>
							O tempo de espera é estimado e pode variar dependendo da demanda.
						</li>
						<li>
							O pagamento só será processado após a seleção do nível de
							urgência.
						</li>
					</ul>
				</div>
			</div>
		);
	}

	// Preparar produto para o checkout
	const product = {
		id: initialData.doctor.id,
		title: `Consulta com Dr. ${initialData.doctor.name || 'Médico'}`,
		description: initialData.doctor.specialty || '',
		type: 'MEDICAL_CONSULTATION' as const,
		price: initialData.price,
		installmentsLimit: 1,
		enableInstallments: false,
		thumbnail: initialData.doctor.profileImage,
		checkoutType: 'DEFAULT' as const,
		acceptedPayments: ['CREDIT_CARD', 'PIX'],
		successUrl: '/checkout/success',
		cancelUrl: '/',
		// Campos específicos para consultas médicas
		scheduledAt: initialData.scheduledAt || undefined,
		duration: initialData.duration,
		doctor: {
			...initialData.doctor,
			// Garantir que name não seja null
			name: initialData.doctor.name || '',
			// Converter null para undefined
			profileImage: initialData.doctor.profileImage || undefined,
		},
		isOnline: initialData.isOnline,
		serviceFee: initialData.serviceFee,
		isOnDuty: initialData.isOnDuty,
		urgencyLevel: initialData.urgencyLevel,
		partner: initialData.partner,
	};



	return (
		<main className='flex flex-col min-h-screen'>
			<div className='container px-4 max-w-7xl mx-auto grow'>
				{/* Steps Indicator for checkout */}
				<div className='max-w-xl mx-auto mb-8 mt-8'>
					<CheckoutSteps currentStep='data' />
				</div>

				<div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
					<div className='order-2 md:order-1'>
						<CheckoutForm product={product} />
					</div>

					<div className='order-1 md:order-2 min-w-0'>
						{/* Mobile Summary - Visible only on mobile devices */}
						<div className='md:hidden mb-6'>
							<Suspense
								fallback={
									<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
								}
							>
								<CheckoutSummarySuspense product={product} isMobile={true} />
							</Suspense>
						</div>

						{/* Desktop Summary - Hidden on mobile, visible on desktop */}
						<div className='hidden md:block'>
							<Suspense
								fallback={
									<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
								}
							>
								<CheckoutSummarySuspense product={product} isMobile={false} />
							</Suspense>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}

async function CheckoutSummarySuspense({
	product,
	isMobile,
}: {
	product: any;
	isMobile: boolean;
}) {
	const ProductSummaryCard = (
		await import('../components/checkout-summary-card')
	).ProductSummaryCard;

	return <ProductSummaryCard product={product} isMobile={isMobile} />;
}
