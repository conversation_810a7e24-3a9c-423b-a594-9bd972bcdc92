import { <PERSON>, CardContent, CardHeader } from '@ui/components/card';
import { CheckCircle2, Stethoscope, ArrowLeft, User, Clock } from 'lucide-react';
import { Button } from '@ui/components/button';
import Link from 'next/link';
import { CheckoutSteps } from '../../components/checkout-steps';

interface PlantaoSuccessPageProps {
  searchParams: Promise<{
    transactionId?: string;
  }>;
}

async function getMagicLink(transactionId: string) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zapvida.com';
    const response = await fetch(`${baseUrl}/api/auth/magic-link`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionId, type: 'PLANTAO' })
    });

    if (!response.ok) {
      console.error('Erro ao gerar magic link:', response.status);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Erro ao buscar magic link:', error);
    return null;
  }
}

export default async function PlantaoSuccessPage({ searchParams }: PlantaoSuccessPageProps) {
  const resolvedSearchParams = await searchParams;
  const transactionId = resolvedSearchParams.transactionId || '';

  // Buscar magic link se temos transactionId
  let magicLinkData = null;
  if (transactionId) {
    magicLinkData = await getMagicLink(transactionId);
  }

  return (
    <div className='min-h-screen bg-gradient-to-b from-white to-gray-50/50'>
      <div className='container max-w-2xl py-16'>
        <div className='max-w-xl mx-auto mb-8'>
          <CheckoutSteps currentStep='payment' />
        </div>

        <Card className='mb-8'>
          <CardHeader className='text-center pb-6'>
            <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
              <CheckCircle2 className='h-6 w-6 text-primary' />
            </div>
            <h1 className='text-2xl font-semibold'>Pagamento Recebido!</h1>
            <p className='text-muted-foreground'>Seu atendimento de plantão será iniciado em instantes</p>
          </CardHeader>

          <CardContent className='border-t pt-6'>
            <div className='space-y-4'>
              {transactionId && (
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>ID da Transação</span>
                  <span className='font-medium'>{transactionId}</span>
                </div>
              )}

              {magicLinkData?.appointment && (
                <div className='space-y-3'>
                  <div className='flex justify-between items-center text-sm'>
                    <span className='text-muted-foreground'>Status do Plantão</span>
                    <span className='font-medium text-green-600'>✓ Pagamento Confirmado</span>
                  </div>
                  <div className='flex justify-between items-center text-sm'>
                    <span className='text-muted-foreground'>Nível de Urgência</span>
                    <span className={`font-medium ${
                      magicLinkData.appointment.urgencyLevel === 'HIGH' ? 'text-red-600' :
                      magicLinkData.appointment.urgencyLevel === 'MEDIUM' ? 'text-orange-600' :
                      'text-blue-600'
                    }`}>
                      {magicLinkData.appointment.urgencyLevel === 'HIGH' ? '🔴 Alta' :
                       magicLinkData.appointment.urgencyLevel === 'MEDIUM' ? '🟡 Média' :
                       '🟢 Baixa'}
                    </span>
                  </div>
                </div>
              )}

              <div className='rounded-lg bg-muted p-4 text-sm text-center text-muted-foreground'>
                <Clock className='h-4 w-4 inline mr-1' />
                Você receberá instruções por WhatsApp e email para iniciar o atendimento agora mesmo
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className='mb-6'>
          <CardHeader className='pb-3'>
            <div className='flex items-center gap-2'>
              <Stethoscope className='h-5 w-5' />
              <h2 className='text-xl font-semibold'>Próximos passos</h2>
            </div>
          </CardHeader>

          <CardContent>
            <div className='space-y-4'>
              <p className='text-muted-foreground'>
                Nossa equipe de médicos online já foi notificada. Você será conectado ao médico disponível em poucos minutos.
              </p>

              <div className='bg-blue-50 border border-blue-100 p-4 rounded-lg text-blue-700 text-sm'>
                <p className='font-medium mb-1'>Dicas:</p>
                <ol className='list-decimal pl-5 space-y-1'>
                  <li>Mantenha o celular com internet estável</li>
                  <li>Deixe o WhatsApp aberto para receber o link rapidamente</li>
                  <li>Fique atento às notificações</li>
                </ol>
              </div>

              {magicLinkData?.success && magicLinkData.magicUrl ? (
                <Button asChild className='w-full'>
                  <Link href={magicLinkData.magicUrl}>
                    <User className='h-4 w-4 mr-2' />
                    Acompanhar meu atendimento
                  </Link>
                </Button>
              ) : (
                <Button asChild className='w-full' variant="outline">
                  <Link href='/auth/login'>
                    Fazer login para acompanhar
                  </Link>
                </Button>
              )}

              {magicLinkData?.user && (
                <div className='bg-green-50 border border-green-100 p-4 rounded-lg text-green-700 text-sm'>
                  <p className='font-medium mb-1'>✓ Tudo pronto, {magicLinkData.user.name}!</p>
                  <p>Clique no botão acima para acessar seu painel sem precisar fazer login.</p>
                </div>
              )}

              <div className='bg-muted p-4 rounded-lg'>
                <p className='text-sm text-muted-foreground'>
                  Se precisar de ajuda, entre em contato com nosso suporte.
                </p>
              </div>

              <hr />

              <div className='flex justify-center text-sm'>
                <Link href='/' className='flex items-center gap-2'>
                  <ArrowLeft className='h-4 w-4' />
                  Voltar para a página inicial
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}


