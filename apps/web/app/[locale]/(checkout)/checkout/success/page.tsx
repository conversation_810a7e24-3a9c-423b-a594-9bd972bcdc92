import { <PERSON>, CardContent, CardHeader } from '@ui/components/card';
import { CheckCircle2, Calendar, ArrowLeft, User } from 'lucide-react';
import { But<PERSON> } from '@ui/components/button';
import Link from 'next/link';
import { CheckoutSteps } from '../components/checkout-steps';
import { PurchaseTracker80 } from '../../../../../modules/analytics/provider/google/purchase-tracker80';
import { PurchaseTracker100 } from '../../../../../modules/analytics/provider/google/purchase-tracker100';
import { PurchaseTracker120 } from '../../../../../modules/analytics/provider/google/purchase-tracker120';
import { getBaseUrl } from 'utils';

interface CheckoutSuccessPageProps {
	searchParams: Promise<{
		transactionId?: string;
		appointmentId?: string;
		amount?: string;
	}>;
}

interface MagicLinkResponse {
	success: boolean;
	magicUrl?: string;
	user?: {
		name: string;
		id: string;
	};
}

async function getMagicLink(transactionId: string): Promise<MagicLinkResponse | null> {
	try {
		const baseUrl = getBaseUrl();
		const response = await fetch(`${baseUrl}/api/auth/magic-link`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ transactionId, type: 'DASHBOARD' })
		});

		if (!response.ok) {
			console.error('Erro ao gerar magic link:', response.status);
			return null;
		}

		return await response.json();
	} catch (error) {
		console.error('Erro ao buscar magic link:', error);
		return null;
	}
}

export default async function CheckoutSuccessPage({ searchParams }: CheckoutSuccessPageProps) {
	const params = await searchParams;
	const transactionId = params.transactionId || params.appointmentId || '';
	const amount = parseFloat(params.amount || '0') || 80.0;

	let magicLinkData: MagicLinkResponse | null = null;
	if (transactionId) {
		magicLinkData = await getMagicLink(transactionId);
	}

	return (
		<div className='min-h-screen bg-gradient-to-b from-white to-gray-50/50'>
			<div className='container max-w-2xl py-16'>
				{/* Etapas do Checkout */}
				<div className='max-w-xl mx-auto mb-8'>
					<CheckoutSteps currentStep='payment' />
				</div>

				{/* Cabeçalho de Sucesso */}
				<Card className='mb-8'>
					<CardHeader className='text-center pb-6'>
						<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
							<CheckCircle2 className='h-6 w-6 text-primary' />
						</div>
						<h1 className='text-2xl font-semibold'>Agendamento Confirmado!</h1>
						<p className='text-muted-foreground'>Seu pagamento foi processado com sucesso</p>
					</CardHeader>

					<CardContent className='border-t pt-6'>
						<div className='space-y-4'>
							{transactionId && (
								<div className='flex justify-between text-sm'>
									<span className='text-muted-foreground'>ID da Transação</span>
									<span className='font-medium'>{transactionId}</span>
								</div>
							)}

							<div className='rounded-lg bg-muted p-4 text-sm text-center text-muted-foreground'>
								Enviamos um email e uma mensagem no WhatsApp para você com todos os detalhes da sua consulta
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Instruções de acesso */}
				<Card className='mb-6'>
					<CardHeader className='pb-3'>
						<div className='flex items-center gap-2'>
							<Calendar className='h-5 w-5' />
							<h2 className='text-xl font-semibold'>Acesse sua consulta</h2>
						</div>
					</CardHeader>

					<CardContent>
						<div className='space-y-4'>
							<p className='text-muted-foreground'>
								Sua consulta foi agendada com sucesso! Acesse seu novo portal do paciente para acompanhar todos os detalhes.
							</p>

							<div className='bg-green-50 border border-green-100 p-4 rounded-lg text-green-700 text-sm'>
								<p className='font-medium mb-1'>✨ Novidades no seu portal:</p>
								<ul className='list-disc pl-5 space-y-1'>
									<li>Dashboard personalizado com suas informações de saúde</li>
									<li>Acompanhamento de consultas em tempo real</li>
									<li>Acesso direto ao chat médico 24h</li>
									<li>Histórico médico completo e organizado</li>
								</ul>
							</div>

							{magicLinkData?.success && magicLinkData.magicUrl ? (
								<Button asChild className='w-full'>
									<Link href={`${magicLinkData.magicUrl}?redirect=/patient/dashboard`}>
										<User className='h-4 w-4 mr-2' />
										Acessar meu portal do paciente
									</Link>
								</Button>
							) : (
								<Button asChild className='w-full' variant="outline">
									<Link href='/auth/login?redirect=/patient/dashboard'>
										Fazer login para acessar
									</Link>
								</Button>
							)}

							{magicLinkData?.user && (
								<div className='bg-green-50 border border-green-100 p-4 rounded-lg text-green-700 text-sm'>
									<p className='font-medium mb-1'>✓ Tudo pronto, {magicLinkData.user.name}!</p>
									<p>Clique no botão acima para acessar suas consultas sem precisar fazer login.</p>
								</div>
							)}

							<div className='bg-muted p-4 rounded-lg'>
								<p className='text-sm text-muted-foreground'>
									Confira seu email e WhatsApp para mais informações sobre sua consulta. Verifique também a caixa de spam.
								</p>
							</div>

							<hr />

							<div className='flex justify-center text-sm'>
								<Link href='/' className='flex items-center gap-2'>
									<ArrowLeft className='h-4 w-4' />
									Voltar para a página inicial
								</Link>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Componentes invisíveis de rastreamento de conversão Google Ads */}
			{transactionId && amount === 80 && (
				<PurchaseTracker80 transactionId={transactionId} value={amount} />
			)}
			{transactionId && amount === 100 && (
				<PurchaseTracker100 transactionId={transactionId} value={amount} />
			)}
			{transactionId && amount === 120 && (
				<PurchaseTracker120 transactionId={transactionId} value={amount} />
			)}
		</div>
	);
}
