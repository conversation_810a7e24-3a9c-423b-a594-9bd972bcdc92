import { <PERSON>ada<PERSON> } from 'next';
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { Suspense, Fragment } from 'react';
import { constructMetadata } from '@lib/utils';
import { CheckoutSteps } from '../components/checkout-steps';
import { SubscriptionCheckoutForm } from './components/subscription-checkout-form';

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Checkout - Assinatura | ZapVida',
    description: 'Finalize sua assinatura do plano médico da ZapVida',
    canonical: 'https://zapvida.com/pay/assinatura',
  });
}

interface CheckoutAssinaturaPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    plan?: string;
  };
}

// Definição dos planos disponíveis
const SUBSCRIPTION_PLANS = {
  'zapvida-sempre': {
    id: 'zapvida-sempre',
    name: '<PERSON><PERSON><PERSON><PERSON> Sempre',
    price: 49.00,
    originalPrice: 160.00,
    consultationsIncluded: 2,
    description: '2 consultas médicas por mês',
    cycle: 'MONTHLY' as const,
    features: [
      '2 consultas médicas por mês',
      'Atendimento por vídeo, áudio ou chat',
      'Acesso a especialistas qualificados',
      'Receitas e atestados digitais válidos',
      'Histórico médico completo',
      'Suporte prioritário 24/7',
      'Cancelamento a qualquer momento'
    ]
  }
};

export default async function CheckoutAssinaturaPage({
  params,
  searchParams,
}: CheckoutAssinaturaPageProps) {
  const planId = searchParams?.plan || 'zapvida-sempre';

  // Verificar se o plano existe
  const plan = SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS];

  if (!plan) {
    return redirect(`/${params.locale}/assinaturas`);
  }

  // Preparar produto para o checkout usando a mesma estrutura
  const product = {
    id: plan.id,
    title: plan.name,
    description: plan.description,
    type: 'SUBSCRIPTION' as const,
    price: plan.price,
    installmentsLimit: 1,
    enableInstallments: false,
    thumbnail: undefined,
    checkoutType: 'SUBSCRIPTION' as const,
    acceptedPayments: ['CREDIT_CARD', 'PIX_AUTO'],
    successUrl: '/checkout/success?type=subscription',
    cancelUrl: '/assinaturas',
    // Campos específicos para assinaturas
    originalPrice: plan.originalPrice,
    consultationsIncluded: plan.consultationsIncluded,
    cycle: plan.cycle,
    features: plan.features,
    isSubscription: true,
  };

  return (
    <main className='flex flex-col min-h-screen'>
      <div className='container px-4 max-w-7xl mx-auto grow'>
        {/* Steps Indicator for subscription checkout */}
        <div className='max-w-xl mx-auto mb-8 mt-8'>
          <SubscriptionCheckoutSteps currentStep='data' />
        </div>

        <div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
          <div className='order-2 md:order-1'>
            <SubscriptionCheckoutForm product={product} />
          </div>

          <div className='order-1 md:order-2 min-w-0'>
            {/* Mobile Summary - Visible only on mobile devices */}
            <div className='md:hidden mb-6'>
              <Suspense
                fallback={
                  <div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
                }
              >
                <SubscriptionSummaryCard product={product} isMobile={true} />
              </Suspense>
            </div>

            {/* Desktop Summary - Hidden on mobile, visible on desktop */}
            <div className='hidden md:block'>
              <Suspense
                fallback={
                  <div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
                }
              >
                <SubscriptionSummaryCard product={product} isMobile={false} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

// Componente Steps específico para assinatura
function SubscriptionCheckoutSteps({ currentStep }: { currentStep: 'plan' | 'data' | 'confirmation' }) {
  const steps = [
    { id: 'plan', label: 'Plano' },
    { id: 'data', label: 'Dados' },
    { id: 'confirmation', label: 'Confirmação' },
  ];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between w-full mb-2">
        {steps.map((step, index) => (
          <Fragment key={step.id}>
            <div className="flex flex-col items-center">
              <div
                className={`${
                  currentStep === step.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 text-gray-500'
                } w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold`}
              >
                {index + 1}
              </div>
              <span
                className={`text-xs font-medium mt-1 ${
                  currentStep === step.id ? 'text-primary' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className="flex-1 mx-2 border-t-2 border-dashed border-gray-300"></div>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
  }

  // Componente Summary específico para assinatura
function SubscriptionSummaryCard({ product, isMobile }: { product: any; isMobile: boolean }) {
  return (
    <div className={`border bg-white rounded-lg ${isMobile ? 'bg-gray-50' : 'sticky top-6'}`}>
      <div className='p-6 border-b border-gray-100 bg-gray-50/80'>
        <h3 className='text-lg font-semibold flex items-center gap-2'>
          <div className='w-5 h-5 rounded-full bg-primary flex items-center justify-center'>
            <div className='w-2 h-2 rounded-full bg-white'></div>
          </div>
          Resumo da Assinatura
        </h3>
      </div>

      <div className='p-6 space-y-5'>
        {/* Plano */}
        <div className='flex items-start gap-3'>
          <div className='w-3 h-3 rounded-full bg-primary mt-2'></div>
          <div className='flex-1'>
            <p className='font-medium'>{product.title}</p>
            <p className='text-sm text-gray-600'>{product.description}</p>
          </div>
        </div>

        {/* Características */}
        <div className='bg-primary/5 rounded-lg p-4 border border-primary/10'>
          <h4 className='text-sm font-semibold text-primary mb-3 flex items-center gap-1.5'>
            <svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
              <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
            </svg>
            Incluso na Assinatura
          </h4>
          <ul className='text-sm text-gray-700 space-y-2'>
            {product.features?.map((feature: string, index: number) => (
              <li key={index} className='flex items-start gap-2'>
                <svg className='w-4 h-4 text-green-500 mt-0.5 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
                </svg>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Benefícios melhorados */}
        <div className='space-y-3'>
          <div className='flex items-center gap-3 text-sm text-gray-700'>
            <div className='w-8 h-8 rounded-full bg-green-100 flex items-center justify-center'>
              <svg className='w-4 h-4 text-green-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z' />
              </svg>
            </div>
            <div>
              <p className='font-medium'>Pagamento 100% seguro</p>
              <p className='text-xs text-gray-500'>Processado pelo Asaas</p>
            </div>
          </div>

          <div className='flex items-center gap-3 text-sm text-gray-700'>
            <div className='w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center'>
              <svg className='w-4 h-4 text-blue-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M13 10V3L4 14h7v7l9-11h-7z' />
              </svg>
            </div>
            <div>
              <p className='font-medium'>Ativação imediata</p>
              <p className='text-xs text-gray-500'>Comece a usar agora mesmo</p>
            </div>
          </div>

          <div className='flex items-center gap-3 text-sm text-gray-700'>
            <div className='w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center'>
              <svg className='w-4 h-4 text-orange-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15' />
              </svg>
            </div>
            <div>
              <p className='font-medium'>Cancele quando quiser</p>
              <p className='text-xs text-gray-500'>Sem taxa de cancelamento</p>
            </div>
          </div>
        </div>

        {/* Preço */}
        <div className='border-t pt-4'>
          {product.originalPrice && (
            <div className='flex justify-between text-sm text-gray-500 mb-2'>
              <span>Valor sem assinatura:</span>
              <span className='line-through'>R$ {product.originalPrice.toFixed(2)}/mês</span>
            </div>
          )}
          <div className='flex justify-between text-lg font-semibold'>
            <span>Valor da assinatura:</span>
            <span className='text-primary'>R$ {product.price.toFixed(2)}/mês</span>
          </div>
          {product.originalPrice && (
            <div className='text-center mt-3 p-2 bg-green-50 rounded-lg border border-green-200'>
              <p className='text-sm font-medium text-green-800'>
                💰 Economize R$ {(product.originalPrice - product.price).toFixed(2)}/mês
              </p>
              <p className='text-xs text-green-600 mt-1'>
                Isso é uma economia de {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% por mês!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
