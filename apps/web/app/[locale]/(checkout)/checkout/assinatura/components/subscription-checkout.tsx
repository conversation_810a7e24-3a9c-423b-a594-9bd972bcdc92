'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>earch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useF<PERSON>, FormProvider, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import {
  Check,
  CreditCard,
  Shield,
  Clock,
  ArrowLeft,
  Loader2,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { FaBarcode, FaPix } from 'react-icons/fa6';
import Link from 'next/link';
import { CheckoutSteps } from '../../components/checkout-steps';
import { CreditCardInput } from '../../components/credit-card-input';
import { CPFInput } from '../../components/cpf-input';
import { BRPhoneInput } from '../../components/phone-input';
import { useToast } from '@ui/hooks/use-toast';
import { apiClient } from '@shared/lib/api-client';
import { cn } from '@ui/lib';

// Schema de validação
const subscriptionFormSchema = z.object({
  customerData: z.object({
    name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
    email: z.string().email('Email inválido'),
    phone: z.string().min(10, 'Telefone inválido'),
    cpf: z.string().min(11, 'CPF inválido'),
  }),
  paymentMethod: z.enum(['CREDIT_CARD', 'PIX', 'BOLETO']),
  creditCard: z.object({
    cardNumber: z.string(),
    cardHolder: z.string(),
    cardExpiry: z.string(),
    cardCvv: z.string(),
  }).optional(),
  acceptTerms: z.boolean().refine(val => val === true, 'Você deve aceitar os termos'),
}).refine((data) => {
  // Validação condicional para cartão de crédito
  if (data.paymentMethod === 'CREDIT_CARD') {
    return (
      data.creditCard &&
      data.creditCard.cardNumber.length >= 13 &&
      data.creditCard.cardHolder.length >= 2 &&
      data.creditCard.cardExpiry.length >= 5 &&
      data.creditCard.cardCvv.length >= 3
    );
  }
  return true;
}, {
  message: 'Dados do cartão de crédito são obrigatórios',
  path: ['creditCard'],
});

type SubscriptionFormData = z.infer<typeof subscriptionFormSchema>;

// Planos disponíveis
const PLANS = {
  'zapvida-sempre': {
    id: 'zapvida-sempre',
    name: 'ZapVida Sempre',
    price: 49.00,
    originalPrice: 160.00,
    consultationsIncluded: 2,
    description: '2 consultas médicas por mês',
    cycle: 'MONTHLY' as const,
    features: [
      '2 consultas médicas por mês',
      'Atendimento por vídeo, áudio ou chat',
      'Acesso a especialistas qualificados',
      'Receitas e atestados digitais válidos',
      'Histórico médico completo',
      'Suporte prioritário 24/7',
      'Cancelamento a qualquer momento'
    ]
  }
};

// Definição dos métodos de pagamento
const PAYMENT_METHODS = {
  CREDIT_CARD: {
    value: 'CREDIT_CARD',
    title: 'Cartão de Crédito',
    icon: 'credit-card' as const,
  },
  PIX: {
    value: 'PIX',
    title: 'PIX',
    icon: 'pix' as const,
  },
  BOLETO: {
    value: 'BOLETO',
    title: 'Boleto',
    icon: 'boleto' as const,
  },
};

// Componente de opção de pagamento
function PaymentOption({
  value,
  title,
  icon,
  selected,
  onSelect,
}: {
  value: string;
  title: string;
  icon: 'credit-card' | 'pix' | 'boleto';
  selected: boolean;
  onSelect: () => void;
}) {
  const renderIcon = () => {
    switch (icon) {
      case 'credit-card':
        return <CreditCard className='h-5 w-5' />;
      case 'pix':
        return <FaPix className='h-5 w-5' />;
      case 'boleto':
        return <FaBarcode className='h-5 w-5' />;
      default:
        return <CreditCard className='h-5 w-5' />;
    }
  };

  return (
    <div
      onClick={onSelect}
      className={cn(
        'relative flex cursor-pointer rounded-lg border p-4 transition-all',
        selected ? 'border-primary bg-primary/5' : 'hover:border-primary/50'
      )}
    >
      <div className='flex items-center gap-3'>
        <div
          className={cn(
            'flex h-10 w-10 items-center justify-center rounded-full',
            selected ? 'bg-primary text-primary-foreground' : 'bg-muted'
          )}
        >
          {renderIcon()}
        </div>
        <span className='font-medium'>{title}</span>
      </div>
    </div>
  );
}

export function SubscriptionCheckout() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const [selectedPlan, setSelectedPlan] = useState<string>('zapvida-sempre');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Configurar formulário
  const methods = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionFormSchema),
    mode: 'onChange',
    defaultValues: {
      customerData: {
        name: '',
        email: '',
        phone: '',
        cpf: '',
      },
      paymentMethod: 'CREDIT_CARD',
      creditCard: {
        cardNumber: '',
        cardHolder: '',
        cardExpiry: '',
        cardCvv: '',
      },
      acceptTerms: false,
    },
  });

  const { handleSubmit, watch, setValue, formState: { errors }, clearErrors, reset, control } = methods;
  const selectedPaymentMethod = watch('paymentMethod');

  // Debug: Monitorar mudanças no método de pagamento
  useEffect(() => {

  }, [selectedPaymentMethod]);

  // Mutation para criar assinatura
  const createSubscriptionMutation = apiClient.subscriptions.createSubscription.useMutation();

  // Função para lidar com mudança de método de pagamento
  const handlePaymentMethodChange = (newMethod: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {


    // Forçar a atualização do estado do formulário
    setValue('paymentMethod', newMethod, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true
    });

    // Limpar campos do cartão se não for cartão de crédito
    if (newMethod !== 'CREDIT_CARD') {

      setValue('creditCard.cardNumber', '');
      setValue('creditCard.cardHolder', '');
      setValue('creditCard.cardExpiry', '');
      setValue('creditCard.cardCvv', '');
      clearErrors('creditCard');
    }

    // Verificar se a mudança foi aplicada
    setTimeout(() => {
      const currentMethod = methods.getValues('paymentMethod');

      if (currentMethod !== newMethod) {
        console.error('ERRO: Método não foi atualizado corretamente!');
        // Tentar novamente
        methods.setValue('paymentMethod', newMethod);
      }
    }, 100);
  };

  useEffect(() => {
    const planFromUrl = searchParams?.get('plan');
    if (planFromUrl && PLANS[planFromUrl as keyof typeof PLANS]) {
      setSelectedPlan(planFromUrl);
    }
  }, [searchParams]);

  const plan = PLANS[selectedPlan as keyof typeof PLANS];

  const onSubmit = async (data: SubscriptionFormData) => {
    if (!plan) return;

    setIsSubmitting(true);

    try {
      toast({
        title: 'Processando assinatura',
        description: 'Aguarde enquanto criamos sua assinatura...',
      });

      // Preparar dados para a API
      const subscriptionData = {
        planId: plan.id,
        billingType: data.paymentMethod,
        customerData: data.customerData,
        ...(data.paymentMethod === 'CREDIT_CARD' && data.creditCard && {
          creditCard: {
            holderName: data.creditCard.cardHolder,
            number: data.creditCard.cardNumber.replace(/\D/g, ''),
            expiryMonth: data.creditCard.cardExpiry.split('/')[0],
            expiryYear: `20${data.creditCard.cardExpiry.split('/')[1]}`,
            ccv: data.creditCard.cardCvv,
          },
          creditCardHolderInfo: {
            name: data.customerData.name,
            email: data.customerData.email,
            cpfCnpj: data.customerData.cpf.replace(/\D/g, ''),
            postalCode: '01311000',
            addressNumber: '123',
            phone: data.customerData.phone.replace(/\D/g, ''),
          }
        })
      };



      const result = await createSubscriptionMutation.mutateAsync(subscriptionData);



      toast({
        title: 'Assinatura criada com sucesso!',
        description: 'Você será redirecionado para a página de confirmação.',
        variant: 'default',
      });

      router.push('/checkout/success?type=subscription');

    } catch (error: any) {
      console.error('Erro ao criar assinatura:', error);

      toast({
        title: 'Erro ao processar assinatura',
        description: error.message || 'Tente novamente em alguns minutos.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!plan) {
    return (
      <div className="container mx-auto px-4 py-20">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Plano não encontrado</h1>
          <p className="text-gray-600 mb-6">O plano selecionado não existe.</p>
          <Button asChild>
            <Link href="/assinaturas">Voltar para planos</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <Link
              href="/assinaturas"
              className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar para planos
            </Link>

            <div className="text-center">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Finalizar Assinatura
              </h1>
              <p className="text-muted-foreground">
                Complete sua assinatura e comece a cuidar da sua saúde hoje mesmo
              </p>
            </div>
          </div>

          <div className="max-w-xl mx-auto mb-8">
            <CheckoutSteps currentStep="payment" />
          </div>

          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="border border-border h-fit lg:sticky lg:top-6">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-semibold">
                        {plan.name}
                      </CardTitle>
                      <Badge className="bg-primary/10 text-primary">
                        Mais popular
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-5">
                    <div className="text-center py-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <span className="text-2xl font-bold text-primary">
                          R$ {plan.price.toFixed(2)}
                        </span>
                        <span className="text-muted-foreground">/mês</span>
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-muted-foreground line-through text-sm">
                          R$ {plan.originalPrice.toFixed(2)}
                        </span>
                        <span className="text-green-600 font-semibold text-xs">
                          Economize R$ {(plan.originalPrice - plan.price).toFixed(2)}
                        </span>
                      </div>
                    </div>

                    <div className="bg-primary/5 rounded-lg p-4 border border-primary/10">
                      <h4 className="text-sm font-semibold text-primary mb-3 flex items-center gap-1.5">
                        <Check className="h-4 w-4" />
                        Incluso na Assinatura
                      </h4>

                      <div className="space-y-3">
                        {plan.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-500 flex-shrink-0" />
                            <span className="text-sm text-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2 pt-2 border-t">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Shield className="h-4 w-4 mr-2 text-green-500" />
                        <span>Pagamento 100% seguro</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2 text-green-500" />
                        <span>Ativação imediata</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <CreditCard className="h-4 w-4 mr-2 text-green-500" />
                        <span>Cancele quando quiser</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold">
                        Dados Pessoais
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Nome completo *</Label>
                          <Input
                            id="name"
                            placeholder="Seu nome completo"
                            {...methods.register('customerData.name')}
                            className={errors.customerData?.name ? 'border-red-500' : ''}
                          />
                          {errors.customerData?.name && (
                            <p className="text-sm text-red-500">{errors.customerData.name.message}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email">Email *</Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            {...methods.register('customerData.email')}
                            className={errors.customerData?.email ? 'border-red-500' : ''}
                          />
                          {errors.customerData?.email && (
                            <p className="text-sm text-red-500">{errors.customerData.email.message}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="phone">Telefone *</Label>
                          <BRPhoneInput
                            value={watch('customerData.phone')}
                            onChange={(value) => setValue('customerData.phone', value)}
                            className={errors.customerData?.phone ? 'border-red-500' : ''}
                          />
                          {errors.customerData?.phone && (
                            <p className="text-sm text-red-500">{errors.customerData.phone.message}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="cpf">CPF *</Label>
                          <CPFInput
                            value={watch('customerData.cpf')}
                            onChange={(value) => setValue('customerData.cpf', value)}
                            className={errors.customerData?.cpf ? 'border-red-500' : ''}
                          />
                          {errors.customerData?.cpf && (
                            <p className="text-sm text-red-500">{errors.customerData.cpf.message}</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold">
                        Método de Pagamento
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        Método atual: {selectedPaymentMethod}
                      </p>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        {Object.values(PAYMENT_METHODS).map((method) => {
                          const isSelected = selectedPaymentMethod === method.value;


                          return (
                            <PaymentOption
                              key={method.value}
                              value={method.value}
                              title={method.title}
                              icon={method.icon}
                              selected={isSelected}
                              onSelect={() => {

                                handlePaymentMethodChange(method.value as 'CREDIT_CARD' | 'PIX' | 'BOLETO');
                              }}
                            />
                          );
                        })}
                      </div>

                      {selectedPaymentMethod === 'CREDIT_CARD' && (
                        <div className="space-y-4 pt-4 border-t" key="credit-card-form">
                          <div className="grid grid-cols-1 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="cardNumber">Número do cartão *</Label>
                              <CreditCardInput
                                id="cardNumber"
                                value={watch('creditCard.cardNumber') || ''}
                                onChange={(value) => setValue('creditCard.cardNumber', value)}
                                error={!!errors.creditCard?.cardNumber || (selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardNumber'))}
                              />
                              {selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardNumber') && (
                                <p className="text-sm text-red-500">Número do cartão é obrigatório</p>
                              )}
                              {selectedPaymentMethod === 'CREDIT_CARD' && watch('creditCard.cardNumber') && watch('creditCard.cardNumber').length < 13 && (
                                <p className="text-sm text-red-500">Número do cartão inválido</p>
                              )}
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="cardHolder">Nome no cartão *</Label>
                              <Input
                                id="cardHolder"
                                placeholder="Nome como está no cartão"
                                {...methods.register('creditCard.cardHolder')}
                                className={
                                  (errors.creditCard?.cardHolder || (selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardHolder')))
                                    ? 'border-red-500' : ''
                                }
                              />
                              {selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardHolder') && (
                                <p className="text-sm text-red-500">Nome no cartão é obrigatório</p>
                              )}
                              {selectedPaymentMethod === 'CREDIT_CARD' && watch('creditCard.cardHolder') && watch('creditCard.cardHolder').length < 2 && (
                                <p className="text-sm text-red-500">Nome deve ter pelo menos 2 caracteres</p>
                              )}
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="cardExpiry">Validade *</Label>
                                <Input
                                  id="cardExpiry"
                                  placeholder="MM/AA"
                                  {...methods.register('creditCard.cardExpiry')}
                                  className={
                                    (errors.creditCard?.cardExpiry || (selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardExpiry')))
                                      ? 'border-red-500' : ''
                                  }
                                />
                                {selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardExpiry') && (
                                  <p className="text-sm text-red-500">Data de expiração é obrigatória</p>
                                )}
                                {selectedPaymentMethod === 'CREDIT_CARD' && watch('creditCard.cardExpiry') && watch('creditCard.cardExpiry').length < 5 && (
                                  <p className="text-sm text-red-500">Data de expiração inválida (MM/AA)</p>
                                )}
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="cardCvv">CVV *</Label>
                                <Input
                                  id="cardCvv"
                                  placeholder="123"
                                  {...methods.register('creditCard.cardCvv')}
                                  className={
                                    (errors.creditCard?.cardCvv || (selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardCvv')))
                                      ? 'border-red-500' : ''
                                  }
                                />
                                {selectedPaymentMethod === 'CREDIT_CARD' && !watch('creditCard.cardCvv') && (
                                  <p className="text-sm text-red-500">CVV é obrigatório</p>
                                )}
                                {selectedPaymentMethod === 'CREDIT_CARD' && watch('creditCard.cardCvv') && watch('creditCard.cardCvv').length < 3 && (
                                  <p className="text-sm text-red-500">CVV deve ter pelo menos 3 dígitos</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {selectedPaymentMethod === 'PIX' && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <p className="text-sm text-blue-800">
                            Após confirmar, você receberá o código PIX para pagamento.
                            Sua assinatura será ativada assim que o pagamento for confirmado.
                          </p>
                        </div>
                      )}

                      {selectedPaymentMethod === 'BOLETO' && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                          <p className="text-sm text-yellow-800">
                            O boleto será gerado após a confirmação.
                            Sua assinatura será ativada em até 2 dias úteis após o pagamento.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div className="flex items-start space-x-2">
                          <input
                            type="checkbox"
                            id="acceptTerms"
                            className="mt-1"
                            {...methods.register('acceptTerms')}
                          />
                          <Label htmlFor="acceptTerms" className="text-sm leading-5">
                            Eu aceito os{' '}
                            <Link href="/termos" className="text-primary hover:underline">
                              termos de uso
                            </Link>{' '}
                            e{' '}
                            <Link href="/privacidade" className="text-primary hover:underline">
                              política de privacidade
                            </Link>
                            . Confirmo que tenho mais de 18 anos e autorizo a cobrança recorrente mensal.
                          </Label>
                        </div>
                        {errors.acceptTerms && (
                          <p className="text-sm text-red-500">{errors.acceptTerms.message}</p>
                        )}

                        <Button
                          type="submit"
                          className="w-full"
                          size="lg"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processando...
                            </>
                          ) : (
                            `Assinar por R$ ${plan.price.toFixed(2)}/mês`
                          )}
                        </Button>

                        <p className="text-xs text-center text-muted-foreground">
                          Pagamento seguro
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </div>
  );
}
