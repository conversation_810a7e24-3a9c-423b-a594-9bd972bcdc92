'use client';

import { <PERSON><PERSON><PERSON>, CreditCard, Loader2, Lock, CheckCircle2, Star } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';
import Image from 'next/image';

import { <PERSON><PERSON> } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import { Badge } from '@ui/components/badge';
import { CreditCardForm } from '../../components/checkout-credit-card-form';
import { CheckoutFooter } from '../../components/checkout-footer';
import { PaymentBrandIcons } from '../../components/payment-brand-icons';

interface SubscriptionPaymentFormProps {
  totalAmount: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
  acceptedPayments?: string[];
  loading?: boolean;
  onPixClick?: () => void;
  onPixAutoClick?: () => void;
}

export function SubscriptionPaymentForm({
  totalAmount,
  installmentsLimit,
  enableInstallments = false,
  acceptedPayments = ['CREDIT_CARD', 'PIX_AUTO'],
  loading = false,
  onPixClick,
  onPixAutoClick,
}: SubscriptionPaymentFormProps) {
  const { control, watch, setValue, trigger, clearErrors, formState: { errors }, getValues } = useFormContext();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const paymentMethod = watch('paymentMethod');

  // Debug: Monitorar mudanças no método de pagamento
  useEffect(() => {
    
  }, [paymentMethod, acceptedPayments, errors]);

  // Set CREDIT_CARD as default for subscriptions
  useEffect(() => {
    if (!paymentMethod && acceptedPayments.includes('CREDIT_CARD')) {
      
      setValue('paymentMethod', 'CREDIT_CARD');
    }
  }, [paymentMethod, acceptedPayments, setValue]);

  const handlePaymentMethodChange = (method: string) => {
    
    

    try {
      // Atualizar o valor do método de pagamento
      setValue('paymentMethod', method, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true
      });

      // Limpar erros de cartão de crédito quando mudar para PIX_AUTO
      if (method === 'PIX_AUTO') {
        
        clearErrors('creditCard');

        // Limpar também os valores dos campos de cartão
        setValue('creditCard.cardNumber', '', { shouldValidate: false });
        setValue('creditCard.cardHolder', '', { shouldValidate: false });
        setValue('creditCard.cardExpiry', '', { shouldValidate: false });
        setValue('creditCard.cardCvv', '', { shouldValidate: false });
      }

      // Verificar se a mudança foi aplicada corretamente
      setTimeout(() => {
        const currentMethod = getValues('paymentMethod');
        
        if (currentMethod !== method) {
          console.error('ERRO: Método não foi aplicado corretamente!');
          setValue('paymentMethod', method, { shouldValidate: true });
        } else {
          
        }
      }, 100);

    } catch (error) {
      console.error('Erro ao alterar método de pagamento:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Para PIX_AUTO, não precisamos validar cartão de crédito
      if (paymentMethod === 'PIX_AUTO') {
        

        // Limpar erros de cartão de crédito antes de prosseguir
        clearErrors('creditCard');

        // Validar apenas campos obrigatórios (não cartão)
        const fieldsToValidate = ['customerData', 'acceptTerms'];
        const isValid = await trigger(fieldsToValidate);

        
        

        if (!isValid) {
          
          return;
        }

        if (onPixAutoClick) {
          await onPixAutoClick();
        }
        return;
      }

      // Para cartão de crédito, validar o formulário completo
      const isValid = await trigger();
      
      

      if (!isValid) {
        
        return;
      }

      if (paymentMethod === 'PIX' && onPixClick) {
        await onPixClick();
      }
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="p-4 sm:p-6 space-y-1">
        <div className="flex items-center gap-2">
          <CreditCard className="h-5 w-5 text-muted-foreground" />
          <CardTitle className="text-lg">Método de Pagamento</CardTitle>
        </div>
        <p className="text-sm text-muted-foreground">
          Escolha como deseja pagar sua assinatura
        </p>
      </CardHeader>

      <CardContent className="p-4 sm:p-6 pt-0 space-y-4 sm:space-y-6">
        <div className="space-y-3">
          {/* Cartão de Crédito */}
          {acceptedPayments.includes('CREDIT_CARD') && (
            <div
              className={`cursor-pointer rounded-lg border p-3 sm:p-4 transition-all ${
                paymentMethod === 'CREDIT_CARD'
                  ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                  : 'border-gray-200 hover:border-primary/50'
              }`}
              onClick={() => {
                
                handlePaymentMethodChange('CREDIT_CARD');
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center flex-shrink-0 ${
                    paymentMethod === 'CREDIT_CARD'
                      ? 'border-primary bg-primary'
                      : 'border-gray-300'
                  }`}>
                    {paymentMethod === 'CREDIT_CARD' && (
                      <div className="w-2 h-2 rounded-full bg-white"></div>
                    )}
                  </div>
                  <CreditCard className="h-5 w-5 text-gray-600 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <span className="font-medium text-sm sm:text-base block truncate">Cartão de Crédito</span>
                    <p className="text-xs sm:text-sm text-gray-600 truncate">Débito automático no cartão</p>
                  </div>
                </div>
                {/* Bandeiras - sempre à direita, compacta no mobile */}
                <div className="flex-shrink-0 ml-2">
                  <div className="scale-75 sm:scale-100">
                    <PaymentBrandIcons />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* PIX Automático */}
          {acceptedPayments.includes('PIX_AUTO') && (
            <div className="relative">
              <div
                className={`cursor-pointer rounded-lg border p-3 sm:p-4 transition-all ${
                  paymentMethod === 'PIX_AUTO'
                    ? 'border-emerald-500 bg-emerald-50 ring-2 ring-emerald-500/20'
                    : 'border-gray-200 hover:border-emerald-300'
                }`}
                onClick={() => {
                  
                  handlePaymentMethodChange('PIX_AUTO');
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center flex-shrink-0 ${
                      paymentMethod === 'PIX_AUTO'
                        ? 'border-emerald-500 bg-emerald-500'
                        : 'border-gray-300'
                    }`}>
                      {paymentMethod === 'PIX_AUTO' && (
                        <div className="w-2 h-2 rounded-full bg-white"></div>
                      )}
                    </div>
                    <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-emerald-200 flex items-center justify-center shadow-sm flex-shrink-0">
                      <Image
                        src="/images/payments/pix.svg"
                        alt="PIX"
                        width={16}
                        height={16}
                        className="text-white w-3 h-3 sm:w-4 sm:h-4"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <span className="font-medium text-sm sm:text-base block truncate">PIX Automático</span>
                      <p className="text-xs sm:text-sm text-gray-600 truncate">Débito automático todo mês</p>
                    </div>
                  </div>
                  {/* Badge Recomendado - sempre à direita, compacto no mobile */}
                  <div className="flex-shrink-0 ml-2">
                    <Badge className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-xs px-2 py-1 shadow-lg border-0 font-medium flex items-center gap-1">
                      <Star className="w-3 h-3 fill-current" />
                      <span className="hidden sm:inline">Recomendado</span>
                      <span className="sm:hidden">★</span>
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Formulário de cartão de crédito */}
        {paymentMethod === 'CREDIT_CARD' && (
          <div className="mt-4 sm:mt-6 space-y-4 p-3 sm:p-4 bg-gray-50 rounded-lg border">
            <CreditCardForm
              price={totalAmount}
              installmentsLimit={installmentsLimit}
              enableInstallments={enableInstallments}
            />
          </div>
        )}

        {/* Informações do PIX */}
        {(paymentMethod === 'PIX' || paymentMethod === 'PIX_AUTO') && (
          <div className="mt-4 sm:mt-6">
            {paymentMethod === 'PIX_AUTO' ? (
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3 sm:p-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-emerald-600 flex items-center justify-center flex-shrink-0">
                    <CheckCircle2 className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-emerald-900 mb-1 text-sm sm:text-base">PIX Automático Selecionado</h4>
                    <p className="text-xs sm:text-sm text-emerald-800 mb-3">
                      Após confirmar, você autorizará o débito automático mensal.
                      Sua assinatura será ativada imediatamente e renovada automaticamente.
                    </p>
                    <div className="text-xs text-emerald-700 space-y-1">
                      <p>• Primeiro pagamento será processado agora</p>
                      <p>• Próximos pagamentos serão automáticos todo dia {new Date().getDate()}</p>
                      <p>• Você pode cancelar a qualquer momento</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
                <p className="text-xs sm:text-sm text-blue-800">
                  Após confirmar, você receberá o código PIX para pagamento.
                  Sua assinatura será ativada assim que o pagamento for confirmado.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Botão de submit - Movido para cá para ser parte do componente */}
        <div className="pt-4 sm:pt-6 border-t">
          {/* Campo de aceitar termos */}
          <div className="mb-4 sm:mb-6">
            <Controller
              name="acceptTerms"
              control={control}
              render={({ field }) => (
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="acceptTerms"
                    checked={field.value}
                    onChange={field.onChange}
                    className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="acceptTerms" className="text-xs sm:text-sm text-gray-700 leading-5">
                    Eu aceito os{' '}
                    <a href="/termos" target="_blank" className="text-primary hover:underline font-medium">
                      termos de uso
                    </a>{' '}
                    e{' '}
                    <a href="/privacidade" target="_blank" className="text-primary hover:underline font-medium">
                      política de privacidade
                    </a>
                    . Confirmo que tenho mais de 18 anos e autorizo a cobrança recorrente mensal.
                  </label>
                </div>
              )}
            />
            {errors.acceptTerms && (
              <p className="text-sm text-red-500 mt-2">
                Você deve aceitar os termos para continuar
              </p>
            )}
          </div>

          <Button
            type="submit"
            className="w-full h-12 sm:h-14 text-sm sm:text-base"
            size="lg"
            disabled={loading || isSubmitting}
            onClick={handleSubmit}
          >
            {loading || isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processando...
              </>
            ) : (
              paymentMethod === 'PIX_AUTO'
                ? `Autorizar PIX Automático - R$ ${totalAmount.toFixed(2).replace('.', ',')}/mês`
                : `Assinar por R$ ${totalAmount.toFixed(2).replace('.', ',')}/mês`
            )}
          </Button>

          <div className="mt-3 sm:mt-4">
            <CheckoutFooter />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
