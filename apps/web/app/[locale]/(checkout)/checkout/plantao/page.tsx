import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { AlertTriangle, Clock, ArrowRight, CheckCircle2 } from 'lucide-react';
import { Suspense } from 'react';
import { CheckoutSteps } from '../components/checkout-steps';
import { ON_DUTY_CONFIG } from 'api/constants/on-duty';
import Link from 'next/link';

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
	return {
		title: 'Checkout - Plantão Médico',
		description: 'Pagamento para atendimento de plantão',
	};
}

interface PlantaoCheckoutPageProps {
	params: {
		locale: string;
	};
	searchParams: {
		urgencyLevel?: 'high' | 'medium' | 'low';
		partner?: string;
		selectUrgency?: string;
	};
}

export default async function PlantaoCheckoutPage({
    params,
    searchParams,
}: PlantaoCheckoutPageProps) {
    const sp = await (searchParams as any);
    const urgencyLevel = sp?.urgencyLevel;
    const partner = sp?.partner;
    const selectUrgency = sp?.selectUrgency;

	// Se não tem urgência definida ou foi solicitada seleção, mostrar seleção de urgência
	if (!urgencyLevel || selectUrgency === 'true') {
		return <UrgencySelectionPage params={params} searchParams={searchParams} />;
	}

	// Validar nível de urgência
	if (!['high', 'medium', 'low'].includes(urgencyLevel)) {
		return <UrgencySelectionPage params={params} searchParams={searchParams} />;
	}

	// Configuração do produto baseada no nível de urgência
	const urgencyConfig = ON_DUTY_CONFIG[urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG];

	// Preparar produto para o checkout
	const product = {
		id: 'plantao-medical-service', // ID genérico para plantão
		title: `Atendimento de Plantão - ${urgencyConfig.label}`,
		description: 'Atendimento médico imediato sem agendamento',
		type: 'ON_DUTY_CONSULTATION' as const,
		price: urgencyConfig.price,
		installmentsLimit: 1,
		enableInstallments: false,
		thumbnail: '/images/plantao-medical.png',
		checkoutType: 'PLANTAO' as const,
		acceptedPayments: ['CREDIT_CARD', 'PIX'],
        successUrl: '/checkout/success',
        cancelUrl: '/pay/plantao',
		// Campos específicos para plantão
		scheduledAt: undefined, // Plantão não tem horário agendado
		duration: 30, // Duração padrão
		isOnline: true,
		serviceFee: 0,
		isOnDuty: true,
		urgencyLevel: urgencyLevel,
		partner: partner,
		// Dados simulados para compatibilidade com o formulário
		doctor: {
			id: 'plantao-service',
			name: 'Médico de Plantão',
			profileImage: '/images/doctor-avatar.png',
			specialty: 'Clínica Geral',
		},
	};



	return (
		<main className='flex flex-col min-h-screen'>
			<div className='container px-4 max-w-7xl mx-auto grow'>
				{/* Steps Indicator */}
				<div className='max-w-xl mx-auto mb-8 mt-8'>
					<CheckoutSteps currentStep='data' />
				</div>

				<div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
					<div className='order-2 md:order-1'>
						<CheckoutForm product={product} />
					</div>

					<div className='order-1 md:order-2 min-w-0'>
						{/* Mobile Summary */}
						<div className='md:hidden mb-6'>
							<Suspense
								fallback={
									<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
								}
							>
								<PlantaoSummaryCard product={product} isMobile={true} />
							</Suspense>
						</div>

						{/* Desktop Summary */}
						<div className='hidden md:block'>
							<Suspense
								fallback={
									<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>
								}
							>
								<PlantaoSummaryCard product={product} isMobile={false} />
							</Suspense>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}

// Componente para resumo do plantão
function PlantaoSummaryCard({ product, isMobile }: { product: any; isMobile: boolean }) {
	const urgencyConfig = ON_DUTY_CONFIG[product.urgencyLevel?.toUpperCase() as keyof typeof ON_DUTY_CONFIG];

	return (
		<div className={`border bg-white rounded-lg p-6 ${isMobile ? 'bg-gray-50' : 'sticky top-6'}`}>
			<h3 className='text-lg font-semibold mb-4'>Resumo do Atendimento</h3>

			<div className='space-y-4'>
				{/* Tipo de atendimento */}
				<div className='flex items-center gap-3'>
					<div className={`w-3 h-3 rounded-full ${
						product.urgencyLevel === 'high' ? 'bg-red-500' :
						product.urgencyLevel === 'medium' ? 'bg-amber-500' : 'bg-green-500'
					}`}></div>
					<div>
						<p className='font-medium'>{product.title}</p>
						<p className='text-sm text-gray-600'>{product.description}</p>
					</div>
				</div>

				{/* Tempo estimado */}
				<div className='flex items-center gap-2 text-sm text-gray-600'>
					<Clock className='h-4 w-4' />
					<span>Tempo de espera: ~{urgencyConfig.waitTime} min</span>
				</div>

				{/* Benefícios */}
				<div className='border-t pt-4'>
					<h4 className='font-medium mb-2'>Incluso no atendimento:</h4>
					<ul className='text-sm text-gray-600 space-y-1'>
						<li className='flex items-center gap-2'>
							<CheckCircle2 className='h-3 w-3 text-green-500' />
							Consulta por chat, áudio ou vídeo
						</li>
						<li className='flex items-center gap-2'>
							<CheckCircle2 className='h-3 w-3 text-green-500' />
							Prescrição médica digital
						</li>
						<li className='flex items-center gap-2'>
							<CheckCircle2 className='h-3 w-3 text-green-500' />
							Atestado médico (se necessário)
						</li>

					</ul>
				</div>

				{/* Preço */}
				<div className='border-t pt-4'>
					<div className='flex justify-between text-lg font-semibold'>
						<span>Total:</span>
						<span>R$ {product.price.toFixed(2)}</span>
					</div>
				</div>
			</div>
		</div>
	);
}

// Componente para seleção de urgência
function UrgencySelectionPage({ params, searchParams }: PlantaoCheckoutPageProps) {
	const { partner } = searchParams;

	// Informações dos níveis de urgência
	const urgencyLevels = [
		{
			id: 'low',
			name: ON_DUTY_CONFIG.LOW.label,
			color: 'bg-green-500',
			price: ON_DUTY_CONFIG.LOW.price,
			waitTime: `${ON_DUTY_CONFIG.LOW.waitTime}`,
			description:
				'Para consultas de rotina, acompanhamento médico ou sintomas leves que não apresentam risco imediato.',
		},
		{
			id: 'medium',
			name: ON_DUTY_CONFIG.MEDIUM.label,
			color: 'bg-amber-500',
			price: ON_DUTY_CONFIG.MEDIUM.price,
			waitTime: `${ON_DUTY_CONFIG.MEDIUM.waitTime}`,
			description:
				'Para condições que necessitam de atenção, mas não são emergenciais. Sintomas moderados ou problemas de saúde que precisam ser avaliados em breve.',
		},
		{
			id: 'high',
			name: ON_DUTY_CONFIG.HIGH.label,
			color: 'bg-red-500',
			price: ON_DUTY_CONFIG.HIGH.price,
			waitTime: `${ON_DUTY_CONFIG.HIGH.waitTime}`,
			description:
				'Para situações que requerem atenção médica imediata, como sintomas intensos ou preocupações graves.',
		},
	];

	return (
		<main className='flex flex-col min-h-screen'>
			<div className='container px-4 max-w-7xl mx-auto grow'>
				{/* Steps Indicator */}
				<div className='max-w-xl mx-auto mb-8 mt-8'>
					<CheckoutSteps currentStep='urgency' />
				</div>

				<div className='max-w-4xl mx-auto'>
					{/* Header */}
					<div className='text-center mb-8'>
						<h1 className='text-2xl font-bold text-gray-900 mb-2'>
							Selecione o nível de urgência para seu atendimento
						</h1>
						<p className='text-gray-500'>
							O tempo de espera e o valor variam de acordo com a urgência selecionada
						</p>
					</div>

					{/* Urgency Selection */}
					<div className='space-y-6'>
						{urgencyLevels.map((level) => (
							<Link
								key={level.id}
                                href={`/${params.locale}/pay/plantao?urgencyLevel=${level.id}${
									partner ? `&partner=${partner}` : ''
								}`}
								className='block'
							>
								<div className='border rounded-lg p-5 hover:border-primary hover:shadow-md transition-all cursor-pointer group bg-white'>
									<div className='flex items-center justify-between mb-3'>
										<div
											className={`${level.color} text-white text-sm font-medium py-1.5 px-3 rounded-full flex gap-2 items-center`}
										>
											{level.id === 'high' && (
												<AlertTriangle className='h-3.5 w-3.5' />
											)}
											{level.name}
										</div>

										<div className='text-lg font-bold text-gray-900'>
											R$ {level.price.toFixed(2)}
										</div>
									</div>

									{/* Footer with wait time and select button */}
									<div className='flex items-center justify-between'>
										<div className='flex items-center gap-2 text-gray-500 text-sm'>
											<Clock className='h-4 w-4' />
											<span>
												Tempo de espera:<br />
												Aproximadamente {level.waitTime} minutos
											</span>
										</div>

										<div className='text-primary flex items-center gap-1 font-medium text-sm group-hover:translate-x-1 transition-transform'>
											Selecionar
											<ArrowRight className='h-4 w-4' />
										</div>
									</div>
								</div>
							</Link>
						))}
					</div>

					{/* Important Notes */}
					<div className='mt-8 bg-blue-50 border border-blue-100 rounded-lg p-5'>
						<h3 className='font-semibold text-blue-900 mb-2 text-sm'>
							📋 Informações importantes:
						</h3>
						<ul className='space-y-1.5 text-blue-800 text-xs'>
							<li className='flex items-start gap-2'>
								<CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
								<span>
									O tempo de espera é estimado e pode variar dependendo da demanda atual
								</span>
							</li>
							<li className='flex items-start gap-2'>
								<CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
								<span>
									Consulta realizada por chat, áudio ou vídeo conforme sua preferência
								</span>
							</li>
							<li className='flex items-start gap-2'>
								<CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
								<span>
									Inclui prescrição médica digital e atestado (se necessário)
								</span>
							</li>
							<li className='flex items-start gap-2'>
								<CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
								<span>
									Retorno em até 24h sem custo adicional
								</span>
							</li>
						</ul>
					</div>

					{/* Emergency Notice */}
					<div className='mt-4 bg-red-50 border border-red-100 rounded-lg p-5 mb-8'>
						<div className='flex items-start gap-3'>
							<AlertTriangle className='h-5 w-5 text-red-500 mt-0.5 flex-shrink-0' />
							<div>
								<h3 className='font-semibold text-red-900 mb-1 text-sm'>
									Em caso de emergência médica:
								</h3>
								<p className='text-red-700 text-xs'>
									Se você está enfrentando uma emergência médica grave (como dor no peito intensa,
									dificuldade respiratória severa, perda de consciência), procure imediatamente
									o pronto-socorro mais próximo ou ligue para o SAMU (192).
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
