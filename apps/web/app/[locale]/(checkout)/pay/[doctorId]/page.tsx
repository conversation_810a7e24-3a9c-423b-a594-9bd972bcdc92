// app/[locale]/(checkout)/pay/[doctorId]/page.tsx

import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';

import { getCheckoutInitialData } from '../../../../../actions/checkout/get-initial-data';
import { CheckoutSteps } from '../../checkout/components/checkout-steps';
import { ON_DUTY_CONFIG } from 'api/constants/on-duty';

async function PayCheckoutFormLazy() {
  const mod = await import('../components/pay-checkout-form');
  return mod.PayCheckoutForm;
}

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Pagamento',
    description: 'Página de pagamento',
  };
}

interface PayPageProps {
  params: Promise<{
    locale: string;
    doctorId?: string;
  }> | { locale: string; doctorId?: string };
  searchParams: Promise<{
    date?: string;
    time?: string;
    isOnDuty?: string;
    urgencyLevel?: 'high' | 'medium' | 'low';
    partner?: string;
  }>;
}

export default async function PayPage({ params, searchParams }: PayPageProps) {
  const p = params instanceof Promise ? await params : params;
  const doctorId = p.doctorId;

  if (!doctorId) {
    return redirect('/');
  }

  const { date, time, isOnDuty: isOnDutyParam, urgencyLevel, partner } = await searchParams;

  const safeSearchParams = Object.fromEntries(
    Object.entries({ date, time, isOnDuty: isOnDutyParam, urgencyLevel, partner }).filter(
      ([_, value]) => value !== undefined && value !== 'undefined' && value !== ''
    )
  ) as Partial<Record<string, string>>;

  const isOnDuty = safeSearchParams.isOnDuty === 'true';

  let parsedUrgencyLevel = isOnDuty
    ? (safeSearchParams.urgencyLevel as 'high' | 'medium' | 'low' | undefined)
    : undefined;

  if (isOnDuty && safeSearchParams.partner && !parsedUrgencyLevel) {
    parsedUrgencyLevel = 'low';
  }

  const initialData = await getCheckoutInitialData({
    doctorId,
    ...(safeSearchParams.date && { date: safeSearchParams.date }),
    ...(safeSearchParams.time && { time: safeSearchParams.time }),
    isOnDuty,
    ...(parsedUrgencyLevel && { urgencyLevel: parsedUrgencyLevel }),
    ...(safeSearchParams.partner && { partner: safeSearchParams.partner }),
  } as any);

  const renderUrgencySelection = isOnDuty && !urgencyLevel && !safeSearchParams.partner;

  const urgencyLevels = [
    {
      id: 'low',
      name: ON_DUTY_CONFIG.LOW.label,
      color: 'bg-green-500',
      price: ON_DUTY_CONFIG.LOW.price,
      waitTime: `Aproximadamente ${ON_DUTY_CONFIG.LOW.waitTime} minutos`,
      description:
        'Para consultas de rotina, acompanhamento médico ou sintomas leves que não apresentam risco imediato.',
    },
    {
      id: 'medium',
      name: ON_DUTY_CONFIG.MEDIUM.label,
      color: 'bg-amber-500',
      price: ON_DUTY_CONFIG.MEDIUM.price,
      waitTime: `Aproximadamente ${ON_DUTY_CONFIG.MEDIUM.waitTime} minutos`,
      description:
        'Para condições que necessitam de atenção, mas não são emergenciais. Sintomas moderados ou problemas de saúde que precisam ser avaliados em breve.',
    },
    {
      id: 'high',
      name: ON_DUTY_CONFIG.HIGH.label,
      color: 'bg-red-500',
      price: ON_DUTY_CONFIG.HIGH.price,
      waitTime: `Aproximadamente ${ON_DUTY_CONFIG.HIGH.waitTime} minutos`,
      description:
        'Para situações que requerem atenção médica imediata, como sintomas intensos ou preocupações graves.',
    },
  ];

  if (renderUrgencySelection) {
    return (
      <div className='container max-w-xl my-8 px-4'>
        <CheckoutSteps currentStep='urgency' />
        <h1 className='text-2xl font-bold mb-6 text-center'>Selecione o nível de urgência para seu atendimento</h1>
        <p className='text-gray-600 mb-8 text-center'>
          O tempo de espera estimado e o valor variam de acordo com a urgência selecionada.
        </p>
        <div className='space-y-4'>
          {urgencyLevels.map((level) => (
            <a
              key={level.id}
              href={`/${p.locale}/pay/${doctorId}?${new URLSearchParams(
                Object.fromEntries(
                  Object.entries({ isOnDuty: 'true', urgencyLevel: level.id }).filter(
                    ([_, value]) => value !== undefined && value !== ''
                  )
                )
              )}`}
              className='block'
            >
              <div className={`border rounded-lg p-4 hover:border-primary hover:shadow-md transition-all cursor-pointer flex flex-col relative overflow-hidden group`}>
                <div className='absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity'></div>
                <div className='flex items-center gap-3 mb-2'>
                  <div className={`${level.color} text-white text-sm font-semibold py-1 px-3 rounded-full flex gap-1 items-center`}>
                    {level.name}
                  </div>
                  <div className='text-lg font-bold ml-auto'>R$ {level.price.toFixed(2)}</div>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2 text-gray-500 text-sm'>
                    <span className=''>Tempo de espera:<br />{level.waitTime}</span>
                  </div>
                  <div className='text-primary flex items-center gap-1 font-medium text-sm group-hover:translate-x-1 transition-transform'>
                    Selecionar
                  </div>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>
    );
  }

  const product = {
    id: initialData.doctor.id,
    title: `Consulta com Dr. ${initialData.doctor.name || 'Médico'}`,
    description: initialData.doctor.specialty || '',
    type: 'MEDICAL_CONSULTATION' as const,
    price: initialData.price,
    installmentsLimit: 1,
    enableInstallments: false,
    thumbnail: initialData.doctor.profileImage,
    checkoutType: 'DEFAULT' as const,
    acceptedPayments: ['CREDIT_CARD', 'PIX'],
    successUrl: '/checkout/success',
    cancelUrl: '/',
    scheduledAt: initialData.scheduledAt || undefined,
    duration: initialData.duration,
    doctor: {
      ...initialData.doctor,
      name: initialData.doctor.name || '',
      profileImage: initialData.doctor.profileImage || undefined,
    },
    isOnline: initialData.isOnline,
    serviceFee: initialData.serviceFee,
    isOnDuty: initialData.isOnDuty,
    urgencyLevel: initialData.urgencyLevel,
    partner: initialData.partner,
  };

  const ProductSummaryCard = (
    await import('../../checkout/components/checkout-summary-card')
  ).ProductSummaryCard;

  const PayCheckoutForm = await PayCheckoutFormLazy();

  return (
    <main className='flex flex-col min-h-screen'>
      <div className='container px-4 max-w-7xl mx-auto grow'>
        <div className='max-w-xl mx-auto mb-8 mt-8'>
          <CheckoutSteps currentStep='data' />
        </div>
        <div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
          <div className='order-2 md:order-1'>
            <PayCheckoutForm product={product} />
          </div>
          <div className='order-1 md:order-2 min-w-0'>
            <div className='md:hidden mb-6'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <ProductSummaryCard product={product} isMobile={true} />
              </Suspense>
            </div>
            <div className='hidden md:block'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <ProductSummaryCard product={product} isMobile={false} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}


