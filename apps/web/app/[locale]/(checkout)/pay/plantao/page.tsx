// app/[locale]/(checkout)/pay/plantao/page.tsx

import { Metadata } from 'next';
import Link from 'next/link';
import { Suspense } from 'react';
import { AlertTriangle, ArrowRight, CheckCircle2, Clock } from 'lucide-react';
import { CheckoutSteps } from '../../checkout/components/checkout-steps';
import { ON_DUTY_CONFIG } from 'api/constants/on-duty';

async function PayCheckoutFormLazy() {
  const mod = await import('../components/pay-checkout-form');
  return mod.PayCheckoutForm;
}

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Pagamento - Plantão Médico',
    description: 'Pagamento para atendimento de plantão',
  };
}

interface PlantaoPayPageProps {
  params: { locale: string };
  searchParams: Promise<{
    urgencyLevel?: 'high' | 'medium' | 'low';
    partner?: string;
    selectUrgency?: string;
  }>;
}

export default async function PlantaoPayPage({ params, searchParams }: PlantaoPayPageProps) {
  const sp = await searchParams;
  const urgencyLevel = sp?.urgencyLevel;
  const partner = sp?.partner;
  const selectUrgency = sp?.selectUrgency;

  if (!urgencyLevel || selectUrgency === 'true' || !['high', 'medium', 'low'].includes(urgencyLevel)) {
    return <UrgencySelectionPage params={params} partner={partner} />;
  }

  const urgencyConfig = ON_DUTY_CONFIG[urgencyLevel.toUpperCase() as keyof typeof ON_DUTY_CONFIG];

  const product = {
    id: 'plantao-medical-service',
    title: `Atendimento de Plantão - ${urgencyConfig.label}`,
    description: 'Atendimento médico imediato sem agendamento',
    type: 'MEDICAL_CONSULTATION' as const,
    price: urgencyConfig.price,
    installmentsLimit: 1,
    enableInstallments: false,
    thumbnail: '/images/plantao-medical.png',
    checkoutType: 'PLANTAO' as const,
    acceptedPayments: ['CREDIT_CARD', 'PIX'],
    successUrl: '/checkout/success',
    cancelUrl: '/pay/plantao',
    scheduledAt: undefined,
    duration: urgencyConfig.duration,
    isOnline: true,
    serviceFee: 0,
    isOnDuty: true,
    urgencyLevel,
    partner,
    doctor: {
      id: 'plantao-service',
      name: 'Médico de Plantão',
      profileImage: '/images/doctor-avatar.png',
      specialty: 'Clínica Geral',
    },
  };

  const ProductSummaryCard = (
    await import('../../checkout/components/checkout-summary-card')
  ).ProductSummaryCard;

  const PayCheckoutForm = await PayCheckoutFormLazy();

  return (
    <main className='flex flex-col min-h-screen'>
      <div className='container px-4 max-w-7xl mx-auto grow'>
        <div className='max-w-xl mx-auto mb-8 mt-8'>
          <CheckoutSteps currentStep='data' />
        </div>
        <div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
          <div className='order-2 md:order-1'>
            <PayCheckoutForm product={product} />
          </div>
          <div className='order-1 md:order-2 min-w-0'>
            <div className='md:hidden mb-6'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <ProductSummaryCard product={product} isMobile={true} />
              </Suspense>
            </div>
            <div className='hidden md:block'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <ProductSummaryCard product={product} isMobile={false} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

function UrgencySelectionPage({ params, partner }: { params: { locale: string }; partner?: string }) {
  const urgencyLevels = [
    {
      id: 'low',
      name: ON_DUTY_CONFIG.LOW.label,
      color: 'bg-green-500',
      price: ON_DUTY_CONFIG.LOW.price,
      waitTime: `${ON_DUTY_CONFIG.LOW.waitTime}`,
    },
    {
      id: 'medium',
      name: ON_DUTY_CONFIG.MEDIUM.label,
      color: 'bg-amber-500',
      price: ON_DUTY_CONFIG.MEDIUM.price,
      waitTime: `${ON_DUTY_CONFIG.MEDIUM.waitTime}`,
    },
    {
      id: 'high',
      name: ON_DUTY_CONFIG.HIGH.label,
      color: 'bg-red-500',
      price: ON_DUTY_CONFIG.HIGH.price,
      waitTime: `${ON_DUTY_CONFIG.HIGH.waitTime}`,
    },
  ] as const;

  return (
    <main className='flex flex-col min-h-screen'>
      <div className='container px-4 max-w-7xl mx-auto grow'>
        <div className='max-w-xl mx-auto mb-8 mt-8'>
          <CheckoutSteps currentStep='urgency' />
        </div>

        <div className='max-w-4xl mx-auto'>
          {/* Header */}
          <div className='text-center mb-8'>
            <h1 className='text-2xl font-bold text-gray-900 mb-2'>
              Selecione o nível de urgência para seu atendimento
            </h1>
            <p className='text-gray-500'>
              O tempo de espera e o valor variam de acordo com a urgência selecionada
            </p>
          </div>

          {/* Urgency Selection */}
          <div className='space-y-6'>
            {urgencyLevels.map((level) => (
              <Link
                key={level.id}
                href={`/${params.locale}/pay/plantao?urgencyLevel=${level.id}${
                  partner ? `&partner=${partner}` : ''
                }`}
                className='block'
              >
                <div className='border rounded-lg p-5 hover:border-primary hover:shadow-md transition-all cursor-pointer group bg-white'>
                  <div className='flex items-center justify-between mb-3'>
                    <div
                      className={`${level.color} text-white text-sm font-medium py-1.5 px-3 rounded-full flex gap-2 items-center`}
                    >
                      {level.id === 'high' && (
                        <AlertTriangle className='h-3.5 w-3.5' />
                      )}
                      {level.name}
                    </div>

                    <div className='text-lg font-bold text-gray-900'>
                      R$ {level.price.toFixed(2)}
                    </div>
                  </div>

                  {/* Footer with wait time and select button */}
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-2 text-gray-500 text-sm'>
                      <Clock className='h-4 w-4' />
                      <span>
                        Tempo de espera:<br />
                        Aproximadamente {level.waitTime} minutos
                      </span>
                    </div>

                    <div className='text-primary flex items-center gap-1 font-medium text-sm group-hover:translate-x-1 transition-transform'>
                      Selecionar
                      <ArrowRight className='h-4 w-4' />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Important Notes */}
          <div className='mt-8 bg-blue-50 border border-blue-100 rounded-lg p-5'>
            <h3 className='font-semibold text-blue-900 mb-2 text-sm'>
              📋 Informações importantes:
            </h3>
            <ul className='space-y-1.5 text-blue-800 text-xs'>
              <li className='flex items-start gap-2'>
                <CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
                <span>
                  O tempo de espera é estimado e pode variar dependendo da demanda atual
                </span>
              </li>
              <li className='flex items-start gap-2'>
                <CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
                <span>
                  Consulta realizada por chat, áudio ou vídeo conforme sua preferência
                </span>
              </li>
              <li className='flex items-start gap-2'>
                <CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
                <span>
                  Inclui prescrição médica digital e atestado (se necessário)
                </span>
              </li>
              <li className='flex items-start gap-2'>
                <CheckCircle2 className='h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0' />
                <span>
                  Retorno em até 24h sem custo adicional
                </span>
              </li>
            </ul>
          </div>

          {/* Emergency Notice */}
          <div className='mt-4 bg-red-50 border border-red-100 rounded-lg p-5 mb-8'>
            <div className='flex items-start gap-3'>
              <AlertTriangle className='h-5 w-5 text-red-500 mt-0.5 flex-shrink-0' />
              <div>
                <h3 className='font-semibold text-red-900 mb-1 text-sm'>
                  Em caso de emergência médica:
                </h3>
                <p className='text-red-700 text-xs'>
                  Se você está enfrentando uma emergência médica grave (como dor no peito intensa,
                  dificuldade respiratória severa, perda de consciência), procure imediatamente
                  o pronto-socorro mais próximo ou ligue para o SAMU (192).
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}


