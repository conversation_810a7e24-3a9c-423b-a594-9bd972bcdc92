// app/[locale]/(checkout)/pay/assinatura/page.tsx

import { Metadata } from 'next';
import { Suspense } from 'react';
import { SubscriptionSummaryCard } from './components/subscription-summary-card';
import { CheckoutSteps } from '../../checkout/components/checkout-steps';

async function PayCheckoutFormLazy() {
  const mod = await import('../components/pay-checkout-form');
  return mod.PayCheckoutForm;
}

// static import above

export const metadata: Metadata = {
  title: 'Assinatura - ZapVida',
  description: 'Assine um plano ZapVida e tenha 2 consultas por mês',
};

export const dynamic = 'force-dynamic';

interface AssinaturaPayPageProps {
  params: { locale: string };
  searchParams?: Promise<{ plan?: string }>; // ex: ?plan=zapvida-sempre
}

const PLANS = {
  'zapvida-sempre': {
    id: 'zapvida-sempre',
    name: 'ZapVida Sempre',
    price: 49.0,
    consultationsIncluded: 2,
    cycle: 'MONTHLY' as const,
    features: [
      '2 consultas por mês',
      'Atendimento por vídeo, áudio ou chat',
      'Receitas e atestados digitais',
      'Cancelamento a qualquer momento',
    ],
  },
};

export default async function AssinaturaPayPage({ params, searchParams }: AssinaturaPayPageProps) {
  const sp = (await searchParams) || {};
  const selectedPlanId = (sp.plan || 'zapvida-sempre') as keyof typeof PLANS;
  const plan = PLANS[selectedPlanId] || PLANS['zapvida-sempre'];

  const product = {
    id: plan.id,
    title: `Assinatura - ${plan.name}`,
    description: `${plan.consultationsIncluded} consultas por mês`,
    type: 'MEDICAL_CONSULTATION' as const,
    price: plan.price,
    installmentsLimit: 1,
    enableInstallments: false,
    thumbnail: '/images/subscription.png',
    checkoutType: 'SUBSCRIPTION' as const,
    acceptedPayments: ['CREDIT_CARD', 'PIX'] as const, // sem boleto
    successUrl: '/checkout/success',
    cancelUrl: '/assinaturas',
    scheduledAt: undefined,
    duration: undefined,
    isOnline: true,
    serviceFee: 0,
    plan,
  } as const;

  const PayCheckoutForm = await PayCheckoutFormLazy();

  return (
    <main className='flex flex-col min-h-screen'>
      <div className='container px-4 max-w-7xl mx-auto grow'>
        <div className='max-w-xl mx-auto mb-8 mt-8'>
          <CheckoutSteps currentStep='data' />
        </div>
        <div className='grid md:grid-cols-[1fr_400px] gap-6 pb-10'>
          <div className='order-2 md:order-1'>
            <PayCheckoutForm product={product as any} />
          </div>
          <div className='order-1 md:order-2 min-w-0'>
            <div className='md:hidden mb-6'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <SubscriptionSummaryCard product={product as any} isMobile={true} />
              </Suspense>
            </div>
            <div className='hidden md:block'>
              <Suspense fallback={<div className='h-48 border rounded-lg animate-pulse bg-gray-100'></div>}>
                <SubscriptionSummaryCard product={product as any} isMobile={false} />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}


