"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { Info, CreditCard, CheckCircle2 } from 'lucide-react';

interface SubscriptionProduct {
  id: string;
  title: string;
  description?: string | null;
  type: 'MEDICAL_CONSULTATION';
  price: number;
  thumbnail?: string | null;
  checkoutType: 'SUBSCRIPTION';
  acceptedPayments: readonly ['CREDIT_CARD', 'PIX'];
  successUrl: string;
  cancelUrl: string;
  isOnline: boolean;
  serviceFee: number;
  plan: {
    id: string;
    name: string;
    price: number;
    consultationsIncluded: number;
    cycle: 'MONTHLY' | 'YEARLY';
    features: string[];
  };
}

export function SubscriptionSummaryCard({ product, isMobile }: { product: SubscriptionProduct; isMobile: boolean }) {
  const calculateTotal = () => Number(product.price) + (product.serviceFee || 0);

  const Content = () => (
    <div className='space-y-5'>
      <div className='bg-blue-50 rounded-lg p-4 border border-blue-100'>
        <h4 className='text-sm font-semibold text-blue-800 mb-3 flex items-center gap-1.5'>
          <Info className='h-4 w-4' />
          Detalhes da Assinatura
        </h4>
        <div className='space-y-3'>
          <div className='text-sm text-gray-700'>
            <span className='font-medium'>{product.plan.name}</span> – {product.plan.consultationsIncluded} consultas/mês
          </div>
          <ul className='space-y-2'>
            {product.plan.features.map((f) => (
              <li key={f} className='flex items-center gap-2 text-sm text-gray-700'>
                <CheckCircle2 className='h-4 w-4 text-green-600' />
                {f}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className='rounded-lg bg-gray-50 p-4 text-sm text-gray-600 border border-gray-100'>
        <h4 className='text-sm font-semibold text-gray-700 mb-2 flex items-center gap-1.5'>
          <Info className='h-4 w-4' />
          Informações Importantes
        </h4>
        <p>
          Sua assinatura é renovada automaticamente {product.plan.cycle === 'MONTHLY' ? 'mensalmente' : 'anualmente'}. Você pode cancelar a qualquer momento.
        </p>
      </div>

      <div className='space-y-3 mt-4'>
        <Separator />
        <div className='flex justify-between items-center text-sm text-gray-700'>
          <span>Assinatura</span>
          <span>R$ {product.price.toFixed(2)}</span>
        </div>
        {product.serviceFee !== undefined && (
          <div className='flex justify-between items-center text-sm text-gray-700'>
            <span>Taxa de serviço</span>
            <span>{product.serviceFee === 0 ? 'Grátis' : `R$ ${product.serviceFee.toFixed(2)}`}</span>
          </div>
        )}
        <div className='flex justify-between items-center pt-2 border-t border-gray-100 mt-2'>
          <span className='text-gray-700 font-medium flex items-center gap-1.5'>
            <CreditCard className='h-4 w-4 text-primary' />
            Total
          </span>
          <span className='text-lg font-bold text-primary'>R$ {calculateTotal().toFixed(2)}</span>
        </div>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Card className='bg-white shadow-sm border-gray-100'>
        <CardContent className='px-4 pb-4 pt-4'>
          <Content />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='border-gray-100 shadow-sm'>
      <CardHeader className='p-5 border-b border-gray-100 bg-gray-50/80'>
        <CardTitle className='text-lg flex items-center gap-1.5'>
          <Info className='h-5 w-5 text-primary' />
          Resumo da Assinatura
        </CardTitle>
      </CardHeader>
      <CardContent className='p-5'>
        <Content />
      </CardContent>
    </Card>
  );
}
