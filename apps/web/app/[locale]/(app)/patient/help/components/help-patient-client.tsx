"use client";

import { useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	HelpCircle,
	Search,
	MessageSquare,
	Phone,
	Mail,
	Clock,
	ChevronDown,
	ChevronRight,
	Book,
	Headphones,
	FileText,
	Video,
	Calendar,
	CreditCard,
	Shield,
	Smartphone,
	Users,
	Star
} from "lucide-react";
import { User } from "@prisma/client";

interface HelpPatientClientProps {
	user: User;
}

interface FAQ {
	id: string;
	question: string;
	answer: string;
	category: string;
	helpful: number;
}

interface HelpCategory {
	id: string;
	name: string;
	description: string;
	icon: any;
	color: string;
	articles: number;
}

export function HelpPatientClient({ user }: HelpPatientClientProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("");
	const [expandedFAQ, setExpandedFAQ] = useState<string>("");

	const helpCategories: HelpCategory[] = [
		{
			id: "getting-started",
			name: "Primeiros Passos",
			description: "Como usar a plataforma pela primeira vez",
			icon: Book,
			color: "blue",
			articles: 8
		},
		{
			id: "appointments",
			name: "Consultas e Agendamentos",
			description: "Dúvidas sobre como marcar e participar de consultas",
			icon: Calendar,
			color: "green",
			articles: 12
		},
		{
			id: "payments",
			name: "Pagamentos e Planos",
			description: "Informações sobre cobranças e assinaturas",
			icon: CreditCard,
			color: "purple",
			articles: 6
		},
		{
			id: "technical",
			name: "Suporte Técnico",
			description: "Problemas técnicos e soluções",
			icon: Smartphone,
			color: "orange",
			articles: 15
		},
		{
			id: "privacy",
			name: "Privacidade e Segurança",
			description: "Como protegemos seus dados médicos",
			icon: Shield,
			color: "red",
			articles: 5
		},
		{
			id: "plantao",
			name: "Plantão Médico",
			description: "Como funciona o atendimento 24h",
			icon: Clock,
			color: "yellow",
			articles: 9
		}
	];

	const faqs: FAQ[] = [
		{
			id: "faq_001",
			question: "Como agendar minha primeira consulta?",
			answer: "Para agendar sua primeira consulta, vá até a seção 'Agendar Consulta' no menu principal. Escolha a especialidade desejada, selecione um médico disponível e escolha o melhor horário para você. Após confirmar, você receberá todas as informações por email e WhatsApp.",
			category: "appointments",
			helpful: 45
		},
		{
			id: "faq_002",
			question: "O que é o Plantão Médico 24h?",
			answer: "O Plantão Médico é um serviço de atendimento médico de urgência disponível 24 horas por dia, 7 dias por semana. Você pode solicitar atendimento a qualquer momento e será conectado rapidamente com um médico disponível para tratar casos que precisam de atenção imediata.",
			category: "plantao",
			helpful: 38
		},
		{
			id: "faq_003",
			question: "Como funciona a cobrança da assinatura?",
			answer: "A cobrança da assinatura ZapVida Sempre é feita mensalmente por R$ 49,00. O pagamento é processado automaticamente no cartão de crédito cadastrado. Você pode cancelar a qualquer momento e continuará tendo acesso aos serviços até o final do período pago.",
			category: "payments",
			helpful: 52
		},
		{
			id: "faq_004",
			question: "Minhas informações médicas estão seguras?",
			answer: "Sim, levamos a segurança dos seus dados muito a sério. Utilizamos criptografia de ponta a ponta, certificações de segurança internacionais e seguimos todas as normas da LGPD. Seus dados médicos são acessíveis apenas por você e pelos médicos que te atendem.",
			category: "privacy",
			helpful: 41
		},
		{
			id: "faq_005",
			question: "Posso usar o ZapVida no meu celular?",
			answer: "Sim! Nossa plataforma é totalmente otimizada para dispositivos móveis. Você pode acessar através do navegador do seu smartphone ou tablet. Em breve teremos também nosso aplicativo nativo disponível na App Store e Google Play.",
			category: "technical",
			helpful: 29
		},
		{
			id: "faq_006",
			question: "Como faço para cancelar uma consulta?",
			answer: "Você pode cancelar uma consulta até 24 horas antes do horário agendado sem custos. Vá na seção 'Minhas Consultas', encontre a consulta que deseja cancelar e clique em 'Cancelar'. Cancelamentos com menos de 24 horas podem ter cobrança.",
			category: "appointments",
			helpful: 33
		}
	];

	const filteredFAQs = faqs.filter(faq => {
		const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
			faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory = selectedCategory === "" || faq.category === selectedCategory;
		return matchesSearch && matchesCategory;
	});

	const handleFAQToggle = (faqId: string) => {
		setExpandedFAQ(expandedFAQ === faqId ? "" : faqId);
	};

	const handleHelpful = (faqId: string) => {
		// Simular voto útil
		console.log(`FAQ ${faqId} marcada como útil`);
	};

	return (
		<div className="container mx-auto py-6 px-4 max-w-6xl">
			{/* Header */}
			<div className="text-center mb-12">
				<div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<HelpCircle className="w-8 h-8 text-blue-600" />
				</div>
				<h1 className="text-3xl font-bold text-gray-900 mb-2">
					Central de Ajuda
				</h1>
				<p className="text-gray-600 text-lg">
					Encontre respostas para suas dúvidas ou entre em contato conosco
				</p>
			</div>

			{/* Search */}
			<div className="relative mb-8 max-w-2xl mx-auto">
				<Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
				<input
					type="text"
					placeholder="Digite sua dúvida aqui..."
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
				/>
			</div>

			{/* Quick Contact */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-12">
				<Card className="text-center hover:shadow-md transition-shadow cursor-pointer">
					<CardContent className="p-6">
						<div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
							<MessageSquare className="w-6 h-6 text-green-600" />
						</div>
						<h3 className="font-semibold mb-2">Chat Online</h3>
						<p className="text-sm text-gray-600 mb-3">
							Fale conosco em tempo real
						</p>
						<Badge className="bg-green-100 text-green-800">Disponível agora</Badge>
					</CardContent>
				</Card>

				<Card className="text-center hover:shadow-md transition-shadow cursor-pointer">
					<CardContent className="p-6">
						<div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
							<Phone className="w-6 h-6 text-blue-600" />
						</div>
						<h3 className="font-semibold mb-2">Telefone</h3>
						<p className="text-sm text-gray-600 mb-3">
							(11) 3000-1234
						</p>
						<Badge variant="outline">Seg-Sex: 8h às 18h</Badge>
					</CardContent>
				</Card>

				<Card className="text-center hover:shadow-md transition-shadow cursor-pointer">
					<CardContent className="p-6">
						<div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
							<Mail className="w-6 h-6 text-purple-600" />
						</div>
						<h3 className="font-semibold mb-2">Email</h3>
						<p className="text-sm text-gray-600 mb-3">
							<EMAIL>
						</p>
						<Badge className="bg-purple-100 text-purple-800">Resposta em 24h</Badge>
					</CardContent>
				</Card>
			</div>

			{/* Help Categories */}
			<div className="mb-12">
				<h2 className="text-2xl font-bold text-gray-900 mb-6">Categorias de Ajuda</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{helpCategories.map(category => {
						const Icon = category.icon;
						return (
							<Card
								key={category.id}
								className={`cursor-pointer hover:shadow-md transition-shadow ${
									selectedCategory === category.id ? "border-blue-500 bg-blue-50" : ""
								}`}
								onClick={() => setSelectedCategory(selectedCategory === category.id ? "" : category.id)}
							>
								<CardContent className="p-6">
									<div className="flex items-start gap-4">
										<div className={`w-12 h-12 bg-${category.color}-100 rounded-lg flex items-center justify-center flex-shrink-0`}>
											<Icon className={`w-6 h-6 text-${category.color}-600`} />
										</div>
										<div className="flex-1">
											<h3 className="font-semibold text-gray-900 mb-1">
												{category.name}
											</h3>
											<p className="text-sm text-gray-600 mb-2">
												{category.description}
											</p>
											<Badge variant="outline" className="text-xs">
												{category.articles} artigos
											</Badge>
										</div>
									</div>
								</CardContent>
							</Card>
						);
					})}
				</div>
			</div>

			{/* FAQ Section */}
			<div className="mb-12">
				<div className="flex items-center justify-between mb-6">
					<h2 className="text-2xl font-bold text-gray-900">
						Perguntas Frequentes
					</h2>
					{selectedCategory && (
						<Button
							variant="outline"
							size="sm"
							onClick={() => setSelectedCategory("")}
						>
							Ver Todas
						</Button>
					)}
				</div>

				{filteredFAQs.length > 0 ? (
					<div className="space-y-4">
						{filteredFAQs.map(faq => (
							<Card key={faq.id} className="overflow-hidden">
								<CardHeader
									className="cursor-pointer hover:bg-gray-50 transition-colors"
									onClick={() => handleFAQToggle(faq.id)}
								>
									<div className="flex items-center justify-between">
										<h3 className="font-semibold text-gray-900 pr-4">
											{faq.question}
										</h3>
										{expandedFAQ === faq.id ? (
											<ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
										) : (
											<ChevronRight className="w-5 h-5 text-gray-500 flex-shrink-0" />
										)}
									</div>
								</CardHeader>

								{expandedFAQ === faq.id && (
									<CardContent className="pt-0">
										<div className="border-t pt-4">
											<p className="text-gray-700 mb-4 leading-relaxed">
												{faq.answer}
											</p>
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-2 text-sm text-gray-500">
													<span>{faq.helpful} pessoas acharam isso útil</span>
												</div>
												<Button
													size="sm"
													variant="outline"
													onClick={() => handleHelpful(faq.id)}
													className="text-green-600 border-green-300 hover:bg-green-50"
												>
													<Star className="w-4 h-4 mr-1" />
													Útil
												</Button>
											</div>
										</div>
									</CardContent>
								)}
							</Card>
						))}
					</div>
				) : (
					<div className="text-center py-12">
						<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
							<Search className="w-8 h-8 text-gray-400" />
						</div>
						<h3 className="text-lg font-semibold text-gray-900 mb-2">
							Nenhuma pergunta encontrada
						</h3>
						<p className="text-gray-600 mb-4">
							Tente buscar com outros termos ou navegue pelas categorias
						</p>
						<Button variant="outline">
							Fazer uma Pergunta
						</Button>
					</div>
				)}
			</div>

			{/* Additional Resources */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Video className="w-5 h-5 text-blue-600" />
							Tutoriais em Vídeo
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-gray-600 mb-4">
							Aprenda a usar a plataforma com nossos vídeos explicativos
						</p>
						<Button variant="outline" className="w-full">
							<Video className="w-4 h-4 mr-2" />
							Assistir Tutoriais
						</Button>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<FileText className="w-5 h-5 text-green-600" />
							Guias Detalhados
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-gray-600 mb-4">
							Documentação completa sobre todas as funcionalidades
						</p>
						<Button variant="outline" className="w-full">
							<FileText className="w-4 h-4 mr-2" />
							Ver Documentação
						</Button>
					</CardContent>
				</Card>
			</div>

			{/* Contact Form CTA */}
			<Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
				<CardContent className="p-8 text-center">
					<div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<Headphones className="w-8 h-8 text-blue-600" />
					</div>
					<h3 className="text-xl font-bold text-gray-900 mb-2">
						Não encontrou o que procurava?
					</h3>
					<p className="text-gray-600 mb-6">
						Nossa equipe de suporte está pronta para ajudar você com qualquer dúvida
					</p>
					<div className="flex gap-4 justify-center">
						<Button className="bg-blue-600 hover:bg-blue-700">
							<MessageSquare className="w-4 h-4 mr-2" />
							Iniciar Chat
						</Button>
						<Button variant="outline">
							<Mail className="w-4 h-4 mr-2" />
							Enviar Email
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
