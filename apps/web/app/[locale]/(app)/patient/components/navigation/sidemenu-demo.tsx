"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Menu, X } from "lucide-react";
import { PatientSidemenu } from "./patient-sidemenu";

// Mock user para demonstração
const mockUser = {
	id: "demo-user",
	name: "<PERSON><PERSON>",
	email: "<EMAIL>",
	avatarUrl: "",
	role: "PATIENT" as const,
	onboardingComplete: true,
	createdAt: new Date(),
	updatedAt: new Date()
};

export function SidemenuDemo() {
	const [showSidemenu, setShowSidemenu] = useState(false);

	return (
		<div className="min-h-screen bg-gray-50 p-8">
			{/* Header de demonstração */}
			<header className="bg-white border-b border-gray-200 mb-8">
				<div className="max-w-7xl mx-auto px-4 py-3">
					<div className="flex items-center justify-between">
						<div className="flex items-center space-x-4">
							<h1 className="text-xl font-bold text-gray-900">ZapVida Saúde</h1>
							<span className="text-sm text-gray-500">Demonstração do Sidemenu</span>
						</div>

						{/* Botão do menu */}
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setShowSidemenu(true)}
							className="lg:hidden"
						>
							<Menu className="w-5 h-5" />
						</Button>
					</div>
				</div>
			</header>

			{/* Conteúdo principal */}
			<main className="max-w-4xl mx-auto">
				<div className="bg-white rounded-lg shadow-sm p-6 mb-6">
					<h2 className="text-lg font-semibold text-gray-900 mb-4">
						🎉 Sidemenu Implementado com Sucesso!
					</h2>

					<div className="space-y-4">
						<div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
							<h3 className="font-medium text-blue-900 mb-2">✨ Características do Novo Sidemenu</h3>
							<ul className="text-sm text-blue-800 space-y-1">
								<li>• Animações suaves e responsivas</li>
								<li>• Interface moderna com gradientes</li>
								<li>• Indicadores visuais por seção</li>
								<li>• Micro-interações no hover</li>
								<li>• Navegação intuitiva e organizada</li>
							</ul>
						</div>

						<div className="p-4 bg-green-50 rounded-lg border border-green-200">
							<h3 className="font-medium text-green-900 mb-2">🚀 Como Usar</h3>
							<ol className="text-sm text-green-800 space-y-1">
								<li>1. Clique no botão de menu (hamburger) no header</li>
								<li>2. O sidemenu deslizará da esquerda com animação</li>
								<li>3. Navegue pelas diferentes seções</li>
								<li>4. Feche clicando no X ou clicando fora</li>
								<li>5. Use a tecla ESC para fechar rapidamente</li>
							</ol>
						</div>

						<div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
							<h3 className="font-medium text-purple-900 mb-2">📱 Responsividade</h3>
							<p className="text-sm text-purple-800">
								O sidemenu é totalmente responsivo e se adapta a diferentes tamanhos de tela.
								Em dispositivos móveis, ocupa até 85% da largura da tela para uma experiência otimizada.
							</p>
						</div>
					</div>
				</div>

				{/* Instruções de desenvolvimento */}
				<div className="bg-white rounded-lg shadow-sm p-6">
					<h2 className="text-lg font-semibold text-gray-900 mb-4">
						🛠️ Para Desenvolvedores
					</h2>

					<div className="space-y-4">
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Arquivos Criados/Modificados:</h3>
							<ul className="text-sm text-gray-600 space-y-1 font-mono">
								<li>• <code>patient-sidemenu.tsx</code> - Novo componente</li>
								<li>• <code>patient-header.tsx</code> - Header atualizado</li>
								<li>• <code>README.md</code> - Documentação completa</li>
								<li>• <code>sidemenu-demo.tsx</code> - Este arquivo de demonstração</li>
							</ul>
						</div>

						<div>
							<h3 className="font-medium text-gray-900 mb-2">Próximos Passos:</h3>
							<ol className="text-sm text-gray-600 space-y-1">
								<li>1. Testar em diferentes dispositivos e navegadores</li>
								<li>2. Integrar com sistema de notificações real</li>
								<li>3. Adicionar analytics para tracking de uso</li>
								<li>4. Implementar testes automatizados</li>
								<li>5. Otimizar performance se necessário</li>
							</ol>
						</div>
					</div>
				</div>
			</main>

			{/* Sidemenu */}
			<PatientSidemenu
				user={mockUser}
				isOpen={showSidemenu}
				onClose={() => setShowSidemenu(false)}
			/>
		</div>
	);
}
