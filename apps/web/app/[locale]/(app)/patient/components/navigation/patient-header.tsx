"use client";

import type { User } from 'database';
import { <PERSON>, Settings, Menu, User2, Home, Calendar, MessageCircle, Heart, CreditCard, FileText, Plus, Clock, LogOut, User2Icon } from "lucide-react";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import Link from "next/link";
import { Logo } from "@shared/components/Logo";
import { useState } from "react";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";
import { PlantaoLink } from "../plantao-link";
import { PatientSidemenu } from "./patient-sidemenu";

const navigationItems = [
	{
		id: "dashboard",
		label: "In<PERSON>cio",
		icon: Home,
		href: "/patient/dashboard",
		activePatterns: ["/patient/dashboard"]
	},
	{
		id: "appointments",
		label: "Consultas",
		icon: Calendar,
		href: "/patient/appointments",
		activePatterns: ["/patient/appointments"]
	},
	{
		id: "zapchat",
		label: "ZapChat",
		icon: MessageCircle,
		href: "/patient/zapchat",
		activePatterns: ["/patient/zapchat", "/patient/chat", "/patient/chats", "/patient/plantao"]
	},

	{
		id: "profile",
		label: "Conta",
		icon: User2Icon,
		href: "/patient/profile",
		activePatterns: ["/patient/profile"]
	},

];

interface PatientHeaderProps {
	user: User;
}

export function PatientHeader({ user }: PatientHeaderProps) {
	const [showSidemenu, setShowSidemenu] = useState(false);
	const pathname = usePathname();

	const getUserInitials = (name: string) => {
		return name
			.split(" ")
			.map(n => n[0])
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const isActive = (patterns: string[]) => {
		if (!pathname) return false;
		return patterns.some(pattern => pathname.startsWith(pattern));
	};

	return (
		<header className="bg-white border-b border-gray-200 sticky top-0 z-40">
			{/* Header principal */}
			<div className="max-w-7xl mx-auto px-4 py-3">
				<div className="flex items-center justify-between">
					{/* Menu Hamburger - Mobile (esquerda) */}
					<div className="lg:hidden">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setShowSidemenu(true)}
						>
							<Menu className="w-5 h-5" />
						</Button>
					</div>

					{/* Logo ZapVida - Centralizado no mobile, esquerda no desktop */}
					<div className="flex items-center lg:flex-none">
						<Link href="/patient/dashboard">
							<Logo />
						</Link>
					</div>

					{/* Menu de navegação centralizado - Apenas desktop */}
					<nav className="hidden lg:flex items-center space-x-1 flex-1 justify-center">
						{navigationItems.map((item) => {
							const Icon = item.icon;
							const active = isActive(item.activePatterns);

							return (
								<Link
									key={item.id}
									href={item.href}
									className={cn(
										"flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
										"hover:bg-gray-50 hover:text-blue-600",
										active
											? "text-blue-600 bg-blue-50 border border-blue-200"
											: "text-gray-600"
									)}
								>
									<Icon className={cn(
										"w-4 h-4",
										active && "text-blue-600"
									)} />
									<span>{item.label}</span>
								</Link>
							);
						})}
					</nav>

					{/* Ações do header - Direita */}
					<div className="flex items-center space-x-2">
						{/* Menu do usuário */}
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="sm" className="flex items-center space-x-2">
									<Avatar className="w-8 h-8">
										<AvatarImage src={user.avatarUrl || ""} alt={user.name || ""} />
										<AvatarFallback className="text-xs">
											{getUserInitials(user.name || "U")}
										</AvatarFallback>
									</Avatar>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end" className="w-56">
								<div className="flex items-center space-x-2 p-2">
									<Avatar className="w-8 h-8">
										<AvatarImage src={user.avatarUrl || ""} alt={user.name || ""} />
										<AvatarFallback className="text-xs">
											{getUserInitials(user.name || "U")}
										</AvatarFallback>
									</Avatar>
									<div className="flex flex-col space-y-1">
										<p className="text-sm font-medium">{user.name}</p>
										<p className="text-xs text-gray-500">{user.email}</p>
									</div>
								</div>
								<DropdownMenuSeparator />
								<DropdownMenuItem asChild>
									<Link href="/patient/profile">
										<User2 className="w-4 h-4 mr-2" />
										Meu Perfil
									</Link>
								</DropdownMenuItem>
								<DropdownMenuItem asChild>
									<Link href="/patient/settings">
										<Settings className="w-4 h-4 mr-2" />
										Configurações
									</Link>
								</DropdownMenuItem>
								<DropdownMenuSeparator />
								<DropdownMenuItem asChild>
									<Link href="/auth/logout">
										Sair
									</Link>
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</div>

			{/* Sidemenu */}
			<PatientSidemenu
				user={user}
				isOpen={showSidemenu}
				onClose={() => setShowSidemenu(false)}
			/>
		</header>
	);
}
