"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { <PERSON>, AlertTriangle, Target, Heart, TrendingUp, Clock, FileText } from "lucide-react";
import { cn } from "@ui/lib";

interface AIInsights {
	riskLevel: 'low' | 'medium' | 'high';
	recommendedSpecialties: string[];
	prioritySymptoms: string[];
	lifestyleRecommendations: string[];
	urgencyScore: number;
	personalizedMessage: string;
	doctorNotes: {
		patientProfile: string;
		keySymptoms: string[];
		recommendedApproach: string;
		followUpSuggestions: string[];
	};
}

interface AIInsightsPanelProps {
	insights: AIInsights;
	className?: string;
	viewMode?: 'patient' | 'doctor';
}

export function AIInsightsPanel({ insights, className, viewMode = 'patient' }: AIInsightsPanelProps) {
	const getRiskLevelColor = (level: string) => {
		switch (level) {
			case 'high': return 'text-red-600 bg-red-50 border-red-200';
			case 'medium': return 'text-orange-600 bg-orange-50 border-orange-200';
			default: return 'text-green-600 bg-green-50 border-green-200';
		}
	};

	const getRiskLevelText = (level: string) => {
		switch (level) {
			case 'high': return 'Alto';
			case 'medium': return 'Médio';
			default: return 'Baixo';
		}
	};

	if (viewMode === 'doctor') {
		return (
			<div className={cn("space-y-4", className)}>
				{/* Header para médicos */}
				<div className="flex items-center gap-2 mb-4">
					<Brain className="w-5 h-5 text-purple-600" />
					<h3 className="text-lg font-semibold text-gray-900">
						Insights de IA - Perfil do Paciente
					</h3>
				</div>

				{/* Perfil do Paciente */}
				<Card>
					<CardHeader>
						<CardTitle className="text-base flex items-center gap-2">
							<FileText className="w-4 h-4 text-blue-600" />
							Perfil do Paciente
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-sm text-gray-700 leading-relaxed">
							{insights.doctorNotes.patientProfile}
						</p>
					</CardContent>
				</Card>

				{/* Nível de Risco e Urgência */}
				<div className="grid grid-cols-2 gap-4">
					<Card>
						<CardContent className="pt-4">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-gray-600">Nível de Risco</span>
								<Badge className={cn("text-xs", getRiskLevelColor(insights.riskLevel))}>
									{getRiskLevelText(insights.riskLevel)}
								</Badge>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="pt-4">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-gray-600">Urgência</span>
								<div className="flex items-center gap-1">
									<Clock className="w-4 h-4 text-gray-500" />
									<span className="text-sm font-semibold">{insights.urgencyScore}/5</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Sintomas Chave */}
				<Card>
					<CardHeader>
						<CardTitle className="text-base flex items-center gap-2">
							<AlertTriangle className="w-4 h-4 text-orange-600" />
							Sintomas Chave
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-2">
							{insights.doctorNotes.keySymptoms.map((symptom, index) => (
								<div key={index} className="flex items-center gap-2">
									<div className="w-2 h-2 bg-orange-500 rounded-full" />
									<span className="text-sm text-gray-700">{symptom}</span>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Abordagem Recomendada */}
				<Card>
					<CardHeader>
						<CardTitle className="text-base flex items-center gap-2">
							<Target className="w-4 h-4 text-blue-600" />
							Abordagem Recomendada
						</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-sm text-gray-700 leading-relaxed">
							{insights.doctorNotes.recommendedApproach}
						</p>
					</CardContent>
				</Card>

				{/* Especialidades Sugeridas */}
				<Card>
					<CardHeader>
						<CardTitle className="text-base flex items-center gap-2">
							<TrendingUp className="w-4 h-4 text-green-600" />
							Especialidades Sugeridas
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex flex-wrap gap-2">
							{insights.recommendedSpecialties.map((specialty, index) => (
								<Badge key={index} variant="secondary" className="text-xs">
									{specialty}
								</Badge>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Sugestões de Acompanhamento */}
				<Card>
					<CardHeader>
						<CardTitle className="text-base flex items-center gap-2">
							<Clock className="w-4 h-4 text-purple-600" />
							Sugestões de Acompanhamento
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="space-y-2">
							{insights.doctorNotes.followUpSuggestions.map((suggestion, index) => (
								<li key={index} className="flex items-start gap-2 text-sm">
									<div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
									<span className="text-gray-700">{suggestion}</span>
								</li>
							))}
						</ul>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Visualização para pacientes
	return (
		<div className={cn("space-y-4", className)}>
			{/* Header */}
			<div className="flex items-center gap-2 mb-4">
				<Brain className="w-5 h-5 text-purple-600" />
				<h3 className="text-lg font-semibold text-gray-900">
					Suas Recomendações Personalizadas
				</h3>
			</div>

			{/* Mensagem Personalizada */}
			<Card className="border-purple-200 bg-purple-50">
				<CardContent className="pt-4">
					<p className="text-sm text-purple-800 leading-relaxed">
						{insights.personalizedMessage}
					</p>
				</CardContent>
			</Card>

			{/* Especialidades Recomendadas */}
			<Card>
				<CardHeader>
					<CardTitle className="text-base flex items-center gap-2">
						<Target className="w-4 h-4 text-blue-600" />
						Especialistas Recomendados
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex flex-wrap gap-2">
						{insights.recommendedSpecialties.map((specialty, index) => (
							<Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700">
								{specialty}
							</Badge>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Sintomas Prioritários */}
			<Card>
				<CardHeader>
					<CardTitle className="text-base flex items-center gap-2">
						<Heart className="w-4 h-4 text-red-600" />
						Pontos de Atenção
					</CardTitle>
				</CardHeader>
				<CardContent>
					<ul className="space-y-2">
						{insights.prioritySymptoms.map((symptom, index) => (
							<li key={index} className="flex items-center gap-2 text-sm">
								<div className="w-2 h-2 bg-red-500 rounded-full" />
								<span className="text-gray-700">{symptom}</span>
							</li>
						))}
					</ul>
				</CardContent>
			</Card>

			{/* Recomendações de Estilo de Vida */}
			<Card>
				<CardHeader>
					<CardTitle className="text-base flex items-center gap-2">
						<TrendingUp className="w-4 h-4 text-green-600" />
						Dicas Personalizadas
					</CardTitle>
				</CardHeader>
				<CardContent>
					<ul className="space-y-3">
						{insights.lifestyleRecommendations.map((rec, index) => (
							<li key={index} className="flex items-start gap-2 text-sm">
								<div className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0">
									✓
								</div>
								<span className="text-gray-700">{rec}</span>
							</li>
						))}
					</ul>
				</CardContent>
			</Card>

			{/* Nível de Urgência */}
			<Card>
				<CardContent className="pt-4">
					<div className="flex items-center justify-between">
						<span className="text-sm font-medium text-gray-600">Nível de Urgência</span>
						<div className="flex items-center gap-2">
							<div className="flex gap-1">
								{[1, 2, 3, 4, 5].map((level) => (
									<div
										key={level}
										className={cn(
											"w-3 h-3 rounded-full",
											level <= insights.urgencyScore
												? "bg-orange-500"
												: "bg-gray-200"
										)}
									/>
								))}
							</div>
							<span className="text-sm font-semibold text-gray-700">
								{insights.urgencyScore}/5
							</span>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
