"use client";

import { But<PERSON> } from "./button";
import { Filter, Plus } from "lucide-react";
import {
	Sheet,
	SheetContent,
	SheetDes<PERSON>,
	SheetHeader,
	SheetTitle,
	SheetTrigger,
} from "@ui/components/sheet";
import { useState } from "react";

interface PageHeaderProps {
	title: string;
	subtitle?: string;
	showFilter?: boolean;
	showNewButton?: boolean;
	filterContent?: React.ReactNode;
	onNewButtonClick?: () => void;
	newButtonText?: string;
	newButtonHref?: string;
	className?: string;
}

export function PageHeader({
	title,
	subtitle,
	showFilter = false,
	showNewButton = false,
	filterContent,
	onNewButtonClick,
	newButtonText = "Nova Consulta",
	newButtonHref,
	className = ""
}: PageHeaderProps) {
	const [isFilterOpen, setIsFilterOpen] = useState(false);

	const NewButton = () => {
		if (newButtonHref) {
			return (
				<a href={newButtonHref}>
					<Button size="sm" className="w-full sm:w-auto text-white py-2.5 px-4 text-sm sm:text-base">
						<Plus className="h-4 w-4 mr-2" />
						{newButtonText}
					</Button>
				</a>
			);
		}

		return (
			<Button
				size="sm"
				className="w-full sm:w-auto text-white py-2.5 px-4 text-sm sm:text-base"
				onClick={onNewButtonClick}
			>
				<Plus className="h-4 w-4 mr-2" />
				{newButtonText}
			</Button>
		);
	};

	return (
		<div className={`space-y-3 sm:space-y-4 ${className}`}>
			{/* Desktop Layout */}
			<div className="hidden md:flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-gray-900 md:text-3xl">
						{title}
					</h1>
					{subtitle && (
						<p className="text-gray-600 mt-1 text-sm md:text-base">
							{subtitle}
						</p>
					)}
				</div>
				<div className="flex items-center space-x-3">
					{showFilter && (
						<Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
							<SheetTrigger asChild>
								<Button variant="outline" size="sm" className="border-gray-300 text-gray-700 hover:bg-gray-50 py-2.5 px-4">
									<Filter className="h-4 w-4 mr-2" />
									Filtrar
								</Button>
							</SheetTrigger>
							<SheetContent side="right" className="w-full sm:w-96">
								<SheetHeader>
									<SheetTitle>Filtros</SheetTitle>
									<SheetDescription>
										Filtre por status e outros critérios
									</SheetDescription>
								</SheetHeader>
								<div className="space-y-4 mt-6">
									{filterContent}
								</div>
							</SheetContent>
						</Sheet>
					)}
					{showNewButton && <NewButton />}
				</div>
			</div>

			{/* Mobile Layout */}
			<div className="md:hidden space-y-3">
				<div>
					<h1 className="text-xl sm:text-2xl font-bold text-gray-900 leading-tight">
						{title}
					</h1>
					{subtitle && (
						<p className="text-gray-600 mt-1 text-sm leading-relaxed">
							{subtitle}
						</p>
					)}
				</div>
				<div className="flex flex-col space-y-3">
					{showFilter && (
						<Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
							<SheetTrigger asChild>
								<Button variant="outline" size="sm" className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-4 text-base">
									<Filter className="h-4 w-4 mr-2" />
									Filtrar
								</Button>
							</SheetTrigger>
							<SheetContent side="right" className="w-full sm:w-96">
								<SheetHeader>
									<SheetTitle>Filtros</SheetTitle>
									<SheetDescription>
										Filtre por status e outros critérios
									</SheetDescription>
								</SheetHeader>
								<div className="space-y-4 mt-6">
									{filterContent}
								</div>
							</SheetContent>
						</Sheet>
					)}
					{showNewButton && <NewButton />}
				</div>
			</div>
		</div>
	);
}
