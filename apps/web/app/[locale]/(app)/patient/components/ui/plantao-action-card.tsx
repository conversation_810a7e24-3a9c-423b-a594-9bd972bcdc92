"use client";

import { cn } from "@ui/lib";
import { LucideIcon, ChevronRight } from "lucide-react";
import { Card } from "./card";
import { PlantaoLink } from "../plantao-link";

export interface PlantaoActionCardProps {
	title: string;
	description?: string;
	icon?: LucideIcon;
	iconColor?: string;
	primary?: boolean;
	disabled?: boolean;
	loading?: boolean;
	children?: React.ReactNode;
	className?: string;
	urgencyLevel?: string;
}

export function PlantaoActionCard({
	title,
	description,
	icon: Icon,
	iconColor = "text-blue-600",
	primary = false,
	disabled = false,
	loading = false,
	children,
	className,
	urgencyLevel = "medium",
}: PlantaoActionCardProps) {
	return (
		<Card
			className={cn(
				"transition-all duration-200 hover:shadow-md",
				primary && "border-blue-200 bg-gradient-to-r from-blue-50 to-blue-50/50",
				disabled && "opacity-50 cursor-not-allowed",
				!disabled && "hover:border-blue-300 cursor-pointer",
				className
			)}
		>
			<PlantaoLink
				className="w-full p-3 sm:p-4 text-left flex items-center justify-between group"
				urgencyLevel={urgencyLevel}
			>
				<div className="flex items-center space-x-3">
					{Icon && (
						<div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center bg-gray-100">
							<Icon className={cn("w-4 h-4 sm:w-5 sm:h-5", iconColor)} />
						</div>
					)}
					<div className="flex-1 min-w-0">
						<h3 className="font-medium text-gray-900 text-sm sm:text-base">{title}</h3>
						{description && (
							<p className="text-xs sm:text-sm text-gray-500 mt-1">{description}</p>
						)}
						{children}
					</div>
				</div>
				<ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
			</PlantaoLink>
		</Card>
	);
}
