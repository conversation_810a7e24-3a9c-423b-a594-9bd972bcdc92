"use client";

import { cn } from "@ui/lib";
import { LucideIcon, ChevronRight } from "lucide-react";
import { Card } from "./card";
import { Button } from "./button";

export interface ActionCardProps {
	title: string;
	description?: string;
	icon?: LucideIcon;
	iconColor?: string;
	primary?: boolean;
	disabled?: boolean;
	loading?: boolean;
	onClick?: () => void;
	href?: string;
	children?: React.ReactNode;
	className?: string;
}

export function ActionCard({
	title,
	description,
	icon: Icon,
	iconColor = "text-blue-600",
	primary = false,
	disabled = false,
	loading = false,
	onClick,
	href,
	children,
	className,
}: ActionCardProps) {
	const Comp = href ? "a" : "button";
	const baseProps = href ? { href } : { onClick, disabled };

	return (
		<Card
			className={cn(
				"transition-all duration-200 hover:shadow-md",
				primary && "border-blue-200 bg-gradient-to-r from-blue-50 to-blue-50/50",
				disabled && "opacity-50 cursor-not-allowed",
				!disabled && "hover:border-blue-300 cursor-pointer",
				className
			)}
		>
			<Comp
				className="w-full p-3 sm:p-4 text-left flex items-center justify-between group"
				{...baseProps}
			>
				<div className="flex items-start space-x-3 flex-1 min-w-0">
					{Icon && (
						<div className={cn(
							"flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center",
							primary ? "bg-blue-100" : "bg-gray-100"
						)}>
							<Icon className={cn("w-4 h-4 sm:w-5 sm:h-5", iconColor)} />
						</div>
					)}

					<div className="flex-1 min-w-0">
						<h3 className={cn(
							"font-semibold text-gray-900 group-hover:text-blue-700 transition-colors text-sm sm:text-base",
							primary && "text-blue-900"
						)}>
							{title}
						</h3>
						{description && (
							<p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2">
								{description}
							</p>
						)}
						{children}
					</div>
				</div>

				<div className="flex-shrink-0 ml-2 sm:ml-3">
					{loading ? (
						<div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
					) : (
						<ChevronRight className={cn(
							"w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-blue-600 transition-colors",
							primary && "text-blue-600"
						)} />
					)}
				</div>
			</Comp>
		</Card>
	);
}
