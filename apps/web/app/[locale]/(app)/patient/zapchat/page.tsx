import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { NewZapChatClient } from "../../../../../components/zapchat/new-zapchat-client";

export const dynamic = "force-dynamic";

export default async function ZapChatPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	return (
		<div className="h-full">
			<NewZapChatClient user={user} enableVideo={true} />
		</div>
	);
}
