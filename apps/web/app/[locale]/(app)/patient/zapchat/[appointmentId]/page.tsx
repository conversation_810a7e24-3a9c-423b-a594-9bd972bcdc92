import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { NewZapChatClient } from "../../../../../../components/zapchat/new-zapchat-client";
import { db } from "database";

export const dynamic = "force-dynamic";

interface ZapChatAppointmentPageProps {
  params: {
    appointmentId: string;
  };
}

export default async function ZapChatAppointmentPage({ params }: ZapChatAppointmentPageProps) {
  const locale = await getLocale();
  const { user } = await currentUser();

  if (!user) {
    return redirect({ href: "/auth/login", locale });
  }

  // Verificar se o appointment existe e pertence ao usuário
  const appointment = await db.appointment.findFirst({
    where: {
      id: params.appointmentId,
      patient: {
        userId: user.id
      }
    },
    include: {
      patient: {
        include: {
          user: true
        }
      }
    }
  });

  if (!appointment) {
    console.error('[ZAPCHAT_APPOINTMENT] Appointment não encontrado ou não pertence ao usuário:', {
      appointmentId: params.appointmentId,
      userId: user.id
    });
    return redirect({ href: "/patient/dashboard", locale });
  }

  console.log('[ZAPCHAT_APPOINTMENT] Carregando chat para appointment:', {
    appointmentId: params.appointmentId,
    patientName: appointment.patient.user.name,
    isOnDuty: appointment.isOnDuty
  });

  return (
    <div className="h-full">
      <NewZapChatClient
        user={user}
        enableVideo={true}
      />
    </div>
  );
}
