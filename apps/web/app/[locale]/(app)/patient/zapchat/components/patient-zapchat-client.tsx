"use client";

import { useState, useEffect } from "react";
import type { User } from 'database';
import { ConsultationService, Consultation } from "@lib/services/consultation.service";
import { ChatInterface } from "../../../../../../components/zapchat/chat-interface";
import { ConsultationList } from "../../../../../../components/zapchat/consultation-list";
import { NotificationProvider, useChatNotifications } from "../../../../../../components/zapchat/chat-notification";
import { RefreshCw, MessageCircle, AlertCircle, ArrowLeft, Plus } from "lucide-react";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useRouter } from "next/navigation";
import { useChatFallback } from "@lib/hooks/use-chat-fallback";

interface PatientZapChatClientProps {
	user: User;
}

function PatientZapChatContent({ user }: PatientZapChatClientProps) {
	const router = useRouter();
	const [consultations, setConsultations] = useState<Consultation[]>([]);
	const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isRefreshing, setIsRefreshing] = useState(false);

	const { addNotification } = useChatNotifications();
	const consultationService = new ConsultationService();

	// Sempre chamar o hook, mas controlar a conexão via autoConnect
	const chatHook = useChatFallback(
		selectedConsultation?.id || '', // Sempre passar um valor, mesmo que vazio
		user.id,
		!!selectedConsultation, // autoConnect só quando há consulta selecionada
		2000  // pollInterval
	);

	const loadConsultations = async (showRefreshIndicator = false) => {
		if (!user?.id) {
			console.log("[Patient ZapChat] Sem user.id para carregar consultas");
			return;
		}

		console.log("[Patient ZapChat] Carregando consultas para paciente:", user.id);

		if (showRefreshIndicator) {
			setIsRefreshing(true);
		} else {
			setIsLoading(true);
		}

		setError(null);

		try {
			const data = await consultationService.getActiveConsultations(user.id, "patient");
			console.log("[Patient ZapChat] Consultas recebidas:", data.length, data);
			setConsultations(data);

			// Auto-selecionar primeira consulta se não houver nenhuma selecionada
			if (data.length > 0 && !selectedConsultation) {
				setSelectedConsultation(data[0]);
			}

			if (showRefreshIndicator && data.length > 0) {
				addNotification({
					type: "success",
					title: "Consultas atualizadas",
					message: `${data.length} consulta(s) carregada(s) com sucesso`,
					duration: 3000
				});
			}
		} catch (err) {
			console.error("[Patient ZapChat] Erro ao carregar consultas:", err);
			const errorMessage = err instanceof Error ? err.message : "Failed to load consultations";
			setError(errorMessage);

			addNotification({
				type: "error",
				title: "Erro ao carregar consultas",
				message: errorMessage,
				duration: 5000
			});
		} finally {
			setIsLoading(false);
			setIsRefreshing(false);
		}
	};

	useEffect(() => {
		if (user?.id) {
			loadConsultations();
		}
	}, [user?.id]);

	const handleConsultationSelect = (consultation: Consultation) => {
		setSelectedConsultation(consultation);
	};

	const handleStartConsultation = async (consultationId: string) => {
		try {
			setIsLoading(true);
			await consultationService.startConsultation(consultationId);
			await loadConsultations();

			addNotification({
				type: "success",
				title: "Consulta iniciada",
				message: "A consulta foi iniciada com sucesso",
				duration: 3000
			});
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Failed to start consultation";
			setError(errorMessage);

			addNotification({
				type: "error",
				title: "Erro ao iniciar consulta",
				message: errorMessage,
				duration: 5000
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleEndConsultation = async (consultationId: string) => {
		try {
			setIsLoading(true);
			await consultationService.endConsultation(consultationId);
			setSelectedConsultation(null);
			await loadConsultations();

			addNotification({
				type: "success",
				title: "Consulta finalizada",
				message: "A consulta foi finalizada com sucesso",
				duration: 3000
			});
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : "Failed to end consultation";
			setError(errorMessage);

			addNotification({
				type: "error",
				title: "Erro ao finalizar consulta",
				message: errorMessage,
				duration: 5000
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleRefresh = () => {
		loadConsultations(true);
	};

	const handleErrorClose = () => {
		setError(null);
	};

	const handleBackToDashboard = () => {
		router.push("/patient/dashboard");
	};

	const handleNewConsultation = () => {
		router.push("/patient/schedule");
	};

	// Monitorar status do chat e mostrar notificações
	useEffect(() => {
		if (selectedConsultation && chatHook) {
			const { connectionStatus, error: chatError } = chatHook;

			// Notificar sobre mudanças de status
			if (connectionStatus.status === 'connected') {
				addNotification({
					type: "success",
					title: "Chat conectado",
					message: `Conectado via ${connectionStatus.mode}`,
					duration: 2000
				});
			} else if (connectionStatus.status === 'error') {
				addNotification({
					type: "error",
					title: "Erro no chat",
					message: connectionStatus.lastError || "Erro desconhecido",
					duration: 5000
				});
			}

			// Notificar sobre erros do chat
			if (chatError) {
				addNotification({
					type: "error",
					title: "Erro no chat",
					message: chatError,
					duration: 5000
				});
			}
		}
	}, [selectedConsultation, chatHook, addNotification]);

	if (isLoading && consultations.length === 0) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
					<p className="text-gray-600">Carregando suas consultas...</p>
				</div>
			</div>
		);
	}

	// Se não há consulta selecionada, mostrar lista
	if (!selectedConsultation) {
		return (
			<div className="space-y-6">
				{/* Header */}
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<Button
							variant="ghost"
							size="sm"
							onClick={handleBackToDashboard}
							className="flex items-center gap-2"
						>
							<ArrowLeft className="w-4 h-4" />
							Voltar
						</Button>
						<div>
							<h1 className="text-xl font-semibold text-gray-900">ZapChat</h1>
							<p className="text-sm text-gray-600">Suas consultas ativas</p>
						</div>
					</div>
					<Button
						variant="outline"
						size="sm"
						onClick={handleRefresh}
						disabled={isRefreshing}
						className="flex items-center gap-2"
					>
						<RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
						Atualizar
					</Button>
				</div>

				{/* Error Display */}
				{error && (
					<Card className="border-red-200 bg-red-50">
						<CardContent className="pt-6">
							<div className="flex items-center gap-2">
								<AlertCircle className="w-4 h-4 text-red-500" />
								<p className="text-sm text-red-600">{error}</p>
							</div>
							<Button
								variant="outline"
								size="sm"
								onClick={handleErrorClose}
								className="mt-2"
							>
								Fechar
							</Button>
						</CardContent>
					</Card>
				)}

				{/* Consultas */}
				{consultations.length === 0 ? (
					<Card>
						<CardContent className="py-12 text-center">
							<div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
								<MessageCircle className="w-8 h-8 text-gray-400" />
							</div>
							<h3 className="text-lg font-medium text-gray-900 mb-2">
								Nenhuma consulta ativa
							</h3>
							<p className="text-gray-600 mb-6">
								Você não possui consultas agendadas no momento
							</p>
							<Button onClick={handleNewConsultation} className="flex items-center gap-2">
								<Plus className="w-4 h-4" />
								Agendar Nova Consulta
							</Button>
						</CardContent>
					</Card>
				) : (
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<MessageCircle className="w-5 h-5 text-green-600" />
								Consultas Ativas ({consultations.length})
							</CardTitle>
						</CardHeader>
						<CardContent>
							<ConsultationList
								consultations={consultations}
								selectedConsultation={selectedConsultation}
								onConsultationSelect={handleConsultationSelect}
								onStartConsultation={handleStartConsultation}
								isLoading={isLoading}
								userRole="patient"
							/>
						</CardContent>
					</Card>
				)}
			</div>
		);
	}

	// Se há consulta selecionada, mostrar chat
	return (
		<div className="space-y-4">
			{/* Header do Chat */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<Button
						variant="ghost"
						size="sm"
						onClick={() => setSelectedConsultation(null)}
						className="flex items-center gap-2"
					>
						<ArrowLeft className="w-4 h-4" />
						Voltar
					</Button>
					<div>
						<h2 className="font-medium text-gray-900">
							{selectedConsultation.doctor_name}
						</h2>
						<p className="text-sm text-gray-600">
							{selectedConsultation.is_on_duty ? "Plantão" : "Consulta"}
						</p>
						{/* Status da conexão */}
						{chatHook && (
							<div className="flex items-center gap-2 mt-1">
								<div className={`w-2 h-2 rounded-full ${
									chatHook.connectionStatus.status === 'connected' ? 'bg-green-500' :
									chatHook.connectionStatus.status === 'connecting' ? 'bg-yellow-500' :
									'bg-red-500'
								}`} />
								<span className="text-xs text-gray-500">
									{chatHook.connectionStatus.status === 'connected' ? 'Conectado' :
									 chatHook.connectionStatus.status === 'connecting' ? 'Conectando...' :
									 'Desconectado'}
									{chatHook.connectionStatus.mode === 'polling' && ' (Polling)'}
								</span>
							</div>
						)}
					</div>
				</div>
				<div className="flex items-center gap-2">
					{/* Botões de ação do chat */}
					{chatHook && (
						<>
							<Button
								variant="outline"
								size="sm"
								onClick={chatHook.refreshMessages}
								disabled={chatHook.isLoading}
								className="flex items-center gap-2"
							>
								<RefreshCw className={`w-4 h-4 ${chatHook.isLoading ? 'animate-spin' : ''}`} />
								Atualizar
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={chatHook.reconnect}
								className="flex items-center gap-2"
							>
								<RefreshCw className="w-4 h-4" />
								Reconectar
							</Button>
						</>
					)}
					<Button
						variant="outline"
						size="sm"
						onClick={() => handleEndConsultation(selectedConsultation.id)}
					>
						Finalizar
					</Button>
				</div>
			</div>

			{/* Interface do Chat */}
			<Card className="h-[calc(100vh-200px)] flex flex-col">
				{chatHook && selectedConsultation ? (
					<ChatInterface
						consultation={selectedConsultation}
						userId={user.id}
						onEndConsultation={handleEndConsultation}
						// Passar dados do hook de chat
						messages={chatHook.messages}
						isLoading={chatHook.isLoading}
						error={chatHook.error}
						onSendMessage={chatHook.sendTextMessage}
						onSendAudio={chatHook.sendAudioMessage}
						onSendFile={chatHook.sendFileMessage}
						isTyping={chatHook.isTyping}
						connectionStatus={chatHook.connectionStatus}
					/>
				) : (
					<div className="flex items-center justify-center h-full">
						<RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
					</div>
				)}
			</Card>
		</div>
	);
}

export function PatientZapChatClient({ user }: PatientZapChatClientProps) {
	return (
		<div className="container mx-auto px-4 py-6">
			<NotificationProvider>
				<PatientZapChatContent user={user} />
			</NotificationProvider>
		</div>
	);
}
