import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserContextProvider } from "@saas/auth/lib/user-context";
import { getLocale } from "next-intl/server";
import type { PropsWithChildren } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function PatientOnboardingLayout({ children }: PropsWithChildren) {
	const locale = await getLocale();
	const { user, teamMembership } = await currentUser();

	console.log("PatientOnboardingLayout - User:", user?.id, "Role:", user?.role, "OnboardingComplete:", user?.onboardingComplete);

	if (!user) {
		console.log("PatientOnboardingLayout - No user, redirecting to login");
		return redirect({ href: "/auth/login", locale });
	}

	// Verificação de role temporariamente desabilitada para debug
	// const isPatient = user.role === "PATIENT" || user.role === "USER";
	// if (!isPatient) {
	// 	return redirect({ href: "/app/dashboard", locale });
	// }

	// Para o onboarding, não verificar se já está completo
	// Isso evita o loop infinito de redirecionamento
	console.log("PatientOnboardingLayout - Rendering children");

	return (
		<UserContextProvider initialUser={user} teamMembership={teamMembership}>
			{children}
		</UserContextProvider>
	);
}
