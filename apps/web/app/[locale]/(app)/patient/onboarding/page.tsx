import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { OnboardingWizard } from "./components/onboarding-wizard";

export const dynamic = "force-dynamic";

export default async function PatientOnboardingPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	console.log("PatientOnboardingPage - User:", user?.id, "Role:", user?.role, "OnboardingComplete:", user?.onboardingComplete);

	if (!user) {
		console.log("PatientOnboardingPage - No user, redirecting to login");
		return redirect({ href: "/auth/login", locale });
	}

	// Verificar se já completou o onboarding
	// const hasCompletedOnboarding = await checkPatientOnboarding(user.id);
	// if (hasCompletedOnboarding) {
	// 	return redirect({ href: "/patient/dashboard", locale });
	// }

	console.log("PatientOnboardingPage - Rendering OnboardingWizard");
	return <OnboardingWizard user={user} />;
}
