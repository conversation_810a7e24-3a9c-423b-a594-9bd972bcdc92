"use client";

import type { User } from 'database';
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { Heart, Pill, AlertTriangle, Shield, Plus, X } from "lucide-react";
import { Button } from "@ui/components/button";

interface HealthProfileStepProps {
	user: User;
	data: any;
	onDataChange: (data: any) => void;
}

export function HealthProfileStep({ user, data, onDataChange }: HealthProfileStepProps) {
	const [formData, setFormData] = useState({
		allergies: data.healthProfile?.allergies || [],
		medications: data.healthProfile?.medications || [],
		conditions: data.healthProfile?.conditions || [],
		insurance: {
			provider: data.healthProfile?.insurance?.provider || "",
			number: data.healthProfile?.insurance?.number || "",
		},
	});

	const [newAllergy, setNewAllergy] = useState("");
	const [newMedication, setNewMedication] = useState("");
	const [newCondition, setNewCondition] = useState("");

	const updateData = (newFormData: any) => {
		setFormData(newFormData);
		onDataChange({ healthProfile: newFormData });
	};

	const addItem = (type: 'allergies' | 'medications' | 'conditions', value: string) => {
		if (!value.trim()) return;

		const newData = {
			...formData,
			[type]: [...formData[type], value.trim()]
		};
		updateData(newData);

		// Clear input
		if (type === 'allergies') setNewAllergy("");
		if (type === 'medications') setNewMedication("");
		if (type === 'conditions') setNewCondition("");
	};

	const removeItem = (type: 'allergies' | 'medications' | 'conditions', index: number) => {
		const newData = {
			...formData,
			[type]: formData[type].filter((_: any, i: number) => i !== index)
		};
		updateData(newData);
	};

	const handleInsuranceChange = (field: string, value: string) => {
		const newData = {
			...formData,
			insurance: { ...formData.insurance, [field]: value }
		};
		updateData(newData);
	};

	return (
		<div className="max-w-md mx-auto space-y-6 pb-24">
			{/* Header */}
			<div className="text-center mb-6">
				<div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<Heart className="w-8 h-8 text-red-600" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Perfil de Saúde
				</h1>
				<p className="text-gray-600">
					Essas informações ajudam nossos médicos a oferecer o melhor atendimento para você
				</p>
			</div>

			{/* Alergias */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<AlertTriangle className="w-5 h-5 text-orange-600" />
						Alergias
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex gap-2">
						<Input
							placeholder="Ex: Penicilina, Poeira, Amendoim..."
							value={newAllergy}
							onChange={(e) => setNewAllergy(e.target.value)}
							onKeyPress={(e) => e.key === 'Enter' && addItem('allergies', newAllergy)}
						/>
						<Button
							size="sm"
							onClick={() => addItem('allergies', newAllergy)}
							disabled={!newAllergy.trim()}
						>
							<Plus className="w-4 h-4" />
						</Button>
					</div>

					{formData.allergies.length > 0 && (
						<div className="flex flex-wrap gap-2">
							{formData.allergies.map((allergy: string, index: number) => (
								<Badge key={index} variant="secondary" className="flex items-center gap-1">
									{allergy}
									<button
										onClick={() => removeItem('allergies', index)}
										className="ml-1 hover:text-red-600"
									>
										<X className="w-3 h-3" />
									</button>
								</Badge>
							))}
						</div>
					)}

					{formData.allergies.length === 0 && (
						<p className="text-sm text-gray-500 italic">
							Nenhuma alergia informada
						</p>
					)}
				</CardContent>
			</Card>

			{/* Medicamentos */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Pill className="w-5 h-5 text-blue-600" />
						Medicamentos em Uso
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex gap-2">
						<Input
							placeholder="Ex: Losartana 50mg, Omeprazol..."
							value={newMedication}
							onChange={(e) => setNewMedication(e.target.value)}
							onKeyPress={(e) => e.key === 'Enter' && addItem('medications', newMedication)}
						/>
						<Button
							size="sm"
							onClick={() => addItem('medications', newMedication)}
							disabled={!newMedication.trim()}
						>
							<Plus className="w-4 h-4" />
						</Button>
					</div>

					{formData.medications.length > 0 && (
						<div className="flex flex-wrap gap-2">
							{formData.medications.map((medication: string, index: number) => (
								<Badge key={index} variant="secondary" className="flex items-center gap-1">
									{medication}
									<button
										onClick={() => removeItem('medications', index)}
										className="ml-1 hover:text-red-600"
									>
										<X className="w-3 h-3" />
									</button>
								</Badge>
							))}
						</div>
					)}

					{formData.medications.length === 0 && (
						<p className="text-sm text-gray-500 italic">
							Nenhum medicamento informado
						</p>
					)}
				</CardContent>
			</Card>

			{/* Condições de saúde */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Heart className="w-5 h-5 text-red-600" />
						Condições de Saúde
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					<div className="flex gap-2">
						<Input
							placeholder="Ex: Diabetes, Hipertensão, Asma..."
							value={newCondition}
							onChange={(e) => setNewCondition(e.target.value)}
							onKeyPress={(e) => e.key === 'Enter' && addItem('conditions', newCondition)}
						/>
						<Button
							size="sm"
							onClick={() => addItem('conditions', newCondition)}
							disabled={!newCondition.trim()}
						>
							<Plus className="w-4 h-4" />
						</Button>
					</div>

					{formData.conditions.length > 0 && (
						<div className="flex flex-wrap gap-2">
							{formData.conditions.map((condition: string, index: number) => (
								<Badge key={index} variant="secondary" className="flex items-center gap-1">
									{condition}
									<button
										onClick={() => removeItem('conditions', index)}
										className="ml-1 hover:text-red-600"
									>
										<X className="w-3 h-3" />
									</button>
								</Badge>
							))}
						</div>
					)}

					{formData.conditions.length === 0 && (
						<p className="text-sm text-gray-500 italic">
							Nenhuma condição informada
						</p>
					)}
				</CardContent>
			</Card>

			{/* Plano de saúde */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Shield className="w-5 h-5 text-green-600" />
						Plano de Saúde (Opcional)
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="insuranceProvider">Operadora</Label>
						<Input
							id="insuranceProvider"
							placeholder="Ex: Unimed, Bradesco Saúde, SulAmérica..."
							value={formData.insurance.provider}
							onChange={(e) => handleInsuranceChange("provider", e.target.value)}
							className="mt-1"
						/>
					</div>

					<div>
						<Label htmlFor="insuranceNumber">Número da Carteirinha</Label>
						<Input
							id="insuranceNumber"
							placeholder="Número do cartão do plano"
							value={formData.insurance.number}
							onChange={(e) => handleInsuranceChange("number", e.target.value)}
							className="mt-1"
						/>
					</div>

					<div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
						<p className="text-xs text-blue-800">
							💡 <strong>Opcional:</strong> Mesmo com plano de saúde, você pode usar o ZapVida para consultas rápidas e práticas.
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
