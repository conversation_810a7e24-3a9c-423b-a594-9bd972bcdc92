"use client";

import type { User } from 'database';
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON>, Clock } from "lucide-react";
import { But<PERSON> } from "@ui/components/button";

interface WelcomeStepProps {
	user: User;
	onNext: () => void;
}

export function WelcomeStep({ user, onNext }: WelcomeStepProps) {
	const firstName = user.name?.split(" ")[0] || "Paciente";

	return (
		<div className="min-h-screen flex flex-col justify-center items-center text-center px-4">
			{/* Logo e animação */}
			<div className="mb-8">
				<div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg">
					<Heart className="w-12 h-12 text-white" />
				</div>

				<div className="animate-pulse">
					<div className="w-32 h-1 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full mx-auto"></div>
				</div>
			</div>

			{/* <PERSON><PERSON><PERSON><PERSON> e sauda<PERSON> */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900 mb-2">
					Bem-vindo ao ZapVida! 🎉
				</h1>
				<p className="text-xl text-gray-600 mb-4">
					Olá, {firstName}!
				</p>
				<p className="text-gray-600 leading-relaxed max-w-md mx-auto">
					Vamos configurar seu perfil em poucos passos para você ter a melhor experiência de atendimento médico online.
				</p>
			</div>

			{/* Features destacadas */}
			<div className="mb-10 space-y-4 max-w-sm mx-auto">
				<div className="flex items-center space-x-3 text-left">
					<div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
						<Clock className="w-5 h-5 text-green-600" />
					</div>
					<div>
						<p className="font-medium text-gray-900">Atendimento 24/7</p>
						<p className="text-sm text-gray-600">Médicos disponíveis a qualquer hora</p>
					</div>
				</div>

				<div className="flex items-center space-x-3 text-left">
					<div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
						<Shield className="w-5 h-5 text-blue-600" />
					</div>
					<div>
						<p className="font-medium text-gray-900">Segurança Total</p>
						<p className="text-sm text-gray-600">Seus dados protegidos e sigilosos</p>
					</div>
				</div>

				<div className="flex items-center space-x-3 text-left">
					<div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
						<Sparkles className="w-5 h-5 text-purple-600" />
					</div>
					<div>
						<p className="font-medium text-gray-900">Experiência Simples</p>
						<p className="text-sm text-gray-600">Interface pensada para você</p>
					</div>
				</div>
			</div>

			{/* Call to action */}
			<div className="w-full max-w-sm mx-auto space-y-4">
				<Button
					fullWidth
					size="lg"
					onClick={onNext}
					className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg"
				>
					Começar Configuração
				</Button>

				<p className="text-xs text-gray-500">
					Leva apenas 2 minutos ⏱️
				</p>
			</div>

			{/* Indicador visual */}
			<div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
				<div className="animate-bounce">
					<div className="w-6 h-10 border-2 border-gray-300 rounded-full p-1">
						<div className="w-1 h-3 bg-gray-400 rounded-full mx-auto animate-pulse"></div>
					</div>
				</div>
			</div>
		</div>
	);
}
