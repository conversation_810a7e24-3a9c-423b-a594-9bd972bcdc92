"use client";

import type { User } from 'database';
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { UserCheck, Phone, Calendar, Users } from "lucide-react";

interface PersonalDataStepProps {
	user: User;
	data: any;
	onDataChange: (data: any) => void;
}

export function PersonalDataStep({ user, data, onDataChange }: PersonalDataStepProps) {
	const [formData, setFormData] = useState({
		phone: data.personalData?.phone || "",
		birthDate: data.personalData?.birthDate || "",
		gender: data.personalData?.gender || "",
		emergencyContact: {
			name: data.personalData?.emergencyContact?.name || "",
			phone: data.personalData?.emergencyContact?.phone || "",
			relationship: data.personalData?.emergencyContact?.relationship || "",
		},
	});

	const handleChange = (field: string, value: string) => {
		const newFormData = { ...formData, [field]: value };
		setFormData(newFormData);
		onDataChange({ personalData: newFormData });
	};

	const handleEmergencyContactChange = (field: string, value: string) => {
		const newEmergencyContact = { ...formData.emergencyContact, [field]: value };
		const newFormData = { ...formData, emergencyContact: newEmergencyContact };
		setFormData(newFormData);
		onDataChange({ personalData: newFormData });
	};

	return (
		<div className="max-w-md mx-auto space-y-6 pb-24">
			{/* Header */}
			<div className="text-center mb-6">
				<div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<UserCheck className="w-8 h-8 text-blue-600" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Seus Dados Pessoais
				</h1>
				<p className="text-gray-600">
					Essas informações nos ajudam a oferecer um atendimento mais personalizado
				</p>
			</div>

			{/* Dados básicos */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Phone className="w-5 h-5" />
						Informações Básicas
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="phone">Telefone/WhatsApp *</Label>
						<Input
							id="phone"
							type="tel"
							placeholder="(11) 99999-9999"
							value={formData.phone}
							onChange={(e) => handleChange("phone", e.target.value)}
							className="mt-1"
						/>
						<p className="text-xs text-gray-500 mt-1">
							Usaremos para enviar lembretes e atualizações
						</p>
					</div>

					<div>
						<Label htmlFor="birthDate">Data de Nascimento</Label>
						<Input
							id="birthDate"
							type="date"
							value={formData.birthDate}
							onChange={(e) => handleChange("birthDate", e.target.value)}
							className="mt-1"
						/>
					</div>

					<div>
						<Label htmlFor="gender">Gênero</Label>
						<Select
							value={formData.gender}
							onValueChange={(value) => handleChange("gender", value)}
						>
							<SelectTrigger className="mt-1">
								<SelectValue placeholder="Selecione seu gênero" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="male">Masculino</SelectItem>
								<SelectItem value="female">Feminino</SelectItem>
								<SelectItem value="other">Outro</SelectItem>
								<SelectItem value="prefer-not-to-say">Prefiro não informar</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Contato de emergência */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Users className="w-5 h-5" />
						Contato de Emergência
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="emergencyName">Nome Completo</Label>
						<Input
							id="emergencyName"
							placeholder="Nome do contato de emergência"
							value={formData.emergencyContact.name}
							onChange={(e) => handleEmergencyContactChange("name", e.target.value)}
							className="mt-1"
						/>
					</div>

					<div>
						<Label htmlFor="emergencyPhone">Telefone</Label>
						<Input
							id="emergencyPhone"
							type="tel"
							placeholder="(11) 99999-9999"
							value={formData.emergencyContact.phone}
							onChange={(e) => handleEmergencyContactChange("phone", e.target.value)}
							className="mt-1"
						/>
					</div>

					<div>
						<Label htmlFor="relationship">Parentesco/Relação</Label>
						<Select
							value={formData.emergencyContact.relationship}
							onValueChange={(value) => handleEmergencyContactChange("relationship", value)}
						>
							<SelectTrigger className="mt-1">
								<SelectValue placeholder="Selecione o parentesco" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="spouse">Cônjuge</SelectItem>
								<SelectItem value="parent">Pai/Mãe</SelectItem>
								<SelectItem value="child">Filho(a)</SelectItem>
								<SelectItem value="sibling">Irmão/Irmã</SelectItem>
								<SelectItem value="relative">Outro familiar</SelectItem>
								<SelectItem value="friend">Amigo(a)</SelectItem>
								<SelectItem value="other">Outro</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
						<p className="text-xs text-yellow-800">
							💡 <strong>Dica:</strong> Em situações de emergência, entraremos em contato com esta pessoa caso necessário.
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
