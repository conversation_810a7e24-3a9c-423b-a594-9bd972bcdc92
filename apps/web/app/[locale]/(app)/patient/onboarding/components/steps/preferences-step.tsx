"use client";

import type { User } from 'database';
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Settings, Bell, Globe, CheckCircle } from "lucide-react";

interface PreferencesStepProps {
	user: User;
	data: any;
	onDataChange: (data: any) => void;
}

export function PreferencesStep({ user, data, onDataChange }: PreferencesStepProps) {
	const [formData, setFormData] = useState({
		notifications: {
			email: data.preferences?.notifications?.email ?? true,
			whatsapp: data.preferences?.notifications?.whatsapp ?? true,
			push: data.preferences?.notifications?.push ?? true,
		},
		language: data.preferences?.language || "pt-BR",
		timezone: data.preferences?.timezone || "America/Sao_Paulo",
	});

	const updateData = (newFormData: any) => {
		setFormData(newFormData);
		onDataChange({ preferences: newFormData });
	};

	const handleNotificationChange = (type: string, value: boolean) => {
		const newData = {
			...formData,
			notifications: { ...formData.notifications, [type]: value }
		};
		updateData(newData);
	};

	const handleChange = (field: string, value: string) => {
		const newData = { ...formData, [field]: value };
		updateData(newData);
	};

	return (
		<div className="max-w-md mx-auto space-y-6 pb-24">
			{/* Header */}
			<div className="text-center mb-6">
				<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<Settings className="w-8 h-8 text-green-600" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Suas Preferências
				</h1>
				<p className="text-gray-600">
					Configure como você quer ser notificado e outras preferências
				</p>
			</div>

			{/* Notificações */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Bell className="w-5 h-5 text-blue-600" />
						Notificações
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex items-center justify-between">
						<div>
							<Label htmlFor="email-notifications" className="font-medium">
								Email
							</Label>
							<p className="text-sm text-gray-600">
								Receba lembretes e atualizações por email
							</p>
						</div>
						<Switch
							id="email-notifications"
							checked={formData.notifications.email}
							onCheckedChange={(checked) => handleNotificationChange('email', checked)}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<Label htmlFor="whatsapp-notifications" className="font-medium">
								WhatsApp
							</Label>
							<p className="text-sm text-gray-600">
								Receba mensagens diretas no seu WhatsApp
							</p>
						</div>
						<Switch
							id="whatsapp-notifications"
							checked={formData.notifications.whatsapp}
							onCheckedChange={(checked) => handleNotificationChange('whatsapp', checked)}
						/>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<Label htmlFor="push-notifications" className="font-medium">
								Notificações Push
							</Label>
							<p className="text-sm text-gray-600">
								Notificações instantâneas no seu dispositivo
							</p>
						</div>
						<Switch
							id="push-notifications"
							checked={formData.notifications.push}
							onCheckedChange={(checked) => handleNotificationChange('push', checked)}
						/>
					</div>

					<div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
						<p className="text-xs text-blue-800">
							💡 <strong>Recomendado:</strong> Manter todas as notificações ativadas para não perder informações importantes sobre suas consultas.
						</p>
					</div>
				</CardContent>
			</Card>

			{/* Idioma e região */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2">
						<Globe className="w-5 h-5 text-green-600" />
						Idioma e Região
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="language">Idioma</Label>
						<Select
							value={formData.language}
							onValueChange={(value) => handleChange("language", value)}
						>
							<SelectTrigger className="mt-1">
								<SelectValue placeholder="Selecione o idioma" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="pt-BR">Português (Brasil)</SelectItem>
								<SelectItem value="en-US">English (US)</SelectItem>
								<SelectItem value="es-ES">Español</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label htmlFor="timezone">Fuso Horário</Label>
						<Select
							value={formData.timezone}
							onValueChange={(value) => handleChange("timezone", value)}
						>
							<SelectTrigger className="mt-1">
								<SelectValue placeholder="Selecione o fuso horário" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="America/Sao_Paulo">Brasília (GMT-3)</SelectItem>
								<SelectItem value="America/Manaus">Manaus (GMT-4)</SelectItem>
								<SelectItem value="America/Rio_Branco">Acre (GMT-5)</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Resumo final */}
			<Card className="border-green-200 bg-gradient-to-r from-green-50 to-green-50/50">
				<CardHeader>
					<CardTitle className="text-lg flex items-center gap-2 text-green-800">
						<CheckCircle className="w-5 h-5 text-green-600" />
						Quase Pronto!
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						<p className="text-sm text-green-700">
							Seu perfil está quase completo. Ao finalizar, você terá acesso a:
						</p>

						<div className="space-y-2">
							<div className="flex items-center gap-2 text-sm text-green-700">
								<CheckCircle className="w-4 h-4 text-green-600" />
								Dashboard personalizado
							</div>
							<div className="flex items-center gap-2 text-sm text-green-700">
								<CheckCircle className="w-4 h-4 text-green-600" />
								Agendamento de consultas
							</div>
							<div className="flex items-center gap-2 text-sm text-green-700">
								<CheckCircle className="w-4 h-4 text-green-600" />
								Chat médico 24h
							</div>
							<div className="flex items-center gap-2 text-sm text-green-700">
								<CheckCircle className="w-4 h-4 text-green-600" />
								Histórico médico completo
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
