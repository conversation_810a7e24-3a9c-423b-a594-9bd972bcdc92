"use client";

import type { User } from 'database';
import { useState } from "react";
import { <PERSON>Left, ArrowRight, Check } from "lucide-react";
import { Button } from "../../components/ui/button";
import { WelcomeStep } from "./steps/welcome-step";
import { PersonalDataStep } from "./steps/personal-data-step";
import { AIHealthQuizStep } from "./steps/ai-health-quiz-step";
import { HealthProfileStep } from "./steps/health-profile-step";
import { PreferencesStep } from "./steps/preferences-step";
import { cn } from "@ui/lib";

interface OnboardingWizardProps {
	user: User;
}

interface OnboardingData {
	personalData: {
		phone?: string;
		birthDate?: string;
		gender?: string;
		emergencyContact?: {
			name: string;
			phone: string;
			relationship: string;
		};
	};
	aiQuizAnswers?: Record<string, string | string[]>;
	aiInsights?: {
		riskLevel: string;
		recommendedSpecialties: string[];
		prioritySymptoms: string[];
		lifestyleRecommendations: string[];
		urgencyScore: number;
		personalizedMessage: string;
	};
	healthProfile: {
		allergies?: string[];
		medications?: string[];
		conditions?: string[];
		insurance?: {
			provider: string;
			number: string;
		};
	};
	preferences: {
		notifications: {
			email: boolean;
			whatsapp: boolean;
			push: boolean;
		};
		language: string;
		timezone: string;
	};
}

const steps = [
	{ id: "welcome", title: "Boas-vindas", component: WelcomeStep },
	{ id: "personal", title: "Dados Pessoais", component: PersonalDataStep },
	{ id: "ai-quiz", title: "Quiz Inteligente", component: AIHealthQuizStep },
	{ id: "health", title: "Perfil de Saúde", component: HealthProfileStep },
	{ id: "preferences", title: "Preferências", component: PreferencesStep },
];

export function OnboardingWizard({ user }: OnboardingWizardProps) {
	const [currentStepIndex, setCurrentStepIndex] = useState(0);
	const [onboardingData, setOnboardingData] = useState<OnboardingData>({
		personalData: {},
		aiQuizAnswers: {},
		aiInsights: undefined,
		healthProfile: {},
		preferences: {
			notifications: {
				email: true,
				whatsapp: true,
				push: true,
			},
			language: "pt-BR",
			timezone: "America/Sao_Paulo",
		},
	});
	const [isLoading, setIsLoading] = useState(false);

	const currentStep = steps[currentStepIndex];
	const isFirstStep = currentStepIndex === 0;
	const isLastStep = currentStepIndex === steps.length - 1;
	const isWelcomeStep = currentStep.id === "welcome";

	const handleNext = () => {
		if (isLastStep) {
			handleComplete();
		} else {
			setCurrentStepIndex(currentStepIndex + 1);
		}
	};

	const handlePrevious = () => {
		if (!isFirstStep) {
			setCurrentStepIndex(currentStepIndex - 1);
		}
	};

	const handleComplete = async () => {
		setIsLoading(true);
		try {
			// Aqui você salvaria os dados do onboarding
			console.log("Saving onboarding data:", onboardingData);

			// Simular delay da API
			await new Promise(resolve => setTimeout(resolve, 1000));

			// Redirecionar para dashboard
			window.location.href = "/patient/dashboard";
		} catch (error) {
			console.error("Error completing onboarding:", error);
			setIsLoading(false);
		}
	};

	const updateOnboardingData = (stepData: Partial<OnboardingData>) => {
		setOnboardingData(prev => ({
			...prev,
			...stepData,
		}));
	};

	const CurrentStepComponent = currentStep.component;

	return (
		<div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
			{/* Header com progresso - apenas se não for welcome */}
			{!isWelcomeStep && (
				<div className="sticky top-0 bg-white/80 backdrop-blur-sm border-b border-gray-200 z-10">
					<div className="px-4 py-4">
						<div className="flex items-center justify-between mb-4">
							<Button
								variant="ghost"
								size="sm"
								onClick={handlePrevious}
								disabled={isFirstStep}
							>
								<ArrowLeft className="w-4 h-4 mr-1" />
								Voltar
							</Button>

							<span className="text-sm text-gray-500">
								{currentStepIndex} de {steps.length - 1}
							</span>
						</div>

						{/* Progress Bar */}
						<div className="w-full bg-gray-200 rounded-full h-2">
							<div
								className="bg-blue-600 h-2 rounded-full transition-all duration-300"
								style={{
									width: `${((currentStepIndex) / (steps.length - 1)) * 100}%`
								}}
							/>
						</div>
					</div>
				</div>
			)}

			{/* Conteúdo do passo atual */}
			<div className={cn(
				"px-4",
				isWelcomeStep ? "py-0" : "py-6"
			)}>
				<CurrentStepComponent
					user={user}
					data={onboardingData}
					onDataChange={updateOnboardingData}
					onNext={handleNext}
					onPrevious={handlePrevious}
					isLoading={isLoading}
					isFirstStep={isFirstStep}
					isLastStep={isLastStep}
				/>
			</div>

			{/* Botões de navegação - apenas se não for welcome */}
			{!isWelcomeStep && (
				<div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
					<div className="flex justify-between space-x-4">
						<Button
							variant="outline"
							onClick={handlePrevious}
							disabled={isFirstStep}
							className="flex-1"
						>
							<ArrowLeft className="w-4 h-4 mr-2" />
							Anterior
						</Button>

						<Button
							onClick={handleNext}
							disabled={isLoading}
							className="flex-1"
						>
							{isLoading ? (
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
							) : isLastStep ? (
								<Check className="w-4 h-4 mr-2" />
							) : (
								<ArrowRight className="w-4 h-4 mr-2" />
							)}
							{isLastStep ? "Finalizar" : "Próximo"}
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
