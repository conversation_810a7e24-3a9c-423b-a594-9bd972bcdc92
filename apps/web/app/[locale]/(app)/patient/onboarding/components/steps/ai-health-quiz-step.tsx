"use client";

import type { User } from 'database';
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Brain, CheckCircle, Circle, ArrowRight, Sparkles, Target, Heart } from "lucide-react";
import { cn } from "@ui/lib";
import { PatientAIInsightsService } from "../../../services/patient-ai-insights.service";

interface AIHealthQuizStepProps {
	user: User;
	data: any;
	onDataChange: (data: any) => void;
}

interface QuizQuestion {
	id: string;
	question: string;
	type: 'single' | 'multiple' | 'scale';
	options?: string[];
	category: 'symptoms' | 'lifestyle' | 'medical_history' | 'preferences';
	aiWeight: number; // Peso para análise da IA
}

const quizQuestions: QuizQuestion[] = [
	{
		id: 'main_concern',
		question: 'Qual é sua principal preocupação de saúde no momento?',
		type: 'single',
		options: [
			'Dores frequentes (cabeça, costas, articulações)',
			'Problemas digestivos',
			'Ansiedade ou estresse',
			'Problemas de sono',
			'Questões cardiovasculares',
			'Controle de peso',
			'Cuidados preventivos',
			'Outro'
		],
		category: 'symptoms',
		aiWeight: 0.9
	},
	{
		id: 'symptoms_frequency',
		question: 'Com que frequência você sente desconfortos ou sintomas?',
		type: 'single',
		options: [
			'Diariamente',
			'Algumas vezes por semana',
			'Algumas vezes por mês',
			'Raramente',
			'Nunca'
		],
		category: 'symptoms',
		aiWeight: 0.8
	},
	{
		id: 'lifestyle_habits',
		question: 'Quais hábitos fazem parte da sua rotina? (Selecione todos que se aplicam)',
		type: 'multiple',
		options: [
			'Exercícios regulares',
			'Alimentação balanceada',
			'Meditação ou relaxamento',
			'Sono regular (7-8h)',
			'Trabalho estressante',
			'Consumo de álcool',
			'Tabagismo',
			'Vida sedentária'
		],
		category: 'lifestyle',
		aiWeight: 0.7
	},
	{
		id: 'medical_priority',
		question: 'O que é mais importante para você em uma consulta médica?',
		type: 'single',
		options: [
			'Diagnóstico rápido e preciso',
			'Tempo para explicar meus sintomas',
			'Orientações de prevenção',
			'Tratamento natural quando possível',
			'Acompanhamento contínuo',
			'Segunda opinião médica'
		],
		category: 'preferences',
		aiWeight: 0.6
	},
	{
		id: 'urgency_level',
		question: 'Como você classificaria a urgência das suas necessidades de saúde?',
		type: 'scale',
		options: ['1', '2', '3', '4', '5'],
		category: 'symptoms',
		aiWeight: 0.8
	},
	{
		id: 'specialty_interest',
		question: 'Que tipo de especialista você gostaria de consultar?',
		type: 'multiple',
		options: [
			'Clínico Geral',
			'Cardiologista',
			'Dermatologista',
			'Psicólogo/Psiquiatra',
			'Ginecologista',
			'Ortopedista',
			'Gastroenterologista',
			'Endocrinologista',
			'Não tenho certeza'
		],
		category: 'preferences',
		aiWeight: 0.9
	}
];

export function AIHealthQuizStep({ user, data, onDataChange }: AIHealthQuizStepProps) {
	const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
	const [answers, setAnswers] = useState<Record<string, string | string[]>>(
		data.aiQuizAnswers || {}
	);
	const [isAnalyzing, setIsAnalyzing] = useState(false);
	const [aiInsights, setAiInsights] = useState<any>(data.aiInsights || null);

	const currentQuestion = quizQuestions[currentQuestionIndex];
	const isLastQuestion = currentQuestionIndex === quizQuestions.length - 1;
	const progress = ((currentQuestionIndex + 1) / quizQuestions.length) * 100;

	const handleAnswer = (answer: string | string[]) => {
		const newAnswers = { ...answers, [currentQuestion.id]: answer };
		setAnswers(newAnswers);

		// Atualizar dados do onboarding
		onDataChange({
			aiQuizAnswers: newAnswers,
			aiInsights: aiInsights
		});
	};

	const handleNext = async () => {
		if (isLastQuestion) {
			await generateAIInsights();
		} else {
			setCurrentQuestionIndex(currentQuestionIndex + 1);
		}
	};

	const handlePrevious = () => {
		if (currentQuestionIndex > 0) {
			setCurrentQuestionIndex(currentQuestionIndex - 1);
		}
	};

	const generateAIInsights = async () => {
		setIsAnalyzing(true);

		try {
			// Usar o serviço de IA para gerar insights
			const insights = await PatientAIInsightsService.generateInsights(answers);
			setAiInsights(insights);

			// Salvar insights no perfil do paciente
			await PatientAIInsightsService.saveInsights(user.id, insights);

			onDataChange({
				aiQuizAnswers: answers,
				aiInsights: insights
			});
		} catch (error) {
			console.error('Erro ao gerar insights:', error);
			// Fallback para análise local em caso de erro
			const fallbackInsights = analyzeAnswersWithAI(answers);
			setAiInsights(fallbackInsights);
			onDataChange({
				aiQuizAnswers: answers,
				aiInsights: fallbackInsights
			});
		} finally {
			setIsAnalyzing(false);
		}
	};

	// Função para simular análise de IA
	const analyzeAnswersWithAI = (userAnswers: Record<string, string | string[]>) => {
		// Esta função seria substituída por uma chamada real para IA
		const insights = {
			riskLevel: 'medium',
			recommendedSpecialties: ['Clínico Geral', 'Cardiologista'],
			prioritySymptoms: ['Dores frequentes', 'Problemas de sono'],
			lifestyleRecommendations: [
				'Considere incorporar exercícios leves na rotina',
				'Mantenha horários regulares de sono',
				'Pratique técnicas de relaxamento'
			],
			urgencyScore: 6,
			personalizedMessage: 'Com base nas suas respostas, recomendamos uma consulta com clínico geral para avaliação inicial e possível encaminhamento para especialista.'
		};

		return insights;
	};

	const isAnswered = currentQuestion.id in answers;
	const currentAnswer = answers[currentQuestion.id];

	if (isAnalyzing) {
		return (
			<div className="max-w-md mx-auto text-center py-12">
				<div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
					<Brain className="w-10 h-10 text-blue-600 animate-pulse" />
				</div>
				<h2 className="text-2xl font-bold text-gray-900 mb-4">
					Analisando suas respostas...
				</h2>
				<p className="text-gray-600 mb-6">
					Nossa IA está processando suas informações para criar recomendações personalizadas
				</p>
				<div className="w-full bg-gray-200 rounded-full h-2 mb-4">
					<div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '70%' }} />
				</div>
				<p className="text-sm text-gray-500">
					Isso levará apenas alguns segundos...
				</p>
			</div>
		);
	}

	if (aiInsights) {
		return (
			<div className="max-w-md mx-auto space-y-6 pb-24">
				<div className="text-center mb-6">
					<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<Sparkles className="w-8 h-8 text-green-600" />
					</div>
					<h1 className="text-2xl font-bold text-gray-900 mb-2">
						Suas Recomendações Personalizadas
					</h1>
					<p className="text-gray-600">
						Com base nas suas respostas, preparamos estas sugestões para você
					</p>
				</div>

				{/* Especialidades Recomendadas */}
				<Card>
					<CardHeader>
						<CardTitle className="text-lg flex items-center gap-2">
							<Target className="w-5 h-5 text-blue-600" />
							Especialistas Recomendados
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex flex-wrap gap-2 mb-3">
							{aiInsights.recommendedSpecialties.map((specialty: string, index: number) => (
								<Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700">
									{specialty}
								</Badge>
							))}
						</div>
						<p className="text-sm text-gray-600">
							{aiInsights.personalizedMessage}
						</p>
					</CardContent>
				</Card>

				{/* Sintomas Prioritários */}
				<Card>
					<CardHeader>
						<CardTitle className="text-lg flex items-center gap-2">
							<Heart className="w-5 h-5 text-red-600" />
							Pontos de Atenção
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="space-y-2">
							{aiInsights.prioritySymptoms.map((symptom: string, index: number) => (
								<li key={index} className="flex items-center gap-2 text-sm">
									<div className="w-2 h-2 bg-red-500 rounded-full" />
									{symptom}
								</li>
							))}
						</ul>
					</CardContent>
				</Card>

				{/* Recomendações de Estilo de Vida */}
				<Card>
					<CardHeader>
						<CardTitle className="text-lg flex items-center gap-2">
							<Sparkles className="w-5 h-5 text-green-600" />
							Dicas Personalizadas
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="space-y-3">
							{aiInsights.lifestyleRecommendations.map((rec: string, index: number) => (
								<li key={index} className="flex items-start gap-2 text-sm">
									<CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
									{rec}
								</li>
							))}
						</ul>
					</CardContent>
				</Card>

				<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
					<p className="text-sm text-blue-800">
						<strong>💡 Próximo passo:</strong> Essas informações serão compartilhadas com seu médico para um atendimento mais personalizado e eficiente.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="max-w-md mx-auto space-y-6 pb-24">
			{/* Header */}
			<div className="text-center mb-6">
				<div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<Brain className="w-8 h-8 text-purple-600" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Quiz Inteligente de Saúde
				</h1>
				<p className="text-gray-600">
					Algumas perguntas rápidas para personalizar seu atendimento
				</p>
			</div>

			{/* Progress */}
			<div className="mb-6">
				<div className="flex justify-between text-sm text-gray-600 mb-2">
					<span>Pergunta {currentQuestionIndex + 1} de {quizQuestions.length}</span>
					<span>{Math.round(progress)}%</span>
				</div>
				<div className="w-full bg-gray-200 rounded-full h-2">
					<div
						className="bg-purple-600 h-2 rounded-full transition-all duration-300"
						style={{ width: `${progress}%` }}
					/>
				</div>
			</div>

			{/* Question */}
			<Card>
				<CardHeader>
					<CardTitle className="text-lg">
						{currentQuestion.question}
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-3">
					{currentQuestion.type === 'single' && (
						<div className="space-y-2">
							{currentQuestion.options?.map((option, index) => (
								<button
									key={index}
									onClick={() => handleAnswer(option)}
									className={cn(
										"w-full p-3 text-left rounded-lg border transition-all",
										currentAnswer === option
											? "border-purple-500 bg-purple-50 text-purple-700"
											: "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
									)}
								>
									<div className="flex items-center gap-3">
										{currentAnswer === option ? (
											<CheckCircle className="w-5 h-5 text-purple-600" />
										) : (
											<Circle className="w-5 h-5 text-gray-400" />
										)}
										{option}
									</div>
								</button>
							))}
						</div>
					)}

					{currentQuestion.type === 'multiple' && (
						<div className="space-y-2">
							{currentQuestion.options?.map((option, index) => {
								const isSelected = Array.isArray(currentAnswer) && currentAnswer.includes(option);
								return (
									<button
										key={index}
										onClick={() => {
											const current = Array.isArray(currentAnswer) ? currentAnswer : [];
											const newAnswer = isSelected
												? current.filter(item => item !== option)
												: [...current, option];
											handleAnswer(newAnswer);
										}}
										className={cn(
											"w-full p-3 text-left rounded-lg border transition-all",
											isSelected
												? "border-purple-500 bg-purple-50 text-purple-700"
												: "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
										)}
									>
										<div className="flex items-center gap-3">
											{isSelected ? (
												<CheckCircle className="w-5 h-5 text-purple-600" />
											) : (
												<Circle className="w-5 h-5 text-gray-400" />
											)}
											{option}
										</div>
									</button>
								);
							})}
						</div>
					)}

					{currentQuestion.type === 'scale' && (
						<div className="space-y-4">
							<div className="flex justify-between text-sm text-gray-600">
								<span>Baixa urgência</span>
								<span>Alta urgência</span>
							</div>
							<div className="flex justify-between gap-2">
								{currentQuestion.options?.map((option, index) => (
									<button
										key={index}
										onClick={() => handleAnswer(option)}
										className={cn(
											"flex-1 p-3 rounded-lg border transition-all",
											currentAnswer === option
												? "border-purple-500 bg-purple-600 text-white"
												: "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
										)}
									>
										{option}
									</button>
								))}
							</div>
						</div>
					)}

					{isAnswered && (
						<Button
							onClick={handleNext}
							className="w-full mt-4"
						>
							{isLastQuestion ? (
								<>
									<Sparkles className="w-4 h-4 mr-2" />
									Gerar Recomendações
								</>
							) : (
								<>
									Próxima Pergunta
									<ArrowRight className="w-4 h-4 ml-2" />
								</>
							)}
						</Button>
					)}
				</CardContent>
			</Card>

			{currentQuestionIndex > 0 && (
				<Button
					variant="outline"
					onClick={handlePrevious}
					className="w-full"
				>
					Pergunta Anterior
				</Button>
			)}
		</div>
	);
}
