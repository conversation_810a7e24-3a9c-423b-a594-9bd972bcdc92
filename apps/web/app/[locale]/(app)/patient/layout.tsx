import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserContextProvider } from "@saas/auth/lib/user-context";
import { getLocale } from "next-intl/server";
import type { PropsWithChildren } from "react";
import { PatientLayoutClient } from "./layout-client";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function PatientLayout({ children }: PropsWithChildren) {
	const locale = await getLocale();
	const { user, teamMembership } = await currentUser();

	console.log("PatientLayout - User:", user?.id, "Role:", user?.role, "OnboardingComplete:", user?.onboardingComplete);

	if (!user) {
		console.log("PatientLayout - No user, redirecting to login");
		return redirect({ href: "/auth/login", locale });
	}

	// Verificação de role temporariamente desabilitada para debug
	// const isPatient = user.role === "PATIENT" || user.role === "USER";
	// if (!isPatient) {
	// 	return redirect({ href: "/app/dashboard", locale });
	// }

	// Verificação de onboarding removida do layout principal para evitar loops infinitos
	// A verificação será feita nas páginas específicas quando necessário
	console.log("PatientLayout - Rendering children");

	return (
		<UserContextProvider initialUser={user} teamMembership={teamMembership}>
			<PatientLayoutClient user={user}>{children}</PatientLayoutClient>
		</UserContextProvider>
	);
}
