import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { PaymentsPatientClient } from "./components/payments-patient-client";

export const dynamic = "force-dynamic";

export default async function PatientPaymentsPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	return <PaymentsPatientClient user={user} />;
}
