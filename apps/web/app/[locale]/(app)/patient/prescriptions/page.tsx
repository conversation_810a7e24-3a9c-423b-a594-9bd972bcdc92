import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import PrescriptionsPatientClient from "./components/prescriptions-patient-client";

export const dynamic = "force-dynamic";

export default async function PatientPrescriptionsPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	return <PrescriptionsPatientClient user={user} />;
}
