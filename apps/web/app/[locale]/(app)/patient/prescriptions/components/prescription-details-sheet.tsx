"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@ui/components/sheet";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import {
  Calendar,
  Clock,
  User,
  Stethoscope,
  FileText,
  Download,
  AlertCircle,
  CheckCircle,
  XCircle,
  Pill,
  Info,
  ExternalLink
} from "lucide-react";
import { cn } from "@ui/lib";

interface PrescriptionDetails {
  id: string;
  appointmentId: string;
  doctor: {
    name: string;
    specialty: string;
    crm: string;
    crmState: string;
    image?: string | null;
  };
  patient: {
    name: string;
    email: string;
  };
  consultation: {
    id: string;
    scheduledAt: string;
    type: string;
    reason: string;
    duration: number;
    status: string;
  };
  prescription: {
    status: string;
    content: any;
    pdfUrl?: string | null;
    memedId?: string | null;
    createdAt: string;
    updatedAt: string;
  };
}

interface PrescriptionDetailsSheetProps {
  prescriptionId: string;
  children: React.ReactNode;
}

export function PrescriptionDetailsSheet({
  prescriptionId,
  children
}: PrescriptionDetailsSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [prescription, setPrescription] = useState<PrescriptionDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchPrescriptionDetails = async () => {
    if (!prescriptionId) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/prescriptions/${prescriptionId}`);
      if (!response.ok) {
        throw new Error('Erro ao buscar detalhes da prescrição');
      }
      const data = await response.json();
      setPrescription(data);
    } catch (error) {
      console.error('Erro ao buscar detalhes da prescrição:', error);
      toast.error('Erro ao carregar detalhes da prescrição');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && prescriptionId) {
      fetchPrescriptionDetails();
    }
  }, [isOpen, prescriptionId]);

  const getStatusInfo = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return {
          text: 'ATIVA',
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="h-4 w-4" />
        };
      case 'completed':
        return {
          text: 'CONCLUÍDA',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <CheckCircle className="h-4 w-4" />
        };
      case 'cancelled':
        return {
          text: 'CANCELADA',
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle className="h-4 w-4" />
        };
      default:
        return {
          text: 'DESCONHECIDO',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Info className="h-4 w-4" />
        };
    }
  };

  const getConsultationTypeInfo = (type: string) => {
    switch (type) {
      case 'ON_DUTY':
        return {
          text: 'PLANTÃO',
          color: 'bg-orange-100 text-orange-800 border-orange-200',
          icon: <AlertCircle className="h-4 w-4" />
        };
      case 'REGULAR':
        return {
          text: 'CONSULTA REGULAR',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <Calendar className="h-4 w-4" />
        };
      default:
        return {
          text: type,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Calendar className="h-4 w-4" />
        };
    }
  };

  const handleDownloadPDF = () => {
    if (prescription?.prescription.pdfUrl) {
      window.open(prescription.prescription.pdfUrl, '_blank');
    } else {
      toast.error('PDF não disponível para download');
    }
  };

  const handleOpenMemed = () => {
    if (prescription?.prescription.memedId) {
      // Abrir Memed em nova aba (URL seria baseada no ID)
      window.open(`https://memed.com.br/receita/${prescription.prescription.memedId}`, '_blank');
    } else {
      toast.error('Receita Memed não disponível');
    }
  };

  if (!prescription && isLoading) {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          {children}
        </SheetTrigger>
        <SheetContent className="w-full sm:max-w-2xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle>Carregando...</SheetTitle>
          </SheetHeader>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-2xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="text-2xl font-bold text-gray-900">
            Detalhes da Receita
          </SheetTitle>
        </SheetHeader>

        {prescription && (
          <div className="mt-6 space-y-6">
            {/* Status e Tipo */}
            <div className="flex flex-wrap gap-3">
              <Badge className={cn("px-3 py-2 text-sm font-medium border", getStatusInfo(prescription.prescription.status).color)}>
                {getStatusInfo(prescription.prescription.status).icon}
                <span className="ml-2">{getStatusInfo(prescription.prescription.status).text}</span>
              </Badge>
              <Badge className={cn("px-3 py-2 text-sm font-medium border", getConsultationTypeInfo(prescription.consultation.type).color)}>
                {getConsultationTypeInfo(prescription.consultation.type).icon}
                <span className="ml-2">{getConsultationTypeInfo(prescription.consultation.type).text}</span>
              </Badge>
            </div>

            {/* Informações do Médico */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={prescription.doctor.image || undefined} />
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-lg font-semibold">
                    {prescription.doctor.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg text-gray-900 mb-1">
                    {prescription.doctor.name}
                  </h3>
                  <p className="text-gray-600 mb-1">
                    {prescription.doctor.specialty}
                  </p>
                  <p className="text-gray-600 text-sm">
                    CRM {prescription.doctor.crm}/{prescription.doctor.crmState}
                  </p>
                </div>
              </div>
            </div>

            {/* Informações da Consulta */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Informações da Consulta
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-800">
                    {format(new Date(prescription.consultation.scheduledAt), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-800">
                    {prescription.consultation.duration} minutos
                  </span>
                </div>
                <div className="flex items-center gap-2 sm:col-span-2">
                  <Stethoscope className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-800">
                    {prescription.consultation.reason}
                  </span>
                </div>
              </div>
            </div>

            {/* Medicamentos Prescritos */}
            {prescription.prescription.content?.medications && prescription.prescription.content.medications.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Pill className="h-5 w-5" />
                  Medicamentos Prescritos
                </h4>
                <div className="space-y-3">
                  {prescription.prescription.content.medications.map((medication: any, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Pill className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-gray-900 mb-1">{medication.name}</h5>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-gray-600">
                          {medication.dosage && (
                            <div>
                              <span className="font-medium">Dosagem:</span> {medication.dosage}
                            </div>
                          )}
                          {medication.frequency && (
                            <div>
                              <span className="font-medium">Frequência:</span> {medication.frequency}
                            </div>
                          )}
                          {medication.duration && (
                            <div>
                              <span className="font-medium">Duração:</span> {medication.duration}
                            </div>
                          )}
                        </div>
                        {medication.instructions && (
                          <div className="mt-2 flex items-start gap-2">
                            <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                            <p className="text-sm text-gray-600">{medication.instructions}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Instruções Gerais */}
            {prescription.prescription.content?.instructions && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h5 className="font-semibold text-amber-900 mb-1">Instruções Gerais</h5>
                    <p className="text-amber-800">{prescription.prescription.content.instructions}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Validade */}
            {prescription.prescription.content?.validUntil && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-green-600" />
                  <div>
                    <h5 className="font-semibold text-green-900 mb-1">Validade da Receita</h5>
                    <p className="text-green-800">
                      Válida até {format(new Date(prescription.prescription.content.validUntil), "dd/MM/yyyy", { locale: ptBR })}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <Separator />

            {/* Ações */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Ações</h4>

              {/* Download PDF */}
              {prescription.prescription.pdfUrl && (
                <Button
                  onClick={handleDownloadPDF}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Baixar PDF da Receita
                </Button>
              )}

              {/* Abrir Memed */}
              {prescription.prescription.memedId && (
                <Button
                  onClick={handleOpenMemed}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Abrir no Memed
                </Button>
              )}

              {/* Mensagem quando não há PDF ou Memed */}
              {!prescription.prescription.pdfUrl && !prescription.prescription.memedId && (
                <div className="text-center py-4 text-gray-500">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>PDF da receita ainda não foi anexado pelo médico</p>
                </div>
              )}
            </div>

            {/* Informações Adicionais */}
            <div className="text-xs text-gray-500 text-center">
              <p>Receita criada em {format(new Date(prescription.prescription.createdAt), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</p>
              {prescription.prescription.updatedAt !== prescription.prescription.createdAt && (
                <p>Última atualização em {format(new Date(prescription.prescription.updatedAt), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</p>
              )}
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
