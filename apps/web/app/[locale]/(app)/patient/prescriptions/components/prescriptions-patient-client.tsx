"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Input } from "@ui/components/input";
import {
	Pill,
	Calendar,
	Download,
	Search,
	Clock,
	User,
	AlertCircle,
	CheckCircle,
	FileText,
	Share,
	Eye,
	Plus,
	X,
	ExternalLink
} from "lucide-react";
import { User as UserType } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import { cn } from "@ui/lib";
import { PageHeader } from "../../components/ui/page-header";
import { PrescriptionDetailsSheet } from "./prescription-details-sheet";

interface Prescription {
	id: string;
	appointmentId: string;
	doctor: {
		name: string;
		specialty: string;
		crm: string;
		image?: string | null;
	};
	date: string;
	consultation: {
		id: string;
		type: string;
		reason: string;
	};
	medications: any[];
	status: string;
	validUntil?: string | null;
	instructions?: string | null;
	digitalSignature: boolean;
	pdfUrl?: string | null;
	memedId?: string | null;
}

export default function PrescriptionsPatientClient({
	user,
}: {
	user: UserType;
}) {
	const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
	const [filteredPrescriptions, setFilteredPrescriptions] = useState<Prescription[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [isLoading, setIsLoading] = useState(true);

	// Buscar prescrições da API
	useEffect(() => {
		const fetchPrescriptions = async () => {
			try {
				const response = await fetch('/api/prescriptions/patient');
				if (!response.ok) {
					throw new Error('Erro ao carregar prescrições');
				}
				const data = await response.json();
				setPrescriptions(data.prescriptions || []);
				setFilteredPrescriptions(data.prescriptions || []);
			} catch (error) {
				console.error('Erro ao carregar prescrições:', error);
				toast.error('Erro ao carregar prescrições');
				setPrescriptions([]);
				setFilteredPrescriptions([]);
			} finally {
				setIsLoading(false);
			}
		};

		fetchPrescriptions();
	}, []);

	const activePrescriptions = prescriptions.filter(p => p.status === 'ACTIVE').length;
	const totalPrescriptions = prescriptions.length;
	const differentMedications = prescriptions.reduce((acc, p) => {
		return acc + (p.medications?.length || 0);
	}, 0);

	const handleSearch = (term: string) => {
		setSearchTerm(term);
		filterPrescriptions(term, statusFilter);
	};

	const handleStatusFilter = (status: string) => {
		setStatusFilter(status);
		filterPrescriptions(searchTerm, status);
	};

	const filterPrescriptions = (search: string, status: string) => {
		let filtered = prescriptions;

		if (search) {
			filtered = filtered.filter(p =>
				p.doctor.name.toLowerCase().includes(search.toLowerCase()) ||
				p.doctor.specialty.toLowerCase().includes(search.toLowerCase()) ||
				p.medications?.some((m: any) => m.name?.toLowerCase().includes(search.toLowerCase()))
			);
		}

		if (status !== "all") {
			filtered = filtered.filter(p => p.status.toLowerCase() === status.toLowerCase());
		}

		setFilteredPrescriptions(filtered);
	};

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case "active":
				return "bg-green-100 text-green-800 border-green-200";
			case "completed":
				return "bg-blue-100 text-blue-800 border-blue-200";
			case "cancelled":
				return "bg-red-100 text-red-800 border-red-200";
			case "expired":
				return "bg-orange-100 text-orange-800 border-orange-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const getStatusText = (status: string) => {
		switch (status.toLowerCase()) {
			case "active":
				return "ATIVA";
			case "completed":
				return "CONCLUÍDA";
			case "cancelled":
				return "CANCELADA";
			case "expired":
				return "EXPIRADA";
			default:
				return status.toUpperCase();
		}
	};

	const getConsultationTypeText = (type: string) => {
		switch (type) {
			case "ON_DUTY":
				return "PLANTÃO";
			case "REGULAR":
				return "CONSULTA REGULAR";
			default:
				return type;
		}
	};

	const handleDownloadPDF = (prescription: Prescription) => {
		if (prescription.pdfUrl) {
			window.open(prescription.pdfUrl, '_blank');
		} else {
			toast.error('PDF não disponível para download');
		}
	};

	const handleOpenMemed = (prescription: Prescription) => {
		if (prescription.memedId) {
			window.open(`https://memed.com.br/receita/${prescription.memedId}`, '_blank');
		} else {
			toast.error('Receita Memed não disponível');
		}
	};

	const handleShare = async (prescription: Prescription) => {
		try {
			if (navigator.share) {
				await navigator.share({
					title: `Receita - ${prescription.doctor.name}`,
					text: `Receita médica de ${prescription.doctor.name} - ${prescription.doctor.specialty}`,
					url: window.location.href
				});
			} else {
				// Fallback para copiar link
				await navigator.clipboard.writeText(window.location.href);
				toast.success('Link copiado para a área de transferência');
			}
		} catch (error) {
			console.error('Erro ao compartilhar:', error);
			toast.error('Erro ao compartilhar');
		}
	};

	if (isLoading) {
		return (
			<div className="container mx-auto max-w-6xl px-4 py-6">
				<div className="space-y-6">
					<PageHeader
						title="Minhas Receitas"
						subtitle="Visualize e gerencie todas as suas prescrições médicas"
						showFilter={true}
						showNewButton={true}
						newButtonText="Solicitar Receita"
					/>
					<div className="flex items-center justify-center py-12">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto max-w-6xl px-4 py-6">
			<div className="space-y-6">
				<PageHeader
					title="Minhas Receitas"
					subtitle="Visualize e gerencie todas as suas prescrições médicas"
					showFilter={true}
					showNewButton={true}
					newButtonText="Solicitar Receita"
					filterContent={
						<>
							<div>
								<label className="text-sm font-medium mb-2 block">Status</label>
								<select
									value={statusFilter}
									onChange={(e) => handleStatusFilter(e.target.value)}
									className="w-full p-2 border border-gray-300 rounded-md"
								>
									<option value="all">Todos os status</option>
									<option value="active">Ativas</option>
									<option value="completed">Concluídas</option>
									<option value="cancelled">Canceladas</option>
									<option value="expired">Expiradas</option>
								</select>
							</div>
							<div>
								<label className="text-sm font-medium mb-2 block">Buscar</label>
								<Input
									placeholder="Buscar por médico, especialidade ou medicamento..."
									value={searchTerm}
									onChange={(e) => handleSearch(e.target.value)}
								/>
							</div>
						</>
					}
				/>

				{/* Summary Cards */}
				<div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
					<Card className="text-center p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600 mb-1">Receitas Ativas</p>
								<p className="text-2xl font-bold text-green-600">{activePrescriptions}</p>
							</div>
							<div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
								<CheckCircle className="h-6 w-6 text-green-600" />
							</div>
						</div>
					</Card>

					<Card className="text-center p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600 mb-1">Total de Receitas</p>
								<p className="text-2xl font-bold text-blue-600">{totalPrescriptions}</p>
							</div>
							<div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
								<FileText className="h-6 w-6 text-blue-600" />
							</div>
						</div>
					</Card>

					<Card className="text-center p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600 mb-1">Medicamentos Diferentes</p>
								<p className="text-2xl font-bold text-purple-600">{differentMedications}</p>
							</div>
							<div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
								<Pill className="h-6 w-6 text-purple-600" />
							</div>
						</div>
					</Card>
				</div>

				{/* Search Bar */}
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
					<Input
						placeholder="Buscar prescrições..."
						value={searchTerm}
						onChange={(e) => handleSearch(e.target.value)}
						className="pl-10"
					/>
				</div>

				{/* Prescriptions List */}
				<div className="space-y-4">
					{filteredPrescriptions.map((prescription) => (
						<Card key={prescription.id} className="overflow-hidden">
							<CardHeader className="pb-4">
								<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
									<div className="flex items-start gap-3">
										<Avatar className="h-12 w-12">
											<AvatarImage src={prescription.doctor.image || undefined} />
											<AvatarFallback className="bg-gray-100 text-gray-600">
												{prescription.doctor.name.split(' ').map(n => n[0]).join('')}
											</AvatarFallback>
										</Avatar>
										<div className="flex-1">
											<div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
												<h3 className="font-semibold text-lg">{prescription.doctor.name}</h3>
												<div className="flex flex-wrap gap-2">
													<Badge className={cn("text-xs", getStatusColor(prescription.status))}>
														{getStatusText(prescription.status)}
													</Badge>
													<Badge className="text-xs bg-gray-100 text-gray-800 border-gray-200">
														{getConsultationTypeText(prescription.consultation.type)}
													</Badge>
												</div>
											</div>
											<p className="text-sm text-gray-600 mb-1">
												{prescription.doctor.specialty} - CRM {prescription.doctor.crm}
											</p>
											<p className="text-sm text-gray-600">
												{format(new Date(prescription.date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
											</p>
										</div>
									</div>
								</div>
							</CardHeader>

							<CardContent className="space-y-4">
								{/* Consultation Reason */}
								<div>
									<h4 className="font-semibold text-gray-900 mb-2">Motivo da Consulta</h4>
									<p className="text-gray-700">{prescription.consultation.reason}</p>
								</div>

								{/* Prescription Summary */}
								<div className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<FileText className="h-5 w-5 text-gray-600" />
											<span className="text-sm text-gray-600">
												{prescription.medications?.length || 0} medicamento(s) prescrito(s)
											</span>
										</div>
										{prescription.digitalSignature && (
											<Badge className="text-xs bg-green-100 text-green-800 border-green-200">
												<CheckCircle className="h-3 w-3 mr-1" />
												Assinada Digitalmente
											</Badge>
										)}
									</div>
									
									{/* Instruções resumidas */}
									{prescription.instructions && (
										<div className="mt-3 pt-3 border-t border-gray-200">
											<p className="text-sm text-gray-700 line-clamp-2">
												{prescription.instructions}
											</p>
										</div>
									)}
								</div>

								{/* Action Buttons */}
								<div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
									<PrescriptionDetailsSheet prescriptionId={prescription.id}>
										<Button variant="outline" size="sm" className="flex-1 sm:flex-none">
											<Eye className="h-4 w-4 mr-2" />
											Ver Detalhes
										</Button>
									</PrescriptionDetailsSheet>
									
									{prescription.pdfUrl ? (
										<Button 
											variant="outline" 
											size="sm" 
											className="flex-1 sm:flex-none"
											onClick={() => handleDownloadPDF(prescription)}
										>
											<Download className="h-4 w-4 mr-2" />
											Baixar PDF
										</Button>
									) : prescription.memedId ? (
										<Button 
											variant="outline" 
											size="sm" 
											className="flex-1 sm:flex-none"
											onClick={() => handleOpenMemed(prescription)}
										>
											<ExternalLink className="h-4 w-4 mr-2" />
											Abrir Memed
										</Button>
									) : (
										<Button 
											variant="outline" 
											size="sm" 
											className="flex-1 sm:flex-none"
											disabled
										>
											<FileText className="h-4 w-4 mr-2" />
											PDF Pendente
										</Button>
									)}
									
									<Button 
										variant="outline" 
										size="sm" 
										className="flex-1 sm:flex-none"
										onClick={() => handleShare(prescription)}
									>
										<Share className="h-4 w-4 mr-2" />
										Compartilhar
									</Button>
								</div>
							</CardContent>
						</Card>
					))}
				</div>

				{filteredPrescriptions.length === 0 && (
					<div className="text-center py-12">
						<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma prescrição encontrada</h3>
						<p className="text-gray-600">
							{searchTerm || statusFilter !== "all"
								? "Tente ajustar os filtros de busca"
								: "Você ainda não possui prescrições médicas"
							}
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
