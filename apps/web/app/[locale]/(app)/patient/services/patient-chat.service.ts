import { ChatService, ChatMessage, ChatServiceConfig } from "../../app/zapchat/services/chat.service";

export interface PatientChatMessage {
  id: string;
  content: string;
  isFromDoctor: boolean;
  createdAt: string;
  type: "text" | "audio" | "file" | "system";
  metadata?: Record<string, any>;
}

export interface PatientChatConfig {
  appointmentId: string;
  userId: string;
  onMessage?: (message: PatientChatMessage) => void;
  onError?: (error: Error) => void;
  onConnectionChange?: (connected: boolean) => void;
}

export class PatientChatService {
  private chatService: ChatService;

  constructor(config: PatientChatConfig) {
    const chatConfig: ChatServiceConfig = {
      appointmentId: config.appointmentId,
      userId: config.userId,
      userRole: 'PATIENT',
      onMessage: config.onMessage ? (message: ChatMessage) => {
        config.onMessage!(this.transformMessage(message));
      } : undefined,
      onError: config.onError,
      onConnectionChange: config.onConnectionChange
    };

    this.chatService = new ChatService(chatConfig);
  }

  async connect(): Promise<void> {
    return this.chatService.connect();
  }

  async disconnect(): Promise<void> {
    return this.chatService.disconnect();
  }

  async getChatMessages(appointmentId: string): Promise<PatientChatMessage[]> {
    try {
      const messages = await this.chatService.getMessages();
      return messages.map(msg => this.transformMessage(msg));
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      throw new Error('Não foi possível carregar as mensagens');
    }
  }

  async sendMessage(appointmentId: string, content: string): Promise<PatientChatMessage> {
    try {
      const message = await this.chatService.sendTextMessage(content);
      return this.transformMessage(message);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw new Error('Não foi possível enviar a mensagem');
    }
  }

  async sendAudioMessage(audioBlob: Blob): Promise<PatientChatMessage> {
    try {
      const message = await this.chatService.sendAudioMessage(audioBlob);
      return this.transformMessage(message);
    } catch (error) {
      console.error('Erro ao enviar áudio:', error);
      throw new Error('Não foi possível enviar o áudio');
    }
  }

  async sendFileMessage(file: File): Promise<PatientChatMessage> {
    try {
      const message = await this.chatService.sendFileMessage(file);
      return this.transformMessage(message);
    } catch (error) {
      console.error('Erro ao enviar arquivo:', error);
      throw new Error('Não foi possível enviar o arquivo');
    }
  }

  getConnectionStatus(): boolean {
    return this.chatService.getConnectionStatus();
  }

  destroy(): void {
    this.chatService.destroy();
  }

  private transformMessage(message: ChatMessage): PatientChatMessage {
    return {
      id: message.id,
      content: message.content,
      isFromDoctor: message.senderRole === 'DOCTOR',
      createdAt: message.createdAt,
      type: message.type.toLowerCase() as "text" | "audio" | "file" | "system",
      metadata: message.metadata
    };
  }
}

// Factory function para criar instância do serviço
export function createPatientChatService(config: PatientChatConfig): PatientChatService {
  return new PatientChatService(config);
}
