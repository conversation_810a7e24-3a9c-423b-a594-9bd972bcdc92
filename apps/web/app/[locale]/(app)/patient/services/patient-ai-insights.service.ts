"use client";

export interface QuizAnswers {
	[key: string]: string | string[];
}

export interface AIInsights {
	riskLevel: 'low' | 'medium' | 'high';
	recommendedSpecialties: string[];
	prioritySymptoms: string[];
	lifestyleRecommendations: string[];
	urgencyScore: number;
	personalizedMessage: string;
	doctorNotes: {
		patientProfile: string;
		keySymptoms: string[];
		recommendedApproach: string;
		followUpSuggestions: string[];
	};
}

export class PatientAIInsightsService {
	/**
	 * Analisa as respostas do quiz e gera insights personalizados
	 */
	static async generateInsights(answers: QuizAnswers): Promise<AIInsights> {
		// Simular processamento de IA - em produção, isso seria uma chamada para API de IA
		const insights = this.analyzeAnswers(answers);

		// Simular delay de processamento
		await new Promise(resolve => setTimeout(resolve, 1500));

		return insights;
	}

	/**
	 * Aná<PERSON><PERSON> das respostas usando lógica de IA simulada
	 */
	private static analyzeAnswers(answers: QuizAnswers): AIInsights {
		const mainConcern = answers.main_concern as string;
		const symptomsFrequency = answers.symptoms_frequency as string;
		const lifestyleHabits = answers.lifestyle_habits as string[];
		const medicalPriority = answers.medical_priority as string;
		const urgencyLevel = parseInt(answers.urgency_level as string) || 3;
		const specialtyInterest = answers.specialty_interest as string[];

		// Calcular nível de risco
		const riskLevel = this.calculateRiskLevel(symptomsFrequency, lifestyleHabits, urgencyLevel);

		// Determinar especialidades recomendadas
		const recommendedSpecialties = this.getRecommendedSpecialties(mainConcern, specialtyInterest);

		// Identificar sintomas prioritários
		const prioritySymptoms = this.getPrioritySymptoms(mainConcern, symptomsFrequency);

		// Gerar recomendações de estilo de vida
		const lifestyleRecommendations = this.getLifestyleRecommendations(lifestyleHabits, mainConcern);

		// Criar mensagem personalizada
		const personalizedMessage = this.createPersonalizedMessage(riskLevel, mainConcern, urgencyLevel);

		// Gerar notas para o médico
		const doctorNotes = this.generateDoctorNotes(answers, riskLevel);

		return {
			riskLevel,
			recommendedSpecialties,
			prioritySymptoms,
			lifestyleRecommendations,
			urgencyScore: urgencyLevel,
			personalizedMessage,
			doctorNotes
		};
	}

	/**
	 * Calcula o nível de risco baseado nas respostas
	 */
	private static calculateRiskLevel(
		frequency: string,
		lifestyle: string[],
		urgency: number
	): 'low' | 'medium' | 'high' {
		let riskScore = 0;

		// Pontuação por frequência de sintomas
		switch (frequency) {
			case 'Diariamente': riskScore += 3; break;
			case 'Algumas vezes por semana': riskScore += 2; break;
			case 'Algumas vezes por mês': riskScore += 1; break;
			default: riskScore += 0;
		}

		// Pontuação por hábitos de vida negativos
		const negativeHabits = ['Trabalho estressante', 'Consumo de álcool', 'Tabagismo', 'Vida sedentária'];
		const negativeCount = lifestyle?.filter(habit => negativeHabits.includes(habit)).length || 0;
		riskScore += negativeCount;

		// Pontuação por urgência
		if (urgency >= 4) riskScore += 2;
		else if (urgency >= 3) riskScore += 1;

		// Determinar nível de risco
		if (riskScore >= 5) return 'high';
		if (riskScore >= 3) return 'medium';
		return 'low';
	}

	/**
	 * Determina especialidades recomendadas baseadas nas preocupações
	 */
	private static getRecommendedSpecialties(mainConcern: string, interests: string[]): string[] {
		const specialtyMap: Record<string, string[]> = {
			'Dores frequentes (cabeça, costas, articulações)': ['Clínico Geral', 'Ortopedista', 'Neurologista'],
			'Problemas digestivos': ['Clínico Geral', 'Gastroenterologista'],
			'Ansiedade ou estresse': ['Clínico Geral', 'Psicólogo', 'Psiquiatra'],
			'Problemas de sono': ['Clínico Geral', 'Neurologista'],
			'Questões cardiovasculares': ['Clínico Geral', 'Cardiologista'],
			'Controle de peso': ['Clínico Geral', 'Endocrinologista', 'Nutricionista'],
			'Cuidados preventivos': ['Clínico Geral']
		};

		let recommended = specialtyMap[mainConcern] || ['Clínico Geral'];

		// Adicionar especialidades de interesse se não estiverem incluídas
		if (interests && interests.length > 0) {
			const validInterests = interests.filter(interest => interest !== 'Não tenho certeza');
			recommended = [...new Set([...recommended, ...validInterests])];
		}

		return recommended.slice(0, 3); // Limitar a 3 especialidades
	}

	/**
	 * Identifica sintomas prioritários para atenção médica
	 */
	private static getPrioritySymptoms(mainConcern: string, frequency: string): string[] {
		const symptoms = [mainConcern];

		if (frequency === 'Diariamente') {
			symptoms.push('Sintomas diários requerem atenção');
		}

		return symptoms;
	}

	/**
	 * Gera recomendações de estilo de vida personalizadas
	 */
	private static getLifestyleRecommendations(habits: string[], concern: string): string[] {
		const recommendations: string[] = [];

		if (!habits?.includes('Exercícios regulares')) {
			recommendations.push('Incorpore atividades físicas leves na rotina diária');
		}

		if (!habits?.includes('Alimentação balanceada')) {
			recommendations.push('Mantenha uma alimentação equilibrada e nutritiva');
		}

		if (!habits?.includes('Sono regular (7-8h)')) {
			recommendations.push('Estabeleça uma rotina de sono de 7-8 horas por noite');
		}

		if (habits?.includes('Trabalho estressante') || concern.includes('Ansiedade')) {
			recommendations.push('Pratique técnicas de relaxamento e gerenciamento do estresse');
		}

		if (habits?.includes('Vida sedentária')) {
			recommendations.push('Faça pausas regulares para movimentar-se durante o dia');
		}

		return recommendations.slice(0, 4); // Limitar a 4 recomendações
	}

	/**
	 * Cria mensagem personalizada para o paciente
	 */
	private static createPersonalizedMessage(riskLevel: string, concern: string, urgency: number): string {
		const urgencyText = urgency >= 4 ? 'com prioridade' : 'quando conveniente';

		switch (riskLevel) {
			case 'high':
				return `Com base nas suas respostas, recomendamos uma consulta médica ${urgencyText}. Suas preocupações com ${concern.toLowerCase()} merecem atenção profissional.`;
			case 'medium':
				return `Suas respostas indicam que uma consulta médica seria benéfica para abordar ${concern.toLowerCase()}. Agende ${urgencyText} para uma avaliação.`;
			default:
				return `É sempre bom manter cuidados preventivos. Uma consulta sobre ${concern.toLowerCase()} pode ajudar a manter sua saúde em dia.`;
		}
	}

	/**
	 * Gera notas específicas para o médico
	 */
	private static generateDoctorNotes(answers: QuizAnswers, riskLevel: string): AIInsights['doctorNotes'] {
		const mainConcern = answers.main_concern as string;
		const frequency = answers.symptoms_frequency as string;
		const lifestyle = answers.lifestyle_habits as string[] || [];
		const priority = answers.medical_priority as string;
		const urgency = parseInt(answers.urgency_level as string) || 3;

		// Perfil do paciente
		const patientProfile = this.createPatientProfile(lifestyle, frequency, urgency);

		// Sintomas chave
		const keySymptoms = [mainConcern];
		if (frequency === 'Diariamente') keySymptoms.push('Sintomas diários');

		// Abordagem recomendada
		const recommendedApproach = this.getRecommendedApproach(priority, riskLevel);

		// Sugestões de acompanhamento
		const followUpSuggestions = this.getFollowUpSuggestions(riskLevel, mainConcern);

		return {
			patientProfile,
			keySymptoms,
			recommendedApproach,
			followUpSuggestions
		};
	}

	private static createPatientProfile(lifestyle: string[], frequency: string, urgency: number): string {
		const positiveHabits = lifestyle.filter(habit =>
			['Exercícios regulares', 'Alimentação balanceada', 'Meditação ou relaxamento', 'Sono regular (7-8h)'].includes(habit)
		);

		const negativeHabits = lifestyle.filter(habit =>
			['Trabalho estressante', 'Consumo de álcool', 'Tabagismo', 'Vida sedentária'].includes(habit)
		);

		let profile = `Paciente com sintomas ${frequency.toLowerCase()}. `;

		if (positiveHabits.length > 0) {
			profile += `Hábitos positivos: ${positiveHabits.join(', ')}. `;
		}

		if (negativeHabits.length > 0) {
			profile += `Fatores de risco: ${negativeHabits.join(', ')}. `;
		}

		profile += `Nível de urgência auto-relatado: ${urgency}/5.`;

		return profile;
	}

	private static getRecommendedApproach(priority: string, riskLevel: string): string {
		const approaches: Record<string, string> = {
			'Diagnóstico rápido e preciso': 'Foque em exame físico direcionado e diagnóstico diferencial',
			'Tempo para explicar meus sintomas': 'Reserve tempo adequado para escuta ativa e anamnese detalhada',
			'Orientações de prevenção': 'Enfatize medidas preventivas e educação em saúde',
			'Tratamento natural quando possível': 'Considere abordagens integrativas quando apropriado',
			'Acompanhamento contínuo': 'Estabeleça plano de seguimento regular',
			'Segunda opinião médica': 'Esteja aberto a discussão e encaminhamentos se necessário'
		};

		let approach = approaches[priority] || 'Abordagem clínica padrão';

		if (riskLevel === 'high') {
			approach += '. Atenção especial aos sintomas de alta prioridade.';
		}

		return approach;
	}

	private static getFollowUpSuggestions(riskLevel: string, concern: string): string[] {
		const suggestions = ['Agendar retorno em 2-4 semanas'];

		if (riskLevel === 'high') {
			suggestions.push('Considerar exames complementares');
			suggestions.push('Monitoramento mais frequente');
		}

		if (concern.includes('Ansiedade') || concern.includes('estresse')) {
			suggestions.push('Avaliar necessidade de suporte psicológico');
		}

		if (concern.includes('Dores')) {
			suggestions.push('Considerar avaliação de especialista se não houver melhora');
		}

		return suggestions;
	}

	/**
	 * Salva os insights no perfil do paciente
	 */
	static async saveInsights(userId: string, insights: AIInsights): Promise<void> {
		// Em produção, isso salvaria no banco de dados
		console.log('Salvando insights para usuário:', userId, insights);

		// Simular salvamento
		await new Promise(resolve => setTimeout(resolve, 500));
	}

	/**
	 * Recupera insights salvos do paciente
	 */
	static async getPatientInsights(userId: string): Promise<AIInsights | null> {
		// Em produção, isso recuperaria do banco de dados
		console.log('Recuperando insights para usuário:', userId);

		// Simular recuperação
		await new Promise(resolve => setTimeout(resolve, 300));
		return null;
	}
}
