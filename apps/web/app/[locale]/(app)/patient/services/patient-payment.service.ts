export interface PaymentMethod {
  id: string;
  type: 'CREDIT_CARD' | 'DEBIT_CARD' | 'PIX' | 'BOLETO' | 'WALLET';
  name: string;
  lastFourDigits?: string;
  brand?: string;
  expiryDate?: string;
  isDefault: boolean;
  isActive: boolean;
}

export interface Payment {
  id: string;
  appointmentId?: string;
  subscriptionId?: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'PAID' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
  method: PaymentMethod;
  description: string;
  createdAt: string;
  paidAt?: string;
  failureReason?: string;
  installments?: number;
  invoice?: Invoice;
}

export interface Invoice {
  id: string;
  number: string;
  downloadUrl: string;
  issuedAt: string;
}

export interface PaymentRequest {
  appointmentId?: string;
  subscriptionId?: string;
  amount: number;
  paymentMethodId: string;
  installments?: number;
  description?: string;
}

export interface PixPayment {
  qrCode: string;
  qrCodeBase64: string;
  expiresAt: string;
  amount: number;
}

export interface BoletoPayment {
  barCode: string;
  digitableLine: string;
  downloadUrl: string;
  expiresAt: string;
  amount: number;
}

export class PatientPaymentService {
  private baseUrl = '/api/patient';

  /**
   * Buscar métodos de pagamento do paciente
   */
  async getPaymentMethods(patientId: string): Promise<PaymentMethod[]> {
    try {
      const response = await fetch(`${this.baseUrl}/payment-methods?patientId=${patientId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPaymentMethods(data);
    } catch (error) {
      console.error('Erro ao buscar métodos de pagamento:', error);
      return this.getFallbackPaymentMethods();
    }
  }

  /**
   * Adicionar novo método de pagamento
   */
  async addPaymentMethod(patientId: string, paymentData: any): Promise<PaymentMethod> {
    try {
      const response = await fetch(`${this.baseUrl}/payment-methods`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId,
          ...paymentData
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPaymentMethod(data);
    } catch (error) {
      console.error('Erro ao adicionar método de pagamento:', error);
      throw new Error('Não foi possível adicionar o método de pagamento');
    }
  }

  /**
   * Remover método de pagamento
   */
  async removePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/payment-methods/${paymentMethodId}`, {
        method: 'DELETE',
      });

      return response.ok;
    } catch (error) {
      console.error('Erro ao remover método de pagamento:', error);
      return false;
    }
  }

  /**
   * Definir método de pagamento padrão
   */
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/payment-methods/${paymentMethodId}/set-default`, {
        method: 'PUT',
      });

      return response.ok;
    } catch (error) {
      console.error('Erro ao definir método padrão:', error);
      return false;
    }
  }

  /**
   * Buscar histórico de pagamentos
   */
  async getPaymentHistory(patientId: string, page = 1, limit = 20): Promise<Payment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/payments?patientId=${patientId}&page=${page}&limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPayments(data.payments || data);
    } catch (error) {
      console.error('Erro ao buscar histórico de pagamentos:', error);
      return this.getFallbackPayments();
    }
  }

  /**
   * Processar pagamento
   */
  async processPayment(paymentRequest: PaymentRequest): Promise<Payment> {
    try {
      const response = await fetch(`${this.baseUrl}/payments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPayment(data);
    } catch (error) {
      console.error('Erro ao processar pagamento:', error);
      throw new Error('Não foi possível processar o pagamento');
    }
  }

  /**
   * Gerar pagamento PIX
   */
  async generatePixPayment(paymentRequest: Omit<PaymentRequest, 'paymentMethodId'>): Promise<PixPayment> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/pix`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        qrCode: data.qrCode,
        qrCodeBase64: data.qrCodeBase64,
        expiresAt: data.expiresAt,
        amount: data.amount
      };
    } catch (error) {
      console.error('Erro ao gerar PIX:', error);
      throw new Error('Não foi possível gerar o pagamento PIX');
    }
  }

  /**
   * Gerar boleto
   */
  async generateBoleto(paymentRequest: Omit<PaymentRequest, 'paymentMethodId'>): Promise<BoletoPayment> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/boleto`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        barCode: data.barCode,
        digitableLine: data.digitableLine,
        downloadUrl: data.downloadUrl,
        expiresAt: data.expiresAt,
        amount: data.amount
      };
    } catch (error) {
      console.error('Erro ao gerar boleto:', error);
      throw new Error('Não foi possível gerar o boleto');
    }
  }

  /**
   * Verificar status de pagamento
   */
  async checkPaymentStatus(paymentId: string): Promise<Payment | null> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/${paymentId}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPayment(data);
    } catch (error) {
      console.error('Erro ao verificar status do pagamento:', error);
      return null;
    }
  }

  /**
   * Baixar nota fiscal
   */
  async downloadInvoice(invoiceId: string): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/invoices/${invoiceId}/download`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Erro ao baixar nota fiscal:', error);
      throw new Error('Não foi possível baixar a nota fiscal');
    }
  }

  /**
   * Solicitar reembolso
   */
  async requestRefund(paymentId: string, reason?: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/payments/${paymentId}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason }),
      });

      return response.ok;
    } catch (error) {
      console.error('Erro ao solicitar reembolso:', error);
      return false;
    }
  }

  // Métodos privados para transformação de dados

  private transformPaymentMethods(data: any[]): PaymentMethod[] {
    return data.map(item => this.transformPaymentMethod(item));
  }

  private transformPaymentMethod(item: any): PaymentMethod {
    return {
      id: item.id,
      type: item.type || 'CREDIT_CARD',
      name: item.name || this.getPaymentMethodName(item),
      lastFourDigits: item.lastFourDigits,
      brand: item.brand,
      expiryDate: item.expiryDate,
      isDefault: item.isDefault || false,
      isActive: item.isActive !== false
    };
  }

  private transformPayments(data: any[]): Payment[] {
    return data.map(item => this.transformPayment(item));
  }

  private transformPayment(item: any): Payment {
    return {
      id: item.id,
      appointmentId: item.appointmentId,
      subscriptionId: item.subscriptionId,
      amount: item.amount || 0,
      currency: item.currency || 'BRL',
      status: item.status || 'PENDING',
      method: this.transformPaymentMethod(item.paymentMethod || {}),
      description: item.description || 'Pagamento',
      createdAt: item.createdAt,
      paidAt: item.paidAt,
      failureReason: item.failureReason,
      installments: item.installments,
      invoice: item.invoice ? {
        id: item.invoice.id,
        number: item.invoice.number,
        downloadUrl: item.invoice.downloadUrl,
        issuedAt: item.invoice.issuedAt
      } : undefined
    };
  }

  private getPaymentMethodName(item: any): string {
    if (item.type === 'PIX') return 'PIX';
    if (item.type === 'BOLETO') return 'Boleto Bancário';
    if (item.brand && item.lastFourDigits) {
      return `${item.brand} •••• ${item.lastFourDigits}`;
    }
    return 'Cartão';
  }

  // Dados de fallback para desenvolvimento

  private getFallbackPaymentMethods(): PaymentMethod[] {
    return [
      {
        id: "pm-1",
        type: "CREDIT_CARD",
        name: "Visa •••• 1234",
        lastFourDigits: "1234",
        brand: "Visa",
        expiryDate: "12/28",
        isDefault: true,
        isActive: true
      },
      {
        id: "pm-2",
        type: "CREDIT_CARD",
        name: "Mastercard •••• 5678",
        lastFourDigits: "5678",
        brand: "Mastercard",
        expiryDate: "06/27",
        isDefault: false,
        isActive: true
      },
      {
        id: "pm-3",
        type: "PIX",
        name: "PIX",
        isDefault: false,
        isActive: true
      }
    ];
  }

  private getFallbackPayments(): Payment[] {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    return [
      {
        id: "payment-1",
        appointmentId: "appointment-1",
        amount: 18000, // R$ 180,00 em centavos
        currency: "BRL",
        status: "PAID",
        method: {
          id: "pm-1",
          type: "CREDIT_CARD",
          name: "Visa •••• 1234",
          lastFourDigits: "1234",
          brand: "Visa",
          isDefault: true,
          isActive: true
        },
        description: "Consulta - Dr. João Silva",
        createdAt: now.toISOString(),
        paidAt: now.toISOString(),
        invoice: {
          id: "inv-1",
          number: "NF-001234",
          downloadUrl: "/api/invoices/inv-1/download",
          issuedAt: now.toISOString()
        }
      },
      {
        id: "payment-2",
        appointmentId: "appointment-2",
        amount: 22000, // R$ 220,00 em centavos
        currency: "BRL",
        status: "PAID",
        method: {
          id: "pm-3",
          type: "PIX",
          name: "PIX",
          isDefault: false,
          isActive: true
        },
        description: "Consulta - Dra. Maria Santos",
        createdAt: oneWeekAgo.toISOString(),
        paidAt: oneWeekAgo.toISOString(),
        invoice: {
          id: "inv-2",
          number: "NF-001235",
          downloadUrl: "/api/invoices/inv-2/download",
          issuedAt: oneWeekAgo.toISOString()
        }
      },
      {
        id: "payment-3",
        subscriptionId: "subscription-1",
        amount: 8900, // R$ 89,00 em centavos
        currency: "BRL",
        status: "PAID",
        method: {
          id: "pm-1",
          type: "CREDIT_CARD",
          name: "Visa •••• 1234",
          lastFourDigits: "1234",
          brand: "Visa",
          isDefault: true,
          isActive: true
        },
        description: "Assinatura ZapVida Sempre - 2 Consultas por Mês",
        createdAt: twoWeeksAgo.toISOString(),
        paidAt: twoWeeksAgo.toISOString(),
        invoice: {
          id: "inv-3",
          number: "NF-001236",
          downloadUrl: "/api/invoices/inv-3/download",
          issuedAt: twoWeeksAgo.toISOString()
        }
      }
    ];
  }
}
