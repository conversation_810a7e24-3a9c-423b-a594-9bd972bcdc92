import { db } from 'database';

export class PatientSubscriptionService {
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const patient = await db.patient.findFirst({
        where: { userId },
        include: {
          subscriptions: {
            where: {
              status: 'ACTIVE',
              OR: [
                { endDate: null },
                { endDate: { gt: new Date() } }
              ]
            },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      if (!patient) {
        return false;
      }

      return patient.subscriptions.length > 0;
    } catch (error) {
      console.error('Erro ao verificar assinatura ativa:', error);
      return false;
    }
  }

  static async getActiveSubscription(userId: string) {
    try {
      const patient = await db.patient.findFirst({
        where: { userId },
        include: {
          subscriptions: {
            where: {
              status: 'ACTIVE',
              OR: [
                { endDate: null },
                { endDate: { gt: new Date() } }
              ]
            },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      return patient?.subscriptions[0] || null;
    } catch (error) {
      console.error('Erro ao buscar assinatura ativa:', error);
      return null;
    }
  }
}