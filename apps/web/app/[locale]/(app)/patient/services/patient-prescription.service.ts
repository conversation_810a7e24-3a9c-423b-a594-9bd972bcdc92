export interface Prescription {
  id: string;
  appointmentId: string;
  doctorName: string;
  doctorCrm: string;
  patientName: string;
  issuedAt: string;
  validUntil?: string;
  status: 'ACTIVE' | 'EXPIRED' | 'USED' | 'CANCELLED';
  medications: Medication[];
  observations?: string;
  digitalSignature?: string;
  qrCode?: string;
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  quantity?: number;
  unit?: string;
  isControlled?: boolean;
  isGeneric?: boolean;
}

export interface PrescriptionFilters {
  status?: string;
  doctorId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export class PatientPrescriptionService {
  private baseUrl = '/api/patient';

  /**
   * Buscar prescrições do paciente
   */
  async getPatientPrescriptions(patientId: string, filters?: PrescriptionFilters): Promise<Prescription[]> {
    try {
      let url = `${this.baseUrl}/prescriptions?patientId=${patientId}`;

      if (filters) {
        if (filters.status) url += `&status=${filters.status}`;
        if (filters.doctorId) url += `&doctorId=${filters.doctorId}`;
        if (filters.dateFrom) url += `&dateFrom=${filters.dateFrom}`;
        if (filters.dateTo) url += `&dateTo=${filters.dateTo}`;
        if (filters.search) url += `&search=${encodeURIComponent(filters.search)}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPrescriptions(data);
    } catch (error) {
      console.error('Erro ao buscar prescrições:', error);
      // Fallback para prescrições mockadas
      return this.getFallbackPrescriptions();
    }
  }

  /**
   * Buscar prescrição específica
   */
  async getPrescription(prescriptionId: string): Promise<Prescription | null> {
    try {
      const response = await fetch(`${this.baseUrl}/prescriptions/${prescriptionId}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPrescription(data);
    } catch (error) {
      console.error('Erro ao buscar prescrição:', error);
      return null;
    }
  }

  /**
   * Buscar prescrições recentes (últimas 30 dias)
   */
  async getRecentPrescriptions(patientId: string): Promise<Prescription[]> {
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - 30);

    return this.getPatientPrescriptions(patientId, {
      dateFrom: dateFrom.toISOString().split('T')[0],
      status: 'ACTIVE'
    });
  }

  /**
   * Baixar prescrição como PDF
   */
  async downloadPrescriptionPDF(prescriptionId: string): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/prescriptions/${prescriptionId}/pdf`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Erro ao baixar PDF da prescrição:', error);
      throw new Error('Não foi possível baixar a prescrição');
    }
  }

  /**
   * Validar prescrição por QR Code
   */
  async validatePrescriptionByQR(qrCode: string): Promise<Prescription | null> {
    try {
      const response = await fetch(`${this.baseUrl}/prescriptions/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ qrCode }),
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformPrescription(data);
    } catch (error) {
      console.error('Erro ao validar prescrição:', error);
      return null;
    }
  }

  /**
   * Marcar medicamento como usado
   */
  async markMedicationAsUsed(prescriptionId: string, medicationId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/prescriptions/${prescriptionId}/medications/${medicationId}/use`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Erro ao marcar medicamento como usado:', error);
      return false;
    }
  }

  /**
   * Buscar histórico de medicamentos
   */
  async getMedicationHistory(patientId: string): Promise<Medication[]> {
    try {
      const response = await fetch(`${this.baseUrl}/medications/history?patientId=${patientId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.transformMedications(data);
    } catch (error) {
      console.error('Erro ao buscar histórico de medicamentos:', error);
      return [];
    }
  }

  // Métodos privados para transformação de dados

  private transformPrescriptions(data: any[]): Prescription[] {
    return data.map(item => this.transformPrescription(item));
  }

  private transformPrescription(item: any): Prescription {
    return {
      id: item.id,
      appointmentId: item.appointmentId,
      doctorName: item.doctor?.name || item.doctorName || 'Médico não informado',
      doctorCrm: item.doctor?.crm || item.doctorCrm || 'CRM não informado',
      patientName: item.patient?.name || item.patientName || 'Paciente',
      issuedAt: item.issuedAt || item.createdAt,
      validUntil: item.validUntil,
      status: item.status || 'ACTIVE',
      medications: this.transformMedications(item.medications || []),
      observations: item.observations,
      digitalSignature: item.digitalSignature,
      qrCode: item.qrCode
    };
  }

  private transformMedications(data: any[]): Medication[] {
    return data.map(item => ({
      id: item.id,
      name: item.name || item.medication || 'Medicamento',
      dosage: item.dosage || 'Conforme orientação médica',
      frequency: item.frequency || 'Conforme orientação médica',
      duration: item.duration || 'Conforme orientação médica',
      instructions: item.instructions || item.usage || 'Usar conforme orientação médica',
      quantity: item.quantity,
      unit: item.unit || 'unidade(s)',
      isControlled: item.isControlled || false,
      isGeneric: item.isGeneric || false
    }));
  }

  // Dados de fallback para desenvolvimento

  private getFallbackPrescriptions(): Prescription[] {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    return [
      {
        id: "prescription-1",
        appointmentId: "appointment-1",
        doctorName: "Dr. João Silva",
        doctorCrm: "CRM/SP 123456",
        patientName: "Paciente",
        issuedAt: now.toISOString(),
        validUntil: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: "ACTIVE",
        observations: "Tomar sempre após as refeições. Evitar bebidas alcoólicas durante o tratamento.",
        medications: [
          {
            id: "med-1",
            name: "Dipirona 500mg",
            dosage: "500mg",
            frequency: "8 em 8 horas",
            duration: "7 dias",
            instructions: "Tomar 1 comprimido de 8 em 8 horas",
            quantity: 21,
            unit: "comprimidos",
            isControlled: false,
            isGeneric: true
          },
          {
            id: "med-2",
            name: "Omeprazol 20mg",
            dosage: "20mg",
            frequency: "1 vez ao dia",
            duration: "30 dias",
            instructions: "Tomar 1 cápsula pela manhã, em jejum",
            quantity: 30,
            unit: "cápsulas",
            isControlled: false,
            isGeneric: true
          }
        ],
        qrCode: "QR123456789"
      },
      {
        id: "prescription-2",
        appointmentId: "appointment-2",
        doctorName: "Dra. Maria Santos",
        doctorCrm: "CRM/SP 789012",
        patientName: "Paciente",
        issuedAt: oneWeekAgo.toISOString(),
        validUntil: new Date(oneWeekAgo.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: "ACTIVE",
        observations: "Aplicar conforme orientação. Não interromper o tratamento sem orientação médica.",
        medications: [
          {
            id: "med-3",
            name: "Betametasona Creme 0,1%",
            dosage: "0,1%",
            frequency: "2 vezes ao dia",
            duration: "14 dias",
            instructions: "Aplicar uma fina camada na região afetada, 2 vezes ao dia",
            quantity: 1,
            unit: "tubo",
            isControlled: false,
            isGeneric: false
          }
        ],
        qrCode: "QR987654321"
      },
      {
        id: "prescription-3",
        appointmentId: "appointment-3",
        doctorName: "Dr. Pedro Costa",
        doctorCrm: "CRM/SP 345678",
        patientName: "Paciente",
        issuedAt: twoWeeksAgo.toISOString(),
        validUntil: new Date(twoWeeksAgo.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: "USED",
        observations: "Tratamento concluído com sucesso.",
        medications: [
          {
            id: "med-4",
            name: "Amoxicilina 500mg",
            dosage: "500mg",
            frequency: "8 em 8 horas",
            duration: "10 dias",
            instructions: "Tomar 1 cápsula de 8 em 8 horas, sempre no mesmo horário",
            quantity: 30,
            unit: "cápsulas",
            isControlled: false,
            isGeneric: true
          }
        ],
        qrCode: "QR456789123"
      }
    ];
  }
}
