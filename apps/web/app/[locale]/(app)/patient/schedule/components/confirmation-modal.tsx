"use client";

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON>Title,
	Di<PERSON>Footer
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Calendar, Clock, CreditCard, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface Doctor {
	id: string;
	name: string;
	specialty: string;
	image?: string;
	crm: string;
	rating: number;
	reviewCount: number;
	price: number;
	location: string;
	availableSlots: string[];
	isOnline: boolean;
}

interface ConfirmationModalProps {
	doctor: Doctor;
	date: Date;
	time: string;
	isLoading: boolean;
	onConfirm: () => void;
	onCancel: () => void;
}

export function ConfirmationModal({
	doctor,
	date,
	time,
	isLoading,
	onConfirm,
	onCancel
}: ConfirmationModalProps) {
	return (
		<Dialog open={true} onOpenChange={onCancel}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle>Confirmar Agendamento</DialogTitle>
				</DialogHeader>

				<div className="space-y-6">
					{/* Doctor Info */}
					<div className="flex items-center gap-4">
						<Avatar className="w-16 h-16">
							<AvatarImage src={doctor.image} />
							<AvatarFallback>
								{doctor.name.split(' ').map(n => n[0]).join('')}
							</AvatarFallback>
						</Avatar>
						<div>
							<h3 className="font-semibold text-gray-900">
								{doctor.name}
							</h3>
							<p className="text-sm text-gray-600">
								{doctor.specialty}
							</p>
							<p className="text-xs text-gray-500">
								{doctor.crm}
							</p>
						</div>
					</div>

					{/* Appointment Details */}
					<div className="space-y-3">
						<div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
							<Calendar className="w-5 h-5 text-blue-600" />
							<div>
								<p className="font-medium">
									{format(date, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
								</p>
								<p className="text-sm text-gray-600">Data da consulta</p>
							</div>
						</div>

						<div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
							<Clock className="w-5 h-5 text-blue-600" />
							<div>
								<p className="font-medium">{time}</p>
								<p className="text-sm text-gray-600">Horário</p>
							</div>
						</div>

						<div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
							<CreditCard className="w-5 h-5 text-green-600" />
							<div>
								<p className="font-medium">R$ {doctor.price}</p>
								<p className="text-sm text-gray-600">Valor da consulta</p>
							</div>
						</div>
					</div>

					{/* Important Info */}
					<div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
						<h4 className="font-medium text-blue-900 mb-2">
							Importante
						</h4>
						<ul className="text-sm text-blue-700 space-y-1">
							<li>• Chegue 15 minutos antes do horário</li>
							<li>• Traga um documento de identidade</li>
							<li>• Você pode cancelar até 24h antes</li>
						</ul>
					</div>
				</div>

				<DialogFooter className="gap-3">
					<Button
						variant="outline"
						onClick={onCancel}
						disabled={isLoading}
					>
						Cancelar
					</Button>
					<Button
						onClick={onConfirm}
						disabled={isLoading}
						className="bg-blue-600 hover:bg-blue-700"
					>
						{isLoading ? (
							<>
								<Loader2 className="w-4 h-4 mr-2 animate-spin" />
								Agendando...
							</>
						) : (
							<>
								<Calendar className="w-4 h-4 mr-2" />
								Confirmar
							</>
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
