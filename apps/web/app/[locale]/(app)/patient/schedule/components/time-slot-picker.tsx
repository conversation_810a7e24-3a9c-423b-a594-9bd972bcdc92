"use client";

interface TimeSlotPickerProps {
	availableSlots: string[];
	selectedTime: string;
	onSelectTime: (time: string) => void;
}

export function TimeSlotPicker({
	availableSlots,
	selectedTime,
	onSelectTime
}: TimeSlotPickerProps) {
	return (
		<div>
			<h4 className="font-medium text-gray-900 mb-3">
				Hor<PERSON>rios Disponíveis
			</h4>
			<div className="grid grid-cols-3 md:grid-cols-4 gap-3">
				{availableSlots.map(time => (
					<button
						key={time}
						onClick={() => onSelectTime(time)}
						className={`p-3 rounded-lg text-center transition-colors ${
							selectedTime === time
								? "bg-blue-600 text-white"
								: "bg-gray-100 hover:bg-gray-200 text-gray-900"
						}`}
					>
						{time}
					</button>
				))}
			</div>

			{availableSlots.length === 0 && (
				<div className="text-center py-8 text-gray-500">
					Nenhum horário disponível para esta data
				</div>
			)}
		</div>
	);
}
