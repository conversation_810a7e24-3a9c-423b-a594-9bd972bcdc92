"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	Crown,
	CreditCard,
	Calendar,
	CheckCircle,
	XCircle,
	AlertCircle,
	Gift,
	Star,
	Zap,
	Shield,
	Heart,
	Clock,
	Users
} from "lucide-react";
import { User } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import { PageHeader } from "../../components/ui/page-header";

interface SubscriptionsPatientClientProps {
	user: User;
}

interface Subscription {
	id: string;
	planName: string;
	status: "ACTIVE" | "CANCELLED" | "EXPIRED" | "TRIAL";
	price: number;
	billingCycle: "MONTHLY" | "YEARLY";
	startDate: string;
	endDate?: string;
	nextBilling?: string;
	features: string[];
	isTrialPeriod: boolean;
	trialDaysLeft?: number;
}

interface Plan {
	id: string;
	name: string;
	description: string;
	price: number;
	originalPrice?: number;
	billingCycle: "MONTHLY" | "YEARLY";
	features: string[];
	isPopular?: boolean;
	icon: any;
	color: string;
}

export function SubscriptionsPatientClient({ user }: SubscriptionsPatientClientProps) {
	const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
	const [availablePlans, setAvailablePlans] = useState<Plan[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [billingCycle, setBillingCycle] = useState<"MONTHLY" | "YEARLY">("MONTHLY");

	// Verificar assinatura ativa do usuário
	useEffect(() => {
		const checkSubscription = async () => {
			try {
				const response = await fetch('/api/patient/subscription/check');
				const data = await response.json();

				if (data.hasActiveSubscription) {
					// Buscar detalhes da assinatura ativa
					const subscriptionResponse = await fetch('/api/patient/subscription/details');
					const subscriptionData = await subscriptionResponse.json();

					if (subscriptionData.subscription) {
						setCurrentSubscription({
							id: subscriptionData.subscription.id,
							planName: subscriptionData.subscription.planName,
							status: subscriptionData.subscription.status,
							price: Number(subscriptionData.subscription.planPrice),
							billingCycle: subscriptionData.subscription.cycle || "MONTHLY",
							startDate: subscriptionData.subscription.startDate,
							nextBilling: subscriptionData.subscription.nextBillingDate,
							features: getPlanFeatures(subscriptionData.subscription.planId),
							isTrialPeriod: false
						});
					}
				}

				// Carregar planos disponíveis
				loadAvailablePlans();
			} catch (error) {
				console.error('Erro ao verificar assinatura:', error);
				// Em caso de erro, carregar planos disponíveis mesmo assim
				loadAvailablePlans();
			} finally {
				setIsLoading(false);
			}
		};

		const loadAvailablePlans = () => {
			const plans: Plan[] = [
				{
					id: "zapvida-sempre",
					name: "ZapVida Sempre",
					description: "Plano principal com 2 consultas por mês",
					price: billingCycle === "MONTHLY" ? 49.00 : 490.00,
					originalPrice: billingCycle === "MONTHLY" ? 160.00 : 1600.00,
					billingCycle,
					features: [
						"2 consultas por mês",
						"Atendimento por vídeo, áudio ou chat",
						"Acesso a especialistas qualificados",
						"Receitas e atestados digitais válidos",
						"Histórico médico completo",
						"Suporte prioritário 24/7"
					],
					isPopular: true,
					icon: Crown,
					color: "purple"
				}
			];

			setAvailablePlans(plans);
		};

		checkSubscription();
	}, [billingCycle]);

	// Função para obter features baseadas no plano
	const getPlanFeatures = (planId: string): string[] => {
		const planFeatures: Record<string, string[]> = {
			"zapvida-sempre": [
				"2 consultas por mês",
				"Atendimento por vídeo, áudio ou chat",
				"Acesso a especialistas qualificados",
				"Receitas e atestados digitais válidos",
				"Histórico médico completo",
				"Suporte prioritário 24/7"
			]
		};

		return planFeatures[planId] || [
			"2 consultas por mês",
			"Atendimento por vídeo, áudio ou chat",
			"Acesso a especialistas qualificados",
			"Receitas e atestados digitais válidos",
			"Histórico médico completo",
			"Suporte prioritário 24/7"
		];
	};

	const handleCancelSubscription = async () => {
		try {
			// Aqui seria a chamada para a API
			// await cancelSubscription(currentSubscription.id);

			setCurrentSubscription(prev => prev ? { ...prev, status: "CANCELLED" } : null);
			toast.success("Assinatura cancelada com sucesso");
		} catch (error) {
			toast.error("Erro ao cancelar assinatura");
			console.error(error);
		}
	};

	const handleChangePlan = async (planId: string) => {
		try {
			// Aqui seria a chamada para a API
			// await changePlan(planId);

			toast.success("Plano alterado com sucesso!");
		} catch (error) {
			toast.error("Erro ao alterar plano");
			console.error(error);
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "ACTIVE":
				return <Badge className="bg-green-100 text-green-800">Ativo</Badge>;
			case "TRIAL":
				return <Badge className="bg-blue-100 text-blue-800">Período de Teste</Badge>;
			case "CANCELLED":
				return <Badge className="bg-red-100 text-red-800">Cancelado</Badge>;
			case "EXPIRED":
				return <Badge className="bg-gray-100 text-gray-800">Expirado</Badge>;
			default:
				return null;
		}
	};

	if (isLoading) {
		return (
			<div className="container mx-auto py-6 px-4">
				<div className="space-y-6">
					{[1, 2, 3].map(i => (
						<Card key={i} className="animate-pulse">
							<CardContent className="p-6">
								<div className="space-y-3">
									<div className="h-4 bg-gray-200 rounded w-1/4" />
									<div className="h-6 bg-gray-200 rounded w-1/2" />
									<div className="h-3 bg-gray-200 rounded w-1/3" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-6 px-4 max-w-6xl">
			<div className="space-y-6">
				<PageHeader
					title="Minha Assinatura"
					subtitle="Gerencie seu plano e veja todas as funcionalidades disponíveis"
				/>

			{/* Current Subscription or Offer */}
			{currentSubscription ? (
				<Card className="mb-8 border-l-4 border-l-purple-500">
					<CardHeader>
						<CardTitle className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Crown className="w-6 h-6 text-purple-600" />
								Assinatura Atual
							</div>
							{getStatusBadge(currentSubscription.status)}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
							<div>
								<h3 className="font-semibold text-lg mb-1">
									{currentSubscription.planName}
								</h3>
								<p className="text-2xl font-bold text-purple-600">
									R$ {currentSubscription.price.toFixed(2)}
								</p>
								<p className="text-sm text-gray-500">
									por {currentSubscription.billingCycle === "MONTHLY" ? "mês" : "ano"}
								</p>
							</div>

							<div>
								<h4 className="font-medium text-gray-900 mb-1">Iniciado em</h4>
								<p className="text-gray-600">
									{format(new Date(currentSubscription.startDate), "dd/MM/yyyy", { locale: ptBR })}
								</p>
							</div>

							{currentSubscription.nextBilling && (
								<div>
									<h4 className="font-medium text-gray-900 mb-1">Próxima cobrança</h4>
									<p className="text-gray-600">
										{format(new Date(currentSubscription.nextBilling), "dd/MM/yyyy", { locale: ptBR })}
									</p>
								</div>
							)}

							<div className="flex gap-2">
								<Button variant="outline" size="sm">
									<CreditCard className="w-4 h-4 mr-2" />
									Alterar Cartão
								</Button>
								{currentSubscription.status === "ACTIVE" && (
									<Button
										variant="outline"
										size="sm"
										onClick={handleCancelSubscription}
										className="text-red-600 hover:text-red-700"
									>
										<XCircle className="w-4 h-4 mr-2" />
										Cancelar
									</Button>
								)}
							</div>
						</div>

						{/* Features */}
						<div className="mt-6">
							<h4 className="font-medium text-gray-900 mb-3">Recursos inclusos:</h4>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-3">
								{currentSubscription.features.map((feature, index) => (
									<div key={index} className="flex items-center gap-2">
										<CheckCircle className="w-4 h-4 text-green-600" />
										<span className="text-sm text-gray-700">{feature}</span>
									</div>
								))}
							</div>
						</div>
					</CardContent>
				</Card>
			) : (
				<Card className="mb-8 border-l-4 border-l-emerald-500 bg-gradient-to-r from-emerald-50 to-green-50">
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Star className="w-6 h-6 text-emerald-600" />
							Comece sua jornada de saúde hoje!
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-center space-y-4">
							<div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
								<Heart className="w-8 h-8 text-emerald-600" />
							</div>
							<h3 className="text-xl font-semibold text-gray-900">
								Assine o ZapVida e tenha acesso a:
							</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
								<div className="flex items-center gap-2">
									<CheckCircle className="w-5 h-5 text-emerald-600" />
									<span className="text-sm text-gray-700">Consultas médicas 24h</span>
								</div>
								<div className="flex items-center gap-2">
									<CheckCircle className="w-5 h-5 text-emerald-600" />
									<span className="text-sm text-gray-700">Plantão médico</span>
								</div>
								<div className="flex items-center gap-2">
									<CheckCircle className="w-5 h-5 text-emerald-600" />
									<span className="text-sm text-gray-700">Receitas digitais</span>
								</div>
								<div className="flex items-center gap-2">
									<CheckCircle className="w-5 h-5 text-emerald-600" />
									<span className="text-sm text-gray-700">Histórico médico</span>
								</div>
							</div>
							<div className="pt-4 flex justify-center">
								<a href="/pay/assinatura">
									<Button size="lg" className="bg-emerald-600 hover:bg-emerald-700 text-white px-8">
										<Crown className="w-5 h-5 mr-2" />
										Assinar Agora
									</Button>
								</a>
							</div>
						</div>
					</CardContent>
				</Card>
			)}





			{/* Benefits */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Gift className="w-5 h-5 text-purple-600" />
						Benefícios da Assinatura
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="text-center">
							<div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
								<Clock className="w-6 h-6 text-blue-600" />
							</div>
							<h3 className="font-semibold mb-2">Atendimento 24h</h3>
							<p className="text-sm text-gray-600">
								Plantão médico disponível todos os dias, a qualquer hora
							</p>
						</div>

						<div className="text-center">
							<div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
								<Shield className="w-6 h-6 text-green-600" />
							</div>
							<h3 className="font-semibold mb-2">Dados Seguros</h3>
							<p className="text-sm text-gray-600">
								Suas informações médicas protegidas com criptografia
							</p>
						</div>

						<div className="text-center">
							<div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
								<Zap className="w-6 h-6 text-purple-600" />
							</div>
							<h3 className="font-semibold mb-2">Resposta Rápida</h3>
							<p className="text-sm text-gray-600">
								Atendimento médico em menos de 5 minutos
							</p>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Support */}
			<div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
				<div className="flex items-start gap-3">
					<AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
					<div>
						<h4 className="font-semibold text-blue-900">
							Precisa de ajuda?
						</h4>
						<p className="text-sm text-blue-700 mb-3">
							Nossa equipe está disponível para esclarecer dúvidas sobre planos e assinaturas.
						</p>
						<Button size="sm" variant="outline" className="border-blue-300 text-blue-700">
							Falar com Suporte
						</Button>
					</div>
				</div>
			</div>
			</div>
		</div>
	);
}
