"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import {
	User,
	Mail,
	Phone,
	Calendar,
	MapPin,
	Edit,
	Save,
	X,
	Camera,
	Settings,
	Bell,
	Shield,
	LogOut,
	Heart,
	Pill,
	AlertTriangle
} from "lucide-react";
import { User as UserType } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";

interface ProfilePatientClientProps {
	user: UserType;
}

interface PatientProfile {
	personalInfo: {
		fullName: string;
		email: string;
		phone: string;
		birthDate: string;
		cpf: string;
		address: string;
		emergencyContact: {
			name: string;
			phone: string;
			relationship: string;
		};
	};
	healthInfo: {
		allergies: string[];
		medications: string[];
		conditions: string[];
		bloodType: string;
		height: string;
		weight: string;
	};
	preferences: {
		notifications: {
			email: boolean;
			sms: boolean;
			whatsapp: boolean;
		};
		privacy: {
			shareDataWithDoctors: boolean;
			allowResearchParticipation: boolean;
		};
	};
}

export function ProfilePatientClient({ user }: ProfilePatientClientProps) {
	const [isEditing, setIsEditing] = useState(false);
	const [activeTab, setActiveTab] = useState<"personal" | "health" | "preferences">("personal");

	const [profile, setProfile] = useState<PatientProfile>({
		personalInfo: {
			fullName: user.name || "",
			email: user.email || "",
			phone: "11999887766",
			birthDate: "1990-01-01",
			cpf: "123.456.789-00",
			address: "Rua das Flores, 123 - São Paulo, SP",
			emergencyContact: {
				name: "João Silva",
				phone: "11888777666",
				relationship: "Cônjuge"
			}
		},
		healthInfo: {
			allergies: ["Penicilina", "Frutos do mar"],
			medications: ["Losartana 50mg", "Sinvastatina 20mg"],
			conditions: ["Hipertensão", "Diabetes tipo 2"],
			bloodType: "O+",
			height: "175",
			weight: "80"
		},
		preferences: {
			notifications: {
				email: true,
				sms: false,
				whatsapp: true
			},
			privacy: {
				shareDataWithDoctors: true,
				allowResearchParticipation: false
			}
		}
	});

	const handleSave = async () => {
		try {
			// Aqui seria a chamada para a API
			// await updatePatientProfile(profile);

			setIsEditing(false);
			toast.success("Perfil atualizado com sucesso!");
		} catch (error) {
			toast.error("Erro ao atualizar perfil");
			console.error(error);
		}
	};

	const handleCancel = () => {
		setIsEditing(false);
		// Restaurar dados originais se necessário
	};

	const tabs = [
		{ id: "personal", label: "Dados Pessoais", icon: User },
		{ id: "health", label: "Saúde", icon: Heart },
		{ id: "preferences", label: "Preferências", icon: Settings }
	];

	return (
		<div className="container mx-auto py-6 px-4 max-w-4xl">
			{/* Header */}
			<div className="mb-8">
				<div className="flex items-center gap-6">
					<div className="relative">
						<Avatar className="w-24 h-24">
							<AvatarImage src={user.image || undefined} />
							<AvatarFallback className="text-2xl">
								{user.name?.split(' ').map(n => n[0]).join('') || 'U'}
							</AvatarFallback>
						</Avatar>
						<button className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
							<Camera className="w-4 h-4" />
						</button>
					</div>
					<div className="flex-1">
						<h1 className="text-2xl font-bold text-gray-900 mb-1">
							{profile.personalInfo.fullName}
						</h1>
						<p className="text-gray-600 mb-2">
							{profile.personalInfo.email}
						</p>
						<div className="flex gap-2">
							<Badge variant="secondary">Paciente</Badge>
							<Badge className="bg-green-100 text-green-800">Ativo</Badge>
						</div>
					</div>
					<div className="flex gap-2">
						{isEditing ? (
							<>
								<Button variant="outline" onClick={handleCancel}>
									<X className="w-4 h-4 mr-2" />
									Cancelar
								</Button>
								<Button onClick={handleSave}>
									<Save className="w-4 h-4 mr-2" />
									Salvar
								</Button>
							</>
						) : (
							<Button onClick={() => setIsEditing(true)}>
								<Edit className="w-4 h-4 mr-2" />
								Editar
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Tabs */}
			<div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
				{tabs.map(tab => {
					const Icon = tab.icon;
					return (
						<button
							key={tab.id}
							onClick={() => setActiveTab(tab.id as any)}
							className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md transition-colors ${
								activeTab === tab.id
									? "bg-white text-blue-600 shadow-sm"
									: "text-gray-600 hover:text-gray-900"
							}`}
						>
							<Icon className="w-4 h-4" />
							<span className="font-medium">{tab.label}</span>
						</button>
					);
				})}
			</div>

			{/* Personal Info Tab */}
			{activeTab === "personal" && (
				<div className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<User className="w-5 h-5" />
								Informações Pessoais
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Nome Completo
									</label>
									<input
										type="text"
										value={profile.personalInfo.fullName}
										onChange={(e) => setProfile(prev => ({
											...prev,
											personalInfo: { ...prev.personalInfo, fullName: e.target.value }
										}))}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										CPF
									</label>
									<input
										type="text"
										value={profile.personalInfo.cpf}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Email
									</label>
									<input
										type="email"
										value={profile.personalInfo.email}
										onChange={(e) => setProfile(prev => ({
											...prev,
											personalInfo: { ...prev.personalInfo, email: e.target.value }
										}))}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Telefone
									</label>
									<input
										type="tel"
										value={profile.personalInfo.phone}
										onChange={(e) => setProfile(prev => ({
											...prev,
											personalInfo: { ...prev.personalInfo, phone: e.target.value }
										}))}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Data de Nascimento
									</label>
									<input
										type="date"
										value={profile.personalInfo.birthDate}
										onChange={(e) => setProfile(prev => ({
											...prev,
											personalInfo: { ...prev.personalInfo, birthDate: e.target.value }
										}))}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Endereço
								</label>
								<input
									type="text"
									value={profile.personalInfo.address}
									onChange={(e) => setProfile(prev => ({
										...prev,
										personalInfo: { ...prev.personalInfo, address: e.target.value }
									}))}
									disabled={!isEditing}
									className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
								/>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Phone className="w-5 h-5" />
								Contato de Emergência
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Nome
									</label>
									<input
										type="text"
										value={profile.personalInfo.emergencyContact.name}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Telefone
									</label>
									<input
										type="tel"
										value={profile.personalInfo.emergencyContact.phone}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Parentesco
									</label>
									<input
										type="text"
										value={profile.personalInfo.emergencyContact.relationship}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Health Info Tab */}
			{activeTab === "health" && (
				<div className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Heart className="w-5 h-5" />
								Informações de Saúde
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Tipo Sanguíneo
									</label>
									<input
										type="text"
										value={profile.healthInfo.bloodType}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Altura (cm)
									</label>
									<input
										type="text"
										value={profile.healthInfo.height}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Peso (kg)
									</label>
									<input
										type="text"
										value={profile.healthInfo.weight}
										disabled={!isEditing}
										className="w-full p-3 border border-gray-300 rounded-lg disabled:bg-gray-50"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										IMC
									</label>
									<input
										type="text"
										value={(parseInt(profile.healthInfo.weight) / Math.pow(parseInt(profile.healthInfo.height) / 100, 2)).toFixed(1)}
										disabled
										className="w-full p-3 border border-gray-300 rounded-lg bg-gray-50"
									/>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<AlertTriangle className="w-5 h-5" />
								Alergias
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="flex flex-wrap gap-2">
								{profile.healthInfo.allergies.map((allergy, index) => (
									<Badge key={index} variant="destructive" className="px-3 py-1">
										{allergy}
									</Badge>
								))}
								{profile.healthInfo.allergies.length === 0 && (
									<p className="text-gray-500">Nenhuma alergia registrada</p>
								)}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Pill className="w-5 h-5" />
								Medicamentos em Uso
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								{profile.healthInfo.medications.map((medication, index) => (
									<div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
										<Pill className="w-4 h-4 text-blue-600" />
										<span>{medication}</span>
									</div>
								))}
								{profile.healthInfo.medications.length === 0 && (
									<p className="text-gray-500">Nenhum medicamento registrado</p>
								)}
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Heart className="w-5 h-5" />
								Condições de Saúde
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="flex flex-wrap gap-2">
								{profile.healthInfo.conditions.map((condition, index) => (
									<Badge key={index} className="bg-orange-100 text-orange-800 px-3 py-1">
										{condition}
									</Badge>
								))}
								{profile.healthInfo.conditions.length === 0 && (
									<p className="text-gray-500">Nenhuma condição registrada</p>
								)}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Preferences Tab */}
			{activeTab === "preferences" && (
				<div className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Bell className="w-5 h-5" />
								Notificações
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<div>
										<h4 className="font-medium">Email</h4>
										<p className="text-sm text-gray-600">Receber notificações por email</p>
									</div>
									<input
										type="checkbox"
										checked={profile.preferences.notifications.email}
										onChange={(e) => setProfile(prev => ({
											...prev,
											preferences: {
												...prev.preferences,
												notifications: {
													...prev.preferences.notifications,
													email: e.target.checked
												}
											}
										}))}
										disabled={!isEditing}
										className="w-4 h-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<div>
										<h4 className="font-medium">SMS</h4>
										<p className="text-sm text-gray-600">Receber notificações por SMS</p>
									</div>
									<input
										type="checkbox"
										checked={profile.preferences.notifications.sms}
										disabled={!isEditing}
										className="w-4 h-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<div>
										<h4 className="font-medium">WhatsApp</h4>
										<p className="text-sm text-gray-600">Receber notificações por WhatsApp</p>
									</div>
									<input
										type="checkbox"
										checked={profile.preferences.notifications.whatsapp}
										disabled={!isEditing}
										className="w-4 h-4"
									/>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="w-5 h-5" />
								Privacidade
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<div>
										<h4 className="font-medium">Compartilhar dados com médicos</h4>
										<p className="text-sm text-gray-600">Permitir que médicos vejam seu histórico</p>
									</div>
									<input
										type="checkbox"
										checked={profile.preferences.privacy.shareDataWithDoctors}
										disabled={!isEditing}
										className="w-4 h-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<div>
										<h4 className="font-medium">Participar de pesquisas</h4>
										<p className="text-sm text-gray-600">Permitir uso dos dados para pesquisas médicas</p>
									</div>
									<input
										type="checkbox"
										checked={profile.preferences.privacy.allowResearchParticipation}
										disabled={!isEditing}
										className="w-4 h-4"
									/>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<LogOut className="w-5 h-5" />
								Conta
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<Button variant="outline" className="w-full justify-start">
									<Settings className="w-4 h-4 mr-2" />
									Configurações da Conta
								</Button>
								<Button variant="outline" className="w-full justify-start text-red-600 hover:text-red-700">
									<LogOut className="w-4 h-4 mr-2" />
									Sair da Conta
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
