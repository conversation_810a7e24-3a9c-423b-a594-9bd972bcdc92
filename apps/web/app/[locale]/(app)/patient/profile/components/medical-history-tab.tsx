'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	Heart,
	Calendar,
	FileText,
	Stethoscope,
	Clock,
	Eye,
	Download,
	Loader2,
	Link
} from "lucide-react";
import type { User as UserType } from "database";
import { useRouter } from "@i18n/routing";

interface MedicalHistoryTabProps {
	user: UserType;
}

interface Appointment {
	id: string;
	date: string;
	doctorName: string;
	specialty: string;
	status: string;
	notes?: string;
}

interface Prescription {
	id: string;
	date: string;
	doctorName: string;
	medications: string[];
	status: string;
}

export function MedicalHistoryTab({ user }: MedicalHistoryTabProps) {
	const [appointments, setAppointments] = useState<Appointment[]>([]);
	const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const router = useRouter();
	useEffect(() => {
		const loadMedicalHistory = async () => {
			try {
				// Carregar consultas
				const appointmentsResponse = await fetch('/api/patient/appointments/history');
				if (appointmentsResponse.ok) {
					const appointmentsData = await appointmentsResponse.json();
					setAppointments(appointmentsData.appointments || []);
				}

				// Carregar receitas
				const prescriptionsResponse = await fetch('/api/patient/prescriptions');
				if (prescriptionsResponse.ok) {
					const prescriptionsData = await prescriptionsResponse.json();
					setPrescriptions(prescriptionsData.prescriptions || []);
				}
			} catch (error) {
				console.error('Erro ao carregar histórico médico:', error);
			} finally {
				setIsLoading(false);
			}
		};

		loadMedicalHistory();
	}, []);

	const getStatusBadge = (status: string) => {
		switch (status) {
			case 'COMPLETED':
				return <Badge className="bg-green-100 text-green-800">Concluída</Badge>;
			case 'SCHEDULED':
				return <Badge className="bg-blue-100 text-blue-800">Agendada</Badge>;
			case 'CANCELLED':
				return <Badge className="bg-red-100 text-red-800">Cancelada</Badge>;
			case 'IN_PROGRESS':
				return <Badge className="bg-yellow-100 text-yellow-800">Em andamento</Badge>;
			default:
				return <Badge variant="secondary">{status}</Badge>;
		}
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader2 className="w-8 h-8 animate-spin" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Resumo do Histórico */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center gap-4">
							<div className="bg-blue-50 p-3 rounded-lg">
								<Calendar className="w-6 h-6 text-blue-600" />
							</div>
							<div>
								<p className="text-2xl font-bold">{appointments.length}</p>
								<p className="text-sm text-muted-foreground">Consultas realizadas</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center gap-4">
							<div className="bg-green-50 p-3 rounded-lg">
								<FileText className="w-6 h-6 text-green-600" />
							</div>
							<div>
								<p className="text-2xl font-bold">{prescriptions.length}</p>
								<p className="text-sm text-muted-foreground">Receitas emitidas</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center gap-4">
							<div className="bg-purple-50 p-3 rounded-lg">
								<Heart className="w-6 h-6 text-purple-600" />
							</div>
							<div>
								<p className="text-2xl font-bold">
									{appointments.filter(a => a.status === 'COMPLETED').length}
								</p>
								<p className="text-sm text-muted-foreground">Consultas concluídas</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Histórico de Consultas */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Stethoscope className="w-5 h-5" />
						Histórico de Consultas
					</CardTitle>
					<CardDescription>
						Suas consultas médicas realizadas na plataforma.
					</CardDescription>
				</CardHeader>
				<CardContent>
					{appointments.length === 0 ? (
						<div className="text-center py-8">
							<Calendar className="w-16 h-16 mx-auto text-gray-300 mb-4" />
							<h3 className="text-lg font-semibold mb-2">Nenhuma consulta encontrada</h3>
							<p className="text-muted-foreground mb-6">
								Você ainda não realizou consultas na nossa plataforma.
							</p>
							<div className="flex justify-center">

							<Button onClick={() => router.push('/patient/schedule')}>
								Agendar Primeira Consulta
							</Button>

							</div>
						</div>
					) : (
						<div className="space-y-4">
							{appointments.slice(0, 5).map((appointment) => (
								<div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
									<div className="flex items-center gap-4">
										<div className="bg-blue-50 p-2 rounded-lg">
											<Stethoscope className="w-5 h-5 text-blue-600" />
										</div>
										<div>
											<h4 className="font-semibold">{appointment.specialty}</h4>
											<p className="text-sm text-muted-foreground">
												Dr. {appointment.doctorName}
											</p>
											<div className="flex items-center gap-2 mt-1">
												<Clock className="w-4 h-4 text-muted-foreground" />
												<span className="text-sm text-muted-foreground">
													{new Date(appointment.date).toLocaleDateString('pt-BR')}
												</span>
											</div>
										</div>
									</div>
									<div className="flex items-center gap-3">
										{getStatusBadge(appointment.status)}
										<Button variant="outline" size="sm">
											<Eye className="w-4 h-4 mr-2" />
											Ver detalhes
										</Button>
									</div>
								</div>
							))}
							{appointments.length > 5 && (
								<div className="text-center pt-4">
									<Button variant="outline">
										Ver todas as consultas ({appointments.length})
									</Button>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Receitas Médicas */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileText className="w-5 h-5" />
						Receitas Médicas
					</CardTitle>
					<CardDescription>
						Suas receitas médicas digitais e prescrições.
					</CardDescription>
				</CardHeader>
				<CardContent>
					{prescriptions.length === 0 ? (
						<div className="text-center py-8">
							<FileText className="w-16 h-16 mx-auto text-gray-300 mb-4" />
							<h3 className="text-lg font-semibold mb-2">Nenhuma receita encontrada</h3>
							<p className="text-muted-foreground">
								Você ainda não possui receitas médicas em nossa plataforma.
							</p>
						</div>
					) : (
						<div className="space-y-4">
							{prescriptions.slice(0, 5).map((prescription) => (
								<div key={prescription.id} className="flex items-center justify-between p-4 border rounded-lg">
									<div className="flex items-center gap-4">
										<div className="bg-green-50 p-2 rounded-lg">
											<FileText className="w-5 h-5 text-green-600" />
										</div>
										<div>
											<h4 className="font-semibold">Receita Médica</h4>
											<p className="text-sm text-muted-foreground">
												Dr. {prescription.doctorName}
											</p>
											<div className="flex items-center gap-2 mt-1">
												<Clock className="w-4 h-4 text-muted-foreground" />
												<span className="text-sm text-muted-foreground">
													{new Date(prescription.date).toLocaleDateString('pt-BR')}
												</span>
											</div>
											<div className="mt-2">
												<p className="text-sm text-muted-foreground">
													Medicamentos: {prescription.medications.join(', ')}
												</p>
											</div>
										</div>
									</div>
									<div className="flex items-center gap-3">
										<Badge variant="outline">Digital</Badge>
										<Button variant="outline" size="sm">
											<Download className="w-4 h-4 mr-2" />
											Download
										</Button>
									</div>
								</div>
							))}
							{prescriptions.length > 5 && (
								<div className="text-center pt-4">
									<Button variant="outline">
										Ver todas as receitas ({prescriptions.length})
									</Button>
								</div>
							)}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Documentos e Exames */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileText className="w-5 h-5" />
						Documentos e Exames
					</CardTitle>
					<CardDescription>
						Gerencie seus documentos médicos e resultados de exames.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="text-center py-8">
						<FileText className="w-16 h-16 mx-auto text-gray-300 mb-4" />
						<h3 className="text-lg font-semibold mb-2">Área de documentos</h3>
						<p className="text-muted-foreground mb-6">
							Em breve você poderá fazer upload e gerenciar seus documentos médicos aqui.
						</p>
						<Button variant="outline" disabled>
							Upload de Documentos (Em breve)
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
