'use client';

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { useToast } from "@ui/hooks/use-toast";
import { Loader2, MapPin, Phone, User, Calendar } from "lucide-react";
import type { User as UserType } from "database";

const personalInfoSchema = z.object({
	name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
	email: z.string().email("Email inválido"),
	cpf: z.string().min(11, "CPF inválido").max(14),
	birthDate: z.string().optional(),
	gender: z.enum(["MALE", "FEMALE", "OTHER", "PREFER_NOT_TO_SAY"]).optional(),
	phone: z.string().min(10, "Telefone inválido"),
	height: z.string().optional(),
	weight: z.string().optional(),
	bloodType: z.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", "UNKNOWN"]).optional(),
	allergies: z.string().optional(),
	address: z.object({
		street: z.string().optional(),
		number: z.string().optional(),
		complement: z.string().optional(),
		neighborhood: z.string().optional(),
		city: z.string().optional(),
		state: z.string().optional(),
		zipCode: z.string().optional(),
	}).optional(),
});

type PersonalInfoValues = z.infer<typeof personalInfoSchema>;

interface PersonalInfoTabProps {
	user: UserType;
}

export function PersonalInfoTab({ user }: PersonalInfoTabProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [initialLoading, setInitialLoading] = useState(true);
	const { toast } = useToast();

	const form = useForm<PersonalInfoValues>({
		resolver: zodResolver(personalInfoSchema),
		defaultValues: {
			name: user.name || "",
			email: user.email || "",
			cpf: "",
			birthDate: "",
			gender: "PREFER_NOT_TO_SAY",
			phone: "",
			height: "",
			weight: "",
			bloodType: "UNKNOWN",
			allergies: "",
			address: {
				street: "",
				number: "",
				complement: "",
				neighborhood: "",
				city: "",
				state: "",
				zipCode: "",
			},
		},
	});

	// Função para formatar telefone
	const formatPhone = (value: string) => {
		if (!value) return "";
		const phoneNumber = value.replace(/\D/g, "");
		if (phoneNumber.length <= 2) {
			return phoneNumber.replace(/^(\d{0,2})/, "($1");
		} else if (phoneNumber.length <= 7) {
			return phoneNumber.replace(/^(\d{2})(\d{0,5})/, "($1) $2");
		} else if (phoneNumber.length <= 11) {
			return phoneNumber.replace(/^(\d{2})(\d{5})(\d{0,4})/, "($1) $2-$3");
		} else {
			return phoneNumber.slice(0, 11).replace(/^(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
		}
	};

	// Função para remover formatação
	const unformatPhone = (value: string) => {
		return value.replace(/\D/g, "");
	};

	// Carregar dados do usuário
	useEffect(() => {
		const loadUserData = async () => {
			try {
				const response = await fetch('/api/patient/profile');
				if (response.ok) {
					const data = await response.json();

					const formattedData = {
						name: user.name || "",
						email: user.email || "",
						cpf: data.cpf || "",
						birthDate: data.birthDate ? new Date(data.birthDate).toISOString().split('T')[0] : "",
						gender: data.gender || "PREFER_NOT_TO_SAY",
						phone: data.phone ? formatPhone(data.phone) : "",
						height: data.height ? data.height.toString() : "",
						weight: data.weight ? data.weight.toString() : "",
						bloodType: data.bloodType || "UNKNOWN",
						allergies: data.allergies ? data.allergies.join(", ") : "",
						address: {
							street: data.address?.street || "",
							number: data.address?.number || "",
							complement: data.address?.complement || "",
							neighborhood: data.address?.neighborhood || "",
							city: data.address?.city || "",
							state: data.address?.state || "",
							zipCode: data.address?.zipCode || "",
						},
					};

					form.reset(formattedData);
				}
			} catch (error) {
				console.error("Erro ao carregar dados:", error);
			} finally {
				setInitialLoading(false);
			}
		};

		loadUserData();
	}, [user, form]);

	const onSubmit = async (data: PersonalInfoValues) => {
		setIsLoading(true);
		try {
			const formattedData = {
				...data,
				phone: data.phone ? unformatPhone(data.phone) : "",
				birthDate: data.birthDate ? new Date(data.birthDate) : null,
				height: data.height ? parseFloat(data.height) : null,
				weight: data.weight ? parseFloat(data.weight) : null,
				allergies: data.allergies ? data.allergies.split(',').map(a => a.trim()) : [],
			};

			const response = await fetch('/api/patient/update', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(formattedData),
			});

			if (!response.ok) {
				throw new Error('Erro ao atualizar perfil');
			}

			toast({
				title: "Perfil atualizado",
				description: "Suas informações pessoais foram atualizadas com sucesso.",
			});
		} catch (error) {
			toast({
				title: "Erro",
				description: "Não foi possível atualizar suas informações. Tente novamente.",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	};

	if (initialLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader2 className="w-8 h-8 animate-spin" />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Informações Básicas */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<User className="w-5 h-5" />
						Informações Básicas
					</CardTitle>
					<CardDescription>
						Mantenha suas informações pessoais atualizadas.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nome Completo</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email</FormLabel>
											<FormControl>
												<Input type="email" {...field} disabled />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="cpf"
									render={({ field }) => (
										<FormItem>
											<FormLabel>CPF</FormLabel>
											<FormControl>
												<Input placeholder="000.000.000-00" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="birthDate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Data de Nascimento</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="gender"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Gênero</FormLabel>
											<Select onValueChange={field.onChange} value={field.value}>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Selecione seu gênero" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="MALE">Masculino</SelectItem>
													<SelectItem value="FEMALE">Feminino</SelectItem>
													<SelectItem value="OTHER">Outro</SelectItem>
													<SelectItem value="PREFER_NOT_TO_SAY">Prefiro não informar</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Telefone</FormLabel>
											<FormControl>
												<Input
													placeholder="(00) 00000-0000"
													value={field.value}
													onChange={(e) => field.onChange(formatPhone(e.target.value))}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Informações Médicas */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
									<Calendar className="w-5 h-5" />
									Informações Médicas
								</h3>

								<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
									<FormField
										control={form.control}
										name="height"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Altura (m)</FormLabel>
												<FormControl>
													<Input type="number" step="0.01" placeholder="1.70" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="weight"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Peso (kg)</FormLabel>
												<FormControl>
													<Input type="number" step="0.1" placeholder="70.0" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="bloodType"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Tipo Sanguíneo</FormLabel>
												<Select onValueChange={field.onChange} value={field.value}>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Selecione" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="A+">A+</SelectItem>
														<SelectItem value="A-">A-</SelectItem>
														<SelectItem value="B+">B+</SelectItem>
														<SelectItem value="B-">B-</SelectItem>
														<SelectItem value="AB+">AB+</SelectItem>
														<SelectItem value="AB-">AB-</SelectItem>
														<SelectItem value="O+">O+</SelectItem>
														<SelectItem value="O-">O-</SelectItem>
														<SelectItem value="UNKNOWN">Não sei</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="allergies"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Alergias (separe por vírgulas)</FormLabel>
											<FormControl>
												<Textarea placeholder="Exemplo: Ovo, Penicilina, Poeira" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Endereço */}
							<div className="border-t pt-6">
								<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
									<MapPin className="w-5 h-5" />
									Endereço
								</h3>

								<div className="space-y-4">
									<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
										<FormField
											control={form.control}
											name="address.street"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Rua</FormLabel>
													<FormControl>
														<Input placeholder="Rua das Flores" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="address.number"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Número</FormLabel>
													<FormControl>
														<Input placeholder="123" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="address.complement"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Complemento</FormLabel>
													<FormControl>
														<Input placeholder="Apto 101" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<FormField
											control={form.control}
											name="address.neighborhood"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Bairro</FormLabel>
													<FormControl>
														<Input placeholder="Centro" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="address.zipCode"
											render={({ field }) => (
												<FormItem>
													<FormLabel>CEP</FormLabel>
													<FormControl>
														<Input placeholder="00000-000" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<FormField
											control={form.control}
											name="address.city"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Cidade</FormLabel>
													<FormControl>
														<Input placeholder="São Paulo" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="address.state"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Estado</FormLabel>
													<Select onValueChange={field.onChange} value={field.value}>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Selecione" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value="AC">AC</SelectItem>
															<SelectItem value="AL">AL</SelectItem>
															<SelectItem value="AP">AP</SelectItem>
															<SelectItem value="AM">AM</SelectItem>
															<SelectItem value="BA">BA</SelectItem>
															<SelectItem value="CE">CE</SelectItem>
															<SelectItem value="DF">DF</SelectItem>
															<SelectItem value="ES">ES</SelectItem>
															<SelectItem value="GO">GO</SelectItem>
															<SelectItem value="MA">MA</SelectItem>
															<SelectItem value="MT">MT</SelectItem>
															<SelectItem value="MS">MS</SelectItem>
															<SelectItem value="MG">MG</SelectItem>
															<SelectItem value="PA">PA</SelectItem>
															<SelectItem value="PB">PB</SelectItem>
															<SelectItem value="PR">PR</SelectItem>
															<SelectItem value="PE">PE</SelectItem>
															<SelectItem value="PI">PI</SelectItem>
															<SelectItem value="RJ">RJ</SelectItem>
															<SelectItem value="RN">RN</SelectItem>
															<SelectItem value="RS">RS</SelectItem>
															<SelectItem value="RO">RO</SelectItem>
															<SelectItem value="RR">RR</SelectItem>
															<SelectItem value="SC">SC</SelectItem>
															<SelectItem value="SP">SP</SelectItem>
															<SelectItem value="SE">SE</SelectItem>
															<SelectItem value="TO">TO</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>
							</div>

							<Button type="submit" className="w-full" disabled={isLoading}>
								{isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
								Salvar Alterações
							</Button>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
