import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { getLocale, getTranslations } from "next-intl/server";
import { PatientProfileClient } from "./components/patient-profile-client";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: "Meu Perfil",
		description: "Gerencie suas informações pessoais, assinatura e configurações da conta",
	};
}

export default async function PatientProfilePage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	if (user.role !== "PATIENT") {
		return redirect({ href: "/app", locale });
	}

	return <PatientProfileClient user={user} />;
}
