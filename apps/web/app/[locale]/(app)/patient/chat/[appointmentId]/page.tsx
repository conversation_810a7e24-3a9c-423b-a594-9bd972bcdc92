import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { SimplePatientChat } from "../components/simple-patient-chat";

export const dynamic = "force-dynamic";
export const revalidate = 0;

interface PatientChatDetailPageProps {
	params: {
		appointmentId: string;
	};
}

export default async function PatientChatDetailPage({
	params
}: PatientChatDetailPageProps) {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	return (
		<SimplePatientChat
			user={user}
			appointmentId={params.appointmentId}
		/>
	);
}
