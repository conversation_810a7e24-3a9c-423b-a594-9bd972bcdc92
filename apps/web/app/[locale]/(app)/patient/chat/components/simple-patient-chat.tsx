"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
	MessageSquare,
	Video,
	Phone,
	Clock,
	ArrowLeft,
	Send,
	Paperclip,
	MoreVertical,
	Loader2
} from "lucide-react";
import { User } from "@prisma/client";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { PatientConsultationService, type ActiveConsultation } from "../../services/patient-consultation.service";
import { PatientChatService, createPatientChatService, type PatientChatMessage } from "../../services/patient-chat.service";
import { toast } from "@ui/hooks/use-toast";
import { PlantaoLink } from "../../components/plantao-link";

interface SimplePatientChatProps {
	user: User;
	appointmentId?: string;
}

export function SimplePatientChat({ user, appointmentId }: SimplePatientChatProps) {
	const router = useRouter();
	const [consultations, setConsultations] = useState<ActiveConsultation[]>([]);
	const [selectedConsultation, setSelectedConsultation] = useState<ActiveConsultation | null>(null);
	const [messages, setMessages] = useState<PatientChatMessage[]>([]);
	const [newMessage, setNewMessage] = useState("");
	const [isLoading, setIsLoading] = useState(true);
	const [isSending, setIsSending] = useState(false);
	const [error, setError] = useState<Error | null>(null);
	const [chatService, setChatService] = useState<PatientChatService | null>(null);
	const [isConnected, setIsConnected] = useState(false);

	const consultationService = new PatientConsultationService();

	// Carregar consultas ativas
	useEffect(() => {
		const loadConsultations = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const consultationsData = await consultationService.getActiveConsultations(user.id);
				setConsultations(consultationsData);

				// Se appointmentId fornecido, selecionar consulta específica
				if (appointmentId) {
					const consultation = consultationsData.find(c => c.id === appointmentId);
					if (consultation) {
						setSelectedConsultation(consultation);
					}
				} else if (consultationsData.length > 0) {
					// Auto-selecionar primeira consulta ativa
					const activeConsultation = consultationsData.find(c => c.status === "active");
					setSelectedConsultation(activeConsultation || consultationsData[0]);
				}
			} catch (err) {
				console.error('Erro ao carregar consultas:', err);
				setError(err as Error);
				setConsultations([]);
			} finally {
				setIsLoading(false);
			}
		};

		if (user?.id) {
			loadConsultations();
		}
	}, [user?.id, appointmentId]);

	// Configurar chat service quando consulta é selecionada
	useEffect(() => {
		if (selectedConsultation && user?.id) {
			// Limpar serviço anterior
			if (chatService) {
				chatService.destroy();
			}

			// Criar novo serviço de chat
			const newChatService = createPatientChatService({
				appointmentId: selectedConsultation.id,
				userId: user.id,
				onMessage: (message) => {
					setMessages(prev => [...prev, message]);
				},
				onError: (error) => {
					console.error('Erro no chat:', error);
					toast.error('Erro na conexão do chat');
				},
				onConnectionChange: (connected) => {
					setIsConnected(connected);
					if (connected) {
						toast.success('Conectado ao chat');
					} else {
						toast.error('Desconectado do chat');
					}
				}
			});

			setChatService(newChatService);

			// Conectar e carregar mensagens
			const initializeChat = async () => {
				try {
					await newChatService.connect();
					const chatMessages = await newChatService.getChatMessages(selectedConsultation.id);
					setMessages(chatMessages);
				} catch (error) {
					console.error('Erro ao inicializar chat:', error);
					toast.error('Erro ao carregar mensagens');
				}
			};

			initializeChat();

			// Cleanup
			return () => {
				newChatService.destroy();
			};
		}
	}, [selectedConsultation, user?.id]);

	// Cleanup ao desmontar componente
	useEffect(() => {
		return () => {
			if (chatService) {
				chatService.destroy();
			}
		};
	}, [chatService]);

	// Simular dados de fallback se não houver consultas reais (desenvolvimento)
	useEffect(() => {
		if (!isLoading && consultations.length === 0 && !error) {
			const mockConsultations: ActiveConsultation[] = [
				{
					id: "1",
					doctor: {
						name: "Dr. João Silva",
						specialty: "Cardiologia",
						image: "/api/placeholder/40/40",
						crm: "CRM/SP 123456"
					},
					status: "active",
					isOnline: true,
					lastMessage: {
						id: "1",
						content: "Como está se sentindo agora?",
						isFromDoctor: true,
						createdAt: new Date(Date.now() - 300000).toISOString(),
						type: "text"
					},
					unreadCount: 2,
					scheduledAt: new Date().toISOString()
				},
				{
					id: "2",
					doctor: {
						name: "Dra. Maria Santos",
						specialty: "Pediatria",
						image: "/api/placeholder/40/40",
						crm: "CRM/SP 789012"
					},
					status: "waiting",
					isOnline: false,
					unreadCount: 0,
					scheduledAt: new Date(Date.now() + 3600000).toISOString()
				}
			];

			const mockMessages: PatientChatMessage[] = [
				{
					id: "1",
					content: "Olá! Sou o Dr. João Silva. Como posso ajudá-lo hoje?",
					isFromDoctor: true,
					createdAt: new Date(Date.now() - 600000).toISOString(),
					type: "text"
				},
				{
					id: "2",
					content: "Oi doutor, estou sentindo dores no peito há algumas horas.",
					isFromDoctor: false,
					createdAt: new Date(Date.now() - 500000).toISOString(),
					type: "text"
				},
				{
					id: "3",
					content: "Entendo. Pode me descrever melhor a dor? É uma dor aguda ou mais como pressão?",
					isFromDoctor: true,
					createdAt: new Date(Date.now() - 400000).toISOString(),
					type: "text"
				},
				{
					id: "4",
					content: "É mais como uma pressão, e às vezes sinto falta de ar.",
					isFromDoctor: false,
					createdAt: new Date(Date.now() - 350000).toISOString(),
					type: "text"
				},
				{
					id: "5",
					content: "Como está se sentindo agora?",
					isFromDoctor: true,
					createdAt: new Date(Date.now() - 300000).toISOString(),
					type: "text"
				}
			];

			setTimeout(() => {
				setConsultations(mockConsultations);
				setMessages(mockMessages);

				// Auto-selecionar consulta se appointmentId for fornecido
				if (appointmentId) {
					const consultation = mockConsultations.find(c => c.id === appointmentId);
					if (consultation) {
						setSelectedConsultation(consultation);
					}
				} else if (mockConsultations.length > 0) {
					// Selecionar primeira consulta ativa
					const activeConsultation = mockConsultations.find(c => c.status === "active");
					setSelectedConsultation(activeConsultation || mockConsultations[0]);
				}

				setIsLoading(false);
			}, 1000);
		}
	}, [appointmentId, isLoading, consultations.length, error]);

	const handleSendMessage = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!newMessage.trim() || isSending || !selectedConsultation || !chatService) return;

		setIsSending(true);

		try {
			await chatService.sendMessage(selectedConsultation.id, newMessage);
			setNewMessage("");
		} catch (error) {
			console.error('Erro ao enviar mensagem:', error);
			toast.error('Erro ao enviar mensagem. Tente novamente.');
		} finally {
			setIsSending(false);
		}
	};

	const handleVideoCall = () => {
		// Integrar com sistema de vídeo existente
		router.push(`/patient/consultation/${selectedConsultation?.id}/video`);
	};

	const handleVoiceCall = () => {
		// Integrar com sistema de áudio
		router.push(`/patient/consultation/${selectedConsultation?.id}/audio`);
	};

	if (isLoading) {
		return (
			<div className="h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
					<p className="text-gray-600">Carregando conversas...</p>
				</div>
			</div>
		);
	}

	// Vista de lista de conversas
	if (!selectedConsultation) {
		return (
			<div className="h-screen bg-gray-50">
				{/* Header */}
				<div className="bg-white border-b border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-xl font-semibold text-gray-900">Conversas</h1>
							<p className="text-sm text-gray-600">Seus chats com médicos</p>
						</div>
						<PlantaoLink>
						<Button size="sm" className="bg-red-600 hover:bg-red-700">
							Plantão 24h
						</Button>
					</PlantaoLink>
					</div>
				</div>

				{/* Lista de conversas */}
				<div className="p-4">
					{consultations.length === 0 ? (
						<div className="text-center py-12">
							<div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
								<MessageSquare className="w-8 h-8 text-gray-400" />
							</div>
							<h3 className="text-lg font-semibold text-gray-900 mb-2">
								Nenhuma conversa ativa
							</h3>
							<p className="text-gray-600 mb-6">
								Solicite um atendimento para conversar com um médico
							</p>
							<PlantaoLink>
							<Button className="bg-red-600 hover:bg-red-700">
								Solicitar Plantão
							</Button>
						</PlantaoLink>
						</div>
					) : (
						<div className="space-y-3">
							{consultations.map(consultation => (
								<Card
									key={consultation.id}
									className="cursor-pointer hover:shadow-md transition-shadow"
									onClick={() => setSelectedConsultation(consultation)}
								>
									<CardContent className="p-4">
										<div className="flex items-center gap-3">
											<div className="relative">
												<Avatar className="w-12 h-12">
													<AvatarImage src={consultation.doctor.image} />
													<AvatarFallback>
														{consultation.doctor.name.split(' ').map(n => n[0]).join('')}
													</AvatarFallback>
												</Avatar>
												{consultation.isOnline && (
													<div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full" />
												)}
											</div>
											<div className="flex-1 min-w-0">
												<div className="flex items-center justify-between">
													<h3 className="font-semibold text-gray-900 truncate">
														{consultation.doctor.name}
													</h3>
													<Badge variant={consultation.status === "active" ? "default" : "secondary"}>
														{consultation.status === "active" ? "Online" : "Agendado"}
													</Badge>
												</div>
												<p className="text-sm text-gray-600 truncate">
													{consultation.doctor.specialty}
												</p>
												{consultation.lastMessage && (
													<p className="text-xs text-gray-500 truncate mt-1">
														{consultation.lastMessage.content}
													</p>
												)}
												{consultation.unreadCount > 0 && (
													<div className="absolute top-0 right-0 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
														{consultation.unreadCount}
													</div>
												)}
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</div>
			</div>
		);
	}

	// Vista do chat
	return (
		<div className="h-screen bg-gray-50 flex flex-col">
			{/* Header do chat */}
			<div className="bg-white border-b border-gray-200 p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setSelectedConsultation(null)}
							className="p-2"
						>
							<ArrowLeft className="w-4 h-4" />
						</Button>
						<div className="relative">
							<Avatar className="w-10 h-10">
								<AvatarImage src={selectedConsultation.doctor.image} />
								<AvatarFallback>
									{selectedConsultation.doctor.name.split(' ').map(n => n[0]).join('')}
								</AvatarFallback>
							</Avatar>
							{selectedConsultation.isOnline && (
								<div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
							)}
						</div>
						<div>
							<h2 className="font-semibold text-gray-900">
								{selectedConsultation.doctor.name}
							</h2>
							<p className="text-sm text-gray-600">
								{selectedConsultation.doctor.specialty}
							</p>
						</div>
					</div>
					<div className="flex gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={handleVoiceCall}
							disabled={!selectedConsultation.isOnline}
						>
							<Phone className="w-4 h-4" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={handleVideoCall}
							disabled={!selectedConsultation.isOnline}
						>
							<Video className="w-4 h-4" />
						</Button>
						<Button variant="ghost" size="sm">
							<MoreVertical className="w-4 h-4" />
						</Button>
					</div>
				</div>
			</div>

			{/* Mensagens */}
			<div className="flex-1 overflow-y-auto p-4 space-y-4">
				{messages.map(message => (
					<div
						key={message.id}
						className={`flex ${message.isFromDoctor ? 'justify-start' : 'justify-end'}`}
					>
						<div
							className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
								message.isFromDoctor
									? 'bg-white border border-gray-200 text-gray-900'
									: 'bg-blue-600 text-white'
							}`}
						>
							<p className="text-sm">{message.content}</p>
							<p className={`text-xs mt-1 ${
								message.isFromDoctor ? 'text-gray-500' : 'text-blue-100'
							}`}>
								{format(new Date(message.createdAt), "HH:mm", { locale: ptBR })}
							</p>
						</div>
					</div>
				))}
				{isSending && (
					<div className="flex justify-end">
						<div className="bg-gray-200 text-gray-600 px-4 py-2 rounded-lg">
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
								<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100" />
								<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200" />
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Input de mensagem */}
			<div className="bg-white border-t border-gray-200 p-4">
				<form onSubmit={handleSendMessage} className="flex items-center gap-2">
					<Button
						type="button"
						variant="ghost"
						size="sm"
						className="p-2"
					>
						<Paperclip className="w-4 h-4" />
					</Button>
					<input
						type="text"
						value={newMessage}
						onChange={(e) => setNewMessage(e.target.value)}
						placeholder="Digite sua mensagem..."
						className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						disabled={!selectedConsultation.isOnline}
					/>
					<Button
						type="submit"
						size="sm"
						disabled={!newMessage.trim() || isSending || !selectedConsultation.isOnline}
						className="px-4 py-2 rounded-full"
					>
						<Send className="w-4 h-4" />
					</Button>
				</form>
				{!selectedConsultation.isOnline && (
					<p className="text-xs text-gray-500 mt-2 text-center">
						O médico está offline. Sua mensagem será entregue quando ele estiver online.
					</p>
				)}
			</div>
		</div>
	);
}
