"use client";

import { Card, CardContent } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { StatusBadge } from "../../components/ui/status-badge";
import { Clock, Stethoscope, Video, MessageSquare } from "lucide-react";

interface Consultation {
	id: string;
	status: string;
	doctorName: string;
	specialties: string[];
	queueTime: number; // em minutos
	estimatedWaitTime?: number;
	type: "VIDEO" | "CHAT" | "ON_DUTY";
}

interface ActiveConsultationCardProps {
	consultation: Consultation;
}

export function ActiveConsultationCard({ consultation }: ActiveConsultationCardProps) {
	const formatQueueTime = (minutes: number) => {
		if (minutes < 60) {
			return `${minutes} min`;
		}
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		return `${hours}h ${remainingMinutes}min`;
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "WAITING":
				return "Aguardando médico";
			case "IN_PROGRESS":
				return "Em atendimento";
			case "CONNECTING":
				return "Conectando...";
			default:
				return status;
		}
	};

	const getActionButton = () => {
		switch (consultation.status) {
			case "WAITING":
				return {
					text: "Aguardar na Fila",
					variant: "outline" as const,
					icon: Clock,
				};
			case "IN_PROGRESS":
				return {
					text: "Continuar Atendimento",
					variant: "default" as const,
					icon: consultation.type === "VIDEO" ? Video : MessageSquare,
				};
			case "CONNECTING":
				return {
					text: "Entrar na Consulta",
					variant: "default" as const,
					icon: Video,
				};
			default:
				return {
					text: "Acessar Consulta",
					variant: "default" as const,
					icon: Stethoscope,
				};
		}
	};

	const action = getActionButton();
	const ActionIcon = action.icon;

	return (
		<Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-blue-50/50">
			<CardContent className="p-4">
				<div className="flex items-center justify-between mb-3">
					<div className="flex items-center space-x-2">
						<div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
							<Stethoscope className="w-4 h-4 text-blue-600" />
						</div>
						<span className="font-semibold text-gray-900">
							Consulta Ativa
						</span>
					</div>
					<StatusBadge status={consultation.status} />
				</div>

				<div className="space-y-2 mb-4">
					<div className="flex items-center text-sm text-gray-600">
						<Clock className="w-4 h-4 mr-2" />
						<span>
							{consultation.status === "WAITING"
								? `Na fila há ${formatQueueTime(consultation.queueTime)}`
								: `Em atendimento há ${formatQueueTime(consultation.queueTime)}`
							}
						</span>
					</div>

					<div className="text-sm text-gray-600">
						<strong>Dr(a). {consultation.doctorName}</strong>
					</div>

					<div className="text-sm text-gray-500">
						{consultation.specialties.join(", ")}
					</div>

					{consultation.estimatedWaitTime && consultation.status === "WAITING" && (
						<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mt-3">
							<p className="text-xs text-yellow-800">
								⏱️ Tempo estimado de espera: {formatQueueTime(consultation.estimatedWaitTime)}
							</p>
						</div>
					)}
				</div>

				<Button
					fullWidth
					variant={action.variant}
					className="flex items-center justify-center gap-2"
				>
					<ActionIcon className="w-4 h-4" />
					{action.text}
				</Button>

				{consultation.status === "WAITING" && (
					<p className="text-xs text-gray-500 text-center mt-2">
						Você receberá uma notificação quando o médico estiver pronto
					</p>
				)}
			</CardContent>
		</Card>
	);
}
