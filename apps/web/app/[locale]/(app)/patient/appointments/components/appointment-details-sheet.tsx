"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON>ger } from "@ui/components/sheet";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Separator } from "@ui/components/separator";
import {
  Calendar,
  Clock,
  Video,
  Stethoscope,
  MapPin,
  MessageSquare,
  Phone,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock3,
  User,
  Building2
} from "lucide-react";
import { ConsultationData } from "../../services/patient-consultation.service";
import { formatCurrency } from "../../../../../../utils/format-currency";

interface AppointmentDetailsSheetProps {
  appointment: ConsultationData;
  children: React.ReactNode;
  onJoinConsultation?: (appointmentId: string) => void;
  onCancelAppointment?: (appointmentId: string) => void;
  onRescheduleAppointment?: (appointmentId: string) => void;
}

export function AppointmentDetailsSheet({
  appointment,
  children,
  onJoinConsultation,
  onCancelAppointment,
  onRescheduleAppointment
}: AppointmentDetailsSheetProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getStatusInfo = (status: string) => {
    switch (status) {
      case "SCHEDULED":
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          text: "Agendada",
          icon: <Clock3 className="h-4 w-4" />
        };
      case "IN_PROGRESS":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          text: "Em Andamento",
          icon: <Video className="h-4 w-4" />
        };
      case "COMPLETED":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          text: "Concluída",
          icon: <CheckCircle className="h-4 w-4" />
        };
      case "CANCELED":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          text: "Cancelada",
          icon: <XCircle className="h-4 w-4" />
        };
      case "WAITING_ON_DUTY":
        return {
          color: "bg-orange-100 text-orange-800 border-orange-200",
          text: "Aguardando Plantão",
          icon: <AlertCircle className="h-4 w-4" />
        };
      case "ACCEPTED_BY_DOCTOR":
        return {
          color: "bg-purple-100 text-purple-800 border-purple-200",
          text: "Aceita pelo Médico",
          icon: <CheckCircle className="h-4 w-4" />
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          text: "Desconhecido",
          icon: <Clock3 className="h-4 w-4" />
        };
    }
  };

  const getConsultationTypeInfo = (type: string) => {
    switch (type) {
      case "TELEMEDICINE":
        return {
          text: "Teleconsulta",
          icon: <Video className="h-4 w-4" />,
          description: "Consulta online por vídeo"
        };
      case "AMBULATORY":
        return {
          text: "Ambulatorial",
          icon: <Stethoscope className="h-4 w-4" />,
          description: "Consulta presencial"
        };
      case "PRE_ANESTHETIC":
        return {
          text: "Pré-Anestésica",
          icon: <FileText className="h-4 w-4" />,
          description: "Avaliação pré-anestésica"
        };
      default:
        return {
          text: "Consulta",
          icon: <Stethoscope className="h-4 w-4" />,
          description: "Tipo de consulta não especificado"
        };
    }
  };

  const getPaymentStatusInfo = (status: string) => {
    switch (status) {
      case "PAID":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          text: "Pago",
          icon: <CheckCircle className="h-4 w-4" />
        };
      case "PENDING":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          text: "Pendente",
          icon: <Clock3 className="h-4 w-4" />
        };
      case "REFUNDED":
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          text: "Reembolsado",
          icon: <CheckCircle className="h-4 w-4" />
        };
      case "FAILED":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          text: "Falhou",
          icon: <XCircle className="h-4 w-4" />
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          text: "Desconhecido",
          icon: <Clock3 className="h-4 w-4" />
        };
    }
  };

  const statusInfo = getStatusInfo(appointment.status);
  const typeInfo = getConsultationTypeInfo(appointment.appointmentType || "TELEMEDICINE");
  const paymentInfo = getPaymentStatusInfo(appointment.paymentStatus);

  const canJoinConsultation = appointment.status === "IN_PROGRESS" || appointment.status === "ACCEPTED_BY_DOCTOR";
  const canCancel = appointment.status === "SCHEDULED" || appointment.status === "WAITING_ON_DUTY";
  const canReschedule = appointment.status === "SCHEDULED";

  const handleJoinConsultation = () => {
    if (onJoinConsultation && canJoinConsultation) {
      onJoinConsultation(appointment.id);
      setIsOpen(false);
    }
  };

  const handleCancelAppointment = () => {
    if (onCancelAppointment && canCancel) {
      onCancelAppointment(appointment.id);
      setIsOpen(false);
    }
  };

  const handleRescheduleAppointment = () => {
    if (onRescheduleAppointment && canReschedule) {
      onRescheduleAppointment(appointment.id);
      setIsOpen(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-full overflow-y-auto bg-white sm:max-w-2xl">
        <SheetHeader className="pb-6">
          <SheetTitle className="text-2xl font-bold text-gray-900">
            Detalhes da Consulta
          </SheetTitle>
        </SheetHeader>

        <div className="space-y-6">
          {/* Status e Tipo */}
          <div className="flex flex-wrap gap-3">
            <Badge className={`px-3 py-2 text-sm font-medium border ${statusInfo.color}`}>
              {statusInfo.icon}
              <span className="ml-2">{statusInfo.text}</span>
            </Badge>
            <Badge className="px-3 py-2 text-sm font-medium bg-gray-100 text-gray-800 border-gray-200">
              {typeInfo.icon}
              <span className="ml-2">{typeInfo.text}</span>
            </Badge>
            {appointment.isOnDuty && (
              <Badge className="px-3 py-2 text-sm font-medium bg-orange-100 text-orange-800 border-orange-200">
                <AlertCircle className="h-4 w-4 mr-2" />
                Plantão
              </Badge>
            )}
          </div>

          {/* Informações do Médico */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Médico
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-4">
                {appointment.doctor.avatarUrl ? (
                  <img
                    src={appointment.doctor.avatarUrl}
                    alt={appointment.doctor.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-8 w-8 text-blue-600" />
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {appointment.doctor.name}
                  </h3>
                  <p className="text-gray-600 mb-2">{appointment.doctor.specialty}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Stethoscope className="h-4 w-4" />
                      CRM: {appointment.doctor.crm}
                    </span>
                    <span className={`flex items-center gap-1 ${appointment.doctor.onlineStatus === 'ONLINE' ? 'text-green-600' : 'text-gray-500'}`}>
                      <div className={`w-2 h-2 rounded-full ${appointment.doctor.onlineStatus === 'ONLINE' ? 'bg-green-500' : 'bg-gray-400'}`} />
                      {appointment.doctor.onlineStatus === 'ONLINE' ? 'Online' : 'Offline'}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações da Consulta */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Calendar className="h-5 w-5 text-green-600" />
                Detalhes da Consulta
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Data</p>
                    <p className="font-medium">
                      {new Date(appointment.scheduledAt).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Horário</p>
                    <p className="font-medium">
                      {new Date(appointment.scheduledAt).toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
                {appointment.duration && (
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Duração</p>
                      <p className="font-medium">{appointment.duration} minutos</p>
                    </div>
                  </div>
                )}
                {appointment.urgencyLevel && (
                  <div className="flex items-center gap-3">
                    <AlertCircle className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Urgência</p>
                      <p className="font-medium capitalize">{appointment.urgencyLevel.toLowerCase()}</p>
                    </div>
                  </div>
                )}
              </div>

              {appointment.symptoms && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-500 mb-2">Sintomas</p>
                  <p className="text-gray-900">{appointment.symptoms}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Hospital */}
          {appointment.hospital && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-purple-600" />
                  Hospital
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  {appointment.hospital.logoUrl ? (
                    <img
                      src={appointment.hospital.logoUrl}
                      alt={appointment.hospital.name}
                      className="w-12 h-12 object-contain"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Building2 className="h-6 w-6 text-purple-600" />
                    </div>
                  )}
                  <div>
                    <h4 className="font-medium text-gray-900">{appointment.hospital.name}</h4>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pagamento */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5 text-yellow-600" />
                Pagamento
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Valor da consulta:</span>
                <span className="text-xl font-bold text-gray-900">
                  {formatCurrency(appointment.amount)}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={`px-3 py-2 text-sm font-medium border ${paymentInfo.color}`}>
                  {paymentInfo.icon}
                  <span className="ml-2">{paymentInfo.text}</span>
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Última Mensagem */}
          {appointment.lastMessage && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-indigo-600" />
                  Última Mensagem
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-500">
                      {appointment.lastMessage.isFromDoctor ? 'Médico' : 'Você'}
                    </span>
                    <span className="text-xs text-gray-400">
                      {new Date(appointment.lastMessage.createdAt).toLocaleString('pt-BR')}
                    </span>
                  </div>
                  <p className="text-gray-900">{appointment.lastMessage.content}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Ações */}
          <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
            {canJoinConsultation && (
              <Button
                onClick={handleJoinConsultation}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                <Video className="h-4 w-4 mr-2" />
                Entrar na Consulta
              </Button>
            )}

            {canReschedule && (
              <Button
                onClick={handleRescheduleAppointment}
                variant="outline"
                className="flex-1"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Reagendar
              </Button>
            )}

            {canCancel && (
              <Button
                onClick={handleCancelAppointment}
                variant="outline"
                className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
