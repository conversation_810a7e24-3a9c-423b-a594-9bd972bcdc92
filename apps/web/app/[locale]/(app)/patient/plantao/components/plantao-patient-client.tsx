"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	Clock,
	AlertCircle,
	Stethoscope,
	MessageSquare,
	PhoneCall,
	Users,
	CheckCircle,
	Loader2
} from "lucide-react";
import { User } from "@prisma/client";
import { toast } from "sonner";
import { UrgencySelector } from "./urgency-selector";
import { PlantaoStatus } from "./plantao-status";
import { PatientSubscriptionService } from "../../services/patient-subscription.service";

interface PlantaoPatientClientProps {
	user: User;
}

export function PlantaoPatientClient({ user }: PlantaoPatientClientProps) {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [isCheckingSubscription, setIsCheckingSubscription] = useState(true);
	const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
	const [urgencyLevel, setUrgencyLevel] = useState<"LOW" | "MEDIUM" | "HIGH">("MEDIUM");
	const [queueInfo, setQueueInfo] = useState({
		position: 0,
		estimatedWait: 0,
		onlineDoctors: 0
	});
	const [hasActiveAppointment, setHasActiveAppointment] = useState(false);
	const [activeAppointmentId, setActiveAppointmentId] = useState<string | null>(null);

	// Verificar assinatura ativa
	useEffect(() => {
		const checkSubscription = async () => {
			try {
				const response = await fetch('/api/patient/subscription/check');
				const data = await response.json();
				
				setHasActiveSubscription(data.hasActiveSubscription);
				
				if (!data.hasActiveSubscription) {
					router.push('/pay/plantao?urgencyLevel=medium');
					return;
				}
			} catch (error) {
				console.error('Erro ao verificar assinatura:', error);
				router.push('/pay/plantao?urgencyLevel=medium');
			} finally {
				setIsCheckingSubscription(false);
			}
		};

		checkSubscription();
	}, [user.id, router]);

	// Simular dados da fila (em produção, viria da API)
	useEffect(() => {
		if (hasActiveSubscription) {
			setQueueInfo({
				position: Math.floor(Math.random() * 8) + 1,
				estimatedWait: Math.floor(Math.random() * 20) + 5,
				onlineDoctors: Math.floor(Math.random() * 10) + 3
			});
		}
	}, [hasActiveSubscription]);

	const handleRequestPlantao = async () => {
		setIsLoading(true);
		try {
			// Aqui seria a chamada para a API de plantão
			// const response = await createOnDutyAppointment({ urgencyLevel });

			// Simulação
			await new Promise(resolve => setTimeout(resolve, 2000));

			setHasActiveAppointment(true);
			setActiveAppointmentId("plantao-" + Date.now());
			toast.success("Solicitação de plantão enviada com sucesso!");
		} catch (error) {
			toast.error("Erro ao solicitar plantão médico");
			console.error(error);
		} finally {
			setIsLoading(false);
		}
	};

	if (isCheckingSubscription) {
		return (
			<div className="container mx-auto py-6 px-4 max-w-2xl">
				<div className="flex items-center justify-center min-h-[400px]">
					<div className="text-center">
						<Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
						<p className="text-gray-600">Verificando assinatura...</p>
					</div>
				</div>
			</div>
		);
	}

	if (hasActiveAppointment) {
		return (
			<PlantaoStatus
				appointmentId={activeAppointmentId!}
				urgencyLevel={urgencyLevel}
				position={queueInfo.position}
				estimatedWait={queueInfo.estimatedWait}
				onlineDoctors={queueInfo.onlineDoctors}
			/>
		);
	}

	return (
		<div className="container mx-auto py-6 px-4 max-w-2xl">
			{/* Header */}
			<div className="text-center mb-8">
				<div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<Stethoscope className="w-8 h-8 text-red-600" />
				</div>
				<h1 className="text-2xl font-bold text-gray-900 mb-2">
					Plantão Médico 24h
				</h1>
				<p className="text-gray-600">
					Atendimento médico de urgência disponível 24 horas por dia
				</p>
			</div>

			{/* Status da Fila */}
			<Card className="mb-6">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Users className="w-5 h-5 text-blue-600" />
						Status da Fila
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-3 gap-4 text-center">
						<div>
							<p className="text-2xl font-bold text-gray-900">{queueInfo.position}</p>
							<p className="text-sm text-gray-500">Posição na fila</p>
						</div>
						<div>
							<p className="text-2xl font-bold text-blue-600">{queueInfo.estimatedWait}min</p>
							<p className="text-sm text-gray-500">Tempo estimado</p>
						</div>
						<div>
							<p className="text-2xl font-bold text-green-600">{queueInfo.onlineDoctors}</p>
							<p className="text-sm text-gray-500">Médicos online</p>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Seletor de Urgência */}
			<Card className="mb-6">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<AlertCircle className="w-5 h-5 text-orange-600" />
						Nível de Urgência
					</CardTitle>
				</CardHeader>
				<CardContent>
					<UrgencySelector
						value={urgencyLevel}
						onChange={setUrgencyLevel}
					/>
				</CardContent>
			</Card>

			{/* Informações Importantes */}
			<Card className="mb-8">
				<CardHeader>
					<CardTitle className="text-lg">Antes de solicitar o atendimento</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						<div className="flex items-start gap-3">
							<CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
							<div>
								<p className="font-medium">Tenha seus documentos em mãos</p>
								<p className="text-sm text-gray-600">RG, CPF e cartão do plano de saúde (se houver)</p>
							</div>
						</div>
						<div className="flex items-start gap-3">
							<CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
							<div>
								<p className="font-medium">Descreva seus sintomas</p>
								<p className="text-sm text-gray-600">Seja claro sobre o que você está sentindo</p>
							</div>
						</div>
						<div className="flex items-start gap-3">
							<CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
							<div>
								<p className="font-medium">Informe medicamentos em uso</p>
								<p className="text-sm text-gray-600">Liste todos os medicamentos que você está tomando</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Botão de Solicitação */}
			<div className="space-y-4">
				<Button
					onClick={handleRequestPlantao}
					disabled={isLoading}
					className="w-full h-14 text-lg font-semibold bg-red-600 hover:bg-red-700"
					size="lg"
				>
					{isLoading ? (
						<>
							<Loader2 className="w-5 h-5 mr-2 animate-spin" />
							Solicitando atendimento...
						</>
					) : (
						<>
							<Stethoscope className="w-5 h-5 mr-2" />
							Solicitar Atendimento de Urgência
						</>
					)}
				</Button>

				<p className="text-center text-sm text-gray-500">
					Você será direcionado para um médico disponível
				</p>
			</div>

			{/* Alerta para Emergências */}
			<div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg">
				<div className="flex items-start gap-3">
					<AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
					<div>
						<p className="font-semibold text-red-900">Em caso de emergência</p>
						<p className="text-sm text-red-700">
							Para emergências médicas graves, ligue para o SAMU: <strong>192</strong> ou
							dirija-se ao pronto-socorro mais próximo.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
