"use client";

import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { Label } from "@ui/components/label";
import { AlertCircle, Clock, Zap } from "lucide-react";

interface UrgencySelectorProps {
	value: "LOW" | "MEDIUM" | "HIGH";
	onChange: (value: "LOW" | "MEDIUM" | "HIGH") => void;
}

export function UrgencySelector({ value, onChange }: UrgencySelectorProps) {
	return (
		<RadioGroup
			value={value}
			onValueChange={onChange}
			className="space-y-4"
		>
			{/* Baixa Urgência */}
			<div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
				<RadioGroupItem value="LOW" id="low" />
				<div className="flex-1">
					<Label htmlFor="low" className="flex items-center gap-2 font-medium cursor-pointer">
						<Clock className="w-4 h-4 text-green-600" />
						Baixa Urgência
					</Label>
					<p className="text-sm text-gray-600 mt-1">
						<PERSON><PERSON><PERSON> leves, consulta pode aguardar algumas horas
					</p>
					<p className="text-xs text-gray-500 mt-1">
						Ex: resfriado, dor de cabeça leve, consulta de rotina
					</p>
				</div>
			</div>

			{/* Média Urgência */}
			<div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
				<RadioGroupItem value="MEDIUM" id="medium" />
				<div className="flex-1">
					<Label htmlFor="medium" className="flex items-center gap-2 font-medium cursor-pointer">
						<AlertCircle className="w-4 h-4 text-orange-600" />
						Média Urgência
					</Label>
					<p className="text-sm text-gray-600 mt-1">
						Sintomas moderados que precisam de atenção médica
					</p>
					<p className="text-xs text-gray-500 mt-1">
						Ex: febre alta, vômitos, dor moderada a forte
					</p>
				</div>
			</div>

			{/* Alta Urgência */}
			<div className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
				<RadioGroupItem value="HIGH" id="high" />
				<div className="flex-1">
					<Label htmlFor="high" className="flex items-center gap-2 font-medium cursor-pointer">
						<Zap className="w-4 h-4 text-red-600" />
						Alta Urgência
					</Label>
					<p className="text-sm text-gray-600 mt-1">
						Sintomas graves que necessitam atendimento imediato
					</p>
					<p className="text-xs text-gray-500 mt-1">
						Ex: dor no peito, dificuldade para respirar, sangramento
					</p>
				</div>
			</div>
		</RadioGroup>
	);
}
