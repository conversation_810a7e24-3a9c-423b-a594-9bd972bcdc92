"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	Clock,
	AlertCircle,
	Stethoscope,
	MessageSquare,
	PhoneCall,
	Users,
	CheckCircle,
	X,
	Loader2
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

interface PlantaoStatusProps {
	appointmentId: string;
	urgencyLevel: "LOW" | "MEDIUM" | "HIGH";
	position: number;
	estimatedWait: number;
	onlineDoctors: number;
}

export function PlantaoStatus({
	appointmentId,
	urgencyLevel,
	position,
	estimatedWait,
	onlineDoctors
}: PlantaoStatusProps) {
	const [status, setStatus] = useState<"WAITING" | "ACCEPTED" | "IN_PROGRESS">("WAITING");
	const [doctorInfo, setDoctorInfo] = useState<{
		name: string;
		specialty: string;
		crm: string;
	} | null>(null);
	const [currentPosition, setCurrentPosition] = useState(position);
	const [currentWait, setCurrentWait] = useState(estimatedWait);

	// Simular atualização da fila
	useEffect(() => {
		const interval = setInterval(() => {
			if (status === "WAITING" && currentPosition > 1) {
				setCurrentPosition(prev => Math.max(1, prev - 1));
				setCurrentWait(prev => Math.max(2, prev - 2));
			}
		}, 30000); // Atualiza a cada 30 segundos

		return () => clearInterval(interval);
	}, [status, currentPosition]);

	// Simular aceite do médico
	useEffect(() => {
		if (status === "WAITING") {
			const timeout = setTimeout(() => {
				setStatus("ACCEPTED");
				setDoctorInfo({
					name: "Dr. João Silva",
					specialty: "Clínico Geral",
					crm: "CRM/SP 123456"
				});
				toast.success("Um médico aceitou seu atendimento!");
			}, Math.random() * 60000 + 30000); // Entre 30s e 1min30s

			return () => clearTimeout(timeout);
		}
	}, [status]);

	const handleCancelRequest = () => {
		toast.success("Solicitação cancelada");
		window.location.reload();
	};

	const handleStartConsultation = () => {
		setStatus("IN_PROGRESS");
		toast.success("Consulta iniciada!");
	};

	const getUrgencyBadge = () => {
		switch (urgencyLevel) {
			case "HIGH":
				return <Badge variant="destructive">Alta Urgência</Badge>;
			case "MEDIUM":
				return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Média Urgência</Badge>;
			case "LOW":
				return <Badge variant="secondary" className="bg-green-100 text-green-800">Baixa Urgência</Badge>;
		}
	};

	const getStatusInfo = () => {
		switch (status) {
			case "WAITING":
				return {
					icon: <Clock className="w-6 h-6 text-blue-600" />,
					title: "Aguardando Médico",
					description: "Sua solicitação está na fila de atendimento"
				};
			case "ACCEPTED":
				return {
					icon: <CheckCircle className="w-6 h-6 text-green-600" />,
					title: "Médico Encontrado",
					description: "Um médico aceitou seu atendimento"
				};
			case "IN_PROGRESS":
				return {
					icon: <Stethoscope className="w-6 h-6 text-purple-600" />,
					title: "Consulta em Andamento",
					description: "Você está sendo atendido"
				};
		}
	};

	const statusInfo = getStatusInfo();

	return (
		<div className="container mx-auto py-6 px-4 max-w-2xl">
			{/* Header Status */}
			<Card className="mb-6">
				<CardContent className="pt-6">
					<div className="text-center">
						<div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
							{statusInfo.icon}
						</div>
						<h1 className="text-2xl font-bold text-gray-900 mb-2">
							{statusInfo.title}
						</h1>
						<p className="text-gray-600 mb-4">
							{statusInfo.description}
						</p>
						{getUrgencyBadge()}
					</div>
				</CardContent>
			</Card>

			{/* Informações da Fila - apenas quando aguardando */}
			{status === "WAITING" && (
				<Card className="mb-6">
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="w-5 h-5 text-blue-600" />
							Sua Posição na Fila
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-3 gap-4 text-center">
							<div>
								<p className="text-3xl font-bold text-blue-600">{currentPosition}</p>
								<p className="text-sm text-gray-500">Posição</p>
							</div>
							<div>
								<p className="text-3xl font-bold text-orange-600">{currentWait}min</p>
								<p className="text-sm text-gray-500">Estimativa</p>
							</div>
							<div>
								<p className="text-3xl font-bold text-green-600">{onlineDoctors}</p>
								<p className="text-sm text-gray-500">Médicos online</p>
							</div>
						</div>

						{currentPosition <= 3 && (
							<div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
								<p className="text-sm text-green-700 text-center">
									🎯 Você está próximo do atendimento!
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* Informações do Médico - quando aceito */}
			{status === "ACCEPTED" && doctorInfo && (
				<Card className="mb-6">
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Stethoscope className="w-5 h-5 text-green-600" />
							Médico Responsável
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex items-center gap-4">
							<div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
								<Stethoscope className="w-6 h-6 text-green-600" />
							</div>
							<div className="flex-1">
								<h3 className="font-semibold text-lg">{doctorInfo.name}</h3>
								<p className="text-gray-600">{doctorInfo.specialty}</p>
								<p className="text-sm text-gray-500">{doctorInfo.crm}</p>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Actions */}
			<div className="space-y-4">
				{status === "WAITING" && (
					<>
						<div className="flex items-center justify-center space-x-2 text-gray-500">
							<Loader2 className="w-4 h-4 animate-spin" />
							<span>Aguardando médico aceitar...</span>
						</div>

						<Button
							variant="outline"
							onClick={handleCancelRequest}
							className="w-full"
						>
							<X className="w-4 h-4 mr-2" />
							Cancelar Solicitação
						</Button>
					</>
				)}

				{status === "ACCEPTED" && (
					<>
						<Button
							onClick={handleStartConsultation}
							className="w-full h-14 text-lg font-semibold bg-green-600 hover:bg-green-700"
							size="lg"
						>
							<MessageSquare className="w-5 h-5 mr-2" />
							Iniciar Consulta
						</Button>

						<div className="grid grid-cols-2 gap-3">
							<Button variant="outline" size="lg">
								<PhoneCall className="w-4 h-4 mr-2" />
								Ligar
							</Button>
							<Button variant="outline" size="lg">
								<MessageSquare className="w-4 h-4 mr-2" />
								Chat
							</Button>
						</div>
					</>
				)}

				{status === "IN_PROGRESS" && (
					<>
						<Link href={`/patient/zapchat?appointment=${appointmentId}`}>
							<Button
								className="w-full h-14 text-lg font-semibold bg-purple-600 hover:bg-purple-700"
								size="lg"
							>
								<MessageSquare className="w-5 h-5 mr-2" />
								Continuar Consulta
							</Button>
						</Link>

						<div className="grid grid-cols-2 gap-3">
							<Button variant="outline" size="lg">
								<PhoneCall className="w-4 h-4 mr-2" />
								Ligar para Médico
							</Button>
							<Button variant="outline" size="lg">
								<AlertCircle className="w-4 h-4 mr-2" />
								Emergência
							</Button>
						</div>
					</>
				)}
			</div>

			{/* ID do Atendimento */}
			<div className="mt-8 p-4 bg-gray-50 rounded-lg">
				<p className="text-center text-sm text-gray-600">
					ID do Atendimento: <span className="font-mono font-semibold">{appointmentId}</span>
				</p>
			</div>
		</div>
	);
}
