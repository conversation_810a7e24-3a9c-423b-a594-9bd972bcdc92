import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import type { Metadata } from "next";
import { Suspense } from "react";
import { ScheduleClient } from "./schedule-client";

export const metadata: Metadata = {
	title: "Agenda | ZapVida",
	description: "Gerencie sua agenda de consultas",
};

interface CalendarPageProps {
	searchParams: {
		doctorId?: string;
	};
}

export default async function CalendarPage({ searchParams }: CalendarPageProps) {
	const { user } = await currentUser();
	const { doctorId } = searchParams;

	// Determine which doctor's appointments to fetch
	let doctorFilter = {};
	if (user?.role === "DOCTOR") {
		// If user is a doctor, show their own appointments
		doctorFilter = { userId: user.id };
	} else if (doctorId) {
		// If doctorId is provided (for ADMIN/HOSPITAL users), show that doctor's appointments
		doctorFilter = { id: doctorId };
	} else if (user?.role === "ADMIN" || user?.role === "HOSPITAL") {
		// For ADMIN/HOSPITAL without doctorId, we'll show an empty calendar
		// They'll need to select a doctor from the dropdown
		return (
			<div className="flex flex-col gap-6">
				<Suspense>
					<ScheduleClient events={[]} />
				</Suspense>
			</div>
		);
	}

	const appointments = await db.appointment.findMany({
		where: {
			doctor: doctorFilter,
		},
		include: {
			patient: {
				include: {
					user: true,
				},
			},
		},
	});

	const events = appointments.map((appointment) => ({
		id: appointment.id,
		title: `Consulta com ${appointment.patient.user.name}`,
		start: appointment.scheduledAt,
		end: new Date(
			new Date(appointment.scheduledAt).getTime() +
				appointment.duration * 60000,
		),
		status: appointment.status,
		consultType: appointment.consultType,
		appointment: appointment
	}))

	return (
		<div className="flex flex-col gap-6">
			<Suspense>
				<ScheduleClient events={events} />
			</Suspense>
		</div>
	);
}
