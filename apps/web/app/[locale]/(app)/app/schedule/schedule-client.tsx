"use client";

import { Plus, Search } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@saas/auth/hooks/use-user";
import { apiClient } from "@shared/lib/api-client";

import { Button } from "@ui/components/button";
import { DashboardHeader } from "@ui/components/header";
import { DoctorCalendar } from "@ui/components/telemed/doctor/calendar/doctor-calendar";
import { AppointmentSheet } from "../appointments/components/appointment-sheet";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";

interface ScheduleClientProps {
	events: any[];
}

export function ScheduleClient({ events: initialEvents }: ScheduleClientProps) {
	const session = useUser();
	const router = useRouter();
	const [isSheetOpen, setIsSheetOpen] = useState(false);
	const [isDoctorSheetOpen, setIsDoctorSheetOpen] = useState(false);
	const [selectedDoctorId, setSelectedDoctorId] = useState<string>();
	const [events, setEvents] = useState(initialEvents);
	const [searchTerm, setSearchTerm] = useState("");

  const { data: doctors, refetch: refetchDoctors } = apiClient.appointments.getDoctorsBySpecialty.useQuery(
    {},
    { enabled: session?.user?.role === "ADMIN" || session?.user?.role === "HOSPITAL" }
  );

  const { data: doctorAppointments } = apiClient.appointments.getAppointmentsByDoctor.useQuery(
    { doctorId: selectedDoctorId || "" },
    { enabled: !!selectedDoctorId }
  );

  useEffect(() => {
    if (doctorAppointments) {
      console.log("Appointments carregados para o médico:", doctorAppointments);

      const calendarEvents = doctorAppointments.map((apt: any) => {
        console.log("Processando appointment:", apt);

        return {
          id: apt.id,
          title: `${apt.patient.user.name} - ${apt.consultType}`,
          start: new Date(apt.scheduledAt),
          end: new Date(new Date(apt.scheduledAt).getTime() + apt.duration * 60000),
          status: apt.status,
          consultType: apt.consultType,
          appointment: apt // Incluir os dados completos da consulta
        };
      });

      console.log("Eventos do calendário processados:", calendarEvents);
      setEvents(calendarEvents);
    }
  }, [doctorAppointments]);

	const userRole = session?.user?.role || "PATIENT";
	const doctorId = selectedDoctorId || (session?.user?.role === "DOCTOR" ? session?.user?.doctor?.id : undefined);

	const handleDoctorChange = async (doctorId: string) => {
		setSelectedDoctorId(doctorId);
		setIsDoctorSheetOpen(false);
		// Navigate to the same page with the selected doctorId as a query parameter
		router.push(`/app/schedule?doctorId=${doctorId}`);
	};

	const handleAppointmentCreated = () => {
		// Refresh the calendar events after a new appointment is created
		router.refresh();
	};

	// The appointments are now fetched using the useQuery hook above

	const filteredDoctors = doctors?.filter(doctor =>
		doctor.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		doctor.specialties.some(specialty => specialty.name.toLowerCase().includes(searchTerm.toLowerCase()))
	) || [];

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<DashboardHeader
					heading="Agenda"
					text="Visão geral do agendamento de consultas."
				/>
				<div className="flex items-center gap-4">
					{(userRole === "ADMIN" || userRole === "HOSPITAL") && (
						<Sheet open={isDoctorSheetOpen} onOpenChange={setIsDoctorSheetOpen}>
							<SheetTrigger asChild>
								<Button variant="outline">
									{selectedDoctorId ? "Trocar Médico" : "Selecionar Médico"}
								</Button>
							</SheetTrigger>
							<SheetContent>
								<SheetHeader>
									<SheetTitle>Selecionar Médico</SheetTitle>
								</SheetHeader>
								<div className="py-4">
									<Input
										placeholder="Buscar médico..."
										value={searchTerm}
										onChange={(e) => setSearchTerm(e.target.value)}
										icon={<Search className="h-4 w-4" />}
									/>
								</div>
								<ScrollArea className="h-[300px] pr-4">
									{filteredDoctors.map((doctor) => (
										<div
											key={doctor.id}
											className="flex items-center space-x-4 py-2 cursor-pointer hover:bg-muted/50 rounded-md p-2"
											onClick={() => handleDoctorChange(doctor.id)}
										>
											<Avatar>
												<AvatarImage src={doctor.user.avatarUrl} />
												<AvatarFallback>{doctor.user.name.charAt(0)}</AvatarFallback>
											</Avatar>
											<div>
												<p className="font-medium">{doctor.user.name}</p>
												<p className="text-sm text-muted-foreground">
													{doctor.specialties.map(s => s.name).join(", ")}
												</p>
											</div>
										</div>
									))}
								</ScrollArea>
							</SheetContent>
						</Sheet>
					)}
					<AppointmentSheet
						userRole={userRole}
						doctorId={doctorId}
						onAppointmentCreated={handleAppointmentCreated}
						isOpen={isSheetOpen}
						onOpenChange={setIsSheetOpen}
					/>

				</div>
			</div>

			<DoctorCalendar events={events} />
		</div>
	);
}
