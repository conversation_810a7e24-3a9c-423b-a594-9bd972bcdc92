/* eslint-disable react/no-unescaped-entities */
'use client';

import {
	AlertCircle,
	BookOpen,
	CheckCircle2,
	ExternalLink,
	HelpCircle,
	Mail,
	MessageCircle,
	Phone,
	PlayCircle,
} from 'lucide-react';

import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@ui/components/accordion';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { DashboardHeader } from '@ui/components/header';
import { RiWhatsappFill } from '@remixicon/react';

const HelpAndSupportPage = () => {
	return (
		<div className='space-y-6'>
			<div className='flex items-center justify-between'>
				<DashboardHeader
					heading='   Ajuda e Suporte'
					text='Tire suas dúvidas aqui e obtenha suporte.'
				/>
			</div>

			<div className='grid gap-6 md:grid-cols-2'>
				{/* FAQs */}
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2'>
							<HelpCircle className='h-5 w-5' />
							Perguntas Frequentes
						</CardTitle>
					</CardHeader>
					<CardContent>
						<Accordion type='single' collapsible className='w-full'>
							<AccordionItem value='item-1'>
								<AccordionTrigger>
									Como faço para agendar uma consulta?
								</AccordionTrigger>
								<AccordionContent>
									Para agendar uma consulta, acesse a seção "Agendamentos" no
									menu principal. Escolha a especialidade desejada, selecione o
									profissional e horário disponível, e confirme sua consulta.
								</AccordionContent>
							</AccordionItem>
							<AccordionItem value='item-2'>
								<AccordionTrigger>
									Como funciona o pagamento das consultas?
								</AccordionTrigger>
								<AccordionContent>
									Aceitamos diversas formas de pagamento, incluindo cartão de
									crédito, PIX e boleto bancário. O pagamento é processado de
									forma segura através da nossa plataforma.
								</AccordionContent>
							</AccordionItem>
							<AccordionItem value='item-3'>
								<AccordionTrigger>
									Como recebo a prescrição médica?
								</AccordionTrigger>
								<AccordionContent>
									Após a consulta, o médico enviará a prescrição digital
									diretamente pela plataforma. Você poderá acessá-la na seção
									"Prescrições" ou baixá-la em PDF.
								</AccordionContent>
							</AccordionItem>
							<AccordionItem value='item-4'>
								<AccordionTrigger>
									Posso remarcar uma consulta?
								</AccordionTrigger>
								<AccordionContent>
									Sim, você pode remarcar sua consulta com até 24 horas de
									antecedência sem custo adicional. Acesse "Meus Agendamentos" e
									selecione a opção de remarcação.
								</AccordionContent>
							</AccordionItem>
						</Accordion>
					</CardContent>
				</Card>

				{/* Contato */}
				<Card>
					<CardHeader>
						<CardTitle className='flex items-center gap-2'>
							<Mail className='h-5 w-5' />
							Entre em Contato
						</CardTitle>
						<CardDescription>
							Nossa equipe está disponível para ajudar
						</CardDescription>
					</CardHeader>
					<CardContent className='space-y-4'>
						<div className='grid gap-4'>
							<div className='flex items-center gap-4 rounded-lg bg-muted p-4'>
								<MessageCircle className='h-5 w-5' />
								<div>
									<p className='font-medium'>Chat Online</p>
									<p className='text-sm text-muted-foreground'>
										Tempo médio de resposta: 5 minutos
									</p>
								</div>
								<Button className='ml-auto'>Iniciar Chat</Button>
							</div>

							<div className='flex items-center gap-4 rounded-lg bg-muted p-4'>
								<Mail className='h-5 w-5' />
								<div>
									<p className='font-medium'>Email</p>
									<p className='text-sm text-muted-foreground'>
										<EMAIL>
									</p>
								</div>
								<Button variant='outline' className='ml-auto'>
									Enviar Email
								</Button>
							</div>

							<div className='flex items-center gap-4 rounded-lg bg-muted p-4'>
								<Phone className='h-5 w-5' />
								<div>
									<p className='font-medium'>WhatsApp</p>
									<p className='text-sm text-muted-foreground'>
										Segunda a Sexta, 8h às 18h
									</p>
								</div>
								<Button
									variant='outline'
									onClick={() =>
										window.open('https://wa.me/5547997708518', '_blank')
									}
									className='ml-auto '
								>
									<RiWhatsappFill className='h-5 w-5 mr-2' />
									+55 (47) 99770-8518
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default HelpAndSupportPage;
