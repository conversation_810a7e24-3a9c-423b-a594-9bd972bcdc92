import { Skeleton } from "@ui/components/skeleton";
import { Card, CardContent } from "@ui/components/card";

export default function PatientProfileLoading() {
  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div>
      <h1 className="text-xl font-semibold mb-6">Perfil do paciente</h1>
      <p className="mb-6 text-gray-600">Atualize suas informações médicas e de contato.</p>

      </div>

      {/* Formulário esqueleto */}
      <div className="space-y-6">
        {/* Primeira linha: CPF e Data de Nascimento */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-5 w-10" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Segunda linha: Gênero e Telefone */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Terceira linha: Altura, Peso e Tipo Sanguíneo */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Quarta linha: Alergias */}
        <div className="space-y-2">
          <Skeleton className="h-5 w-48" />
          <Skeleton className="h-24 w-full" />
        </div>


        {/* Botão */}
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}
