import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { InviteMemberForm } from "@saas/settings/components/InviteMemberForm";
import { TeamMembersBlock } from "@saas/settings/components/TeamMembersBlock";
import { createApiCaller } from "api/trpc/caller";
import { getLocale, getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("settings.team.title"),
	};
}

export default async function MembersPage() {
	const locale = await getLocale();
	const apiCaller = await createApiCaller();
	const { user } = await currentUser();


	console.log("user MembersPage", user);

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	if (user.role !== "HOSPITAL" && user.role !== "ADMIN") {
		return redirect({ href: "/app/settings/account/general", locale });
	}

	if (!user.teamMemberships) {
		return redirect({ href: "/app/settings/account/general", locale });
	}

	const memberships = await apiCaller.team.memberships({
		teamId: user.teamMemberships[0].teamId,
	});

	const invitations = await apiCaller.team.invitations({
		teamId: user.teamMemberships[0].teamId,
	});

	return (
		<div className="grid grid-cols-1 gap-6">
			<InviteMemberForm teamId={user.teamMemberships[0].teamId} />
			<TeamMembersBlock memberships={memberships} invitations={invitations} />
		</div>
	);
}
