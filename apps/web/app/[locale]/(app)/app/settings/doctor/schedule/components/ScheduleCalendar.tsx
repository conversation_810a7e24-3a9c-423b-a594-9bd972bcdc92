'use client';

import React, { useEffect, useMemo, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction'; // para dateClick, eventClick, etc.
import listPlugin from '@fullcalendar/list';
import ptBrLocale from '@fullcalendar/core/locales/pt-br'; // Importar locale
import { toast } from "sonner";
import { format } from "date-fns";

// Reutilizar interfaces do componente pai
interface DoctorScheduleItem {
  id: string;
  doctorId: string;
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
  isBreak: boolean;
}

interface ScheduleBlock {
  id: string;
  doctorId: string;
  startTime: Date;
  endTime: Date;
  reason: string | null;
  type: "VACATION" | "HOLIDAY" | "LUNCH" | "PERSONAL" | "OTHER";
  description: string | null;
}

interface Appointment {
  id: string;
  startTime: Date;
  endTime: Date;
  title?: string;
  status?: string;
  consultType?: string;
  // Adicione outros campos se necessário
}

interface ScheduleCalendarProps {
  doctorId: string;
  schedules: DoctorScheduleItem[];
  blocks: ScheduleBlock[];
  appointments: Appointment[];
  // onDataChange?: () => void; // Opcional: para recarregar dados após interação
}

// Adicionar um estado de carregamento local
export const ScheduleCalendar: React.FC<ScheduleCalendarProps> = ({
  doctorId,
  schedules,
  blocks,
  appointments,
}) => {
  const [isCalendarReady, setIsCalendarReady] = useState(false);

  // Adicionar useEffect para simular carregamento progressivo
  useEffect(() => {
    // Dar tempo para o FullCalendar renderizar
    const timer = setTimeout(() => {
      setIsCalendarReady(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const calendarEvents = useMemo(() => {
    const events: any[] = [];

    // 1. Adicionar Bloqueios (têm prioridade visual sobre horários)
    blocks.forEach(block => {
      events.push({
        id: `block-${block.id}`,
        title: block.reason || block.type || 'Bloqueado',
        start: block.startTime,
        end: block.endTime,
        allDay: false, // Assumindo que bloqueios têm hora
        backgroundColor: '#fecaca', // Exemplo: vermelho claro
        borderColor: '#f87171',
        textColor: '#7f1d1d',
        extendedProps: { type: 'block', blockData: block }
      });
    });

    // 2. Adicionar Consultas Agendadas
    appointments.forEach(apt => {
      let color = '#60a5fa'; // Azul padrão
      if (apt.status === 'COMPLETED') color = '#4ade80'; // Verde
      if (apt.status === 'CANCELED' || apt.status === 'NO_SHOW') color = '#a1a1aa'; // Cinza

      events.push({
        id: `apt-${apt.id}`,
        title: apt.title || 'Consulta',
        start: apt.startTime,
        end: apt.endTime,
        backgroundColor: color,
        borderColor: color,
        textColor: '#ffffff',
        extendedProps: { type: 'appointment', appointmentData: apt }
      });
    });

    // 3. Adicionar Horários de Trabalho (como background events)
    // Isso só funciona bem na visualização semanal/diária (timeGridWeek/timeGridDay)
    schedules.filter(s => s.isEnabled && !s.isBreak).forEach(schedule => {
        events.push({
            // id: `sched-${schedule.id}`, // ID não é estritamente necessário para background
            // title: 'Disponível', // Não mostra título para background
            daysOfWeek: [ schedule.weekDay ],
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            display: 'background',
            color: '#dcfce7', // Verde muito claro
            extendedProps: { type: 'schedule', scheduleData: schedule }
        });
    });

     // 4. Adicionar Intervalos (como background events ou eventos normais cinza)
    schedules.filter(s => s.isEnabled && s.isBreak).forEach(schedule => {
        events.push({
            // id: `break-${schedule.id}`,
            daysOfWeek: [ schedule.weekDay ],
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            display: 'background',
            color: '#e5e7eb', // Cinza claro
            extendedProps: { type: 'break', scheduleData: schedule }
            // Alternativa: Mostrar como evento normal
            // title: 'Intervalo',
            // backgroundColor: '#e5e7eb',
            // borderColor: '#d1d5db',
            // textColor: '#4b5563',
        });
    });


    return events;
  }, [schedules, blocks, appointments]);

  // Handlers para interações (opcional)
  const handleDateClick = (arg: any) => {
    // Ex: Abrir modal para adicionar bloqueio ou consulta
    console.log('Date clicked:', arg.dateStr);
    // Poderia chamar um modal aqui
  };

  const handleEventClick = (arg: any) => {
    // Ex: Mostrar detalhes do evento
    console.log('Event clicked:', arg.event);
    const props = arg.event.extendedProps;
    if (props.type === 'appointment') {
        toast.info(`Consulta: ${arg.event.title}\nStatus: ${props.appointmentData.status}`);
        // Poderia navegar para a página da consulta
    } else if (props.type === 'block') {
         toast.info(`Bloqueio: ${arg.event.title}\nDe: ${format(props.blockData.startTime, 'Pp', { locale: ptBrLocale })}\nAté: ${format(props.blockData.endTime, 'Pp', { locale: ptBrLocale })}`);
    }
  };

  return (
    <div>
      {/* Renderizar o FullCalendar */}
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        initialView="timeGridWeek" // Visão inicial
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
        }}
        events={calendarEvents}
        locale={ptBrLocale} // Aplicar locale pt-BR
        nowIndicator={true} // Mostrar indicador de hora atual
        editable={false} // Desabilitar arrastar/redimensionar por padrão
        selectable={true} // Permitir selecionar datas/horas
        selectMirror={true}
        dayMaxEvents={true} // Limitar número de eventos no mês
        weekends={true} // Mostrar fins de semana
        slotMinTime="06:00:00" // Hora de início do dia no calendário
        slotMaxTime="22:00:00" // Hora de fim do dia no calendário
        allDaySlot={false} // Ocultar slot "all-day" se não for usado
        // businessHours={{ // Opcional: destacar horários de trabalho (alternativa ao background events)
        //   daysOfWeek: [ 1, 2, 3, 4, 5 ], // Segunda a Sexta
        //   startTime: '08:00',
        //   endTime: '18:00',
        // }}
        dateClick={handleDateClick} // Handler para clique em data/hora
        eventClick={handleEventClick} // Handler para clique em evento
        // select={handleDateSelect} // Handler para seleção de intervalo
        // eventDrop={handleEventDrop} // Handler para arrastar evento
        // eventResize={handleEventResize} // Handler para redimensionar evento
        height="auto" // Ajustar altura automaticamente
      />
    </div>
  );
};
