'use client';

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { apiClient } from "@shared/lib/api-client";
import { But<PERSON> } from "@ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@ui/components/table";
import { toast } from "sonner";
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  File,
  Upload,
  X
} from "lucide-react";
import { Badge } from "@ui/components/badge";
import { useRouter } from "next/navigation";
import DoctorDocumentsLoading from "./loading";

// Helpers para status dos documentos
const getStatusBadge = (status: string) => {
  switch (status) {
    case "APPROVED":
      return <Badge className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" /> Aprovado</Badge>;
    case "REJECTED":
      return <Badge className="bg-red-500"><X className="w-3 h-3 mr-1" /> Rejeitado</Badge>;
    case "PENDING":
    default:
      return <Badge className="bg-yellow-500"><Clock className="w-3 h-3 mr-1" /> Pendente</Badge>;
  }
};

const getDocumentTypeLabel = (type: string) => {
  const types: Record<string, string> = {
    "CRM": "Carteira do CRM",
    "SPECIALTY": "Certificado de Especialidade",
    "ID": "Documento de Identidade",
    "COLLEGE_DEGREE": "Diploma",
    "RESIDENCY": "Certificado de Residência",
    "OTHER": "Outro documento"
  };

  return types[type] || type;
};

export default function DoctorDocumentsPage() {
  const router = useRouter();
  const [documents, setDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadType, setUploadType] = useState<string>("CRM");
  const [file, setFile] = useState<File | null>(null);

  // Queries
  const { data: doctorData, isLoading: isDoctorLoading } = apiClient.doctors.getCurrentDoctor.useQuery();
  const { data: doctorDocuments, isLoading: isDocumentsLoading } = apiClient.doctors.getDocuments.useQuery(
    { doctorId: doctorData?.id ?? "" },
    { enabled: !!doctorData?.id }
  );

  // Mutations
  const uploadDocumentMutation = apiClient.doctors.uploadDocument.useMutation();
  const deleteDocumentMutation = apiClient.doctors.deleteDocument.useMutation();

  // Carregar documentos
  useEffect(() => {
    if (doctorDocuments) {
      setDocuments(doctorDocuments);
    }
  }, [doctorDocuments]);

  // Handler para upload de arquivo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  // Handler para submeter upload
  const handleUpload = async () => {
    if (!file || !doctorData?.id) {
      toast.error("Selecione um arquivo para upload");
      return;
    }

    setIsLoading(true);

    try {
      // Get a signed URL for upload
      const response = await uploadDocumentMutation.mutateAsync({
        doctorId: doctorData.id,
        fileName: file.name,
        fileType: file.type,
        type: uploadType as "CRM" | "SPECIALTY" | "ID" | "COLLEGE_DEGREE" | "RESIDENCY" | "OTHER"
      });

      // Upload directly to S3 with the signed URL
      const uploadResponse = await fetch(response.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file to storage');
      }

      // Document was created in the database and file was uploaded
      setDocuments(prev => [response.document, ...prev]);
      toast.success("Documento enviado com sucesso!");
      setFile(null);
    } catch (error) {
      console.error(error);
      toast.error("Erro ao enviar documento. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handler para excluir documento
  const handleDeleteDocument = async (documentId: string) => {
    setIsLoading(true);

    try {
      await deleteDocumentMutation.mutateAsync({
        id: documentId
      });

      setDocuments(documents.filter(doc => doc.id !== documentId));
      toast.success("Documento excluído com sucesso!");
    } catch (error) {
      console.error(error);
      toast.error("Erro ao excluir documento");
    } finally {
      setIsLoading(false);
    }
  };

  if (isDoctorLoading || isDocumentsLoading) {
    return <DoctorDocumentsLoading />;
  }

  return (
    <div>
      <h1 className="text-xl font-semibold mb-2">Documentos</h1>
      <p className="mb-6 text-gray-600">Gerencie seus documentos profissionais e certificações.</p>

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Enviar novo documento</CardTitle>
            <CardDescription>Envie seus documentos para verificação pela equipe.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium">Tipo de documento</label>
                <select
                  className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md mt-1"
                  value={uploadType}
                  onChange={(e) => setUploadType(e.target.value)}
                >
                  <option value="CRM">Carteira do CRM</option>
                  <option value="SPECIALTY">Certificado de Especialidade</option>
                  <option value="ID">Documento de Identidade</option>
                  <option value="COLLEGE_DEGREE">Diploma</option>
                  <option value="RESIDENCY">Certificado de Residência</option>
                  <option value="OTHER">Outro documento</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium">Arquivo</label>
                <div className="mt-1">
                  <input
                    type="file"
                    className="hidden"
                    id="document-file"
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                  <label
                    htmlFor="document-file"
                    className="flex items-center justify-center w-full h-10 px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {file ? file.name : "Selecionar arquivo"}
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    Formatos aceitos: PDF, JPG, JPEG, PNG (máx. 5MB)
                  </p>
                </div>
              </div>
            </div>

            <Button
              className="w-full mt-4"
              disabled={isLoading || !file}
              onClick={handleUpload}
            >
              {isLoading ? "Enviando..." : "Enviar documento"}
            </Button>

            {doctorData && (
              <div className="flex items-center p-4 mt-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
                <p className="text-sm text-yellow-700">
                  Sua conta está pendente de aprovação. Envie os documentos necessários para agilizar o processo.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Documentos enviados</CardTitle>
            <CardDescription>Lista de documentos enviados e seu status de aprovação.</CardDescription>
          </CardHeader>
          <CardContent>
            {documents.length === 0 ? (
              <div className="text-center p-8 text-gray-500">
                <File className="w-12 h-12 mx-auto text-gray-300" />
                <p className="mt-2">Nenhum documento enviado</p>
                <p className="text-sm">Envie seus documentos para completar seu cadastro.</p>
              </div>
            ) : (
              <Table>
                <TableCaption>Lista de documentos enviados</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Nome do arquivo</TableHead>
                    <TableHead>Enviado em</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell className="font-medium">{getDocumentTypeLabel(document.type)}</TableCell>
                      <TableCell>{document.fileName}</TableCell>
                      <TableCell>{format(new Date(document.createdAt), "dd/MM/yyyy HH:mm")}</TableCell>
                      <TableCell>{getStatusBadge(document.status)}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(document.fileUrl, "_blank")}
                          >
                            Ver
                          </Button>
                          {document.status === "PENDING" && (
                            <Button
                              variant="error"
                              size="sm"
                              onClick={() => handleDeleteDocument(document.id)}
                              disabled={isLoading}
                            >
                              Excluir
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Requisitos de documentação</CardTitle>
            <CardDescription>Documentos necessários para aprovação na plataforma.</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 list-disc pl-5">
              <li>
                <span className="font-medium">Carteira do CRM:</span>{" "}
                <span className="text-gray-600">Documento oficial atualizado que comprove seu registro no Conselho Regional de Medicina</span>
              </li>
              <li>
                <span className="font-medium">Documento de Identidade:</span>{" "}
                <span className="text-gray-600">RG, CNH ou outro documento de identificação com foto</span>
              </li>
              <li>
                <span className="font-medium">Diploma:</span>{" "}
                <span className="text-gray-600">Certificado de conclusão do curso de medicina</span>
              </li>
              <li>
                <span className="font-medium">Certificado de Especialidade:</span>{" "}
                <span className="text-gray-600">Comprovante de especialização (RQE) ou título de especialista</span>
              </li>
            </ul>

            <Separator className="my-4" />

            <div className="text-sm text-gray-600">
              <p className="mb-2">Observações importantes:</p>
              <ul className="space-y-1 list-disc pl-5">
                <li>Todos os documentos devem estar legíveis</li>
                <li>São aceitos arquivos nos formatos PDF, JPG, JPEG e PNG</li>
                <li>O tamanho máximo permitido é de 5MB por arquivo</li>
                <li>A análise dos documentos pode levar até 2 dias úteis</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
