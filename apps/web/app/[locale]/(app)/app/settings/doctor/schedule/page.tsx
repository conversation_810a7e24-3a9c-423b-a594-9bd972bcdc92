// Substitua no arquivo page.tsx (app/settings/doctor/schedule)

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Clock, Loader2 } from "lucide-react";
// import { useCurrentUser } from "@/hooks/use-current-user";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { ScheduleCalendar } from './components/ScheduleCalendar';
import { ScheduleListEditor } from './components/ScheduleListEditor';
// import { ScheduleBlockManager } from './components/ScheduleBlockManager';
import { QuickScheduleSetup } from './components/QuickScheduleSetup';
import { useUser } from '@saas/auth/hooks/use-user';
import { ScheduleBlockManager } from './components/ScheduleBlockManager';

// Definir interfaces para os tipos de dados
interface DoctorProfile {
  id: string;
}

interface DoctorScheduleItem {
  id: string;
  doctorId: string;
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
  isBreak: boolean;
}

interface ScheduleBlock {
  id: string;
  doctorId: string;
  startTime: Date;
  endTime: Date;
  reason: string | null;
  type: "VACATION" | "HOLIDAY" | "LUNCH" | "PERSONAL" | "OTHER";
  description: string | null;
}

interface ScheduleInputItem {
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
  isBreak: boolean;
}

interface Appointment {
  id: string;
  startTime: Date;
  endTime: Date;
  patient?: {
    name: string;
  };
  title?: string;
  status?: string;
  consultType?: string;
}

export default function DoctorSchedulePage() {
  const router = useRouter();
  const { user } = useUser();
  const [schedules, setSchedules] = useState<DoctorScheduleItem[]>([]);
  const [blocks, setBlocks] = useState<ScheduleBlock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false); // Novo estado para controlar salvamento
  const [doctorProfile, setDoctorProfile] = useState<DoctorProfile | null>(null);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("list"); // Controlar a aba ativa

  // Função unificada para buscar todos os dados
  const fetchData = useCallback(async (doctorId: string) => {
    setIsLoading(true);
    setError(null);
    console.log(`[SchedulePage] Fetching data for doctorId: ${doctorId}`);

    // Primeiro carregamos os horários (mais importantes para a primeira aba)
    try {
      const scheduleResponse = await fetch(`/api/doctor/schedule?doctorId=${doctorId}`);

      if (scheduleResponse.ok) {
        const scheduleData = await scheduleResponse.json();
        if (scheduleData.success) {
          setSchedules(scheduleData.schedules || []);
          console.log("[SchedulePage] Schedules loaded:", scheduleData.schedules);
        } else {
          throw new Error(scheduleData.message || "Falha ao buscar horários");
        }
      } else {
        throw new Error(`Erro ${scheduleResponse.status} ao buscar horários`);
      }

      // Reduzimos o loading inicial após carregar os horários
      setIsLoading(false);

      // Carregamos o resto dos dados em segundo plano
      Promise.all([
        fetch(`/api/doctor/schedule/blocks?doctorId=${doctorId}`),
        fetch(`/api/appointments?doctorId=${doctorId}&view=calendar`)
      ]).then(async ([blocksResponse, appointmentsResponse]) => {
        // Processar Bloqueios
        if (blocksResponse.ok) {
          const blocksData = await blocksResponse.json();
          const formattedBlocks = blocksData.map((b: any) => ({
              ...b,
              startTime: new Date(b.startTime),
              endTime: new Date(b.endTime)
          }));
          setBlocks(formattedBlocks);
          console.log("[SchedulePage] Blocks loaded:", formattedBlocks);
        } else {
          console.warn(`[SchedulePage] Failed to load blocks: ${blocksResponse.status}`);
        }

        // Processar Consultas
        if (appointmentsResponse.ok) {
          const appointmentsData = await appointmentsResponse.json();
          const formattedAppointments = (appointmentsData.appointments || appointmentsData || []).map((apt: any) => ({
            id: apt.id,
            title: apt.patient?.user?.name ? `${apt.patient.user.name} (${apt.consultType})` : `Consulta (${apt.consultType})`,
            start: new Date(apt.scheduledAt || apt.startTime),
            end: new Date(new Date(apt.scheduledAt || apt.startTime).getTime() + (apt.duration || 30) * 60000),
            status: apt.status,
            consultType: apt.consultType,
          }));
          setAppointments(formattedAppointments);
          console.log("[SchedulePage] Appointments loaded:", formattedAppointments);
        } else {
          console.warn(`[SchedulePage] Failed to load appointments: ${appointmentsResponse.status}`);
        }
      }).catch(err => {
        console.error("[SchedulePage] Error loading secondary data:", err);
        // Não mostramos erro para o usuário aqui, pois os dados principais já carregaram
      });

    } catch (err: any) {
      console.error("[SchedulePage] Error fetching data:", err);
      setError(err.message || "Erro ao carregar dados da agenda.");
      toast.error(err.message || "Erro ao carregar dados da agenda.");
      setIsLoading(false);
    }
  }, []);

  // Buscar perfil do médico e dados iniciais
  useEffect(() => {
    const loadInitialData = async () => {
      if (user?.id && user.role === 'DOCTOR') {
        setIsLoading(true);
        setError(null);
        try {
          const profileResponse = await fetch('/api/doctor/profile');
          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            if (profileData?.id) {
              setDoctorProfile(profileData);
              console.log("[SchedulePage] Doctor profile loaded:", profileData);
              await fetchData(profileData.id);
            } else {
              throw new Error("ID do médico não encontrado no perfil retornado pela API.");
            }
          } else if (profileResponse.status === 404) {
             throw new Error("Perfil de médico não encontrado. Por favor, complete seu cadastro primeiro.");
          } else {
            const errorText = await profileResponse.text();
            throw new Error(`Erro ${profileResponse.status} ao buscar perfil: ${errorText}`);
          }
        } catch (err: any) {
          console.error("[SchedulePage] Error loading initial data:", err);
          setError(err.message || "Erro ao carregar informações iniciais.");
          toast.error(err.message || "Erro ao carregar informações iniciais.");
          setIsLoading(false);
        }
      } else if (user && user.role !== 'DOCTOR') {
         setError("Acesso não autorizado. Esta página é apenas para médicos.");
         setIsLoading(false);
      } else if (!user) {
         console.log("[SchedulePage] Waiting for user authentication...");
      }
    };

    loadInitialData();
  }, [user, fetchData]);

  // Handler para salvar todos os horários
  const handleSaveSchedules = async (newScheduleItems: ScheduleInputItem[]) => {
    if (!doctorProfile?.id) {
      toast.error("ID do médico não encontrado.");
      return;
    }
    setIsLoading(true);
    console.log(`[SchedulePage] Saving ${newScheduleItems.length} schedule items for doctor ${doctorProfile.id}`);
    try {
      const validItems = newScheduleItems.filter(item =>
          item.weekDay >= 0 && item.weekDay <= 6 &&
          item.startTime && item.endTime && item.startTime < item.endTime
      );

      if (validItems.length !== newScheduleItems.length) {
          console.warn("[SchedulePage] Alguns horários inválidos foram filtrados antes do envio.");
          toast.warning("Alguns horários inválidos (ex: fim antes do início) foram ignorados.");
      }

      if (validItems.length === 0 && newScheduleItems.length > 0) {
          throw new Error("Nenhum horário válido para salvar.");
      }

      // Garantir que o payload seja um objeto válido
      const payload = {
        doctorId: doctorProfile.id,
        schedule: validItems
      };

      console.log("[SchedulePage] Payload being sent:", JSON.stringify(payload));

      const response = await fetch('/api/doctor/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      // Verificar se a resposta é válida antes de tentar parsear JSON
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || "Horários salvos com sucesso!");
        await fetchData(doctorProfile.id);
      } else {
        throw new Error(result.message || `Erro ao salvar horários.`);
      }
    } catch (err: any) {
      console.error("[SchedulePage] Error saving schedules:", err);
      toast.error(err.message || "Falha ao salvar horários.");
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handler para adicionar um bloqueio
  const handleAddBlock = async (blockData: Omit<ScheduleBlock, 'id' | 'doctorId'>) => {
     if (!doctorProfile?.id) {
      toast.error("ID do médico não encontrado.");
      return;
    }
    setIsLoading(true);
    console.log(`[SchedulePage] Adding block for doctor ${doctorProfile.id}`, blockData);
    try {
        if (!blockData.startTime || !blockData.endTime || blockData.startTime >= blockData.endTime) {
            throw new Error("Data/hora de início e fim são obrigatórias e o início deve ser anterior ao fim.");
        }
        if (!blockData.reason && !blockData.description) {
            blockData.reason = "Bloqueio";
        }

        const response = await fetch('/api/doctor/schedule/blocks', {
        method: 'POST',
            headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
                ...blockData,
                doctorId: doctorProfile.id,
                startTime: blockData.startTime.toISOString(),
                endTime: blockData.endTime.toISOString(),
        })
      });

        const newBlock = await response.json();

        if (response.ok && newBlock.id) {
            toast.success("Bloqueio adicionado com sucesso!");
            setBlocks(prev => [...prev, { ...newBlock, startTime: new Date(newBlock.startTime), endTime: new Date(newBlock.endTime) }]);
        } else {
            const errorMsg = newBlock.error || newBlock.message || `Erro ${response.status} ao adicionar bloqueio.`;
             if (response.status === 409) {
                 toast.error(`Conflito: ${errorMsg} Verifique se já existem consultas neste período.`);
      } else {
                 toast.error(errorMsg);
             }
            throw new Error(errorMsg);
        }
    } catch (err: any) {
        console.error("[SchedulePage] Error adding block:", err);
        if (!String(err.message).includes("Conflito")) {
             toast.error(err.message || "Falha ao adicionar bloqueio.");
        }
        setError(err.message);
    } finally {
        setIsLoading(false);
    }
  };

  // Handler para deletar um bloqueio
  const handleDeleteBlock = async (blockId: string) => {
     if (!doctorProfile?.id) {
      toast.error("ID do médico não encontrado.");
      return;
    }
    setIsLoading(true);
    console.log(`[SchedulePage] Deleting block ${blockId} for doctor ${doctorProfile.id}`);
    try {
        const response = await fetch(`/api/doctor/schedule/blocks?id=${blockId}`, {
            method: 'DELETE',
      });

      if (response.ok) {
            toast.success("Bloqueio removido com sucesso!");
            setBlocks(prev => prev.filter(b => b.id !== blockId));
      } else {
             let errorMsg = `Erro ${response.status} ao remover bloqueio.`;
             try {
                 const result = await response.json();
                 errorMsg = result.error || result.message || errorMsg;
             } catch {
                 // Ignorar erro de parsing se não houver corpo JSON
             }
            throw new Error(errorMsg);
        }
    } catch (err: any) {
        console.error("[SchedulePage] Error deleting block:", err);
        toast.error(err.message || "Falha ao remover bloqueio.");
        setError(err.message);
    } finally {
        setIsLoading(false);
    }
  };

  if (isLoading && !doctorProfile && !error) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Carregando agenda do médico...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error">
        <AlertTitle>Erro</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
        {doctorProfile && (
             <Button onClick={() => fetchData(doctorProfile.id)} className="mt-4">
                 Tentar Novamente
             </Button>
        )}
      </Alert>
    );
  }

  if (!doctorProfile) {
  return (
       <Alert variant="default">
         <AlertTitle>Acesso Negado ou Perfil Incompleto</AlertTitle>
         <AlertDescription>
             {user ? "Você precisa ser um médico registrado para acessar esta página." : "Por favor, faça login para continuar."}
         </AlertDescription>
       </Alert>
  );
}

return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">Gerenciar Agenda</h1>

      <Tabs defaultValue="list" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list">Horários Semanais</TabsTrigger>
          <TabsTrigger value="blocks">Bloqueios</TabsTrigger>
          <TabsTrigger value="calendar">Calendário</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
        <Card>
          <CardHeader>
              <CardTitle>Horários de Atendimento Semanais</CardTitle>
              <CardDescription>
                Defina seus horários recorrentes.
              </CardDescription>
          </CardHeader>
          <CardContent>
              {isLoading && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin" /></div>}
              {!isLoading && (
                <>
                  {/* <QuickScheduleSetup
                      onSave={handleSaveSchedules}
                      isLoading={isSaving}
                  /> */}
                  <hr className="my-6" />
                  <ScheduleListEditor
                    initialSchedules={schedules}
                    onSave={handleSaveSchedules}
                    isLoading={isSaving}
                  />
                </>
              )}
          </CardContent>
        </Card>
      </TabsContent>

        <TabsContent value="blocks">
        <Card>
          <CardHeader>
              <CardTitle>Bloqueios de Agenda</CardTitle>
              <CardDescription>
                Adicione períodos específicos em que você não estará disponível (férias, eventos, etc.).
              </CardDescription>
          </CardHeader>
          <CardContent>
              {isLoading && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin" /></div>}
              {!isLoading && (
                <ScheduleBlockManager
                  blocks={blocks}
                  onAddBlock={handleAddBlock}
                  onDeleteBlock={handleDeleteBlock}
                  isLoading={isLoading}
                />
              )}
          </CardContent>
        </Card>
      </TabsContent>

        <TabsContent value="calendar">
        <Card>
          <CardHeader>
              <CardTitle>Visualização em Calendário</CardTitle>
              <CardDescription>
                Veja sua agenda combinada (horários base, bloqueios e consultas).
              </CardDescription>
          </CardHeader>
            <CardContent>
              {isLoading && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin" /></div>}
              {!isLoading && (
                <ScheduleCalendar
                  doctorId={doctorProfile.id}
                  schedules={schedules}
                  blocks={blocks}
                  appointments={appointments}
                />
              )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
);
}
