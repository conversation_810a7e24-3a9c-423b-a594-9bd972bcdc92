'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import { Trash2, PlusCircle, Clock } from "lucide-react";
import { toast } from "sonner";

interface ScheduleInputItem {
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
  isBreak: boolean;
}

// Usar DoctorScheduleItem da página pai para initialSchedules
interface DoctorScheduleItem extends ScheduleInputItem {
    id: string;
    doctorId: string;
}


interface ScheduleListEditorProps {
  initialSchedules: DoctorScheduleItem[];
  onSave: (items: ScheduleInputItem[]) => Promise<void>;
  isLoading: boolean;
}

const weekDays = [
  { value: 0, label: "Domingo" }, { value: 1, label: "Segunda-feira" },
  { value: 2, label: "Terça-feira" }, { value: 3, label: "Quarta-feira" },
  { value: 4, label: "<PERSON>uinta-feira" }, { value: 5, label: "Sexta-feira" },
  { value: 6, label: "Sábado" }
];

// Helper para gerar um ID temporário para novos itens
const tempId = () => `temp_${Math.random().toString(36).substr(2, 9)}`;

export const ScheduleListEditor: React.FC<ScheduleListEditorProps> = ({ initialSchedules, onSave, isLoading }) => {
  // Estado local para gerenciar os horários sendo editados
  const [editedSchedules, setEditedSchedules] = useState<DoctorScheduleItem[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // Inicializa o estado local com os dados recebidos
    setEditedSchedules(initialSchedules);
    setHasChanges(false); // Reseta flag de mudanças ao receber novos dados iniciais
  }, [initialSchedules]);

  const handleScheduleChange = (id: string, field: keyof ScheduleInputItem, value: string | boolean | number) => {
    setEditedSchedules(prev =>
      prev.map(s => (s.id === id ? { ...s, [field]: value } : s))
    );
    setHasChanges(true);
  };

  const handleAddTimeSlot = (weekDay: number, isBreak: boolean = false) => {
    const newSlot: DoctorScheduleItem = {
      id: tempId(), // ID temporário
      doctorId: '', // Não necessário para salvar
      weekDay,
      startTime: isBreak ? "12:00" : "09:00",
      endTime: isBreak ? "13:00" : "17:00",
      isEnabled: true,
      isBreak,
    };
    setEditedSchedules(prev => [...prev, newSlot].sort((a, b) => a.weekDay - b.weekDay || a.startTime.localeCompare(b.startTime)));
    setHasChanges(true);
  };

  const handleRemoveTimeSlot = (id: string) => {
    setEditedSchedules(prev => prev.filter(s => s.id !== id));
    setHasChanges(true);
  };

  // Modificar apenas o método handleSaveChanges para melhor validação
  const handleSaveChanges = () => {
    // Validar antes de salvar
    const invalidSchedules = editedSchedules.filter(s => {
      return !s.startTime || !s.endTime || s.startTime >= s.endTime;
    });
    
    if (invalidSchedules.length > 0) {
      const dayNames = invalidSchedules.map(s => weekDays[s.weekDay].label);
      toast.error(`Horários inválidos encontrados para: ${dayNames.join(', ')}. Corrija ou remova.`);
      return;
    }

    // Mapear para o formato esperado pela API (sem id, doctorId)
    const itemsToSave: ScheduleInputItem[] = editedSchedules.map(({ id, doctorId, ...rest }) => rest);
    
    // Feedback visual antes de iniciar o salvamento
    const loadingToast = toast.loading("Salvando horários...");
    
    onSave(itemsToSave)
      .then(() => {
        toast.dismiss(loadingToast);
        setHasChanges(false); // Reseta flag após salvar com sucesso
        toast.success("Horários salvos com sucesso!");
      })
      .catch(error => {
        toast.dismiss(loadingToast);
        console.error("Erro ao salvar horários:", error);
        toast.error("Erro ao salvar horários. Tente novamente.");
      });
  };

  return (
    <div className="space-y-6">
      <h4 className="font-medium">Edição Manual de Horários</h4>
      {weekDays.map(day => (
        <div key={day.value} className="border rounded-md p-4 space-y-3">
          <h5 className="font-semibold">{day.label}</h5>
          {editedSchedules.filter(s => s.weekDay === day.value).length === 0 && (
            <p className="text-sm text-muted-foreground">Nenhum horário definido.</p>
          )}
          {editedSchedules
            .filter(s => s.weekDay === day.value)
            .map(schedule => (
              <div key={schedule.id} className={`flex flex-wrap items-center gap-2 p-2 rounded ${schedule.isBreak ? 'bg-muted/50' : ''}`}>
                {schedule.isBreak && <Clock className="w-4 h-4 text-muted-foreground mr-1" />}
                <Input
                  type="time"
                  value={schedule.startTime}
                  onChange={(e) => handleScheduleChange(schedule.id, 'startTime', e.target.value)}
                  className="w-24"
                  aria-label={`Início ${schedule.isBreak ? 'intervalo' : 'horário'} ${day.label}`}
                />
                <span>-</span>
                <Input
                  type="time"
                  value={schedule.endTime}
                  onChange={(e) => handleScheduleChange(schedule.id, 'endTime', e.target.value)}
                  className="w-24"
                  aria-label={`Fim ${schedule.isBreak ? 'intervalo' : 'horário'} ${day.label}`}
                />
                <div className="flex items-center space-x-2 ml-auto">
                   <Switch
                     checked={schedule.isEnabled}
                     onCheckedChange={(checked) => handleScheduleChange(schedule.id, 'isEnabled', checked)}
                     aria-label={`Ativar/Desativar ${schedule.isBreak ? 'intervalo' : 'horário'} ${day.label}`}
                   />
                   <Button
                     variant="ghost"
                     size="icon"
                     onClick={() => handleRemoveTimeSlot(schedule.id)}
                     aria-label={`Remover ${schedule.isBreak ? 'intervalo' : 'horário'} ${day.label}`}
                   >
                     <Trash2 className="w-4 h-4 text-destructive" />
                   </Button>
                </div>
              </div>
            ))}
          <div className="flex gap-2 mt-2">
            <Button variant="outline" size="sm" onClick={() => handleAddTimeSlot(day.value, false)}>
              <PlusCircle className="w-4 h-4 mr-1" /> Adicionar Horário
            </Button>
             <Button variant="outline" size="sm" onClick={() => handleAddTimeSlot(day.value, true)}>
              <Clock className="w-4 h-4 mr-1" /> Adicionar Intervalo
            </Button>
          </div>
        </div>
      ))}
      {hasChanges && (
        <div className="flex justify-end mt-6">
          <Button 
            onClick={handleSaveChanges} 
            disabled={isLoading}
            className="relative"
          >
            {isLoading ? (
              <>
                <span className="opacity-0">Salvar Alterações nos Horários</span>
                <span className="absolute inset-0 flex items-center justify-center">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
              </>
            ) : "Salvar Alterações nos Horários"}
          </Button>
        </div>
      )}
    </div>
  );
};
