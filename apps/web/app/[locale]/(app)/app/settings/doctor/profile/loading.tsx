'use client';

import { Skeleton } from "@ui/components/skeleton";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";

export default function DoctorProfileLoading() {
  return (
    <div>
      <h1 className="text-xl font-semibold mb-2">Perfil do médico</h1>
      <p className="mb-6 text-gray-600">Atualize suas informações profissionais e configurações de atendimento.</p>

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Informações básicas</CardTitle>
            <CardDescription>Seus dados profissionais principais.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>

            <Skeleton className="h-32 w-full" />

            <div>
              <Skeleton className="h-4 w-24 mb-3" />
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 border rounded-md p-4">
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configurações de consulta</CardTitle>
            <CardDescription>Defina valores e opções para consultas com pagamento.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-16 w-full" />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>

            <div>
              <Skeleton className="h-4 w-36 mb-3" />
              <div className="flex flex-wrap gap-4">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-5 w-24" />
              </div>
            </div>

            <div className="space-y-4">
              <Skeleton className="h-5 w-28" />
              <Skeleton className="h-4 w-full max-w-md" />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>

              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>

        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}
