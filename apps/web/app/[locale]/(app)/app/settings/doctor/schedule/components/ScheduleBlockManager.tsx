'use client';

import React, { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Calendar } from "@ui/components/calendar"; // Usar shadcn/ui Calendar
import { Popover, PopoverContent, PopoverTrigger } from "@ui/components/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Label } from "@ui/components/label";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar as CalendarIcon, Trash2 } from "lucide-react";
import { cn } from "@ui/lib"; // Importar cn se não estiver global
import { toast } from 'sonner';

// Reutilizar interface do componente pai
interface ScheduleBlock {
  id: string;
  doctorId: string;
  startTime: Date;
  endTime: Date;
  reason: string | null;
  type: "VACATION" | "HOLIDAY" | "LUNCH" | "PERSONAL" | "OTHER";
  description: string | null;
}

interface ScheduleBlockManagerProps {
  blocks: ScheduleBlock[];
  onAddBlock: (block: Omit<ScheduleBlock, 'id' | 'doctorId'>) => Promise<void>;
  onDeleteBlock: (id: string) => Promise<void>;
  isLoading: boolean;
}

const blockTypes = [
    { value: "PERSONAL", label: "Pessoal" },
    { value: "VACATION", label: "Férias" },
    { value: "HOLIDAY", label: "Feriado" },
    { value: "OTHER", label: "Outro" },
    // LUNCH geralmente é gerenciado nos horários semanais, mas pode ser adicionado aqui se necessário
];

export const ScheduleBlockManager: React.FC<ScheduleBlockManagerProps> = ({ blocks, onAddBlock, onDeleteBlock, isLoading }) => {
  const [newBlock, setNewBlock] = useState<Omit<ScheduleBlock, 'id' | 'doctorId'>>({
    startTime: new Date(),
    endTime: new Date(new Date().getTime() + 60 * 60 * 1000), // Default 1 hour
    reason: '',
    type: 'PERSONAL',
    description: ''
  });
  const [showForm, setShowForm] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewBlock(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: keyof typeof newBlock, value: string) => {
     setNewBlock(prev => ({ ...prev, [name]: value as any }));
  };

  const handleDateChange = (name: 'startTime' | 'endTime', date: Date | undefined) => {
    if (date) {
      // Preservar a hora existente se apenas a data for alterada pelo Calendar
      const currentTime = newBlock[name] || new Date();
      date.setHours(currentTime.getHours(), currentTime.getMinutes(), 0, 0);
      setNewBlock(prev => ({ ...prev, [name]: date }));
    }
  };

   const handleTimeChange = (name: 'startTime' | 'endTime', timeValue: string) => {
        const [hours, minutes] = timeValue.split(':').map(Number);
        const currentDate = newBlock[name] || new Date();
        const newDate = new Date(currentDate);
        newDate.setHours(hours, minutes, 0, 0);
        setNewBlock(prev => ({ ...prev, [name]: newDate }));
    };

  // Modificar apenas o método handleAddClick para melhor tratamento de erros
  const handleAddClick = () => {
  // Validação antes de enviar ao servidor
  if (!newBlock.startTime || !newBlock.endTime) {
    toast.error("Preencha as datas e horários de início e fim.");
    return;
  }

  if (newBlock.startTime >= newBlock.endTime) {
    toast.error("O horário de início deve ser anterior ao horário de fim.");
    return;
  }

  if (!newBlock.type) {
    toast.error("Selecione um tipo de bloqueio.");
    return;
  }

  onAddBlock(newBlock).then(() => {
    // Reset form after successful add
    setNewBlock({
      startTime: new Date(),
      endTime: new Date(new Date().getTime() + 60 * 60 * 1000),
      reason: '', type: 'PERSONAL', description: ''
    });
    setShowForm(false);
    toast.success("Bloqueio adicionado com sucesso!");
  }).catch((error) => {
    console.error("Erro ao adicionar bloqueio:", error);
    // Não reseta o form se der erro para o usuário corrigir
  });
};

  return (
    <div className="space-y-6">
      {!showForm && (
        <Button onClick={() => setShowForm(true)}>Adicionar Novo Bloqueio</Button>
      )}

      {showForm && (
        <div className="border rounded-md p-4 space-y-4">
          <h4 className="font-medium">Novo Bloqueio</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
             {/* Start DateTime */}
             <div>
                <Label>Início</Label>
                <div className="flex gap-2">
                    <Popover>
                        <PopoverTrigger asChild>
                        <Button
                            variant={"outline"}
                            className={cn(
                            "w-[150px] justify-start text-left font-normal",
                            !newBlock.startTime && "text-muted-foreground"
                            )}
                        >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {newBlock.startTime ? format(newBlock.startTime, "dd/MM/yy") : <span>Data</span>}
                        </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                        <Calendar
                            mode="single"
                            selected={newBlock.startTime}
                            onSelect={(date) => handleDateChange('startTime', date)}
                            initialFocus
                            locale={ptBR}
                        />
                        </PopoverContent>
                    </Popover>
                    <Input
                        type="time"
                        value={format(newBlock.startTime || new Date(), "HH:mm")}
                        onChange={(e) => handleTimeChange('startTime', e.target.value)}
                        className="w-[100px]"
                    />
                </div>
             </div>
             {/* End DateTime */}
             <div>
                <Label>Fim</Label>
                 <div className="flex gap-2">
                    <Popover>
                        <PopoverTrigger asChild>
                        <Button
                            variant={"outline"}
                            className={cn(
                            "w-[150px] justify-start text-left font-normal",
                            !newBlock.endTime && "text-muted-foreground"
                            )}
                        >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {newBlock.endTime ? format(newBlock.endTime, "dd/MM/yy") : <span>Data</span>}
                        </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                        <Calendar
                            mode="single"
                            selected={newBlock.endTime}
                            onSelect={(date) => handleDateChange('endTime', date)}
                            initialFocus
                            locale={ptBR}
                        />
                        </PopoverContent>
                    </Popover>
                    <Input
                        type="time"
                        value={format(newBlock.endTime || new Date(), "HH:mm")}
                        onChange={(e) => handleTimeChange('endTime', e.target.value)}
                        className="w-[100px]"
                    />
                </div>
             </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="reason">Motivo (Curto)</Label>
              <Input id="reason" name="reason" value={newBlock.reason || ''} onChange={handleInputChange} placeholder="Ex: Férias, Congresso" />
            </div>
            <div>
              <Label htmlFor="type">Tipo</Label>
              <Select name="type" value={newBlock.type} onValueChange={(value) => handleSelectChange('type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  {blockTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Descrição (Opcional)</Label>
            <Textarea id="description" name="description" value={newBlock.description || ''} onChange={handleInputChange} placeholder="Detalhes adicionais sobre o bloqueio..." />
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="ghost" onClick={() => setShowForm(false)} disabled={isLoading}>Cancelar</Button>
            <Button onClick={handleAddClick} disabled={isLoading}>
              {isLoading ? "Adicionando..." : "Adicionar Bloqueio"}
            </Button>
          </div>
        </div>
      )}

      <div>
        <h4 className="font-medium mb-2">Bloqueios Existentes</h4>
        {blocks.length === 0 ? (
          <p className="text-sm text-muted-foreground">Nenhum bloqueio cadastrado.</p>
        ) : (
          <div className="space-y-2">
            {blocks.sort((a,b) => a.startTime.getTime() - b.startTime.getTime()).map(block => (
              <div key={block.id} className="flex items-center justify-between border rounded p-2">
                <div>
                  <span className="font-medium">{block.reason || block.type}</span>
                  <p className="text-sm text-muted-foreground">
                    {format(block.startTime, "dd/MM/yy HH:mm", { locale: ptBR })} - {format(block.endTime, "dd/MM/yy HH:mm", { locale: ptBR })}
                  </p>
                  {block.description && <p className="text-xs text-muted-foreground mt-1">{block.description}</p>}
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onDeleteBlock(block.id)}
                  disabled={isLoading}
                  aria-label="Remover bloqueio"
                >
                  <Trash2 className="w-4 h-4 text-destructive" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
