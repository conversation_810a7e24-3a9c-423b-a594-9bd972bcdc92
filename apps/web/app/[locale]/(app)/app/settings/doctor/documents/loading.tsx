'use client';

import { Skeleton } from "@ui/components/skeleton";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";

export default function DoctorDocumentsLoading() {
  return (
    <div>
      <h1 className="text-xl font-semibold mb-2">Documentos</h1>
      <p className="mb-6 text-gray-600">Gerencie seus documentos profissionais e certificações.</p>

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Enviar novo documento</CardTitle>
            <CardDescription>Envie seus documentos para verificação pela equipe.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>

              <div className="space-y-2">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>

            <div className="mt-4">
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Documentos enviados</CardTitle>
            <CardDescription>Lista de documentos enviados e seu status de aprovação.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between border-b pb-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-5 w-20" />
                <Skeleton className="h-5 w-16" />
              </div>

              {[1, 2, 3].map((i) => (
                <div key={i} className="flex justify-between py-2">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-6 w-20" />
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-12" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Requisitos de documentação</CardTitle>
            <CardDescription>Documentos necessários para aprovação na plataforma.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="flex">
                    <Skeleton className="h-5 w-5 mr-2" />
                    <Skeleton className="h-5 w-full" />
                  </div>
                ))}
              </div>

              <Skeleton className="h-1 w-full" />

              <div className="space-y-2">
                <Skeleton className="h-5 w-40" />
                <div className="pl-5 space-y-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex">
                      <Skeleton className="h-5 w-5 mr-2" />
                      <Skeleton className="h-5 w-full" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
