'use client';

import React, { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Checkbox } from "@ui/components/checkbox";
import { Label } from "@ui/components/label";
import { toast } from "sonner";

// Reutilizar a interface do componente pai
interface ScheduleInputItem {
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
  isBreak: boolean;
}

interface QuickScheduleSetupProps {
  onSave: (items: ScheduleInputItem[]) => Promise<void>;
  isLoading: boolean;
  // currentSchedules?: any[]; // Opcional: para pré-popular
}

const weekDaysMap = [
  { value: 1, label: "Seg" }, { value: 2, label: "Ter" }, { value: 3, label: "Qua" },
  { value: 4, label: "Qui" }, { value: 5, label: "Sex" }, { value: 6, label: "Sáb" },
  { value: 0, label: "Dom" }
];

export const QuickScheduleSetup: React.FC<QuickScheduleSetupProps> = ({ onSave, isLoading }) => {
  const [showForm, setShowForm] = useState(false);
  const [config, setConfig] = useState({
    startTime: "08:00",
    endTime: "18:00",
    lunchBreak: true,
    lunchStartTime: "12:00",
    lunchEndTime: "14:00",
    workDays: [1, 2, 3, 4, 5] // Segunda a sexta
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setConfig(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleDayChange = (dayValue: number, checked: boolean) => {
    setConfig(prev => {
      const currentDays = prev.workDays;
      if (checked) {
        return { ...prev, workDays: [...currentDays, dayValue].sort() };
      } else {
        return { ...prev, workDays: currentDays.filter(d => d !== dayValue) };
      }
    });
  };

  const handleSubmit = () => {
    const scheduleItems: ScheduleInputItem[] = [];
    const { workDays, startTime, endTime, lunchBreak, lunchStartTime, lunchEndTime } = config;

    if (!startTime || !endTime || workDays.length === 0) {
        toast.error("Preencha o horário de início, fim e selecione os dias de trabalho.");
        return;
    }

    // Validação adicional para evitar erros no backend
    if (lunchBreak && (!lunchStartTime || !lunchEndTime)) {
        toast.error("Preencha os horários de início e fim do almoço.");
        return;
    }

    for (const weekDay of workDays) {
      if (lunchBreak && lunchStartTime && lunchEndTime) {
        // Validação para garantir que os horários estão em ordem
        if (startTime < lunchStartTime) {
          scheduleItems.push({ weekDay, startTime, endTime: lunchStartTime, isEnabled: true, isBreak: false });
        }
        if (lunchStartTime < lunchEndTime) {
          scheduleItems.push({ weekDay, startTime: lunchStartTime, endTime: lunchEndTime, isEnabled: true, isBreak: true });
        }
        if (lunchEndTime < endTime) {
          scheduleItems.push({ weekDay, startTime: lunchEndTime, endTime, isEnabled: true, isBreak: false });
        }
      } else {
        if (startTime < endTime) {
          scheduleItems.push({ weekDay, startTime, endTime, isEnabled: true, isBreak: false });
        }
      }
    }

    if (scheduleItems.length === 0) {
        toast.warning("Nenhum horário válido gerado com base na configuração.");
        return;
    }

    console.log("[QuickSetup] Gerando horários:", scheduleItems);
    
    // Mostrar toast de carregamento
    const loadingToast = toast.loading("Aplicando configuração...");
    
    // Adicionando tratamento de erro
    onSave(scheduleItems)
      .then(() => {
        toast.dismiss(loadingToast);
        setShowForm(false); // Fechar o formulário após salvar
        toast.success("Configuração aplicada com sucesso!");
      })
      .catch(error => {
        toast.dismiss(loadingToast);
        console.error("[QuickSetup] Erro ao salvar:", error);
        toast.error("Erro ao aplicar configuração. Tente novamente.");
      });
  };

  if (!showForm) {
    return (
      <Button variant="outline" onClick={() => setShowForm(true)}>
        Usar Configuração Rápida
      </Button>
    );
  }

  return (
    <div className="border rounded-md p-4 space-y-4 bg-secondary/5">
      <h4 className="font-medium">Configuração Rápida</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="startTime">Horário de início</Label>
          <Input type="time" id="startTime" name="startTime" value={config.startTime} onChange={handleInputChange} />
        </div>
        <div>
          <Label htmlFor="endTime">Horário de término</Label>
          <Input type="time" id="endTime" name="endTime" value={config.endTime} onChange={handleInputChange} />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox id="lunchBreak" name="lunchBreak" checked={config.lunchBreak} onCheckedChange={(checked) => setConfig(prev => ({ ...prev, lunchBreak: checked === true }))} />
        <Label htmlFor="lunchBreak">Incluir intervalo para almoço</Label>
      </div>

      {config.lunchBreak && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
          <div>
            <Label htmlFor="lunchStartTime">Início do almoço</Label>
            <Input type="time" id="lunchStartTime" name="lunchStartTime" value={config.lunchStartTime} onChange={handleInputChange} />
          </div>
          <div>
            <Label htmlFor="lunchEndTime">Fim do almoço</Label>
            <Input type="time" id="lunchEndTime" name="lunchEndTime" value={config.lunchEndTime} onChange={handleInputChange} />
          </div>
        </div>
      )}

      <div>
        <Label>Dias de atendimento</Label>
        <div className="flex flex-wrap gap-4 mt-2">
          {weekDaysMap.map(day => (
            <div key={day.value} className="flex items-center space-x-2">
              <Checkbox
                id={`day-${day.value}`}
                checked={config.workDays.includes(day.value)}
                onCheckedChange={(checked) => handleDayChange(day.value, checked === true)}
              />
              <Label htmlFor={`day-${day.value}`}>{day.label}</Label>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="ghost" onClick={() => setShowForm(false)} disabled={isLoading}>
          Cancelar
        </Button>
        <Button 
          onClick={handleSubmit} 
          disabled={isLoading}
          className="relative"
        >
          {isLoading ? (
            <>
              <span className="opacity-0">Aplicar e Salvar</span>
              <span className="absolute inset-0 flex items-center justify-center">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
            </>
          ) : "Aplicar e Salvar"}
        </Button>
      </div>
      <p className="text-xs text-muted-foreground">
        Atenção: Aplicar esta configuração substituirá todos os horários semanais existentes.
      </p>
    </div>
  );
};
