'use client';

import { Skeleton } from "@ui/components/skeleton";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";

export default function DoctorScheduleLoading() {
  return (
    <div>
      <h1 className="text-xl font-semibold mb-2">Agenda</h1>
      <p className="mb-6 text-gray-600">Configure seus horários de atendimento e períodos indisponíveis.</p>

      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Horários de atendimento</CardTitle>
            <CardDescription>Defina seus horários disponíveis para atendimentos.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              {[1, 2, 3, 4, 5].map((day) => (
                <div key={day} className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-5 w-28" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-24" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Intervalos</CardTitle>
            <CardDescription>Configure horários de almoço e outros intervalos.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              {[1, 2].map((day) => (
                <div key={day} className="flex items-center justify-between p-4 border rounded-md">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-5 w-28" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-24" />
                  </div>
                </div>
              ))}
            </div>

            <Skeleton className="h-10 w-full max-w-xs" />
          </CardContent>
        </Card>

        {/* <Card>
          <CardHeader>
            <CardTitle>Bloqueios de agenda</CardTitle>
            <CardDescription>Gerencie períodos de indisponibilidade como férias, feriados ou outros compromissos.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex justify-between mb-4">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-36" />
            </div>

            <div className="space-y-4">
              {[1, 2, 3].map((block) => (
                <div key={block} className="flex justify-between items-center p-4 border rounded-md">
                  <div className="space-y-1">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <Skeleton className="h-8 w-24" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card> */}
      </div>
    </div>
  );
}
