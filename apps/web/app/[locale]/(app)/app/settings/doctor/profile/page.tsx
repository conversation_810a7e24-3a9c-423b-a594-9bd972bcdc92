'use client';

import { zodResolver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { useToast } from "@ui/hooks/use-toast";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState, useEffect } from "react";
import DoctorProfileLoading from "./loading";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import { isDirectPaymentEnabled } from "utils";


// Schema para validação do perfil do médico
const doctorProfileSchema = z.object({
  crm: z.string().min(1, "CRM é obrigatório"),
  crmState: z.string().length(2, "Estado do CRM inválido"),
  biography: z.string().optional(),
  specialties: z.array(z.string()).min(1, "Selecione pelo menos uma especialidade"),
  consultationPrice: z.string().min(1, "Preço da consulta é obrigatório"),
  consultationDuration: z.string().min(1, "Duração da consulta é obrigatória"),
  bankAccount: z.object({
    bank: z.string().optional(),
    agency: z.string().optional(),
    account: z.string().optional(),
    accountType: z.enum(["CHECKING", "SAVINGS"]).optional(),
    document: z.string().optional()
  }).optional(),
}).refine(
  (data) => {
    // Se enablePaymentConfig estiver ativado, consultationPrice e consultationDuration são obrigatórios
    if (data.enablePaymentConfig) {
      return !!data.consultationPrice && !!data.consultationDuration && (data.consultTypes?.length ?? 0) > 0;
    }
    return true;
  },
  {
    message: "Configurações de consulta são obrigatórias quando pagamento está ativado",
    path: ["consultationPrice"],
  }
);

type DoctorProfileValues = z.infer<typeof doctorProfileSchema>;

export default function DoctorProfilePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [initialData, setInitialData] = useState<DoctorProfileValues | null>(null);
  const [specialtiesList, setSpecialtiesList] = useState<{id: string, name: string}[]>([]);
  const { toast } = useToast();

  // Verificar modo de pagamento do sistema
  const directPaymentEnabled = isDirectPaymentEnabled();

  const form = useForm<DoctorProfileValues>({
    resolver: zodResolver(doctorProfileSchema),
    defaultValues: {
      crm: "",
      crmState: "",
      biography: "",
      specialties: [],
      consultationPrice: "",
      consultationDuration: "30",
      bankAccount: {
        bank: "",
        agency: "",
        account: "",
        accountType: "CHECKING",
        document: ""
      }
    },
  });


  // Queries
  const { data: doctorData, isLoading: isDoctorLoading } = apiClient.doctors.getProfile.useQuery(undefined, {
    enabled: !isLoading
  });
  const { data: specialties, isLoading: isSpecialtiesLoading } = apiClient.doctors.getSpecialties.useQuery(undefined, {
    enabled: !isLoading
  });

  // Mutations
  const updateDoctorMutation = apiClient.doctors.updateProfile.useMutation();

  // Carregar dados do médico
  useEffect(() => {
    if (doctorData && !isDoctorLoading && !isLoading) {
      console.log("Dados do médico:", doctorData);
      // Verificar se há configurações de pagamento
      const hasPaymentConfig = !!doctorData.consultationPrice && Number(doctorData.consultationPrice) > 0;

      // Formatar os dados para o formulário
      const formattedData: DoctorProfileValues = {
        crm: doctorData.crm || "",
        crmState: doctorData.crmState || "",
        biography: doctorData.biography || "",
        specialties: doctorData.specialties?.map((spec: any) => spec.id) || [],
        consultationPrice: doctorData.consultationPrice?.toString() || "",
        consultationDuration: doctorData.consultationDuration?.toString() || "30",
        bankAccount: doctorData.bankAccount ? {
          bank: typeof doctorData.bankAccount === 'object' ? doctorData.bankAccount.bank || "" : "",
          agency: typeof doctorData.bankAccount === 'object' ? doctorData.bankAccount.agency || "" : "",
          account: typeof doctorData.bankAccount === 'object' ? doctorData.bankAccount.account || "" : "",
          accountType: typeof doctorData.bankAccount === 'object' ?
            (typeof doctorData.bankAccount === 'object' && 'accountType' in doctorData.bankAccount ? doctorData.bankAccount.accountType as "CHECKING" | "SAVINGS" : "CHECKING") : "CHECKING",
          document: typeof doctorData.bankAccount === 'object' ? doctorData.bankAccount.document || "" : ""
        } : undefined
      };

      console.log("Dados formatados:", formattedData);
      setInitialData(formattedData);
      form.reset(formattedData);
    }
  }, [doctorData, isDoctorLoading, form, directPaymentEnabled, isLoading]);

  // Carregar lista de especialidades
  useEffect(() => {
    if (specialties && !isSpecialtiesLoading && !isLoading) {
      const processedSpecialties = specialties;
      const filteredSpecialties = processedSpecialties.filter(s => s && s.id && s.name);
      setSpecialtiesList(filteredSpecialties);
    }
  }, [specialties, isSpecialtiesLoading, isLoading]);

  async function onSubmit(data: DoctorProfileValues) {
    setIsLoading(true);

    try {
      // Preparar os dados para atualização
      let updateData: any = {
        id: doctorData?.id,
        crm: data.crm,
        crmState: data.crmState,
        biography: data.biography,
        specialtyIds: data.specialties,
        consultationPrice: parseFloat(data.consultationPrice || ""),
        consultationDuration: parseInt(data.consultationDuration || "30"),
        returnPeriod: 0, // Valor padrão para período de retorno
        consultTypes: ["VIDEO", "AUDIO", "CHAT"], // Incluir todos os tipos de consulta por padrão
      };

      // Verificar se todos os campos bancários obrigatórios estão preenchidos
      const hasBankInfo = data.bankAccount &&
                          data.bankAccount.bank &&
                          data.bankAccount.bank.trim() !== "" &&
                          data.bankAccount.agency &&
                          data.bankAccount.agency.trim() !== "" &&
                          data.bankAccount.account &&
                          data.bankAccount.account.trim() !== "" &&
                          data.bankAccount.document &&
                          data.bankAccount.document.trim().length >= 11;

      if (hasBankInfo) {
        // Se todos os campos bancários obrigatórios estão preenchidos, incluir no update
        updateData.bankAccount = {
          bank: data.bankAccount?.bank || "",
          agency: data.bankAccount?.agency || "",
          account: data.bankAccount?.account || "",
          accountType: data.bankAccount?.accountType || "CHECKING",
          document: data.bankAccount?.document || ""
        };
      } else {
        // Se não há dados bancários completos, não enviar o campo bankAccount
        // Isso evita a validação do backend para campos obrigatórios
        updateData.bankAccount = null;
      }

      await updateDoctorMutation.mutateAsync(updateData);

      toast({
        title: "Perfil atualizado",
        description: "Seus dados foram atualizados com sucesso."
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro na atualização",
        description: "Ocorreu um erro ao atualizar seus dados. Tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  if (isDoctorLoading || isSpecialtiesLoading) {
    return <DoctorProfileLoading />;
  }

  return (
    <div>
      <h1 className="text-xl font-semibold mb-2">Perfil do médico</h1>
      <p className="mb-6 text-gray-600">Atualize suas informações profissionais e configurações de atendimento.</p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Informações básicas</CardTitle>
              <CardDescription>Seus dados profissionais principais.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="crm"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CRM</FormLabel>
                      <FormControl>
                        <Input placeholder="12345" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="crmState"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado do CRM</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="AC">AC</SelectItem>
                          <SelectItem value="AL">AL</SelectItem>
                          <SelectItem value="AP">AP</SelectItem>
                          <SelectItem value="AM">AM</SelectItem>
                          <SelectItem value="BA">BA</SelectItem>
                          <SelectItem value="CE">CE</SelectItem>
                          <SelectItem value="DF">DF</SelectItem>
                          <SelectItem value="ES">ES</SelectItem>
                          <SelectItem value="GO">GO</SelectItem>
                          <SelectItem value="MA">MA</SelectItem>
                          <SelectItem value="MT">MT</SelectItem>
                          <SelectItem value="MS">MS</SelectItem>
                          <SelectItem value="MG">MG</SelectItem>
                          <SelectItem value="PA">PA</SelectItem>
                          <SelectItem value="PB">PB</SelectItem>
                          <SelectItem value="PR">PR</SelectItem>
                          <SelectItem value="PE">PE</SelectItem>
                          <SelectItem value="PI">PI</SelectItem>
                          <SelectItem value="RJ">RJ</SelectItem>
                          <SelectItem value="RN">RN</SelectItem>
                          <SelectItem value="RS">RS</SelectItem>
                          <SelectItem value="RO">RO</SelectItem>
                          <SelectItem value="RR">RR</SelectItem>
                          <SelectItem value="SC">SC</SelectItem>
                          <SelectItem value="SP">SP</SelectItem>
                          <SelectItem value="SE">SE</SelectItem>
                          <SelectItem value="TO">TO</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="biography"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Biografia</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Descreva sua formação, experiência e áreas de atuação"
                        className="h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="specialties"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Especialidades</FormLabel>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 border rounded-md p-4">
                      {isSpecialtiesLoading ? (
                        <p className="text-sm text-muted-foreground col-span-full text-center py-2">
                          Carregando especialidades...
                        </p>
                      ) : specialtiesList.length === 0 ? (
                        <div className="col-span-full text-center py-2">
                          <p className="text-sm text-muted-foreground">
                            Nenhuma especialidade encontrada
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Entre em contato com o administrador para adicionar especialidades.
                          </p>
                        </div>
                      ) : (
                        specialtiesList.map((specialty) => (
                          <div key={specialty.id} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={specialty.id}
                              checked={field.value?.includes(specialty.id)}
                              onChange={(e) => {
                                const checked = e.target.checked;
                                const currentValues = [...(field.value || [])];

                                if (checked) {
                                  field.onChange([...currentValues, specialty.id]);
                                } else {
                                  field.onChange(currentValues.filter(id => id !== specialty.id));
                                }
                              }}
                              className="h-4 w-4 rounded border-gray-300"
                            />
                            <label htmlFor={specialty.id} className="text-sm">
                              {specialty.name}
                            </label>
                          </div>
                        ))
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Configurações de consulta</CardTitle>
              <CardDescription>Defina valores e opções para suas consultas.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="consultationPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preço da consulta (R$)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="0,00"
                          inputMode="decimal"
                          {...field}
                          onChange={(e) => {
                            // Permite apenas números, vírgula e ponto
                            const value = e.target.value.replace(/[^\d.,]/g, '');
                            // Atualiza o valor no formulário
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultationDuration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duração (minutos)</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="15">15 minutos</SelectItem>
                          <SelectItem value="30">30 minutos</SelectItem>
                          <SelectItem value="45">45 minutos</SelectItem>
                          <SelectItem value="60">60 minutos</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="pt-2">
                <h3 className="text-sm font-medium mb-2">Dados bancários (opcional)</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Estes dados serão utilizados para transferência dos seus honorários.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bankAccount.bank"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Banco</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Banco do Brasil" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bankAccount.agency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Agência</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: 1234" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="bankAccount.account"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Conta</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: 12345-6" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bankAccount.accountType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tipo de conta</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="CHECKING">Conta Corrente</SelectItem>
                            <SelectItem value="SAVINGS">Poupança</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="bankAccount.document"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CPF/CNPJ do titular</FormLabel>
                        <FormControl>
                          <Input placeholder="Apenas números" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Salvando..." : "Salvar Alterações"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
