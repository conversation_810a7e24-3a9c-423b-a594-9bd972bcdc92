import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "@i18n/routing";
import { getLocale } from "next-intl/server";
import { DoctorPrescriptionsClient } from "./components/doctor-prescriptions-client";

export const dynamic = "force-dynamic";

export default async function DoctorPrescriptionsPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Verificar se é médico ou admin
	if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
		return redirect({ href: "/app/dashboard", locale });
	}

	return <DoctorPrescriptionsClient user={user} />;
}
