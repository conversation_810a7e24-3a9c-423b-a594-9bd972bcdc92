import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserContextProvider } from "@saas/auth/lib/user-context";

import { getLocale } from "next-intl/server";
import type { PropsWithChildren } from "react";
import LayoutClient from "./layout-client";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function Layout({ children }: PropsWithChildren) {
	const locale = await getLocale();
	const { user, teamMembership } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// NOVO: Redirecionar pacientes para o fluxo específico de pacientes
	// Garantir que pacientes não acessem o /app/ diretamente
	if (user.role === "PATIENT" || user.role === "USER") {
		return redirect({ href: "/patient/dashboard", locale });
	}

	// if (!user.onboardingComplete || !user.teamMemberships?.length) {
	// 	return redirect({ href: "/onboarding", locale });
	// }

	// if (!teamMembership) {
	// 	return redirect({ href: "/", locale });
	// }

	return (
		<UserContextProvider initialUser={user} teamMembership={teamMembership}>
			<LayoutClient user={user}>{children}</LayoutClient>
		</UserContextProvider>
	);
}
