import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'ZapChat - ZapVida',
  description: 'Chat em tempo real para consultas médicas',
};

export default function ZapChatHead() {
  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Forçar o carregamento das variáveis de ambiente
            window.__ENV__ = {
              NEXT_PUBLIC_SUPABASE_URL: "${process.env.NEXT_PUBLIC_SUPABASE_URL || ''}",
              NEXT_PUBLIC_SUPABASE_ANON_KEY: "${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}",
              NODE_ENV: "${process.env.NODE_ENV || 'development'}"
            };

            console.log('🔧 Variáveis de ambiente carregadas no head:', window.__ENV__);

            // Verificar se as variáveis estão disponíveis
            if (!window.__ENV__.NEXT_PUBLIC_SUPABASE_URL || !window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
              console.error('❌ Variáveis de ambiente do Supabase não encontradas no head');
              console.error('NEXT_PUBLIC_SUPABASE_URL:', window.__ENV__.NEXT_PUBLIC_SUPABASE_URL);
              console.error('NEXT_PUBLIC_SUPABASE_ANON_KEY:', window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurada' : 'Não configurada');
            } else {
              console.log('✅ Variáveis de ambiente do Supabase carregadas com sucesso no head');
            }
          `,
        }}
      />
    </>
  );
}
