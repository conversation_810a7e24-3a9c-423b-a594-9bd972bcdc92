"use client";

import { useEffect, useState } from "react";
import { useUser } from "@saas/auth/hooks/use-user";
import { NewZapChatClient } from "../../../../../components/zapchat/new-zapchat-client";
import { EnvDebugPanel } from "../../../../../components/zapchat/env-debug-panel";
import { useEnvVars } from "../../../../../lib/hooks/use-env-vars";
import { RefreshCw, AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import ZapChatBody from "./body";

function ZapChatContent() {
  const { user, loaded } = useUser();
  const router = useRouter();
  const { envVars, isLoaded, isValid } = useEnvVars();

  useEffect(() => {
    if (loaded && !user) {
      router.push("/auth/login");
    }
  }, [loaded, user, router]);

  if (!loaded) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Se houver erro nas variáveis de ambiente, mostrar painel de debug
  if (isLoaded && !isValid) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Erro de Configuração do Supabase
            </h1>
                        <p className="text-gray-600">
              As variáveis de ambiente necessárias não foram encontradas.
              Verifique a configuração abaixo.
            </p>
            <div className="mt-4 text-sm text-gray-500">
              <p><strong>Status:</strong> {isLoaded ? 'Verificado' : 'Verificando...'}</p>
              <p><strong>URL:</strong> {envVars.NEXT_PUBLIC_SUPABASE_URL ? '✓ Configurada' : '✗ Não configurada'}</p>
              <p><strong>Chave:</strong> {envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Configurada' : '✗ Não configurada'}</p>
            </div>
          </div>

          <EnvDebugPanel />

          <div className="mt-8 text-center text-sm text-gray-500">
            <p>Se o problema persistir, verifique se o arquivo .env está na raiz do projeto.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ZapChatBody />
      <NewZapChatClient user={user} enableVideo={true} />
    </>
  );
}

export default function ZapChatPage() {
  return <ZapChatContent />;
}
