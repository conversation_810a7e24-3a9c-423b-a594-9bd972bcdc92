import { ReactNode } from 'react';

interface ZapChatLayoutProps {
  children: ReactNode;
}

export default function ZapChatLayout({ children }: ZapChatLayoutProps) {
  return (
    <>
      {/* Script para forçar o carregamento das variáveis de ambiente */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.__ENV__ = {
              NEXT_PUBLIC_SUPABASE_URL: "${process.env.NEXT_PUBLIC_SUPABASE_URL || ''}",
              NEXT_PUBLIC_SUPABASE_ANON_KEY: "${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}",
              NODE_ENV: "${process.env.NODE_ENV || 'development'}"
            };

            console.log('🔧 Variáveis de ambiente carregadas no cliente:', window.__ENV__);
          `,
        }}
      />
      {children}
    </>
  );
}
