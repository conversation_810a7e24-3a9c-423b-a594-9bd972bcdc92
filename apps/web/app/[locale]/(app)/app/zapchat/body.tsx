export default function ZapChatBody() {
  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Forçar o carregamento das variáveis de ambiente no body
            if (typeof window !== 'undefined') {
              // Tentar obter do process.env primeiro
              let supabaseUrl = "${process.env.NEXT_PUBLIC_SUPABASE_URL || ''}";
              let supabaseKey = "${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''}";
              let nodeEnv = "${process.env.NODE_ENV || 'development'}";

              // Se não estiverem disponíveis, usar valores hardcoded para desenvolvimento
              if (!supabaseUrl && nodeEnv === 'development') {
                console.warn('⚠️ NEXT_PUBLIC_SUPABASE_URL não encontrada, usando valor padrão para desenvolvimento');
                supabaseUrl = 'https://moupvfqlulvqbzwajkif.supabase.co';
              }
              if (!supabaseKey && nodeEnv === 'development') {
                console.warn('⚠️ NEXT_PUBLIC_SUPABASE_ANON_KEY não encontrada, usando valor padrão para desenvolvimento');
                supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vdXB2ZnFsdWx2cWJ6d2Fqa2lmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEyMTk2NDYsImV4cCI6MjA1Njc5NTY0Nn0.MFeB-phlRJBVc_a2ZeS-yUP6LOLc9C0L4jF6BIqv0i0';
              }

              // Definir as variáveis no window
              window.__ENV__ = {
                NEXT_PUBLIC_SUPABASE_URL: supabaseUrl,
                NEXT_PUBLIC_SUPABASE_ANON_KEY: supabaseKey,
                NODE_ENV: nodeEnv
              };

              console.log('🔧 Variáveis de ambiente carregadas no body:', window.__ENV__);

              // Verificar se as variáveis estão disponíveis
              if (!window.__ENV__.NEXT_PUBLIC_SUPABASE_URL || !window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
                console.error('❌ Variáveis de ambiente do Supabase não encontradas no body');
                console.error('NEXT_PUBLIC_SUPABASE_URL:', window.__ENV__.NEXT_PUBLIC_SUPABASE_URL);
                console.error('NEXT_PUBLIC_SUPABASE_ANON_KEY:', window.__ENV__.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurada' : 'Não configurada');
              } else {
                console.log('✅ Variáveis de ambiente do Supabase carregadas com sucesso no body');
              }
            }
          `,
        }}
      />
    </>
  );
}
