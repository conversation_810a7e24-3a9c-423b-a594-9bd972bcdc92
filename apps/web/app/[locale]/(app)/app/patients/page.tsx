// apps/web/app/[locale]/(app)/app/patients/page.tsx
import { Suspense } from "react";
import { db } from "database";
import { PaginationButton } from "@ui/components/pagination-button";
import { DashboardHeader } from "@ui/components/header";
import { AddPatientForm } from "./components/add-patient-form";
import Loading from "./loading";
import { PatientsClient } from "./components/patients-client";

// Definindo a página como dinâmica para evitar cache
export const dynamic = "force-dynamic";

interface PageProps {
  searchParams?: {
    page?: string;
    search?: string;
  };
}

export default async function PatientsManagementPage({
  searchParams,
}: PageProps = {}) {
  try {
    const resolvedSearchParams = await searchParams;
    // Parâmetros de paginação seguros
    const page = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
    const pageSize = 9;
    const skip = Math.max(0, ((!isNaN(page) ? page : 1) - 1) * pageSize);
    const search = resolvedSearchParams?.search || "";

    // Construir condições de busca
    const whereCondition: any = {};

    // Adicionar busca por nome, email ou CPF se houver termo de busca
    if (search) {
      whereCondition.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          cpf: {
            contains: search.replace(/\D/g, ""),
          },
        },
      ];
    }

    // Buscar contagem total de pacientes com os filtros aplicados
    const totalPatients = await db.patient.count({
      where: whereCondition,
    });

    // Buscar pacientes com uma query segura (sem incluir user diretamente)
    const patients = await db.patient.findMany({
      where: whereCondition,
      skip,
      take: pageSize,
      orderBy: {
        createdAt: "desc", // Pacientes mais recentes primeiro
      },
      select: {
        id: true,
        userId: true,
        cpf: true,
        birthDate: true,
        gender: true,
        allergies: true,
        chronicConditions: true,
        createdAt: true,
        appointments: {
          select: {
            id: true
          },
          take: 10
        }
      }
    });

    // Buscar os usuários correspondentes
    const userIds = patients.map(patient => patient.userId);
    const users = await db.user.findMany({
      where: {
        id: {
          in: userIds
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        avatarUrl: true
      }
    });

    // Criar um mapa para acesso rápido
    const userMap = users.reduce((acc, user) => {
      acc[user.id] = user;
      return acc;
    }, {} as Record<string, typeof users[0]>);

    // Combinar os dados no formato esperado pelos componentes
    const patientsWithUsers = patients.map(patient => {
      const user = userMap[patient.userId] || {
        id: patient.userId,
        name: "Usuário não encontrado",
        email: "<EMAIL>",
        phone: null,
        avatarUrl: null
      };

      return {
        ...patient,
        user,
        // Adicionando o campo address esperado pelo componente PatientCard
        address: {
          city: "",
          state: ""
        }
      };
    });

    // Calcular o total de páginas
    const totalPages = Math.ceil(totalPatients / pageSize);

    return (
      <div className="flex flex-col gap-6">
        <Suspense fallback={<Loading />}>
          <PatientsClient patients={patientsWithUsers} />
        </Suspense>

        {totalPages > 1 && (
          <div className="flex justify-center">
            <PaginationButton
              currentPage={!isNaN(page) ? page : 1}
              totalPages={totalPages}
              baseUrl="/app/patients"
              searchParams={search ? { search } : undefined}
            />
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error("Erro ao carregar pacientes:", error);

    return (
      <div className="container mx-auto p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Erro ao carregar pacientes</h2>
        <p className="text-muted-foreground mb-6">
          Ocorreu um erro ao tentar carregar a lista de pacientes.
          Por favor, tente novamente mais tarde.
        </p>

        <div className="bg-red-50 border border-red-200 p-4 rounded-md text-left mb-6">
          <p className="text-red-800 font-medium mb-2">Detalhes do erro:</p>
          <pre className="bg-white p-3 rounded overflow-auto text-sm">
            {error instanceof Error
              ? `${error.name}: ${error.message}\n\n${error.stack}`
              : JSON.stringify(error, null, 2)}
          </pre>
        </div>

        <a href="/app/dashboard" className="text-primary hover:underline">
          Voltar para o Dashboard
        </a>
      </div>
    );
  }
}
