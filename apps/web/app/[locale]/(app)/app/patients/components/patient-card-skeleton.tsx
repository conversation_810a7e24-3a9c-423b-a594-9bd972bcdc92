// apps/web/app/[locale]/(saas)/app/patients-server/loading.tsx
import { DashboardHeader } from "@ui/components/header";
import { Skeleton } from "@ui/components/skeleton";
import { Card, CardContent, CardHeader } from "@ui/components/card";

export default function PatientsLoading() {
  return (
    <>
      <DashboardHeader
        heading="Pacientes  "
        text="Gerencie os pacientes cadastrados no sistema."
      />
      <div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 9 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center gap-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
}
