"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@ui/components/button";
import { PlusCircle, Search } from "lucide-react";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@ui/components/dialog";
import { AddPatientForm } from "./add-patient-form";
import PatientCardList from "./patient-card-list";
import { DashboardHeader } from "@ui/components/header";
import { Input } from "@ui/components/input";

interface PatientsClientProps {
  patients: Array<{
    id: string;
    user: {
      id: string;
      name: string | null;
      email: string;
      phone: string | null;
      avatarUrl: string | null;
    };
    cpf: string;
    birthDate: Date;
    gender: string;
    appointments: Array<{ id: string }>;
  }>;
}

export function PatientsClient({ patients }: PatientsClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isAddPatientOpen, setIsAddPatientOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");

  const handleAddPatientSuccess = () => {
    setIsAddPatientOpen(false);
    router.refresh();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Create new URLSearchParams object
    const params = new URLSearchParams(searchParams);

    // Update or remove search parameter
    if (searchTerm) {
      params.set("search", searchTerm);
    } else {
      params.delete("search");
    }

    // Reset to page 1 when searching
    params.delete("page");

    // Navigate with the new search params
    router.push(`/app/patients?${params.toString()}`);
  };

  return (
    <div className="flex flex-col gap-6">
      <DashboardHeader
        heading="Pacientes"
        text="Gerencie os pacientes cadastrados no sistema."
      >
        <Dialog open={isAddPatientOpen} onOpenChange={setIsAddPatientOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Cadastrar Paciente
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px] bg-white">
            <DialogHeader>
              <DialogTitle>Cadastrar Novo Paciente</DialogTitle>
            </DialogHeader>
            <AddPatientForm onSuccess={handleAddPatientSuccess} />
          </DialogContent>
        </Dialog>
      </DashboardHeader>

      {/* Search bar */}
      <div className="px-4">
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <Input
            type="search"
            placeholder="Buscar por nome, email ou CPF"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </form>
      </div>

      {patients.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p className="text-muted-foreground mb-4">
            {searchParams.has("search")
              ? "Nenhum paciente encontrado para esta busca."
              : "Nenhum paciente cadastrado."}
          </p>
          <Button onClick={() => setIsAddPatientOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Adicionar Paciente
          </Button>
        </div>
      ) : (
        <PatientCardList patients={patients} />
      )}
    </div>
  );
}
