"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface AddPatientFormProps {
  hospitalId?: string;
  doctorId?: string;
  patientId?: string;
  initialData?: {
    name: string;
    email: string;
    phone: string;
    cpf: string;
    birthDate: string;
    gender: "M" | "F" | "O" | "N";
    bloodType?: "A+" | "A-" | "B+" | "B-" | "AB+" | "AB-" | "O+" | "O-" | "NA";
    allergies?: string;
    chronicConditions?: string;
  };
  onSuccess?: () => void;
}

const formSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(14, "Telefone inválido").max(15, "Telefone inválido"),
  cpf: z.string().min(14, "CPF inválido").max(14, "CPF inválido"),
  birthDate: z.string().min(1, "Data de nascimento é obrigatória"),
  gender: z.enum(["M", "F", "O", "N"], {
    invalid_type_error: "Selecione uma opção válida",
  }),
  bloodType: z.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", "NA"], {
    invalid_type_error: "Selecione uma opção válida",
  }).optional(),
  allergies: z.string().optional(),
  chronicConditions: z.string().optional(),
});

// Tipo seguro com Zod para o formulário
type PatientFormValues = z.infer<typeof formSchema>;

// Transformar o tipo de NA para string vazia (necessário para API)
const mapBloodTypeForApi = (bloodType?: string): "A+" | "A-" | "B+" | "B-" | "AB+" | "AB-" | "O+" | "O-" | "" | undefined => {
  if (!bloodType || bloodType === "NA") return "";
  return bloodType as "A+" | "A-" | "B+" | "B-" | "AB+" | "AB-" | "O+" | "O-";
};

export function AddPatientForm({
  hospitalId,
  doctorId,
  patientId,
  initialData,
  onSuccess,
}: AddPatientFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!patientId;

  // Use tRPC mutations
  const createPatientMutation = apiClient.patients.create.useMutation();
  const updatePatientMutation = apiClient.patients.update.useMutation();

  const form = useForm<PatientFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      email: "",
      phone: "",
      cpf: "",
      birthDate: "",
      gender: "N",
      bloodType: "NA",
      allergies: "",
      chronicConditions: "",
    },
  });

  // Update form values when initialData changes
  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        form.setValue(key as any, value);
      });
    }
  }, [initialData, form]);

  // Função para formatar CPF (adiciona pontos e traço)
  const formatCPF = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `${digits.slice(0, 3)}.${digits.slice(3)}`;
    if (digits.length <= 9) return `${digits.slice(0, 3)}.${digits.slice(3, 6)}.${digits.slice(6)}`;
    return `${digits.slice(0, 3)}.${digits.slice(3, 6)}.${digits.slice(6, 9)}-${digits.slice(9, 11)}`;
  };

  // Função para formatar telefone celular brasileiro (XX) XXXXX-XXXX
  const formatPhone = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (!digits) return "";
    if (digits.length <= 2) return `(${digits}`;
    if (digits.length <= 7) return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
    if (digits.length <= 11) return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
  };

  async function onSubmit(values: PatientFormValues) {
    try {
      setIsLoading(true);
      console.log("[AddPatientForm] Submitting form with values:", values);

      // Remover formatação do CPF antes de enviar
      const formattedCpf = values.cpf.replace(/\D/g, "");

      // Remover formatação do telefone antes de enviar
      const formattedPhone = values.phone.replace(/\D/g, "");

      // Mapear bloodType corretamente para o formato esperado pela API
      const bloodType = mapBloodTypeForApi(values.bloodType);

      // Preparar dados para envio
      const patientData = {
        name: values.name,
        email: values.email,
        phone: formattedPhone,
        cpf: formattedCpf,
        birthDate: values.birthDate,
        gender: values.gender,
        bloodType,
        allergies: values.allergies,
        chronicConditions: values.chronicConditions,
        hospitalId,
        doctorId,
      };

      console.log("[AddPatientForm] Processed data for API:", patientData);

      if (isEditing) {
        // Atualizar paciente existente
        console.log("[AddPatientForm] Updating patient with ID:", patientId);
        await updatePatientMutation.mutateAsync({
          id: patientId,
          ...patientData,
        });
        console.log("[AddPatientForm] Patient updated successfully");
        toast.success("Paciente atualizado com sucesso!");
      } else {
        // Criar novo paciente
        console.log("[AddPatientForm] Creating new patient");
        await createPatientMutation.mutateAsync(patientData);
        console.log("[AddPatientForm] Patient created successfully");
        toast.success("Paciente adicionado com sucesso!");
        form.reset();
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error(`[AddPatientForm] Error ${isEditing ? 'updating' : 'creating'} patient:`, error);

      // Extrair mensagem de erro da resposta tRPC se disponível
      const errorMessage = error.message ||
                           error.shape?.message ||
                           `Erro ao ${isEditing ? 'atualizar' : 'adicionar'} paciente`;

      // Log detalhado para diagnóstico
      if (error.shape?.data?.zodError) {
        console.error("[AddPatientForm] Validation errors:", error.shape.data.zodError);
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome completo*</FormLabel>
                <FormControl>
                  <Input placeholder="Nome do paciente" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email*</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefone*</FormLabel>
                <FormControl>
                  <Input
                    placeholder="(00) 00000-0000"
                    value={formatPhone(field.value || "")}
                    onChange={(e) => {
                      const formatted = formatPhone(e.target.value);
                      field.onChange(formatted);
                    }}
                    maxLength={16}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="cpf"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CPF*</FormLabel>
                <FormControl>
                  <Input
                    placeholder="000.000.000-00"
                    value={formatCPF(field.value || "")}
                    onChange={(e) => {
                      const formatted = formatCPF(e.target.value);
                      field.onChange(formatted);
                    }}
                    maxLength={14}
                    disabled={isEditing} // Disable CPF field when editing
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Data de Nascimento*</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Gênero*</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="M">Masculino</SelectItem>
                    <SelectItem value="F">Feminino</SelectItem>
                    <SelectItem value="O">Outro</SelectItem>
                    <SelectItem value="N">Prefiro não informar</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bloodType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tipo Sanguíneo</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="NA">Não informado</SelectItem>
                    <SelectItem value="A+">A+</SelectItem>
                    <SelectItem value="A-">A-</SelectItem>
                    <SelectItem value="B+">B+</SelectItem>
                    <SelectItem value="B-">B-</SelectItem>
                    <SelectItem value="AB+">AB+</SelectItem>
                    <SelectItem value="AB-">AB-</SelectItem>
                    <SelectItem value="O+">O+</SelectItem>
                    <SelectItem value="O-">O-</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <FormField
            control={form.control}
            name="allergies"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Alergias</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Separe as alergias por vírgula"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="chronicConditions"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Condições Crônicas</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Separe as condições por vírgula"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Salvando..." : isEditing ? "Atualizar Paciente" : "Adicionar Paciente"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
