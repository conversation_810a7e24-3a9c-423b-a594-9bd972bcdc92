// apps/web/app/[locale]/(saas)/app/patients-server/components/patient-card.tsx
"use client";
import {
  Calendar,
  CalendarDays,
  Mail,
  MoreVertical,
  Phone,
  User,
} from "lucide-react";
import { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Separator } from "@ui/components/separator";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetTrigger,
} from "@ui/components/sheet";

interface PatientCardProps {
  patient: {
    id: string;
    user: {
      name: string | null;
      email: string;
      image?: string | null;
      avatarUrl?: string | null;
      phone: string | null;
    };
    cpf: string;
    birthDate: Date;
    gender: string;
    address: {
      city: string;
      state: string;
    };
    appointments: any[];
  };
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function PatientCard({ patient, onEdit, onDelete }: PatientCardProps) {
  const [open, setOpen] = useState(false);

  const genderText = {
    M: "Masculino",
    F: "Feminino",
    O: "Outro",
    N: "Não informado",
  };

  // Usar avatarUrl se disponível, senão usar image (para compatibilidade)
  const avatarUrl = patient.user.avatarUrl || patient.user.image;

  return (
    <>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Card className="cursor-pointer transition-shadow hover:shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={avatarUrl || ""} />
                  <AvatarFallback>
                    {patient.user.name?.substring(0, 2).toUpperCase() || "??"}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold">{patient.user.name || "Sem nome"}</h3>
                  <p className="text-muted-foreground text-sm">
                    {format(new Date(patient.birthDate), "dd/MM/yyyy", {
                      locale: ptBR,
                    })}
                  </p>
                </div>
              </div>
              {(onEdit || onDelete) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onEdit && (
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onEdit(patient.id);
                      }}>
                        Editar
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <DropdownMenuItem
                        className="text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(patient.id);
                        }}
                      >
                        Excluir
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-gray-600 text-sm">
                    CPF: {patient.cpf}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-gray-600 text-sm">
                    {patient.appointments.length} consultas
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </SheetTrigger>

        <SheetContent className="w-full overflow-y-auto sm:max-w-2xl">
          <SheetHeader>
            <SheetTitle>Detalhes do Paciente</SheetTitle>
          </SheetHeader>
          <div className="mt-6 space-y-6">
            <div className="flex items-start gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={avatarUrl || ""} />
                <AvatarFallback>
                  {patient.user.name?.substring(0, 2).toUpperCase() || "??"}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <h3 className="font-semibold">{patient.user.name || "Sem nome"}</h3>
                <div className="text-sm text-muted-foreground">
                  {genderText[patient.gender as keyof typeof genderText] || "Não informado"}
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Informações de Contato</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    {patient.user.email}
                  </div>
                  {patient.user.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      {patient.user.phone}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Informações Pessoais</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    CPF: {patient.cpf}
                  </div>
                  <div className="flex items-center gap-2">
                    <CalendarDays className="h-4 w-4 text-gray-500" />
                    Nascimento:{" "}
                    {format(new Date(patient.birthDate), "PPP", {
                      locale: ptBR,
                    })}
                  </div>
                </div>
              </div>
            </div>

            <SheetFooter>
              {onEdit && (
                <Button
                  onClick={() => {
                    onEdit(patient.id);
                    setOpen(false);
                  }}
                >
                  Editar Paciente
                </Button>
              )}
            </SheetFooter>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
