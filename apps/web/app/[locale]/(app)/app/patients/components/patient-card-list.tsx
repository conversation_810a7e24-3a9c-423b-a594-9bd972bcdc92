// apps/web/app/[locale]/(app)/app/patients/components/patient-card-list.tsx
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { PatientCard } from './patient-card';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@ui/components/dialog";
import { AddPatientForm } from "./add-patient-form";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@ui/components/alert-dialog";
import { apiClient } from "@shared/lib/api-client";

interface PatientCardListProps {
  patients: Array<{
    id: string;
    user: {
      id: string;
      name: string | null;
      email: string;
      phone: string | null;
      avatarUrl: string | null;
    };
    cpf: string;
    birthDate: Date;
    gender: string;
    appointments: Array<{ id: string }>;
  }>;
}

export default function PatientCardList({ patients }: PatientCardListProps) {
  const router = useRouter();
  const [editingPatientId, setEditingPatientId] = useState<string | null>(null);
  const [deletingPatientId, setDeletingPatientId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Declare the mutation at the component level
  const deletePatientMutation = apiClient.patients.delete_.useMutation();

  // Find the patient being edited
  const patientToEdit = editingPatientId
    ? patients.find(p => p.id === editingPatientId)
    : null;

  const handleEditSuccess = () => {
    setEditingPatientId(null);
    router.refresh();
  };

  const handleDeletePatient = async () => {
    if (!deletingPatientId) return;

    try {
      setIsDeleting(true);
      console.log("Starting patient deletion for ID:", deletingPatientId);

      // Use the mutation declared at the component level
      await deletePatientMutation.mutateAsync({ id: deletingPatientId });

      toast.success("Paciente excluído com sucesso");
      setDeletingPatientId(null);
      router.refresh();
    } catch (error: any) {
      console.error("Erro detalhado ao excluir paciente:", error);

      // Extract the most specific error message available
      let errorMessage = "Erro ao excluir paciente";

      // Try to extract TRPC error details
      if (error.data?.zodError) {
        errorMessage = "Erro de validação ao excluir paciente";
      } else if (error.data?.code === "FORBIDDEN") {
        errorMessage = "Não é possível excluir um paciente com consultas ativas ou agendadas";
      } else if (error.data?.code === "NOT_FOUND") {
        errorMessage = "O paciente não foi encontrado ou já foi excluído";
      } else if (error.data?.code === "TIMEOUT") {
        errorMessage = "A operação demorou muito tempo. Tente novamente.";
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Log additional details if available
      if (error.shape) {
        console.error("Error shape:", error.shape);
      }

      if (error.cause) {
        console.error("Error cause:", error.cause);
      }

      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 gap-4 px-4 md:grid-cols-2 lg:grid-cols-3">
        {patients.map((patient) => (
          <PatientCard
            key={patient.id}
            patient={{
              ...patient,
              address: {
                city: "",
                state: ""
              }
            }}
            onEdit={(id) => setEditingPatientId(id)}
            onDelete={(id) => setDeletingPatientId(id)}
          />
        ))}
      </div>

      {/* Edit Patient Dialog */}
      {patientToEdit && (
        <Dialog open={!!editingPatientId} onOpenChange={(open) => !open && setEditingPatientId(null)}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Editar Paciente</DialogTitle>
            </DialogHeader>
            <AddPatientForm
              patientId={editingPatientId || undefined}
              initialData={{
                name: patientToEdit.user.name || "",
                email: patientToEdit.user.email,
                phone: patientToEdit.user.phone || "",
                cpf: patientToEdit.cpf,
                birthDate: new Date(patientToEdit.birthDate).toISOString().split('T')[0],
                gender: patientToEdit.gender as "M" | "F" | "O" | "N",
              }}
              onSuccess={handleEditSuccess}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingPatientId} onOpenChange={(open) => !open && setDeletingPatientId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir este paciente? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePatient}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Excluindo..." : "Excluir"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
