// apps/web/app/[locale]/(saas)/app/doctors/components/doctor-form.tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Sheet,
	SheetContent,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

interface DoctorFormProps {
	doctor?: any;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

// Tipo para hospital
interface Hospital {
	id: string;
	name: string;
}

// Schema do formulário incluindo hospitalId
const doctorFormSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	phone: z.string().min(10, "Telefone inválido"),
	crm: z.string().min(4, "CRM inválido"),
	crmState: z.string().min(2, "Estado de registro inválido"),
	specialtyIds: z
		.array(z.string())
		.min(1, "Selecione pelo menos uma especialidade"),
	hospitalId: z.string().min(1, "Hospital é obrigatório"),
	consultationDuration: z.number().min(15).default(30),
	consultationPrice: z.number().min(0).nullable().default(null),
	chargeable: z.boolean().default(false),
	biography: z.string().optional(),
});

type FormValues = z.infer<typeof doctorFormSchema>;

export function DoctorForm({ doctor, open, onOpenChange }: DoctorFormProps) {
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [hospitals, setHospitals] = useState<Hospital[]>([]);
	const [loadingHospitals, setLoadingHospitals] = useState(false);

	const createDoctorMutation = apiClient.doctors.create.useMutation();
	const updateDoctorMutation = apiClient.doctors.update.useMutation();

	// Buscar especialidades
	const { data: specialties, isLoading: loadingSpecialties } =
		apiClient.doctors.getSpecialties.useQuery(undefined, {
			enabled: open,
		});

	// Buscar hospitais quando o modal abrir
	useEffect(() => {
		if (open) {
			setLoadingHospitals(true);

			fetch("/api/hospitals")
				.then((res) => {
					if (!res.ok) {
						throw new Error("Erro ao buscar hospitais");
					}
					return res.json();
				})
				.then((data) => {
					setHospitals(data);
				})
				.catch((error) => {
					console.error("Erro ao buscar hospitais:", error);
					toast.error("Não foi possível carregar a lista de hospitais");
				})
				.finally(() => {
					setLoadingHospitals(false);
				});
		}
	}, [open]);

	// Inicializar formulário
	const form = useForm<FormValues>({
		resolver: zodResolver(doctorFormSchema),
		defaultValues: {
			name: "",
			email: "",
			phone: "",
			crm: "",
			crmState: "",
			specialtyIds: [],
			hospitalId: "",
			consultationDuration: 30,
			consultationPrice: null,
			chargeable: false,
			biography: "",
		},
	});

	// Preencher o formulário com dados existentes
	useEffect(() => {
		if (doctor && open) {
			// Extrair hospitalId do primeiro hospital associado
			const hospitalId =
				doctor.hospitals && doctor.hospitals.length > 0
					? doctor.hospitals[0].hospitalId
					: "";

			// Check if the doctor has a price set
			const hasPrice = doctor.consultationPrice > 0;

			form.reset({
				name: doctor.user.name,
				email: doctor.user.email,
				phone: doctor.user.phone || "",
				crm: doctor.crm,
				crmState: doctor.crmState,
				specialtyIds: doctor.specialties?.map((s) => s.id) || [],
				hospitalId,
				consultationDuration: doctor.consultationDuration || 30,
				consultationPrice: hasPrice ? Number(doctor.consultationPrice) : null,
				chargeable: hasPrice,
				biography: doctor.biography || "",
			});
		} else if (open) {
			// Reset para valores padrão
			form.reset({
				name: "",
				email: "",
				phone: "",
				crm: "",
				crmState: "",
				specialtyIds: [],
				hospitalId: "",
				consultationDuration: 30,
				consultationPrice: null,
				chargeable: false,
				biography: "",
			});
		}
	}, [doctor, form, open]);

	// Preencher hospitalId assim que os hospitais forem carregados
	useEffect(() => {
		if (hospitals.length > 0 && !doctor && form.getValues().hospitalId === "") {
			form.setValue("hospitalId", hospitals[0].id);
		}
	}, [hospitals, doctor, form]);

	// Watch for changes to the chargeable field
	const chargeable = form.watch("chargeable");

	// Update price based on chargeability
	useEffect(() => {
		// If chargeable is toggled off, reset the price to null
		if (!chargeable) {
			form.setValue("consultationPrice", null);
		} else if (form.getValues("consultationPrice") === null) {
			// If toggled on and price is null, set a default value
			form.setValue("consultationPrice", 0);
		}
	}, [chargeable, form]);

	// Função para formatar telefone
	function formatPhoneNumber(value: string) {
		const digits = value.replace(/\D/g, "");
		if (!digits) return "";

		if (digits.length <= 2) {
			return `(${digits}`;
		} else if (digits.length <= 6) {
			return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
		} else if (digits.length <= 10) {
			return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6, 10)}`;
		} else {
			return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
		}
	}

	async function onSubmit(data: FormValues) {
		try {
			setLoading(true);

			if (doctor) {
				// Atualizar médico existente
				await updateDoctorMutation.mutateAsync({
					// Usando mutateAsync em vez de mutate
					id: doctor.id,
					...data,
					// Set price to 0 if not charging
					consultationPrice: data.chargeable ? data.consultationPrice || 0 : 0,
				});

				toast.success("Médico atualizado com sucesso!");
			} else {
				// Criar novo médico
				await createDoctorMutation.mutateAsync({
					...data,
					// Set price to 0 if not charging
					consultationPrice: data.chargeable ? data.consultationPrice || 0 : 0,
				});

				toast.success("Médico cadastrado com sucesso!");
			}

			router.refresh();
			onOpenChange(false);
		} catch (error) {
			console.error("Erro:", error);
			toast.error(
				error instanceof Error
					? error.message
					: doctor
						? "Erro ao atualizar médico"
						: "Erro ao cadastrar médico",
			);
		} finally {
			setLoading(false);
		}
	}

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full overflow-y-auto min:max-w-3xl">
				<SheetHeader>
					<SheetTitle>{doctor ? "Editar" : "Novo"} Médico</SheetTitle>
				</SheetHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="mt-8 space-y-6"
					>
						<div className="space-y-4">
							{/* Nome */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Nome</FormLabel>
										<FormControl>
											<Input placeholder="Nome do médico" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Email */}
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="Email do médico"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Telefone com máscara */}
							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Telefone</FormLabel>
										<FormControl>
											<Input
												type="tel"
												placeholder="(XX) XXXXX-XXXX"
												value={formatPhoneNumber(field.value)}
												onChange={(e) => {
													const digits = e.target.value.replace(/\D/g, "");
													field.onChange(digits);
												}}
												maxLength={16}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Hospital */}
							<FormField
								control={form.control}
								name="hospitalId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Hospital</FormLabel>
										<Select
											value={field.value}
											onValueChange={field.onChange}
											disabled={loadingHospitals}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue
														placeholder={
															loadingHospitals
																? "Carregando..."
																: "Selecione um hospital"
														}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{hospitals.length === 0 ? (
													<SelectItem value="no-hospital">
														Nenhum hospital disponível
													</SelectItem>
												) : (
													hospitals.map((hospital) => (
														<SelectItem key={hospital.id} value={hospital.id}>
															{hospital.name}
														</SelectItem>
													))
												)}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* CRM e Estado */}
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="crm"
									render={({ field }) => (
										<FormItem>
											<FormLabel>CRM</FormLabel>
											<FormControl>
												<Input placeholder="CRM do médico" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="crmState"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Estado do CRM</FormLabel>
											<Select
												value={field.value}
												onValueChange={field.onChange}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Estado" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{[
														"AC",
														"AL",
														"AP",
														"AM",
														"BA",
														"CE",
														"DF",
														"ES",
														"GO",
														"MA",
														"MT",
														"MS",
														"MG",
														"PA",
														"PB",
														"PR",
														"PE",
														"PI",
														"RJ",
														"RN",
														"RS",
														"RO",
														"RR",
														"SC",
														"SP",
														"SE",
														"TO",
													].map((uf) => (
														<SelectItem key={uf} value={uf}>
															{uf}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Especialidades */}
							<FormField
								control={form.control}
								name="specialtyIds"
								render={() => (
									<FormItem>
										<FormLabel>Especialidades</FormLabel>
										<div className="mt-2 space-y-2">
											{loadingSpecialties ? (
												<div className="text-sm text-muted-foreground">
													Carregando especialidades...
												</div>
											) : !specialties || specialties.length === 0 ? (
												<div className="text-sm text-muted-foreground">
													Nenhuma especialidade encontrada
												</div>
											) : (
												specialties.map((specialty) => (
													<FormField
														key={specialty.id}
														control={form.control}
														name="specialtyIds"
														render={({ field }) => {
															return (
																<FormItem
																	key={specialty.id}
																	className="flex flex-row items-start space-x-3 space-y-0"
																>
																	<FormControl>
																		<Checkbox
																			checked={field.value?.includes(
																				specialty.id,
																			)}
																			onCheckedChange={(checked) => {
																				return checked
																					? field.onChange([
																							...field.value,
																							specialty.id,
																						])
																					: field.onChange(
																							field.value?.filter(
																								(value) =>
																									value !== specialty.id,
																							),
																						);
																			}}
																		/>
																	</FormControl>
																	<FormLabel className="font-normal">
																		{specialty.name}
																	</FormLabel>
																</FormItem>
															);
														}}
													/>
												))
											)}
										</div>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Duração e Preço */}
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="consultationDuration"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Duração da Consulta (minutos)</FormLabel>
											<FormControl>
												<Input
													type="number"
													min="15"
													placeholder="Duração da consulta"
													{...field}
													onChange={(e) =>
														field.onChange(Number(e.target.value))
													}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="space-y-4">
									{/* <FormField
										control={form.control}
										name="chargeable"
										render={({ field }) => (
											<FormItem className="flex items-center space-x-2 rounded-md p-2">
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
												<FormLabel className="!mt-0">
													{field.value
														? "Cobra diretamente pela consulta"
														: "Não cobra diretamente pela consulta"}
												</FormLabel>
											</FormItem>
										)}
									/> */}

									{form.watch("chargeable") && (
										<FormField
											control={form.control}
											name="consultationPrice"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Preço da Consulta (R$)</FormLabel>
													<FormControl>
														<Input
															type="number"
															min="0"
															step="0.01"
															placeholder="Preço da consulta"
															{...field}
															value={field.value === null ? "" : field.value}
															onChange={(e) =>
																field.onChange(Number(e.target.value))
															}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}
								</div>
							</div>

							{/* Biografia */}
							<FormField
								control={form.control}
								name="biography"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Biografia</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Informações sobre formação e experiência do médico"
												className="resize-none"
												rows={4}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Botões de ação */}
						<div className="flex justify-end space-x-4 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
								disabled={loading}
							>
								Cancelar
							</Button>
							<Button type="submit" disabled={loading}>
								{loading ? "Salvando..." : doctor ? "Salvar" : "Cadastrar"}
							</Button>
						</div>
					</form>
				</Form>
			</SheetContent>
		</Sheet>
	);
}
