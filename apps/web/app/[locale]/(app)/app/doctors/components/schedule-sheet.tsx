"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@ui/components/sheet";
import { DoctorCalendar } from "@ui/components/telemed/doctor/calendar/doctor-calendar";

interface ScheduleSheetProps {
  doctorId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ScheduleSheet({ doctorId, isOpen, onOpenChange }: ScheduleSheetProps) {
  const router = useRouter();
  const [events, setEvents] = useState([]);

  const { data: doctorAppointments } = apiClient.appointments.getAppointmentsByDoctor.useQuery(
    { doctorId },
    { enabled: !!doctorId }
  );

  // Update events when appointments change
  useEffect(() => {
    if (doctorAppointments) {
      const calendarEvents = doctorAppointments.map((apt: any) => ({
        id: apt.id,
        title: `${apt.patient.user.name} - ${apt.consultType}`,
        start: new Date(apt.scheduledAt),
        end: new Date(new Date(apt.scheduledAt).getTime() + apt.duration * 60000),
        status: apt.status,
        consultType: apt.consultType
      }));
      setEvents(calendarEvents);
    }
  }, [doctorAppointments]);

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-[900px]">
        <SheetHeader>
          <SheetTitle>Agenda do Médico</SheetTitle>
        </SheetHeader>
        <div className="mt-6">
          <DoctorCalendar
            events={events}
            doctorId={doctorId}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}
