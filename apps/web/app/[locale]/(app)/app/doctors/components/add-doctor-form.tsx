"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { Switch } from "@ui/components/switch";
import { Label } from "@ui/components/label";

const doctorFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido"),
  crm: z.string().min(4, "CRM inválido"),
  crmState: z.string().length(2, "Estado de registro inválido"),
  specialtyIds: z.array(z.string()).min(1, "Selecione pelo menos uma especialidade"),
  hospitalId: z.string().min(1, "Hospital é obrigatório"),
  consultationDuration: z.number().min(15).default(30),
  consultationPrice: z.number().min(0).nullable().default(null),
  chargeable: z.boolean().default(false),
  biography: z.string().optional(),
});

type DoctorFormValues = z.infer<typeof doctorFormSchema>;

// Função para formatar o telefone no padrão brasileiro: (XX) XXXXX-XXXX
const formatPhone = (value: string) => {
  if (!value) return "";

  // Remove tudo que não for número
  const phoneNumber = value.replace(/\D/g, "");

  // Aplica a formatação
  if (phoneNumber.length <= 2) {
    return phoneNumber.replace(/^(\d{0,2})/, "($1");
  } else if (phoneNumber.length <= 7) {
    return phoneNumber.replace(/^(\d{2})(\d{0,5})/, "($1) $2");
  } else if (phoneNumber.length <= 11) {
    return phoneNumber.replace(/^(\d{2})(\d{5})(\d{0,4})/, "($1) $2-$3");
  } else {
    // Limita a 11 dígitos (DDD + 9 dígitos)
    return phoneNumber.slice(0, 11).replace(/^(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  }
};

// Função para remover a formatação do telefone
const unformatPhone = (value: string) => {
  return value.replace(/\D/g, "");
};

interface AddDoctorFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddDoctorForm({ open, onOpenChange }: AddDoctorFormProps) {
  const router = useRouter();
  const createDoctorMutation = apiClient.doctors.create.useMutation();
  const [specialties, setSpecialties] = useState<{ id: string; name: string }[]>([]);
  const [hospitals, setHospitals] = useState<{ id: string; name: string }[]>([]);
  const [priceEnabled, setPriceEnabled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<DoctorFormValues>({
    resolver: zodResolver(doctorFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      crm: "",
      crmState: "",
      specialtyIds: [],
      hospitalId: "",
      consultationDuration: 30,
      consultationPrice: null,
      chargeable: false,
      biography: "",
    },
  });

  // Watch for changes to the chargeable field
  const chargeable = form.watch("chargeable");

  // Update price based on chargeability
  useEffect(() => {
    // If chargeable is toggled off, reset the price to null
    if (!chargeable) {
      form.setValue("consultationPrice", null);
    } else if (form.getValues("consultationPrice") === null) {
      // If toggled on and price is null, set a default value
      form.setValue("consultationPrice", 0);
    }
  }, [chargeable, form]);

  // Carregar especialidades e hospitais
  useEffect(() => {
    const loadData = async () => {
      try {
        // Try alternative approach for loading data
        try {
          // Load specialties first
          const specialtyResponse = await fetch('/api/specialties');
          if (specialtyResponse.ok) {
            const specialtyData = await specialtyResponse.json();
            setSpecialties(specialtyData);
            console.log("Specialties loaded:", specialtyData);
          } else {
            throw new Error(`Failed to load specialties: ${specialtyResponse.statusText}`);
          }

          // Then load hospitals
          const hospitalResponse = await fetch('/api/hospitals');
          if (hospitalResponse.ok) {
            const hospitalData = await hospitalResponse.json();
            setHospitals(hospitalData);
            console.log("Hospitals loaded:", hospitalData);
          } else {
            throw new Error(`Failed to load hospitals: ${hospitalResponse.statusText}`);
          }
        } catch (error) {
          console.error("Error loading data:", error);
          toast.error("Erro ao carregar dados. Por favor, tente novamente.");
        }
      } catch (error) {
        console.error("Erro geral ao carregar dados:", error);
        toast.error("Erro ao carregar dados necessários");
      }
    };

    if (open) {
      loadData();
    }
  }, [open, toast]);

  const onSubmit = async (data: DoctorFormValues) => {
    try {
      setIsSubmitting(true);

      // Formatar o telefone e usar valores padrão
      const formattedData = {
        ...data,
        consultationDuration: 30, // Valor padrão fixo
        consultationPrice: 0, // Valor padrão fixo
        phone: unformatPhone(data.phone),
      };

      await createDoctorMutation.mutateAsync(formattedData);

      // Mostrar mensagem de sucesso
      toast.success("Médico cadastrado com sucesso");

      // Limpar formulário
      form.reset();

      // Fechar modal
      onOpenChange(false);

      // Forçar atualização da lista com timeout
      setTimeout(() => {
        router.refresh();
      }, 500);
    } catch (error) {
      console.error(error);
      toast.error(
        error instanceof Error ? error.message : "Erro ao cadastrar médico"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Cadastrar Médico</DialogTitle>
          <DialogDescription>
            Preencha os dados do médico para cadastrá-lo no sistema.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome completo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(00) 00000-0000"
                      value={field.value}
                      onChange={(e) => {
                        field.onChange(formatPhone(e.target.value));
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hospitalId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hospital Principal</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um hospital" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {hospitals.map((hospital) => (
                        <SelectItem key={hospital.id} value={hospital.id}>
                          {hospital.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="crm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CRM</FormLabel>
                  <FormControl>
                    <Input placeholder="Número do CRM" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="crmState"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Estado do CRM</FormLabel>
                  <FormControl>
                    <Input placeholder="SP" maxLength={2} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="specialtyIds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Especialidades</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={(value) => field.onChange([...field.value, value])}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione especialidades" />
                    </SelectTrigger>
                    <SelectContent>
                      {specialties.map((specialty) => (
                        <SelectItem key={specialty.id} value={specialty.id}>
                          {specialty.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                {field.value.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {field.value.map((id) => {
                      const specialty = specialties.find((s) => s.id === id);
                      return specialty ? (
                        <div key={id} className="bg-primary/10 text-primary rounded-md px-2 py-1 text-sm flex items-center gap-1">
                          {specialty.name}
                          <button
                            type="button"
                            onClick={() => field.onChange(field.value.filter((v) => v !== id))}
                            className="text-primary hover:text-primary/80"
                          >
                            ×
                          </button>
                        </div>
                      ) : null;
                    })}
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="biography"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Biografia</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Informações sobre a formação e experiência do médico"
                    className="resize-none"
                    rows={5}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Campos ocultos temporariamente */}
          <input type="hidden" {...form.register("consultationDuration")} value="30" />
          <input type="hidden" {...form.register("chargeable")} value="false" />

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Cadastrando..." : "Cadastrar"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
