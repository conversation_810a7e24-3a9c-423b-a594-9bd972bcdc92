import { <PERSON>, CardContent, CardHeader } from "@ui/components/card";
// apps/web/app/[locale]/(saas)/app/doctors/loading.tsx
import { DashboardHeader } from "@ui/components/header";
import { Skeleton } from "@ui/components/skeleton";

export default function Loading() {
	return (
		<>
			<DashboardHeader
				heading="Médicos"
				text="Gerencie os médicos cadastrados no sistema."
			/>

			<div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				{Array.from({ length: 6 }).map((_, i) => (
					<Card key={i}>
						<CardHeader className="flex flex-row items-center gap-4 pb-2">
							<Skeleton className="h-16 w-16 rounded-full" />
							<div className="space-y-2">
								<Skeleton className="h-4 w-[150px]" />
								<Skeleton className="h-3 w-[100px]" />
							</div>
						</CardHeader>
						<CardContent>
							<div className="space-y-2">
								<Skeleton className="h-3 w-full" />
								<Skeleton className="h-3 w-[80%]" />
								<Skeleton className="h-3 w-[60%]" />
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</>
	);
}
