// apps/web/app/[locale]/(saas)/app/specialties/page.tsx
import { createApiCaller } from "api/trpc/caller";
import { Suspense } from "react";
import { SpecialtiesClient } from "./components/specialties-client";
import Loading from "./loading";

export default async function SpecialtiesPage() {
	const apiCaller = await createApiCaller();
	const specialties = await apiCaller.specialties.list();

	return (
		<Suspense fallback={<Loading />}>
			<SpecialtiesClient initialSpecialties={specialties} />
		</Suspense>
	);
}
