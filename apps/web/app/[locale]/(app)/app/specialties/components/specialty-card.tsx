// apps/web/app/[locale]/(saas)/app/specialties/components/specialty-card.tsx
"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Edit2, Stethoscope, Trash2 } from "lucide-react";

interface SpecialtyCardProps {
	specialty: {
		id: string;
		name: string;
		description: string | null;
	};
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
}

export function SpecialtyCard({
	specialty,
	onEdit,
	onDelete,
}: SpecialtyCardProps) {
	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<div className="flex items-center space-x-2">
					<Stethoscope className="h-4 w-4 text-muted-foreground" />
					<CardTitle className="font-medium text-sm">
						{specialty.name}
					</CardTitle>
				</div>
				<div className="flex items-center space-x-2">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => onEdit(specialty.id)}
					>
						<Edit2 className="h-4 w-4" />
					</Button>
					<Button
						variant="ghost"
						size="icon"
						onClick={() => onDelete(specialty.id)}
					>
						<Trash2 className="h-4 w-4 text-destructive" />
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<p className="text-muted-foreground text-sm">{specialty.description}</p>
			</CardContent>
		</Card>
	);
}
