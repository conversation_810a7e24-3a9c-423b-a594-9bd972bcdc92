// apps/web/app/[locale]/(saas)/app/specialties/components/specialty-form.tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@ui/components/button";
import {
	<PERSON>alog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";

const formSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	description: z.string().min(1, "Descrição é obrigatória"),
});

interface SpecialtyFormProps {
	specialty?: {
		id: string;
		name: string;
		description: string | null;
	};
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit: (
		data: z.infer<typeof formSchema> & { id?: string },
	) => Promise<void>;
	isLoading: boolean;
}

export function SpecialtyForm({
	specialty,
	open,
	onOpenChange,
	onSubmit,
	isLoading,
}: SpecialtyFormProps) {
	const t = useTranslations();

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: specialty?.name || "",
			description: specialty?.description || "",
		},
	});

	// Reset form when specialty changes
	useEffect(() => {
		if (open) {
			form.reset({
				name: specialty?.name || "",
				description: specialty?.description || "",
			});
		}
	}, [specialty, open, form]);

	const handleSubmit = async (data: z.infer<typeof formSchema>) => {
		await onSubmit({
			...(specialty?.id ? { id: specialty.id } : {}),
			...data,
		});
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>
						{specialty ? "Editar Especialidade" : "Nova Especialidade"}
					</DialogTitle>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleSubmit)}
						className="space-y-4"
					>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Nome</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Descrição</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button type="submit" className="w-full" disabled={isLoading}>
							{isLoading ? (
								<span className="flex items-center gap-2">
									<Loader2 className="h-4 w-4 animate-spin" />
									{specialty ? "Salvando..." : "Criando..."}
								</span>
							) : specialty ? (
								"Salvar Alterações"
							) : (
								"Criar Especialidade"
							)}
						</Button>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
