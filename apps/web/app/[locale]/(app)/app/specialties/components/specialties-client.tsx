// apps/web/app/[locale]/(saas)/app/specialties/components/specialties-client.tsx
"use client";

import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { DashboardHeader } from "@ui/components/header";
import { DeleteConfirmation } from "./delete-confirmation";
import { SpecialtyCard } from "./specialty-card";
import { SpecialtyForm } from "./specialty-form";

interface SpecialtiesClientProps {
	initialSpecialties: {
		id: string;
		name: string;
		description: string | null;
		searchCount: number;
		createdAt: Date;
	}[];
}

export function SpecialtiesClient({
	initialSpecialties,
}: SpecialtiesClientProps) {
	const router = useRouter();

	const [specialties, setSpecialties] = useState(initialSpecialties);
	const [formOpen, setFormOpen] = useState(false);
	const [deleteOpen, setDeleteOpen] = useState(false);
	const [selectedSpecialty, setSelectedSpecialty] = useState<any>(undefined);
	const [specialtyToDelete, setSpecialtyToDelete] = useState<
		string | undefined
	>();

	// Mutations setup
	const createSpecialtyMutation = apiClient.specialties.create.useMutation();
	const updateSpecialtyMutation = apiClient.specialties.update.useMutation();
	const deleteSpecialtyMutation = apiClient.specialties.delete_.useMutation();

	const handleEdit = (id: string) => {
		const specialty = specialties.find((s) => s.id === id);
		if (specialty) {
			setSelectedSpecialty(specialty);
			setFormOpen(true);
		}
	};

	const handleDeleteClick = (id: string) => {
		setSpecialtyToDelete(id);
		setDeleteOpen(true);
	};

	const handleDeleteConfirm = async () => {
		if (!specialtyToDelete) return;

		try {
			await deleteSpecialtyMutation.mutateAsync({
				id: specialtyToDelete,
			});

			setSpecialties(specialties.filter((s) => s.id !== specialtyToDelete));
			toast.success("Especialidade excluída com sucesso!");
			setDeleteOpen(false);
			setSpecialtyToDelete(undefined);
			router.refresh();
		} catch (error) {
			toast.error("Erro ao excluir especialidade");
		}
	};

	const handleCreateOrUpdate = async (data: {
		id?: string;
		name: string;
		description: string;
	}) => {
		try {
			if (data.id) {
				// Update
				const updated = await updateSpecialtyMutation.mutateAsync({
					id: data.id,
					name: data.name,
					description: data.description,
				});

				setSpecialties(
					specialties.map((s) => (s.id === data.id ? updated : s)),
				);
				toast.success("Especialidade atualizada com sucesso!");
			} else {
				// Create
				const created = await createSpecialtyMutation.mutateAsync({
					name: data.name,
					description: data.description,
				});

				setSpecialties([...specialties, created]);
				toast.success("Especialidade criada com sucesso!");
			}

			setFormOpen(false);
			setSelectedSpecialty(undefined);
			router.refresh();
		} catch (error) {
			toast.error(
				data.id
					? "Erro ao atualizar especialidade"
					: "Erro ao criar especialidade",
			);
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<DashboardHeader
					heading="Especialidades"
					text="Gerencie as especialidades médicas disponíveis para todos os hospitais."
				/>
				<Button
					onClick={() => {
						setSelectedSpecialty(undefined);
						setFormOpen(true);
					}}
				>
					<Plus className="mr-2 h-4 w-4" />
					Nova Especialidade
				</Button>
			</div>

			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
				{specialties.map((specialty) => (
					<SpecialtyCard
						key={specialty.id}
						specialty={specialty}
						onEdit={handleEdit}
						onDelete={handleDeleteClick}
					/>
				))}
			</div>

			<SpecialtyForm
				specialty={selectedSpecialty}
				open={formOpen}
				onOpenChange={(open) => {
					setFormOpen(open);
					if (!open) setSelectedSpecialty(undefined);
				}}
				onSubmit={handleCreateOrUpdate}
				isLoading={
					createSpecialtyMutation.isPending || updateSpecialtyMutation.isPending
				}
			/>

			<DeleteConfirmation
				open={deleteOpen}
				onOpenChange={setDeleteOpen}
				onConfirm={handleDeleteConfirm}
				isLoading={deleteSpecialtyMutation.isPending}
			/>
		</div>
	);
}
