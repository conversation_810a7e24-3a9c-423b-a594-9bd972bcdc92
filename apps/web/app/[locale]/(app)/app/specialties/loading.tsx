// apps/web/app/[locale]/(saas)/app/specialties/loading.tsx
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { DashboardHeader } from "@ui/components/header";
import { Skeleton } from "@ui/components/skeleton";

export default function Loading() {
	return (
		<>
			<DashboardHeader
				heading="Especialidades"
				text="<PERSON><PERSON><PERSON><PERSON> as especialidades médicas disponíveis para todos os hospitais."
			/>

			<div className="mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				{Array.from({ length: 6 }).map((_, i) => (
					<Card key={i}>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<Skeleton className="h-4 w-[150px]" />
							<div className="flex items-center space-x-2">
								<Skeleton className="h-8 w-8 rounded-md" />
								<Skeleton className="h-8 w-8 rounded-md" />
							</div>
						</CardHeader>
						<CardContent>
							<Skeleton className="mb-2 h-4 w-full" />
							<Skeleton className="h-4 w-[70%]" />
						</CardContent>
					</Card>
				))}
			</div>
		</>
	);
}
