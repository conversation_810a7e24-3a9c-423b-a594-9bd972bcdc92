"use client";
import React, { useEffect, useRef, useState } from "react";
import { useChat } from "@ai-sdk/react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@ui/components/sheet";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import { User, <PERSON><PERSON>, BotIcon } from "lucide-react";

type MessageRole = "user" | "assistant" | "system";

function MessageBubble({ role, content }: { role: MessageRole; content: string }) {
  const isUser = role === "user";
  return (
    <div className={`flex items-start gap-2 ${isUser ? "justify-end" : "justify-start"}`}>
      {!isUser && (
        <div className="mt-1 inline-flex h-6 w-6 items-center justify-center rounded-full bg-muted">
          <Bot className="h-4 w-4" />
        </div>
      )}
      <div className={isUser ? "max-w-[78%] rounded-2xl bg-primary text-primary-foreground px-4 py-2 shadow-sm" : "max-w-[78%] rounded-2xl bg-muted px-4 py-2 text-foreground shadow-sm"}>
        <div className="whitespace-pre-wrap leading-relaxed text-sm">{content}</div>
      </div>
      {isUser && (
        <div className="mt-1 inline-flex h-6 w-6 items-center justify-center rounded-full bg-primary/15 text-primary">
          <User className="h-4 w-4" />
        </div>
      )}
    </div>
  );
}

const SUGGESTIONS: string[] = [
  "Paciente 45a, dor torácica súbita e sudorese. Próximos passos?",
  "Criança 3a, febre há 2 dias e tosse. Sinais de alarme?",
  "Mulher 28a, dor QID há 24h. Diferenciais?",
  "Gerar resumo SOAP para dor lombar mecânica leve",
];

export default function DoctorAgentPage() {
  const [doctorName] = useState<string>("Dra. Zapvida");
  const [clinicName] = useState<string | undefined>("Zapvida Clinic");
  const [model, setModel] = useState<string>("gpt-4o-mini");
  const [agentSheetOpen, setAgentSheetOpen] = useState(false);
  const [agentCreateOpen, setAgentCreateOpen] = useState(false);

  const { messages, input, handleInputChange, handleSubmit, isLoading, setMessages, setInput } = useChat({
    api: "/api/doctor-agent",
    body: { doctorName, clinicName },
    sendExtraMessageFields: true,
  });

  useEffect(() => {
    if (messages.length === 0) {
      setMessages([
        {
          id: "intro",
          role: "assistant",
          content:
            "Olá! Sou seu assistente clínico com IA. Descreva o caso do paciente (queixa principal, antecedentes, sinais vitais) e eu ajudo com hipóteses, próximos passos e um resumo SOAP.",
        },
      ]);
    }
  }, [messages.length, setMessages]);

  const containerRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const el = containerRef.current;
    if (!el) return;
    el.scrollTop = el.scrollHeight;
  }, [messages, isLoading]);

  const onSuggestion = (s: string) => setInput(s);
  const onEnterSubmit: React.KeyboardEventHandler<HTMLTextAreaElement> = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      const form = e.currentTarget.closest("form");
      if (form) form.requestSubmit();
    }
  };

  return (
    <div className="fixed inset-0 left-[240px]   right-0 bottom-0 p-4">{/* fills viewport except sidebar/header */}
      <Card className="h-full border-none overflow-hidden shadow-lg flex flex-col">
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Agente Clínico (IA)</CardTitle>
              <p className="text-muted-foreground text-sm">Assistente para apoio ao atendimento.</p>
            </div>
            <div className="hidden md:flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Label className="text-xs hidden text-muted-foreground">Modelo</Label>
                <Select value={model} onValueChange={setModel}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Selecione LLM" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4o-mini">ChatGPT (OpenAI)</SelectItem>
                    <SelectItem value="claude-3-5-sonnet">Claude Sonnet</SelectItem>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* <Separator orientation="vertical" className="h-6" />
              {SUGGESTIONS.slice(0, 2).map((s) => (
                <Button key={s} variant="outline" size="sm" onClick={() => onSuggestion(s)}>
                  {s.length > 38 ? s.slice(0, 38) + "…" : s}
                </Button>
              ))} */}
              <Sheet open={agentSheetOpen} onOpenChange={setAgentSheetOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm"> <BotIcon className="w-4 h-4" /> { " "} Agentes IA</Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[420px] sm:w-[520px]">
                  <SheetHeader>
                    <SheetTitle>Configurar Agente</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6 space-y-6">
                    <div className="space-y-2">
                      <Label className="text-sm">Agente pronto</Label>
                      <Select defaultValue="clinico">
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="clinico">Clínico Geral (padrão)</SelectItem>
                          <SelectItem value="pediatria">Pediatria</SelectItem>
                          <SelectItem value="gineco">Ginecologia</SelectItem>
                          <SelectItem value="dermato">Dermatologia</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium">Criar agente personalizado</div>
                        <div className="text-xs text-muted-foreground">Defina objetivo, tom, ferramentas e instruções.</div>
                      </div>
                      <Button size="sm" onClick={() => setAgentCreateOpen(true)}>Criar</Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
              <Sheet open={agentCreateOpen} onOpenChange={setAgentCreateOpen}>
                <SheetContent side="right" className="w-full sm:max-w-[820px]">
                  <SheetHeader>
                    <SheetTitle>Novo agente</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6 grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label>Nome</Label>
                      <Textarea placeholder="Ex.: Agente de Triagem Pediátrica" className="min-h-[44px]" />
                    </div>
                    <div className="space-y-2">
                      <Label>Objetivo</Label>
                      <Textarea placeholder="Ex.: Triar sintomas, sugerir DDx e red flags." className="min-h-[44px]" />
                    </div>
                    <div className="space-y-2 sm:col-span-2">
                      <Label>Instruções (prompt do sistema)</Label>
                      <Textarea placeholder="Descreva o comportamento do agente, regras, segurança e formato de respostas." className="min-h-[160px]" />
                    </div>
                    <div className="space-y-2 sm:col-span-2">
                      <Label>Ferramentas (mock)</Label>
                      <Textarea placeholder="Ex.: quickSearch, guidelinesLookup" className="min-h-[96px]" />
                    </div>
                    <div className="sm:col-span-2 flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setAgentCreateOpen(false)}>Cancelar</Button>
                      <Button disabled>Salvar (em breve)</Button>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden p-0">
          <div ref={containerRef} className="h-full overflow-y-auto p-4 space-y-3 bg-background">
            {messages.map((m) => (
              <MessageBubble key={m.id} role={m.role as MessageRole} content={m.content} />
            ))}
            {isLoading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="size-2 rounded-full bg-muted-foreground/40 animate-bounce [animation-delay:-200ms]" />
                <div className="size-2 rounded-full bg-muted-foreground/40 animate-bounce [animation-delay:-100ms]" />
                <div className="size-2 rounded-full bg-muted-foreground/40 animate-bounce" />
                <span>Gerando resposta…</span>
              </div>
            )}
          </div>
        </CardContent>

        <form onSubmit={handleSubmit} className="border-t p-3 flex flex-col gap-2">
          <div className="flex flex-wrap gap-2 md:hidden">
            {SUGGESTIONS.slice(0, 3).map((s) => (
              <Button key={s} variant="outline" size="sm" onClick={() => onSuggestion(s)} type="button">
                {s.length > 30 ? s.slice(0, 30) + "…" : s}
              </Button>
            ))}
          </div>
          <div className="flex items-end gap-2">
            <Textarea
              value={input}
              onChange={handleInputChange}
              onKeyDown={onEnterSubmit}
              placeholder="Mensagem ao agente…"
              className="min-h-[52px] max-h-40 resize-y rounded-full px-5"
            />
            <Button type="submit" className="shrink-0 rounded-full px-6" disabled={isLoading || !input.trim()}>
              Enviar
            </Button>
          </div>
          <p className="text-[11px] text-muted-foreground">Este assistente não substitui o julgamento médico.</p>
        </form>
      </Card>
    </div>
  );
}


