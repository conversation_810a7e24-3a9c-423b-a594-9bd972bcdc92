'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@saas/auth/hooks/use-user';
import { RefreshCw } from 'lucide-react';

export default function ChatsRedirect() {
	const router = useRouter();
	const { user, loaded } = useUser();

	useEffect(() => {
		if (loaded && user) {
			// Redirecionar para o zapchat baseado no role do usuário
			if (user.role === 'PATIENT') {
				router.replace('/patient/zapchat');
			} else {
				router.replace('/app/zapchat');
			}
		}
	}, [loaded, user, router]);

	if (!loaded) {
		return (
			<div className="flex h-screen items-center justify-center bg-gray-50">
				<div className="text-center">
					<RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
					<p className="text-gray-600">Redirecionando para o ZapChat...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex h-screen items-center justify-center bg-gray-50">
			<div className="text-center">
				<RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
				<p className="text-gray-600">Redirecionando para o ZapChat...</p>
			</div>
		</div>
	);
}
