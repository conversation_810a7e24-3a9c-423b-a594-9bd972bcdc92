// hooks/use-chat.ts
import { useCallback, useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { Message } from "database";
import {
  getAppointmentMessages,
  sendTextMessage,
  sendAttachment as sendAttachmentAction,
  sendAudioMessage
} from "../../../../../../actions/appointments/messages/messages";

export function useChat(appointmentId: string, userId?: string) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Referências - create fresh client for each appointment
  const supabaseClient = useRef<any>(null);
  const channelRef = useRef<any>(null);
  const isMountedRef = useRef(true);
  const processedMessageIds = useRef(new Set<string>());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const MAX_RECONNECT_ATTEMPTS = 10;
  const isReconnectingRef = useRef(false);
  const currentAppointmentId = useRef<string | null>(null);

  // Função para verificar se uma mensagem já foi processada
  const isMessageProcessed = useCallback((messageId: string) => {
    return processedMessageIds.current.has(messageId);
  }, []);

  // Função para adicionar mensagem ao estado com verificação de duplicatas
  const addMessage = useCallback((message: Message) => {
    if (!message || !message.id) {
      console.warn("Tentativa de adicionar mensagem inválida:", message);
      return;
    }

    // Verificar se a mensagem já foi processada
    if (isMessageProcessed(message.id)) {
      console.log("Mensagem já processada, ignorando:", message.id);
      return;
    }

    // Marcar a mensagem como processada
    processedMessageIds.current.add(message.id);

    console.log("Adicionando mensagem ao estado:", message.id, "senderId:", message.senderId, "userId atual:", userId);

    // Atualizar o estado de mensagens
    setMessages(prevMessages => {
      // Verificação extra para garantir que não haja duplicatas
      if (prevMessages.some(m => m.id === message.id)) {
        console.log("Mensagem já existe no estado, ignorando:", message.id);
        return prevMessages;
      }

      // Ordenar por data de criação
      const updatedMessages = [...prevMessages, message].sort((a, b) => {
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      });

      return updatedMessages;
    });
  }, [userId]);

  // Função para carregar mensagens
  const loadMessages = useCallback(async () => {
    if (!appointmentId) {
      console.log("Sem appointmentId para carregar mensagens");
      setMessages([]);
      setIsLoading(false);
      return;
    }

    console.log(`Carregando mensagens para consulta: ${appointmentId}`);
    setIsLoading(true);

    try {
      const result = await getAppointmentMessages(appointmentId);

      if (!result) {
        throw new Error('Resposta inválida do servidor');
      }

      const { messages: data, error: fetchError } = result;

      if (fetchError) {
        throw new Error(fetchError);
      }

      if (!Array.isArray(data)) {
        console.error("Formato de dados inválido:", data);
        throw new Error('Formato de dados inválido recebido do servidor');
      }

      console.log(`${data.length} mensagens recebidas do servidor`);

      // Limpar o cache de IDs processados
      processedMessageIds.current.clear();

      // Processar mensagens
      if (isMountedRef.current) {
        // Marcar todas as mensagens como processadas
        data.forEach(msg => {
          if (msg && msg.id) {
            processedMessageIds.current.add(msg.id);
          }
        });

        setMessages(data);
        setIsLoading(false);
        setError(null);
      }
    } catch (err) {
      console.error("Erro ao carregar mensagens:", err);
      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error('Erro ao carregar mensagens'));
        setIsLoading(false);
      }
    }
  }, [appointmentId]);

  // Função para calcular o delay de reconexão com base em exponential backoff
  const getReconnectDelay = useCallback(() => {
    const attempt = reconnectAttemptsRef.current;
    // Tempo base: 1s * 2^tentativa com um jitter aleatório
    const baseTime = Math.min(1000 * Math.pow(2, attempt), 30000); // Máximo 30s
    const jitter = Math.random() * 1000; // Adiciona até 1s de aleatoriedade
    return baseTime + jitter;
  }, []);

  // Função que tenta reconectar o canal
  const reconnectChannel = useCallback(() => {
    if (!isMountedRef?.current || !appointmentId) return;

    // Se já estiver tentando reconectar, não inicie outra tentativa
    if (isReconnectingRef.current) {
      console.log("Já existe uma tentativa de reconexão em andamento");
      return;
    }

    isReconnectingRef.current = true;

    // Limpar qualquer timeout pendente
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Calcular delay baseado em tentativas anteriores
    const delay = getReconnectDelay();
    reconnectAttemptsRef.current += 1;

    console.log(`Tentando reconectar em ${delay}ms (tentativa ${reconnectAttemptsRef.current})`);

    // Se excedeu o número máximo de tentativas, parar
    if (reconnectAttemptsRef.current > MAX_RECONNECT_ATTEMPTS) {
      console.warn(`Máximo de ${MAX_RECONNECT_ATTEMPTS} tentativas excedido. Parando reconexões automáticas.`);
      isReconnectingRef.current = false;
      return;
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      console.log(`Executando reconexão (tentativa ${reconnectAttemptsRef.current})`);
      setupRealtimeChannel();
      isReconnectingRef.current = false;
    }, delay);
  }, [appointmentId, getReconnectDelay]);

  // Configurar canal do Supabase Realtime
  const setupRealtimeChannel = useCallback(() => {
    if (!appointmentId) {
      console.log("Sem appointmentId para configurar canal realtime");
      return null;
    }

    // Check if we're switching appointments - if so, clean up completely
    if (currentAppointmentId.current && currentAppointmentId.current !== appointmentId) {
      console.log(`Switching from appointment ${currentAppointmentId.current} to ${appointmentId}`);

      // Clean up previous channel and client
      if (channelRef.current && supabaseClient.current) {
        try {
          console.log("Removing previous channel during appointment switch");
          supabaseClient.current.removeChannel(channelRef.current);
        } catch (error) {
          console.error("Erro ao remover canal anterior:", error);
        }
      }

      // Reset refs and clear processed messages
      channelRef.current = null;
      supabaseClient.current = null;
      processedMessageIds.current.clear();

      // Reset reconnection state
      reconnectAttemptsRef.current = 0;
      isReconnectingRef.current = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    }

    // Update current appointment ID
    currentAppointmentId.current = appointmentId;

    // Remover canal existente se houver
    if (channelRef.current) {
      console.log("Removendo canal existente antes de criar um novo");
      try {
        if (supabaseClient.current) {
          supabaseClient.current.removeChannel(channelRef.current);
        }
      } catch (error) {
        console.error("Erro ao remover canal existente:", error);
      }
      channelRef.current = null;
    }

    // Nome do canal
    const channelName = `messages-${appointmentId}`;
    console.log(`Configurando canal Supabase: ${channelName}`);

    try {
      // Create fresh client for each appointment
      console.log("Criando novo cliente Supabase para appointment:", appointmentId);
      supabaseClient.current = createRealtimeClient();

      // Criar um novo canal
      const channel = supabaseClient.current
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `appointmentId=eq.${appointmentId}`  // IMPORTANTE: Usando camelCase para corresponder ao banco
          },
          (payload: any) => {
            console.log("Evento de mensagem recebido:", payload);

            if (!payload.new) {
              console.warn("Payload sem dados 'new'");
              return;
            }

            const newMessage = payload.new as Message;

            // Verificar se a mensagem é válida
            if (!newMessage || !newMessage.id) {
              console.error("Mensagem inválida recebida via realtime:", newMessage);
              return;
            }

            console.log("Mensagem recebida via realtime:", newMessage.id);

            // Se estiver montado, adicionar a mensagem
            if (isMountedRef.current) {
              addMessage(newMessage);
            }
          }
        )
        .subscribe((status: string) => {
          console.log(`Status de inscrição: ${status} para appointment: ${appointmentId}`);

          if (status === 'SUBSCRIBED') {
            console.log(`Canal inscrito com sucesso: ${channelName}`);
            // Resetar contador de tentativas quando conectar com sucesso
            reconnectAttemptsRef.current = 0;
          } else if (status === 'CHANNEL_ERROR') {
            console.error(`Erro no canal: ${status}`);
            if (isMountedRef.current && !isReconnectingRef.current) {
              // Iniciar processo de reconexão com backoff exponencial
              reconnectChannel();
            }
          } else if (status === 'TIMED_OUT') {
            console.warn(`Timeout no canal: ${status}`);
            if (isMountedRef.current && !isReconnectingRef.current) {
              reconnectChannel();
            }
          } else if (status === 'CLOSED') {
            console.warn(`Canal fechado: ${status} - isso é normal durante mudanças de appointment`);
            // Don't reconnect on CLOSED status as it's often intentional during cleanup
          }
        });

      channelRef.current = channel;
      return channel;
    } catch (error) {
      console.error("Erro ao configurar canal realtime:", error);

      // Tentar reconectar mesmo em caso de erro na configuração
      if (isMountedRef.current) {
        reconnectChannel();
      }

      return null;
    }
  }, [appointmentId, addMessage, reconnectChannel]);

  // Effect principal - carrega mensagens e configura o canal
  useEffect(() => {
    if (!appointmentId) {
      console.log("Effect: sem appointmentId, resetando estado");
      setMessages([]);
      setIsLoading(false);
      setError(null);
      currentAppointmentId.current = null;
      return;
    }

    console.log(`Effect principal executando para appointmentId: ${appointmentId}`);

    // Reset state when switching appointments
    if (currentAppointmentId.current !== appointmentId) {
      console.log(`Resetting state for new appointment: ${appointmentId}`);
      setMessages([]);
      setIsLoading(true);
      setError(null);
    }

    // Resetar contadores de tentativas quando mudar o appointmentId
    reconnectAttemptsRef.current = 0;
    isReconnectingRef.current = false;

    // Limpar timeout de reconexão existente
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Carregar mensagens iniciais
    loadMessages();

    // Configurar canal realtime
    const channel = setupRealtimeChannel();

    // Cleanup
    return () => {
      console.log(`Cleanup do effect para appointmentId: ${appointmentId}`);

      // Limpar timeout de reconexão
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (channel) {
        console.log("Removendo canal no cleanup");
        try {
          supabaseClient.current.removeChannel(channel);
        } catch (error) {
          console.error("Erro ao remover canal no cleanup:", error);
        }
        channelRef.current = null;
      }
    };
  }, [appointmentId, loadMessages, setupRealtimeChannel, reconnectChannel]);

  // Função para enviar mensagem de texto
  const sendMessage = async (content: string) => {
    if (!content.trim() || !appointmentId || !userId) {
      console.error("Erro ao enviar mensagem: Dados incompletos", { content, appointmentId, userId });
      toast.error("Não foi possível enviar a mensagem");
      return;
    }

    console.log(`[sendMessage] Enviando mensagem para ${appointmentId} como usuário ${userId}`);

    try {
      // Chamar API para enviar mensagem
      const result = await sendTextMessage(appointmentId, content);

      console.log("[sendMessage] Resultado:", result);

      if (!result.success || result.error) {
        console.error(`[sendMessage] Erro no envio: ${result.error || "Desconhecido"}`);
        throw new Error(result.error || "Erro ao enviar mensagem");
      }

      // Verificar se a mensagem foi retornada corretamente
      if (!result.message || !result.message.id) {
        console.error("[sendMessage] Resposta sem mensagem válida:", result);
        throw new Error('Formato de resposta inválido');
      }

      const message = result.message;
      console.log(`[sendMessage] Enviada com sucesso: ${message.id}`);
      console.log(`[sendMessage] Detalhes - senderId: ${message.senderId}, userId: ${userId}`);

      return message;
    } catch (error) {
      console.error("[sendMessage] Erro:", error);
      toast.error("Erro ao enviar mensagem");
      throw error;
    }
  };

  // Função para enviar áudio
  const sendAudio = async (audioBlob: Blob) => {
    if (!audioBlob || !appointmentId) {
      toast.error("Não foi possível enviar o áudio");
      return;
    }

    console.log(`Enviando áudio para ${appointmentId}`);

    try {
      const arrayBuffer = await audioBlob.arrayBuffer();

      // Chamar API para enviar áudio
      const result = await sendAudioMessage(appointmentId, arrayBuffer);

      console.log("Resultado da API sendAudioMessage:", result);

      if (!result.success || result.error) {
        console.error(`Erro no envio de áudio: ${result.error || "Desconhecido"}`);
        throw new Error(result.error || "Erro ao enviar áudio");
      }

      // Verificar se a mensagem foi retornada corretamente
      if (!result.message || !result.message.id) {
        console.error("Resposta sem mensagem válida:", result);
        throw new Error('Formato de resposta inválido');
      }

      const message = result.message;
      console.log(`Áudio enviado com sucesso: ${message.id}`);

      // Adicionar a mensagem ao estado imediatamente, sem esperar pelo evento realtime
      addMessage(message);

      return message;
    } catch (error) {
      console.error("Erro ao enviar áudio:", error);
      toast.error("Erro ao enviar áudio");
      throw error;
    }
  };

  // Função para enviar anexo
  const sendAttachment = async (file: File) => {
    if (!file || !appointmentId) {
      toast.error("Não foi possível enviar o arquivo");
      return;
    }

    console.log(`Enviando anexo para ${appointmentId}`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    try {
      const arrayBuffer = await file.arrayBuffer();

      // Chamar API para enviar anexo
      const result = await sendAttachmentAction(
        appointmentId,
        file,
        file.type,
        file.name,
        file.size,
        arrayBuffer
      );

      console.log("Resultado da API sendAttachment:", result);

      if (!result.success || result.error) {
        console.error(`Erro no envio de anexo: ${result.error || "Desconhecido"}`);
        throw new Error(result.error || "Erro ao enviar arquivo");
      }

      // Verificar se a mensagem foi retornada corretamente
      if (!result.message || !result.message.id) {
        console.error("Resposta sem mensagem válida:", result);
        throw new Error('Formato de resposta inválido');
      }

      const message = result.message;
      console.log(`Anexo enviado com sucesso: ${message.id}`);

      // Adicionar a mensagem ao estado imediatamente, sem esperar pelo evento realtime
      addMessage(message);

      return message;
    } catch (error) {
      console.error("Erro ao enviar anexo:", error);
      toast.error("Erro ao enviar arquivo");
      throw error;
    }
  };

  // Função para atualizar mensagens manualmente e reconectar canal
  const refreshMessages = useCallback(async () => {
    console.log("Atualizando mensagens manualmente e reconectando canal");

    // Recriar client Supabase para garantir uma conexão limpa
    supabaseClient.current = createRealtimeClient();

    // Resetar contador de tentativas de reconexão
    reconnectAttemptsRef.current = 0;
    isReconnectingRef.current = false;

    // Reconfigurar canal e recarregar mensagens
    setupRealtimeChannel();
    await loadMessages();
  }, [loadMessages, setupRealtimeChannel]);

  // Limpar recursos ao desmontar
  useEffect(() => {
    isMountedRef.current = true;
    console.log("Componente montado");

    return () => {
      isMountedRef.current = false;
      console.log("Componente desmontado");

      // Limpar timeout de reconexão
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (channelRef.current) {
        console.log("Removendo canal ao desmontar componente");
        try {
          supabaseClient.current.removeChannel(channelRef.current);
        } catch (error) {
          console.error("Erro ao remover canal na desmontagem:", error);
        }
        channelRef.current = null;
      }
    };
  }, []);

  // Verificar status do canal a cada 30 segundos (reduzido para ser mais responsivo)
  useEffect(() => {
    if (!appointmentId) return;

    const intervalId = setInterval(() => {
      if (channelRef.current) {
        try {
          const channelState = channelRef.current.state;
          console.log(`[Health Check] Estado do canal: ${channelState}`);

          // Se o canal não estiver em um estado bom, tentar reconectar
          if (channelState !== "joined" && channelState !== "joining") {
            console.log("[Health Check] Canal não está em bom estado. Tentando reconectar...");
            setupRealtimeChannel();
          }
        } catch (error) {
          console.error("[Health Check] Erro ao verificar estado do canal:", error);
          // Se ocorrer erro ao verificar estado, tentar recriar canal
          setupRealtimeChannel();
        }
      } else {
        console.log("[Health Check] Canal não existe. Criando novo canal...");
        setupRealtimeChannel();
      }
    }, 30000); // Verificar a cada 30 segundos

    return () => clearInterval(intervalId);
  }, [appointmentId, setupRealtimeChannel]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    sendAudio,
    sendAttachment,
    refreshMessages
  };
}
