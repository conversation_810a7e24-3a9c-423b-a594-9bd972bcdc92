import { useState } from 'react'
import { IconSearch, IconLoader2, IconMessages } from '@tabler/icons-react'
import { Button } from '@ui/components/button'
import { Input } from '@ui/components/input'
import { ScrollArea } from '@ui/components/scroll-area'
import { cn } from '@ui/lib'
import { format, isToday, formatDistanceToNow } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Skeleton } from '@ui/components/skeleton'
import { Badge } from '@ui/components/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar'
import { MagnifyingGlassIcon, PlusIcon } from '@radix-ui/react-icons'
import { Loader2, LifeBuoy, AlertCircle, AlertTriangle, Info } from 'lucide-react'

interface AppointmentChat {
  id: string
  patient: {
    user: {
      id: string
      name: string
      avatarUrl?: string
    }
  }
  doctor: {
    user: {
      id: string
      name: string
      avatarUrl?: string
    }
  }
  scheduledAt: string
  status: string
  updatedAt: string
  messages: any[]
  // Campos específicos para plantão
  isOnDuty?: boolean
  urgencyLevel?: 'HIGH' | 'MEDIUM' | 'LOW' | null
  acceptedAt?: string | null
  acceptedByDoctorId?: string | null
}

interface ChatListProps {
  chats: AppointmentChat[]
  selectedAppointmentId: string | null
  isLoading: boolean
  loadingMore?: boolean
  hasMore?: boolean
  error: string | null
  userRole: 'DOCTOR' | 'PATIENT' | null
  search: string
  onSearchChange: (value: string) => void
  onSelectAppointment: (id: string) => void
  onCreateChat: () => void
  onLoadMore?: () => void
}

// Skeleton component for loading state
function ChatListSkeleton() {
  return (
    <div className="space-y-2 p-2 px-3">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="flex items-start gap-3 rounded-lg p-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-16" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function ChatList({
  chats,
  selectedAppointmentId,
  isLoading,
  loadingMore = false,
  hasMore = false,
  error,
  userRole,
  search,
  onSearchChange,
  onSelectAppointment,
  onCreateChat,
  onLoadMore
}: ChatListProps) {
  // Filter chats based on search
  const filteredChats = chats.filter(chat => {
    const patientName = chat.patient?.user?.name?.toLowerCase() || ''
    const doctorName = chat.doctor?.user?.name?.toLowerCase() || ''
    const name = userRole === 'DOCTOR' ? patientName : doctorName
    return name.includes(search.toLowerCase())
  })

  return (
    <div className="flex  h-[90%] min-h-0 flex-col">
           <div className='flex  px-4 py-3 items-center gap-2'>
           <IconMessages size={20} /> <h1 className='text-2xl font-bold'>Atendimentos</h1>

          </div>
      <div className="flex items-center gap-2 p-4">




        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Buscar..."
            className="pl-8"
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

      </div>

      <div className="border-t pt-4 px-4 text-sm text-muted-foreground">
        {filteredChats.length} conversas
      </div>

      {isLoading ? (
        <ScrollArea className="flex-1">
          <ChatListSkeleton />
        </ScrollArea>
      ) : error ? (
        <div className="flex flex-1 flex-col items-center justify-center py-6 text-center">
          <div className="mb-2 rounded-full bg-destructive/10 p-3 text-destructive">
            <MagnifyingGlassIcon className="h-6 w-6" />
          </div>
          <h3 className="text-base font-medium">Erro ao carregar conversas</h3>
          <p className="mt-2 max-w-xs text-sm text-muted-foreground">
            {error}
          </p>
        </div>
      ) : filteredChats.length === 0 ? (
        <div className="flex flex-1 flex-col items-center justify-center py-6 text-center">
          <div className="mb-2 rounded-full bg-secondary p-3">
            <MagnifyingGlassIcon className="h-6 w-6" />
          </div>
          <h3 className="text-base font-medium">Nenhuma conversa encontrada</h3>
          <p className="mt-2 max-w-xs text-sm text-muted-foreground">
            {search
              ? 'Tente outro termo de busca'
              : 'Inicie uma nova conversa com o botão +'}
          </p>
        </div>
      ) : (
        <ScrollArea className="flex-1 p-2 px-3">
          <div className="space-y-2">
            {filteredChats.map((chat) => {
              const name = userRole === 'DOCTOR'
                ? chat.patient?.user?.name
                : chat.doctor?.user?.name
              const avatar = userRole === 'DOCTOR'
                ? chat.patient?.user?.avatarUrl
                : chat.doctor?.user?.avatarUrl
              const initial = name ? name.charAt(0) : '?'

              // Função para obter cor, texto e ícone da urgência
              const getUrgencyBadge = (urgencyLevel: string | null | undefined) => {
                switch (urgencyLevel) {
                  case 'HIGH':
                    return {
                      text: 'Muito Urgente',
                      color: 'bg-red-500 hover:bg-red-600 text-white border-red-500',
                      icon: <AlertCircle className="h-3 w-3" />
                    }
                  case 'MEDIUM':
                    return {
                      text: 'Urgente',
                      color: 'bg-orange-500 hover:bg-orange-600 text-white border-orange-500',
                      icon: <AlertTriangle className="h-3 w-3" />
                    }
                  case 'LOW':
                    return {
                      text: 'Pouco Urgente',
                      color: 'bg-blue-500 hover:bg-blue-600 text-white border-blue-500',
                      icon: <Info className="h-3 w-3" />
                    }
                  default:
                    return null
                }
              }

              // Função para obter informações de aceitação
              const getAcceptanceInfo = (chat: AppointmentChat) => {
                if (!chat.isOnDuty) return null

                if (chat.acceptedAt && chat.acceptedByDoctorId) {
                  const acceptedDate = new Date(chat.acceptedAt)
                  return {
                    text: `Aceito ${formatDistanceToNow(acceptedDate, { addSuffix: true, locale: ptBR })}`,
                    color: 'bg-green-100 text-green-800 border-green-200'
                  }
                }

                return {
                  text: 'Aguardando aceitação',
                  color: 'bg-yellow-100 text-yellow-800 border-yellow-200'
                }
              }

              const urgencyBadge = chat.isOnDuty ? getUrgencyBadge(chat.urgencyLevel) : null
              const acceptanceInfo = getAcceptanceInfo(chat)

              return (
                <button
                  key={chat.id}
                  className={cn(
                    'flex w-full items-start gap-3 rounded-lg p-3 pl-4 text-left transition-colors hover:bg-muted/50',
                    selectedAppointmentId === chat.id && 'bg-muted',
                    chat.isOnDuty && 'border-l-4 border-l-blue-500' // Indicador visual para plantão
                  )}
                  onClick={() => onSelectAppointment(chat.id)}
                >
                 
                  <div className="flex-1 overflow-hidden">
                    {/* Show urgency badge above patient name for plantão */}
                    {chat.isOnDuty && urgencyBadge && (
                      <div className="mb-1">
                        <Badge className={cn("text-xs flex items-center gap-1 w-fit", urgencyBadge.color)}>
                          {urgencyBadge.icon}
                          {urgencyBadge.text}
                        </Badge>
                      </div>
                    )}

                    <div className="flex flex-col justify-between">
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <p className="truncate font-medium text-sm">{name}</p>
                        
                      </div>
                      
                    </div>

                    {/* Streamlined info: only date/time */}
                    <div className="mt-1">
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(chat.scheduledAt), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                      </p>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>

          {/* Load more button */}
          {hasMore && (
            <div className="flex justify-center py-4">
              <Button
                variant="outline"
                size="sm"
                onClick={onLoadMore}
                disabled={loadingMore}
              >
                {loadingMore ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Carregando...
                  </>
                ) : (
                  'Carregar mais'
                )}
              </Button>
            </div>
          )}
        </ScrollArea>
      )}
    </div>
  )
}
