'use client';

import { Skeleton } from '@ui/components/skeleton';
import { cn } from '@ui/lib';

// Individual skeleton components for different parts of the chat

export function ChatHeaderSkeleton() {
	return (
		<div className='bg-white border-b px-4 sm:px-8 py-3 sticky top-0 z-10'>
			<div className='flex items-center justify-between'>
				<div className='flex items-center gap-2 sm:gap-3'>
					{/* Back button skeleton */}
					<Skeleton className='h-8 w-8 rounded-md' />
					{/* Avatar skeleton */}
					<Skeleton className='h-10 w-10 rounded-full flex-shrink-0' />
					<div className='min-w-0 space-y-2'>
						{/* Name skeleton */}
						<Skeleton className='h-5 w-32' />
						{/* Info skeleton */}
						<Skeleton className='h-4 w-24' />
					</div>
				</div>
				{/* Action buttons skeleton */}
				<div className='flex items-center gap-2'>
					<Skeleton className='h-8 w-8 rounded-md' />
					<Skeleton className='h-8 w-8 rounded-md' />
				</div>
			</div>
		</div>
	);
}

export function MessageSkeleton({
	isMyMessage = false,
	messageType = 'text',
	width = 'normal'
}: {
	isMyMessage?: boolean;
	messageType?: 'text' | 'long' | 'short';
	width?: 'normal' | 'wide' | 'narrow';
}) {
	// Vary message widths for more realistic appearance
	const getMessageWidth = () => {
		if (messageType === 'long') return isMyMessage ? 'w-40' : 'w-48';
		if (messageType === 'short') return isMyMessage ? 'w-16' : 'w-20';
		if (width === 'wide') return isMyMessage ? 'w-36' : 'w-40';
		if (width === 'narrow') return isMyMessage ? 'w-20' : 'w-24';
		return isMyMessage ? 'w-28' : 'w-32';
	};

	return (
		<div
			className={cn(
				'flex items-end gap-2 mb-4',
				isMyMessage ? 'justify-end' : 'justify-start'
			)}
		>
			{/* Avatar skeleton - only for other messages */}
			{!isMyMessage && (
				<Skeleton className='h-8 w-8 rounded-full mb-1' />
			)}

			<div
				className={cn(
					'flex flex-col max-w-[70%]',
					isMyMessage ? 'items-end' : 'items-start'
				)}
			>
				{/* Sender name skeleton - only for other messages */}
				{!isMyMessage && (
					<Skeleton className='h-3 w-16 mb-1' />
				)}

				{/* Message bubble skeleton */}
				<div
					className={cn(
						'rounded-lg p-3',
						isMyMessage
							? 'bg-muted rounded-tr-none'
							: 'bg-muted rounded-tl-none'
					)}
				>
					<Skeleton className={cn('h-4', getMessageWidth())} />
					{/* Add second line for longer messages */}
					{messageType === 'long' && (
						<Skeleton className={cn('h-4 mt-2', isMyMessage ? 'w-24' : 'w-28')} />
					)}
				</div>

				{/* Timestamp skeleton */}
				<Skeleton className='h-3 w-12 mt-1' />
			</div>
		</div>
	);
}

export function MessageListSkeleton() {
	return (
		<div className='flex-1 overflow-hidden'>
			<div className='h-full overflow-y-auto p-4 space-y-4'>
				{/* Generate a realistic mix of sent and received message skeletons with varied lengths */}
				<MessageSkeleton isMyMessage={false} messageType="short" />
				<MessageSkeleton isMyMessage={true} messageType="text" width="wide" />
				<MessageSkeleton isMyMessage={false} messageType="long" />
				<MessageSkeleton isMyMessage={false} messageType="text" width="narrow" />
				<MessageSkeleton isMyMessage={true} messageType="short" />
				<MessageSkeleton isMyMessage={false} messageType="text" />
				<MessageSkeleton isMyMessage={true} messageType="long" />
				<MessageSkeleton isMyMessage={true} messageType="text" width="narrow" />
				<MessageSkeleton isMyMessage={false} messageType="text" width="wide" />
				<MessageSkeleton isMyMessage={true} messageType="short" />
			</div>
		</div>
	);
}

export function InputAreaSkeleton() {
	return (
		<div className='p-3 py-5 border-t bg-background'>
			<div className='flex items-center gap-2'>
				{/* Attachment button skeleton */}
				<Skeleton className='h-10 w-10 rounded-md shrink-0' />
				
				{/* Audio button skeleton */}
				<Skeleton className='h-10 w-10 rounded-md shrink-0' />
				
				{/* Input field skeleton */}
				<Skeleton className='flex-1 h-14 rounded-full' />
				
				{/* Send button skeleton */}
				<Skeleton className='h-14 w-14 rounded-full shrink-0' />
			</div>
		</div>
	);
}

// Main chat skeleton component that combines all parts
export function ChatSkeleton() {
	return (
		<div className='flex h-full flex-col bg-background animate-in fade-in-0 duration-300'>
			{/* Header skeleton */}
			<ChatHeaderSkeleton />

			{/* Messages area skeleton */}
			<MessageListSkeleton />

			{/* Input area skeleton */}
			<InputAreaSkeleton />
		</div>
	);
}
