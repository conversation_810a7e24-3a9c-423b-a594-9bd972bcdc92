'use client';

import { useState, useRef, useEffect, useMemo } from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
	FileText,
	Loader2,
	MessageSquare,
	Mic,
	Paperclip,
	RefreshCw,
	Send,
	File as FileIcon,
	Download,
	X,
	Image as ImageIcon,
	Phone as LucidePhone,
	Video as LucideVideo,
	FileText as LucideFileText,
	Eye,
	Stethoscope,
} from 'lucide-react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Button } from '@ui/components/button';
import { ScrollArea } from '@ui/components/scroll-area';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from '@ui/components/dialog';
import { cn } from '@ui/lib';
import { getCompleteImageUrl } from '@lib/image-utils';
import { SignedAvatar } from '../../../../../../components/shared/signed-avatar';
import { UserAvatar } from '@shared/components/UserAvatar';
import { ChatHeader } from './chat-header';
import { ChatSkeleton } from './chat-skeleton';

// Define basic message type
interface Message {
	id: string;
	content: string;
	createdAt: string;
	senderId: string;
	type: 'TEXT' | 'AUDIO' | 'ATTACHMENT' | 'SYSTEM' | 'FILE';
	senderRole?: string;
	fileName?: string;
	fileType?: string;
	duration?: number;
	metadata?: {
		fileName?: string;
		fileType?: string;
		contentType?: string;
		duration?: number;
	};
}

export interface ChatInterfaceProps {
	appointmentId: string;
	messages: any[];
	isLoading: boolean;
	error?: string;
	recipientName?: string;
	recipientImage?: string | null;
	recipientInfo?: string;
	appointmentDate?: string | null;
	currentUserId: string;
	userRole?: 'DOCTOR' | 'PATIENT';
	userId?: string;
	onSendMessage: (text: string) => Promise<any>;
	onSendAudio: (audioBlob: Blob) => Promise<any>;
	onSendAttachment: (file: File) => Promise<any>;
	refreshMessages?: () => Promise<void>;
	onVideoCall?: () => void;
	onAudioCall?: () => void;
	onMedicalRecord?: () => void;
	onBack?: () => void; // Função para voltar à tela anterior
	// Campos específicos para plantão
	isOnDuty?: boolean;
	urgencyLevel?: 'HIGH' | 'MEDIUM' | 'LOW' | null;
	acceptedAt?: string | null;
}

export function ChatInterface({
	appointmentId,
	messages,
	isLoading,
	error,
	recipientName,
	recipientImage,
	recipientInfo,
	appointmentDate,
	currentUserId,
	userRole,
	userId,
	onSendMessage,
	onSendAudio,
	onSendAttachment,
	refreshMessages,
	onVideoCall,
	onAudioCall,
	onMedicalRecord,
	onBack,
	isOnDuty,
	urgencyLevel,
	acceptedAt,
}: ChatInterfaceProps) {
	const [messageText, setMessageText] = useState('');
	const [isSending, setIsSending] = useState(false);
	const [isRecording, setIsRecording] = useState(false);
	const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
	const [reconnecting, setReconnecting] = useState(false);
	const [tempMessage, setTempMessage] = useState<Message | null>(null);

	// Combine messages with temporary message for optimistic updates
	const displayMessages = useMemo(() => {
		if (!tempMessage) return messages;

		// Only add the temporary message if there's no matching message already in the messages array
		const messageExists = messages.some(
			(m) => m.content === tempMessage.content && m.senderId === currentUserId
		);

		return messageExists ? messages : [...messages, tempMessage];
	}, [messages, tempMessage, currentUserId]);

	const fileInputRef = useRef<HTMLInputElement>(null);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const mediaRecorderRef = useRef<MediaRecorder | null>(null);
	const audioChunksRef = useRef<Blob[]>([]);
	const [showMedicalRecordDialog, setShowMedicalRecordDialog] = useState(false);

	// Function to scroll to the bottom of the chat
	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({
			behavior: 'smooth',
			block: 'end',
		});
	};

	// Scroll to bottom when messages change
	useEffect(() => {
		if (!isLoading && displayMessages?.length > 0) {
			scrollToBottom();
		}
	}, [displayMessages, isLoading]);

	// Handle errors with layout consistency
	useEffect(() => {
		if (error) {
			// Log error for debugging
			console.error('Chat error:', error);

			// Make sure we scroll to proper position even with error state
			setTimeout(() => {
				const chatContainer = document.querySelector('.chat-container');
				if (chatContainer) {
					chatContainer.scrollTop = 0;
				}
			}, 100);
		}
	}, [error]);



	const handleSendMessageSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!messageText.trim() || isSending) return;

		// Generate a temporary ID for the optimistic message
		const tempId = `temp-${Date.now()}`;

		const optimisticMessage = {
			id: tempId,
			content: messageText,
			createdAt: new Date().toISOString(),
			senderId: currentUserId,
			type: 'TEXT' as const,
			senderRole: userRole,
		};

		// Clear input immediately
		const messageToSend = messageText;
		setMessageText('');

		// Optimistically add message to UI
		setTempMessage(optimisticMessage);

		// Send message in the background
		setIsSending(true);
		try {
			const resultMessage = await onSendMessage(messageToSend);

			// After successful send, remove temp message - server will push real one
			setTempMessage(null);
		} catch (error) {
			console.error('[sendMessage] Erro:', error);
			toast.error('Falha ao enviar mensagem');

			// Remove the optimistic message if there was an error
			setTempMessage(null);

			// Restore the message text so the user can try again
			setMessageText(messageToSend);
		} finally {
			setIsSending(false);
		}
	};

	const handleFileUpload = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		// Create optimistic attachment message
		const tempId = `temp-${Date.now()}`;
		const optimisticMessage = {
			id: tempId,
			content: URL.createObjectURL(file),
			createdAt: new Date().toISOString(),
			senderId: currentUserId,
			type: 'FILE' as const,
			senderRole: userRole,
			metadata: {
				fileName: file.name,
				fileType: file.type,
				contentType: file.type,
			},
		};

		// Show optimistic message immediately
		setTempMessage(optimisticMessage);

		// Send file in background
		setIsSending(true);
		try {
			await onSendAttachment(file);
			// After successful send, remove temp message - server will push real one
			setTempMessage(null);

			if (fileInputRef.current) {
				fileInputRef.current.value = '';
			}
		} catch (error) {
			console.error('Error uploading file:', error);
			toast.error(
				error instanceof Error ? error.message : 'Erro ao enviar arquivo'
			);

			// Remove optimistic message on error
			setTempMessage(null);
		} finally {
			setIsSending(false);
		}
	};

	// Audio recording functions
	const startRecording = async () => {
		try {
			const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
			const mediaRecorder = new MediaRecorder(stream);
			mediaRecorderRef.current = mediaRecorder;
			audioChunksRef.current = [];

			mediaRecorder.ondataavailable = (event) => {
				if (event.data.size > 0) {
					audioChunksRef.current.push(event.data);
				}
			};

			mediaRecorder.onstop = () => {
				const audioBlob = new Blob(audioChunksRef.current, {
					type: 'audio/webm',
				});
				setAudioBlob(audioBlob);
				// Stop all tracks from the stream
				stream.getTracks().forEach((track) => track.stop());
			};

			mediaRecorder.start();
			setIsRecording(true);
		} catch (error) {
			console.error('Error starting recording:', error);
			toast.error('Erro ao iniciar gravação de áudio');
		}
	};

	const stopRecording = () => {
		if (mediaRecorderRef.current && isRecording) {
			mediaRecorderRef.current.stop();
			setIsRecording(false);
		}
	};

	const cancelRecording = () => {
		if (mediaRecorderRef.current && isRecording) {
			mediaRecorderRef.current.stop();
			setIsRecording(false);
			setAudioBlob(null);
		}
	};

	const resetRecording = () => {
		setAudioBlob(null);
	};

	const handleSendAudioSubmit = async () => {
		if (!audioBlob) return;

		// Create optimistic audio message
		const tempId = `temp-${Date.now()}`;
		const optimisticMessage = {
			id: tempId,
			content: URL.createObjectURL(audioBlob),
			createdAt: new Date().toISOString(),
			senderId: currentUserId,
			type: 'AUDIO' as const,
			senderRole: userRole,
			metadata: {
				duration: 0,
				fileType: 'audio/webm',
			},
		};

		// Show optimistic message immediately
		setTempMessage(optimisticMessage);

		// Reset audio recording UI
		resetRecording();

		// Send audio in background
		setIsSending(true);
		try {
			await onSendAudio(audioBlob);
			// After successful send, remove temp message - server will push real one
			setTempMessage(null);
		} catch (error) {
			console.error('Error sending audio:', error);
			toast.error('Falha ao enviar áudio');

			// Remove optimistic message on error
			setTempMessage(null);
		} finally {
			setIsSending(false);
		}
	};

	const handleReconnect = async () => {
		if (!refreshMessages) return;
		setReconnecting(true);
		try {
			await refreshMessages();
		} catch (error) {
			console.error('Error reconnecting:', error);
		} finally {
			setReconnecting(false);
		}
	};

	// Render different types of messages
	const renderMessageContent = (message: any) => {
		if (!message || !message.type) {
			return <p className='text-red-500 text-xs italic'>Mensagem inválida</p>;
		}

		switch (message.type) {
			case 'AUDIO':
				return (
					<div className='w-full min-w-[200px] max-w-[300px] overflow-hidden rounded-lg'>
						<audio controls preload='metadata' className='h-[36px] w-full'>
							<source
								src={message.content}
								type={
									message.metadata?.fileType || message.fileType || 'audio/webm'
								}
							/>
							Seu navegador não suporta o elemento de áudio.
						</audio>
					</div>
				);

			case 'ATTACHMENT':
			case 'FILE':
				const metadata = message.metadata || {};
				const contentType =
					metadata.contentType || metadata.fileType || message.fileType;
				const fileName = metadata.fileName || message.fileName || 'Arquivo';
				const fileUrl = message.content;

				if (!fileUrl) {
					return (
						<p className='text-red-500 text-xs italic'>
							URL do arquivo inválida
						</p>
					);
				}

				if (contentType?.startsWith('image/')) {
					return (
						<div className='relative max-w-xs overflow-hidden rounded-lg cursor-pointer'>
							<a
								href={
									fileUrl.startsWith('blob:')
										? fileUrl
										: getCompleteImageUrl(fileUrl)
								}
								target='_blank'
								rel='noopener noreferrer'
							>
								<img
									src={
										fileUrl.startsWith('blob:')
											? fileUrl
											: getCompleteImageUrl(fileUrl)
									}
									alt={fileName}
									className='h-auto w-full object-cover block'
									loading='lazy'
									onError={(e) => {
										e.currentTarget.style.display = 'none';
										const parent = e.currentTarget.parentElement;
										if (parent) {
											parent.innerHTML += `<span class="text-xs italic text-red-300">Erro ao carregar imagem</span>`;
										}
									}}
								/>
							</a>
						</div>
					);
				}

				if (contentType === 'application/pdf') {
					const isSender = message.senderId === currentUserId;
					const bgColor = isSender ? 'bg-primary' : 'bg-muted';
					const textColor = isSender
						? 'text-primary-foreground'
						: 'text-foreground';
					const iconColor = isSender
						? 'text-primary-foreground'
						: 'text-foreground';

					return (
						<div className={cn('flex items-center gap-3', bgColor, textColor)}>
							<LucideFileText className={cn('h-8 w-8 shrink-0', iconColor)} />
							<div className='min-w-0 flex-1'>
								<p className='truncate font-medium text-sm'>{fileName}</p>
								<div className='mt-1 flex gap-2'>
									<a
										href={fileUrl}
										target='_blank'
										rel='noopener noreferrer'
										className={cn(
											'inline-flex items-center gap-1 text-xs hover:underline',
											textColor
										)}
									>
										<Eye className='h-3 w-3' />
										Visualizar
									</a>
									<a
										href={fileUrl}
										download={fileName}
										className={cn(
											'inline-flex items-center gap-1 text-xs hover:underline',
											textColor
										)}
									>
										<Download className='h-3 w-3' />
										Download
									</a>
								</div>
							</div>
						</div>
					);
				}

				const isSenderGeneric = message.senderId === currentUserId;
				const bgColorGeneric = isSenderGeneric ? 'bg-primary' : 'bg-muted';
				const textColorGeneric = isSenderGeneric
					? 'text-primary-foreground'
					: 'text-foreground';
				const iconColorGeneric = isSenderGeneric
					? 'text-primary-foreground'
					: 'text-foreground';

				return (
					<div
						className={cn(
							'flex items-center gap-3',
							bgColorGeneric,
							textColorGeneric
						)}
					>
						<FileIcon className={cn('h-6 w-6 shrink-0', iconColorGeneric)} />
						<div className='min-w-0 flex-1'>
							<p className='truncate font-medium text-sm'>{fileName}</p>
							<a
								href={fileUrl}
								download={fileName}
								className={cn(
									'inline-flex items-center gap-1 text-xs hover:underline',
									textColorGeneric
								)}
							>
								<Download className='h-3 w-3' />
								Baixar arquivo
							</a>
						</div>
					</div>
				);

			case 'SYSTEM':
				return (
					<div className='my-2 w-full text-center text-xs italic text-muted-foreground'>
						{message.content}
					</div>
				);

			default:
				return (
					<p className='whitespace-pre-wrap break-words'>{message.content}</p>
				);
		}
	};

	const handleOpenMedicalRecord = () => {
		if (onMedicalRecord) {
			onMedicalRecord();
		} else {
			setShowMedicalRecordDialog(true);
		}
	};

	// Render function for the main chat area
	const renderChatContent = () => {
		if (isLoading) {
			return <ChatSkeleton />;
		}

		if (error) {
			return (
				<div className='flex h-full min-h-[60vh] flex-col items-center justify-center p-6 text-center'>
					<div className='rounded-full bg-destructive/10 p-4 text-destructive mb-4'>
						<MessageSquare className='h-10 w-10' />
					</div>
					<h3 className='text-lg font-medium'>Erro ao carregar mensagens</h3>
					<p className='text-sm text-muted-foreground mt-2 max-w-md'>{error}</p>
					{refreshMessages && (
						<Button
							variant='outline'
							className='mt-4'
							onClick={handleReconnect}
							disabled={reconnecting}
						>
							{reconnecting ? (
								<>
									<Loader2 className='mr-2 h-4 w-4 animate-spin' />
									Tentando novamente...
								</>
							) : (
								<>
									<RefreshCw className='mr-2 h-4 w-4' />
									Tentar novamente
								</>
							)}
						</Button>
					)}
				</div>
			);
		}

		if (!displayMessages || displayMessages.length === 0) {
			return (
				<div className='flex h-full min-h-[60vh] flex-col items-center justify-center p-6 text-center'>
					<div className='rounded-full bg-muted p-4 mb-4'>
						<MessageSquare className='h-10 w-10 text-muted-foreground' />
					</div>
					<h3 className='text-lg font-medium'>Nenhuma mensagem</h3>
					<p className='text-sm text-muted-foreground mt-2'>
						Envie uma mensagem para iniciar a conversa
					</p>
				</div>
			);
		}

		return (
			<div className='space-y-4'>
				{displayMessages.map((message) => {
					if (
						!message ||
						!message.id ||
						!message.senderId ||
						typeof message.content === 'undefined'
					) {
						console.warn('Skipping rendering invalid message:', message);
						return null;
					}

					if (message.type === 'SYSTEM') {
						return (
							<div key={message.id} className='flex justify-center my-2'>
								<div className='px-3 py-1 bg-muted rounded-md text-xs text-muted-foreground italic max-w-[80%] text-center'>
									{renderMessageContent(message)}
								</div>
							</div>
						);
					}

					// Determine message sender type (self or other)
					const isMyMessage = message.senderId === currentUserId;
					const senderRole = message.senderRole;

					// Determine message styling
					const messageAlignment = isMyMessage
						? 'justify-end'
						: 'justify-start';
					const messageDirection = isMyMessage
						? 'flex-row-reverse'
						: 'flex-row';
					const textAlignment = isMyMessage ? 'items-end' : 'items-start';

					// Determine bubble style
					let bubbleStyle = '';
					if (isMyMessage) {
						bubbleStyle = 'bg-primary text-primary-foreground rounded-tr-none';
					} else {
						bubbleStyle = 'bg-muted text-foreground rounded-tl-none';
					}

					// Determine sender name (for display purposes)
					let senderName = isMyMessage
						? 'Você'
						: recipientName ||
						  (senderRole === 'DOCTOR' ? 'Médico' : 'Paciente');

					// Determine sender avatar
					let avatarUrl: string | null = null;
					if (!isMyMessage) {
						// Only show avatar for other people's messages
						avatarUrl = recipientImage || null;
					}

					return (
						<div
							key={message.id}
							className={cn(
								'flex items-end gap-2 mb-4',
								isMyMessage ? 'justify-end' : 'justify-start'
							)}
							data-msg-id={message.id}
							data-sender-id={message.senderId}
							data-sender-role={message.senderRole}
							data-is-my-message={isMyMessage ? 'true' : 'false'}
						>
							{/* Only show avatar for messages from others */}
							{!isMyMessage && (
								<UserAvatar
									avatarUrl={avatarUrl}
									name={senderName}
									className='h-8 w-8 mb-1'
								/>
							)}

							<div
								className={cn(
									'flex flex-col max-w-[70%]',
									isMyMessage ? 'items-end' : 'items-start'
								)}
							>
								{/* Show sender name only for messages from others */}
								{!isMyMessage && (
									<span
										className={cn(
											'text-xs font-medium mb-1',
											message.senderRole === 'DOCTOR'
												? 'text-blue-600'
												: 'text-primary'
										)}
									>
										{senderName}
									</span>
								)}

								<div
									className={cn(
										'rounded-lg p-3 text-sm leading-relaxed',
										isMyMessage
											? 'bg-primary text-primary-foreground rounded-tr-none'
											: 'bg-muted text-foreground rounded-tl-none'
									)}
								>
									{renderMessageContent(message)}
								</div>

								<span className='text-xs text-muted-foreground mt-1'>
									{message.createdAt
										? format(new Date(message.createdAt), 'HH:mm', {
												locale: ptBR,
										  })
										: '--:--'}
								</span>
							</div>

							{/* Only show avatar for my messages on the right */}
							{isMyMessage && (
								<UserAvatar
									avatarUrl={null} // For current user, we might want to show their avatar too
									name='Você'
									className='h-8 w-8 mb-1'
								/>
							)}
						</div>
					);
				})}
				<div ref={messagesEndRef} style={{ height: '1px' }} />
			</div>
		);
	};

	return (
		<div className='flex h-[100%] flex-col bg-background'>
			{/* Header with ChatHeader component */}
			<ChatHeader
				appointmentId={appointmentId}
				userRole={userRole || 'PATIENT'}
				onVideoCall={onVideoCall}
				onMedicalRecord={onMedicalRecord}
				onBack={onBack}
				initialData={{
					recipientName: recipientName || '',
					recipientImage: recipientImage || null,
					recipientInfo: recipientInfo || '',
					appointmentDate: appointmentDate || undefined,
					symptoms:
						userRole === 'DOCTOR'
							? recipientInfo || 'Consulta médica'
							: undefined,
					specialty:
						userRole === 'PATIENT'
							? recipientInfo || 'Especialista'
							: undefined,
					// Campos específicos para plantão
					isOnDuty: isOnDuty,
					urgencyLevel: urgencyLevel,
					acceptedAt: acceptedAt,
				}}
			/>

			{/* Chat Messages */}
			<ScrollArea className='flex-1 py-4 px-6 chat-container'>
				{renderChatContent()}
			</ScrollArea>

			{/* Audio Recording Preview */}
			{audioBlob && !isRecording && (
				<div className='mx-4 mb-2 flex items-center gap-2 rounded-lg bg-muted p-2 border border-border'>
					<audio
						src={URL.createObjectURL(audioBlob)}
						controls
						className='flex-1 h-8'
					/>
					<Button
						type='button'
						variant='ghost'
						size='icon'
						onClick={resetRecording}
						className='text-destructive hover:bg-destructive/10 h-8 w-8 shrink-0'
						title='Cancelar Áudio'
					>
						<X className='h-4 w-4' />
					</Button>
					<Button
						type='button'
						variant='default'
						size='icon'
						onClick={handleSendAudioSubmit}
						disabled={isSending}
						className='bg-primary hover:bg-primary/90 text-primary-foreground rounded-full h-8 w-8 shrink-0'
						title='Enviar Áudio'
					>
						{isSending ? (
							<Loader2 className='h-4 w-4 animate-spin' />
						) : (
							<Send className='h-4 w-4' />
						)}
					</Button>
				</div>
			)}

			{/* Recording status indicator */}
			{isRecording && (
				<div className='mx-4 mb-2 flex items-center gap-2 rounded-lg bg-destructive/10 p-2 border border-destructive/20 text-destructive text-xs'>
					<div className='flex-1 flex items-center gap-2'>
						<div className='h-2 w-2 rounded-full bg-destructive animate-pulse'></div>
						<span>Gravando áudio...</span>
					</div>
				</div>
			)}

			{/* Input Area */}
			<div className='p-3 py-5 border-t bg-background'>
				<form
					onSubmit={handleSendMessageSubmit}
					className='flex items-center gap-2'
				>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleFileUpload}
						className='hidden'
						accept='image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt'
						disabled={isRecording || isSending}
					/>

					<Button
						type='button'
						variant='ghost'
						size='icon'
						className='shrink-0 text-muted-foreground hover:text-foreground'
						onClick={() => fileInputRef.current?.click()}
						disabled={isRecording || isSending}
						title='Anexar arquivo'
					>
						<Paperclip className='h-5 w-5' />
					</Button>

					<Button
						type='button'
						variant='ghost'
						size='icon'
						className={cn(
							'shrink-0 hover:text-foreground',
							isRecording
								? 'text-destructive animate-pulse'
								: 'text-muted-foreground'
						)}
						onClick={isRecording ? stopRecording : startRecording}
						disabled={isSending}
						title={isRecording ? 'Parar gravação' : 'Gravar áudio'}
					>
						<Mic className='h-5 w-5' />
					</Button>

					<textarea
						value={messageText}
						onChange={(e) => setMessageText(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === 'Enter' && !e.shiftKey) {
								e.preventDefault();
								handleSendMessageSubmit(e as any);
							}
						}}
						placeholder='Digite sua mensagem...'
						className='flex-1 resize-none rounded-full border bg-muted px-4 py-4 text-sm focus:outline-none focus:ring-1 focus:ring-primary disabled:opacity-70'
						rows={1}
						disabled={isRecording || isSending}
					/>

					<Button
						type='submit'
						size='lg'
						className='shrink-0 w-14 h-14 rounded-full bg-primary p-2 text-primary-foreground hover:bg-primary/90 disabled:opacity-50'
						disabled={!messageText.trim() || isSending || isRecording}
						title='Enviar mensagem'
					>
						{isSending && !isRecording ? (
							<Loader2 className='h-5 w-5 animate-spin' />
						) : (
							<Send className='h-5 w-5' />
						)}
					</Button>
				</form>
			</div>

			<Dialog
				open={showMedicalRecordDialog}
				onOpenChange={setShowMedicalRecordDialog}
			>
				<DialogContent className='sm:max-w-xl'>
					<DialogHeader>
						<DialogTitle>Prontuário</DialogTitle>
						<DialogDescription>
							Preencha as informações do prontuário para esta consulta (
							{recipientName}).
						</DialogDescription>
					</DialogHeader>
					<div className='space-y-4 py-4'>
						<div className='space-y-2'>
							<label htmlFor='complaint' className='text-sm font-medium'>
								Queixa Principal
							</label>
							<textarea
								id='complaint'
								className='w-full min-h-[80px] resize-none rounded-md border bg-background px-3 py-2 text-sm'
								placeholder='Descreva a queixa principal do paciente...'
							/>
						</div>
						<div className='space-y-2'>
							<label htmlFor='diagnosis' className='text-sm font-medium'>
								Diagnóstico
							</label>
							<textarea
								id='diagnosis'
								className='w-full min-h-[80px] resize-none rounded-md border bg-background px-3 py-2 text-sm'
								placeholder='Descreva o diagnóstico...'
							/>
						</div>
						<div className='space-y-2'>
							<label htmlFor='conduct' className='text-sm font-medium'>
								Conduta
							</label>
							<textarea
								id='conduct'
								className='w-full min-h-[80px] resize-none rounded-md border bg-background px-3 py-2 text-sm'
								placeholder='Descreva a conduta médica...'
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant='outline'
							onClick={() => setShowMedicalRecordDialog(false)}
						>
							Cancelar
						</Button>
						<Button
							onClick={() => {
								toast.success('Prontuário salvo com sucesso (simulado)');
								setShowMedicalRecordDialog(false);
							}}
						>
							Salvar
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
