import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog"
import { Button } from '@ui/components/button'
import { Input } from '@ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select'
import { IconLoader2 } from '@tabler/icons-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface CreateChatDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CreateChatDialog({ open, onOpenChange }: CreateChatDialogProps) {
  const router = useRouter()
  const [selectedDoctorId, setSelectedDoctorId] = useState<string>('')
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [doctors, setDoctors] = useState<Array<{id: string, name: string}>>([])
  const [isLoadingDoctors, setIsLoadingDoctors] = useState(false)

  // Load doctors when dialog opens
  const loadDoctors = async () => {
    if (open && doctors.length === 0 && !isLoadingDoctors) {
      setIsLoadingDoctors(true)
      try {
        // Use fetch API instead of direct API caller
        const response = await fetch('/api/trpc/doctors.list?batch=1&input={"0":{}}');

        if (!response.ok) {
          throw new Error("Failed to load doctors");
        }

        const apiResponse = await response.json();
        const result = apiResponse[0].result.data;

        setDoctors(result.map(doctor => ({
          id: doctor.userId,
          name: doctor.user.name
        })))
      } catch (error) {
        console.error("Error loading doctors:", error)
        toast.error("Não foi possível carregar a lista de médicos")
      } finally {
        setIsLoadingDoctors(false)
      }
    }
  }

  // Trigger doctor loading when dialog opens
  if (open && doctors.length === 0 && !isLoadingDoctors) {
    loadDoctors()
  }

  const handleCreateChat = async () => {
    if (!selectedDoctorId) {
      toast.error('Selecione um médico')
      return
    }

    setIsLoading(true)
    try {
      // Use fetch API instead of direct API caller
      const response = await fetch('/api/trpc/appointments.create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          json: {
            doctorId: selectedDoctorId,
            message: message.trim()
          }
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create chat");
      }

      const result = await response.json();
      const appointment = result.result.data;

      toast.success('Conversa iniciada com sucesso!')
      onOpenChange(false)

      // Reset form
      setSelectedDoctorId('')
      setMessage('')

      // Redirect to the new appointment chat
      if (appointment.id) {
        			router.push(`/app/zapchat?appointment=${appointment.id}`)
      }
    } catch (error) {
      console.error("Error creating chat:", error)
      toast.error("Não foi possível iniciar a conversa")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Nova Conversa</DialogTitle>
          <DialogDescription>
            Inicie uma conversa com um médico para tirar dúvidas ou agendar uma consulta.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="doctor" className="text-sm font-medium">
              Médico
            </label>
            <Select
              value={selectedDoctorId}
              onValueChange={setSelectedDoctorId}
              disabled={isLoadingDoctors}
            >
              <SelectTrigger id="doctor">
                <SelectValue placeholder="Selecione um médico" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingDoctors ? (
                  <div className="flex items-center justify-center p-4">
                    <IconLoader2 className="h-5 w-5 animate-spin" />
                  </div>
                ) : doctors.length === 0 ? (
                  <div className="p-2 text-center text-sm text-muted-foreground">
                    Nenhum médico disponível
                  </div>
                ) : (
                  doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      {doctor.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <label htmlFor="message" className="text-sm font-medium">
              Mensagem inicial (opcional)
            </label>
            <Input
              id="message"
              placeholder="Digite sua mensagem..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleCreateChat}
            disabled={!selectedDoctorId || isLoading}
          >
            {isLoading ? (
              <>
                <IconLoader2 className="mr-2 h-4 w-4 animate-spin" />
                Iniciando...
              </>
            ) : (
              'Iniciar conversa'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
