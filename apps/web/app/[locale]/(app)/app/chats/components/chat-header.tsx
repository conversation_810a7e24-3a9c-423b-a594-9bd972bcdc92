'use client';

import { useEffect, useState } from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
	Calendar,
	Stethoscope,
	Video,
	AlertCircle,
	ArrowLeft,
} from 'lucide-react';
import { Badge } from '@ui/components/badge';
import { useRouter } from 'next/navigation';
import { Button } from '@ui/components/button';
import { SignedAvatar } from '../../../../../../components/shared/signed-avatar';
import { UserAvatar } from '@shared/components/UserAvatar';
import { Skeleton } from '@ui/components/skeleton';
import { useMediaQuery } from '@ui/hooks/use-media-query';
import { cn } from '@ui/lib';

interface AppointmentDetails {
	id: string;
	doctor?: {
		user?: {
			id?: string;
			name?: string;
			image?: string | null;
		};
		specialty?: string;
	};
	patient?: {
		user?: {
			id?: string;
			name?: string;
			image?: string | null;
		};
		symptoms?: string;
		medicalHistory?: string;
	};
	scheduledAt?: string;
	status?: string;
}

interface ChatHeaderProps {
	appointmentId: string;
	userRole: 'DOCTOR' | 'PATIENT';
	onVideoCall?: () => void;
	onMedicalRecord?: () => void;
	onBack?: () => void; // Função para voltar à tela anterior
	// Dados opcionais pré-carregados para evitar requisição extra
	initialData?: {
		recipientName?: string;
		recipientImage?: string | null;
		recipientInfo?: string;
		appointmentDate?: string;
		symptoms?: string;
		specialty?: string;
		// Campos específicos para plantão
		isOnDuty?: boolean;
		urgencyLevel?: 'HIGH' | 'MEDIUM' | 'LOW' | null;
		acceptedAt?: string | null;
	};
}

// Helper functions for urgency badges
function getUrgencyBadgeColor(urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW') {
	switch (urgencyLevel) {
		case 'HIGH':
			return 'bg-red-500 hover:bg-red-600 text-white border-red-500';
		case 'MEDIUM':
			return 'bg-orange-500 hover:bg-orange-600 text-white border-orange-500';
		case 'LOW':
			return 'bg-blue-500 hover:bg-blue-600 text-white border-blue-500';
		default:
			return 'bg-gray-500 hover:bg-gray-600 text-white border-gray-500';
	}
}

function getUrgencyIcon(urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW') {
	switch (urgencyLevel) {
		case 'HIGH':
			return <AlertCircle className="h-3 w-3" />;
		case 'MEDIUM':
			return <AlertCircle className="h-3 w-3" />;
		case 'LOW':
			return <AlertCircle className="h-3 w-3" />;
		default:
			return null;
	}
}

function getUrgencyText(urgencyLevel: 'HIGH' | 'MEDIUM' | 'LOW') {
	switch (urgencyLevel) {
		case 'HIGH':
			return 'Muito Urgente';
		case 'MEDIUM':
			return 'Urgente';
		case 'LOW':
			return 'Pouco Urgente';
		default:
			return '';
	}
}

export function ChatHeader({
	appointmentId,
	userRole,
	onVideoCall,
	onMedicalRecord,
	onBack,
	initialData,
}: ChatHeaderProps) {
	const router = useRouter();
	const { isMobile } = useMediaQuery();
	const [appointmentDetails, setAppointmentDetails] =
		useState<AppointmentDetails | null>(null);
	const [isLoading, setIsLoading] = useState(!initialData);
	const [error, setError] = useState<string | null>(null);

	// Determinar quais informações exibir com base no papel do usuário
	const isDoctor = userRole === 'DOCTOR';

	// Usar dados iniciais se fornecidos
	useEffect(() => {
		if (initialData) {
			// Criar um objeto de detalhes da consulta a partir dos dados iniciais
			const details: AppointmentDetails = {
				id: appointmentId,
				scheduledAt: initialData.appointmentDate,
			};

			// Note: A lógica está correta aqui - quando o usuário é médico, queremos mostrar os detalhes do paciente no cabeçalho
			// e quando o usuário é paciente, queremos mostrar os detalhes do médico.
			if (isDoctor) {
				details.patient = {
					user: {
						name: initialData.recipientName,
						image: initialData.recipientImage,
					},
					symptoms: initialData.symptoms,
				};
			} else {
				details.doctor = {
					user: {
						name: initialData.recipientName,
						image: initialData.recipientImage,
					},
					specialty: initialData.specialty,
				};
			}

			setAppointmentDetails(details);
			setIsLoading(false);
		}
	}, [initialData, appointmentId, isDoctor]);

	// Buscar detalhes da consulta se não houver dados iniciais
	useEffect(() => {
		if (!initialData && appointmentId) {
			fetchAppointmentDetails();
		}
	}, [appointmentId, initialData]);

	async function fetchAppointmentDetails() {
		try {
			setIsLoading(true);
			setError(null);

			// Fetch appointment details from API
			const response = await fetch(`/api/appointments/${appointmentId}`, {
				method: 'GET',
				headers: {
					Accept: 'application/json',
					'Cache-Control': 'no-cache',
				},
			});

			// Verificar o tipo de conteúdo da resposta
			const contentType = response.headers.get('content-type') || '';

			if (!response.ok) {
				// Tentar obter mensagem de erro da resposta
				let errorMessage = 'Erro ao buscar detalhes da consulta';

				if (contentType.includes('application/json')) {
					try {
						const errorData = await response.json();
						if (errorData && errorData.message) {
							errorMessage = errorData.message;
						} else if (errorData && errorData.error) {
							errorMessage = errorData.error;
						}
					} catch (e) {
						// Se não conseguir analisar o JSON, usar o status text
						errorMessage = `${errorMessage}: ${response.statusText}`;
					}
				} else {
					// Se a resposta não for JSON, usar o status text
					errorMessage = `${errorMessage} (${response.status}: ${response.statusText})`;
					const responseText = await response
						.text()
						.catch(() => 'Não foi possível ler o conteúdo');
					console.error(
						'Resposta não-JSON recebida:',
						responseText.substring(0, 200)
					);
				}

				throw new Error(errorMessage);
			}

			// Verificar se a resposta é JSON antes de tentar analisá-la
			if (!contentType.includes('application/json')) {
				console.error('Resposta com tipo de conteúdo inválido:', contentType);
				const text = await response.text().catch(() => null);
				console.error(
					'Conteúdo da resposta:',
					text ? text.substring(0, 200) + '...' : 'Não foi possível ler'
				);
				throw new Error(`Formato de resposta inesperado: ${contentType}`);
			}

			// Como a resposta já foi consumida nas verificações acima se tentarmos fazer text() ou json()
			// novamente, a Promise será rejeitada. Portanto, fazemos uma nova solicitação
			// apenas se tivermos chegado até este ponto e a resposta não for JSON.
			let data;
			try {
				data = await response.clone().json();
			} catch (jsonError) {
				console.error('Erro ao analisar resposta JSON:', jsonError);
				// Fazer nova requisição para obter o conteúdo em texto para diagnóstico
				const text = await response
					.text()
					.catch(() => 'Não foi possível ler o conteúdo');
				console.error('Conteúdo da resposta recebida:', text.substring(0, 200));
				const errorMessage =
					jsonError instanceof Error
						? jsonError.message
						: 'Erro desconhecido ao processar resposta';
				throw new Error(
					`Formato de resposta inválido do servidor: ${errorMessage}`
				);
			}

			if (!data) {
				throw new Error('Dados da consulta não encontrados');
			}

			setAppointmentDetails(data);
		} catch (error) {
			console.error('Erro ao buscar detalhes da consulta:', error);
			setError(error instanceof Error ? error.message : 'Erro desconhecido');
		} finally {
			setIsLoading(false);
		}
	}

	// Determinar as informações a serem exibidas com base no papel do usuário
	const recipientName = isDoctor
		? appointmentDetails?.patient?.user?.name ||
		  initialData?.recipientName ||
		  'Paciente'
		: appointmentDetails?.doctor?.user?.name ||
		  initialData?.recipientName ||
		  'Médico';

	const recipientImage = isDoctor
		? appointmentDetails?.patient?.user?.image || initialData?.recipientImage
		: appointmentDetails?.doctor?.user?.image || initialData?.recipientImage;

	const appointmentDate =
		appointmentDetails?.scheduledAt || initialData?.appointmentDate;

	// Para pacientes, sempre exibir a especialidade do médico
	const specialty = !isDoctor
		? appointmentDetails?.doctor?.specialty ||
		  initialData?.specialty ||
		  'Especialista'
		: undefined;

	const symptoms = isDoctor
		? appointmentDetails?.patient?.symptoms || initialData?.symptoms
		: undefined;

	// Função para lidar com o clique no botão de prontuário
	const handleOpenMedicalRecord = () => {
		if (onMedicalRecord) {
			onMedicalRecord();
		}
	};

	// Renderizar estado de carregamento
	if (isLoading) {
		return (
			<div className='bg-white border-b px-4 py-3 sticky top-0 z-10'>
				<div className='flex items-center justify-between'>
					<div className='flex items-center gap-3'>
						<Skeleton className='h-10 w-10 rounded-full' />
						<div>
							<Skeleton className='h-5 w-32' />
							<Skeleton className='h-4 w-24 mt-1' />
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Renderizar estado de erro
	if (error) {
		return (
			<div className='bg-white border-b px-4 py-3 sticky top-0 z-10'>
				<div className='flex items-center gap-2 text-destructive'>
					<AlertCircle className='h-5 w-5' />
					<span className='text-sm'>Erro ao carregar informações: {error}</span>
				</div>
			</div>
		);
	}

	// Função para lidar com o botão de voltar
	const handleBack = () => {
		if (onBack) {
			onBack();
		} else {
			router.push('/app/zapchat');
		}
	};

	return (
		<div className='bg-white border-b px-4 sm:px-8 py-3 sticky top-0 z-10'>
			<div className='flex items-center justify-between'>
				<div className='flex items-center gap-2 sm:gap-3'>
					<Button
						variant='ghost'
						size='icon'
						onClick={handleBack}
						className='h-8 w-8 flex-shrink-0'
					>
						<ArrowLeft className='h-4 w-4' />
					</Button>
					<UserAvatar
						avatarUrl={recipientImage || null}
						name={recipientName}
						className='h-10 w-10 flex-shrink-0'
					/>
					<div className='min-w-0'>
						<div className='flex items-center gap-2'>
							<h2 className='font-semibold text-lg truncate'>{recipientName}</h2>
							{/* Show plantão badge and urgency in header */}
							{initialData?.isOnDuty && (
								<Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800 flex items-center gap-1">
									<Stethoscope className="h-3 w-3" />
									Plantão
								</Badge>
							)}
							{initialData?.urgencyLevel && (
								<Badge className={cn("text-xs flex items-center gap-1", getUrgencyBadgeColor(initialData.urgencyLevel))}>
									{getUrgencyIcon(initialData.urgencyLevel)}
									{getUrgencyText(initialData.urgencyLevel)}
								</Badge>
							)}
						</div>

						{/* Show acceptance info for plantão */}
						{initialData?.isOnDuty && initialData?.acceptedAt && (
							<div className="mt-1">
								<p className="text-xs text-green-600">
									Aceito {formatDistanceToNow(new Date(initialData.acceptedAt), { addSuffix: true, locale: ptBR })}
								</p>
							</div>
						)}

						{/* Informações específicas baseadas no papel do usuário */}
						{/* Para pacientes: mostrar especialidade do médico */}
						{!isDoctor && specialty && (
							<p className='text-sm text-muted-foreground truncate'>
								{specialty}
							</p>
						)}

						{/* Para médicos: mostrar sintomas do paciente */}
						{isDoctor && symptoms && (
							<p className='text-sm text-muted-foreground line-clamp-1'>
								<span className='font-medium'>Sintomas:</span> {symptoms}
							</p>
						)}



						{/* Data da consulta - escondida em mobile */}
						{appointmentDate && !isMobile && (
							<div className='flex items-center gap-1 text-xs text-muted-foreground mt-1'>
								<Calendar className='h-3 w-3' />
								<span>
									{format(
										new Date(appointmentDate),
										"dd 'de' MMMM', às' HH:mm",
										{ locale: ptBR }
									)}
								</span>
							</div>
						)}
					</div>
				</div>

				{/* Botões de ação - versão desktop */}
				{!isMobile && (
					<div className='flex items-center gap-2'>
						{/* Botão de chamada de vídeo - apenas para médicos */}
						{isDoctor && onVideoCall && (
							<Button
								variant='ghost'
								onClick={onVideoCall}
								title='Chamada de vídeo'
								className='flex items-center gap-2'
							>
								<Video className='h-5 w-5' />
								Chamada de vídeo
							</Button>
						)}

						{/* Botão de prontuário - apenas para médicos */}
						{isDoctor && onMedicalRecord && (
							<Button
								variant='ghost'
								onClick={handleOpenMedicalRecord}
								title='Prontuário'
								className='flex items-center gap-2'
							>
								<Stethoscope className='h-5 w-5' />
								Prontuário
							</Button>
						)}
					</div>
				)}

				{/* Botões de ação para mobile - apenas ícones */}
				{isMobile && isDoctor && (
					<div className='flex items-center gap-2'>
						{/* Botão de chamada de vídeo - apenas ícone */}
						{onVideoCall && (
							<Button
								variant='ghost'
								size='icon'
								onClick={onVideoCall}
								title='Chamada de vídeo'
								className='h-8 w-8'
							>
								<Video className='h-5 w-5' />
							</Button>
						)}

						{/* Botão de prontuário - apenas ícone */}
						{onMedicalRecord && (
							<Button
								variant='ghost'
								size='icon'
								onClick={handleOpenMedicalRecord}
								title='Prontuário'
								className='h-8 w-8'
							>
								<Stethoscope className='h-5 w-5' />
							</Button>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
