// ChatDebugger.tsx
import { useCallback, useEffect, useState } from 'react';
import { Button } from '@ui/components/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { createRealtimeClient } from '@shared/lib/supabase/realtime-client';
import { ScrollArea } from '@ui/components/scroll-area';
import { CopyIcon, CheckIcon, RotateCw, SendHorizonal } from 'lucide-react';

interface ChatDebuggerProps {
  appointmentId: string;
  useChat: any; // Referência ao hook que estamos testando
}

export function ChatDebugger({ appointmentId, useChat }: ChatDebuggerProps) {
  const [sendMessageText, setSendMessageText] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('Não conectado');
  const [logs, setLogs] = useState<any[]>([]);
  const [copied, setCopied] = useState(false);
  const [testSubscription, setTestSubscription] = useState<any>(null);
  const [realTimeLogs, setRealTimeLogs] = useState<any[]>([]);
  const [connectedAt, setConnectedAt] = useState<string | null>(null);

  // Tentar fazer referência ao objeto useChat exportado
  const { messages, sendMessage, getDebugLogs } = useChat;

  // Atualizar logs a cada 2 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      if (typeof getDebugLogs === 'function') {
        const newLogs = getDebugLogs();
        setLogs(newLogs);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [getDebugLogs]);

  // Setup de canal de teste independente
  useEffect(() => {
    if (!appointmentId) return;

    const supabase = createRealtimeClient();
    console.log("Criando canal de teste para", appointmentId);

    try {
      const channel = supabase
        .channel(`diagnostic-${appointmentId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `appointment_id=eq.${appointmentId}`
          },
          (payload) => {
            console.log("DIAGNÓSTICO: Evento recebido:", payload);
            setRealTimeLogs(prev => [...prev, {
              id: Math.random().toString(36).substring(2, 9),
              type: 'RECEIVED',
              payload,
              timestamp: new Date().toISOString()
            }]);
          }
        )
        .subscribe((status) => {
          console.log("DIAGNÓSTICO: Status da inscrição:", status);
          setConnectionStatus(status);

          if (status === 'SUBSCRIBED') {
            setConnectedAt(new Date().toISOString());
          }

          setRealTimeLogs(prev => [...prev, {
            id: Math.random().toString(36).substring(2, 9),
            type: 'STATUS',
            status,
            timestamp: new Date().toISOString()
          }]);
        });

      setTestSubscription(channel);

      return () => {
        console.log("Removendo canal de teste");
        supabase.removeChannel(channel);
      };
    } catch (error) {
      console.error("Erro ao configurar canal de teste:", error);
      setConnectionStatus('ERRO');
      setRealTimeLogs(prev => [...prev, {
        id: Math.random().toString(36).substring(2, 9),
        type: 'ERROR',
        error,
        timestamp: new Date().toISOString()
      }]);
    }
  }, [appointmentId]);

  // Função para copiar logs
  const copyLogs = useCallback(() => {
    const text = logs.map(log =>
      `[${log.timestamp}] [${log.type}] ${log.message} ${log.data ? JSON.stringify(log.data) : ''}`
    ).join('\n');

    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  }, [logs]);

  // Função para limpar logs
  const clearLogs = useCallback(() => {
    setLogs([]);
    setRealTimeLogs([]);
  }, []);

  // Função para testar envio de mensagem
  const handleSendTestMessage = useCallback(async () => {
    if (!sendMessageText.trim() || !sendMessage) return;

    try {
      console.log("Enviando mensagem de teste:", sendMessageText);
      const result = await sendMessage(sendMessageText);

      console.log("Resultado do envio de teste:", result);
      setRealTimeLogs(prev => [...prev, {
        id: Math.random().toString(36).substring(2, 9),
        type: 'SENT',
        content: sendMessageText,
        result,
        timestamp: new Date().toISOString()
      }]);

      setSendMessageText('');
    } catch (error) {
      console.error("Erro ao enviar mensagem de teste:", error);
      setRealTimeLogs(prev => [...prev, {
        id: Math.random().toString(36).substring(2, 9),
        type: 'ERROR',
        content: sendMessageText,
        error,
        timestamp: new Date().toISOString()
      }]);
    }
  }, [sendMessageText, sendMessage]);

  return (
    <div className="border rounded-lg p-4 max-w-4xl mx-auto my-8 bg-white shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Chat Debugger</h2>
        <div className="flex items-center gap-2">
          <Badge variant={
            connectionStatus === 'SUBSCRIBED' ? 'success' :
            connectionStatus === 'CHANNEL_ERROR' || connectionStatus === 'TIMED_OUT' ? 'destructive' :
            'secondary'
          }>
            {connectionStatus}
          </Badge>
          {connectedAt && (
            <span className="text-xs text-muted-foreground">
              Conectado em: {new Date(connectedAt).toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center mb-2">
          <span className="text-sm font-medium mr-2">appointmentId:</span>
          <code className="bg-muted px-2 py-1 rounded text-xs">{appointmentId || 'não definido'}</code>
        </div>

        <div className="flex items-center">
          <span className="text-sm font-medium mr-2">Total de mensagens:</span>
          <Badge variant="outline">{Array.isArray(messages) ? messages.length : '?'}</Badge>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex gap-2">
          <div className="flex-1">
            <input
              type="text"
              value={sendMessageText}
              onChange={(e) => setSendMessageText(e.target.value)}
              placeholder="Digite uma mensagem de teste..."
              className="w-full p-2 border rounded"
            />
          </div>
          <Button onClick={handleSendTestMessage} disabled={!sendMessageText.trim()}>
            <SendHorizonal className="h-4 w-4 mr-1" />
            Enviar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="logs">
        <TabsList className="mb-2">
          <TabsTrigger value="logs">Logs de Hook</TabsTrigger>
          <TabsTrigger value="realtime">Eventos Realtime</TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="border rounded-md p-2">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Logs de Depuração ({logs.length})</span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyLogs}
                className="h-8 px-2"
              >
                {copied ? <CheckIcon className="h-3 w-3" /> : <CopyIcon className="h-3 w-3" />}
                <span className="ml-1">{copied ? 'Copiado' : 'Copiar'}</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearLogs}
                className="h-8 px-2"
              >
                <RotateCw className="h-3 w-3 mr-1" />
                Limpar
              </Button>
            </div>
          </div>

          <ScrollArea className="h-80 border rounded-md bg-slate-950 text-slate-50 p-2 text-xs font-mono">
            {logs.length === 0 ? (
              <div className="text-slate-400 italic p-2">Nenhum log disponível</div>
            ) : (
              logs.map((log) => (
                <div key={log.id} className="pb-1 border-b border-slate-800 mb-1">
                  <div className="flex items-center gap-1">
                    <span className="text-slate-400">[{new Date(log.timestamp).toISOString().slice(11, 19)}]</span>
                    <Badge variant={
                      log.type === 'ERROR' ? 'destructive' :
                      log.type === 'SUCCESS' ? 'success' :
                      log.type === 'WARN' ? 'warning' :
                      log.type === 'REALTIME' ? 'purple' : 'default'
                    } className="text-[10px] h-4">
                      {log.type}
                    </Badge>
                    <span>{log.message}</span>
                  </div>
                  {log.data && (
                    <pre className="mt-1 pl-12 text-slate-300 whitespace-pre-wrap text-[10px]">
                      {typeof log.data === 'object' ? JSON.stringify(log.data, null, 2) : log.data}
                    </pre>
                  )}
                </div>
              ))
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="realtime" className="border rounded-md p-2">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Eventos Realtime ({realTimeLogs.length})</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setRealTimeLogs([])}
              className="h-8 px-2"
            >
              <RotateCw className="h-3 w-3 mr-1" />
              Limpar
            </Button>
          </div>

          <ScrollArea className="h-80 border rounded-md bg-slate-950 text-slate-50 p-2 text-xs font-mono">
            {realTimeLogs.length === 0 ? (
              <div className="text-slate-400 italic p-2">Nenhum evento recebido</div>
            ) : (
              realTimeLogs.map((log) => (
                <div key={log.id} className="pb-1 border-b border-slate-800 mb-1">
                  <div className="flex items-center gap-1">
                    <span className="text-slate-400">[{new Date(log.timestamp).toLocaleTimeString()}]</span>
                    <Badge variant={
                      log.type === 'ERROR' ? 'destructive' :
                      log.type === 'SENT' ? 'success' :
                      log.type === 'RECEIVED' ? 'info' : 'default'
                    } className="text-[10px] h-4">
                      {log.type}
                    </Badge>

                    {log.type === 'STATUS' && (
                      <span>Status: {log.status}</span>
                    )}

                    {log.type === 'SENT' && (
                      <span>Enviado: {log.content?.substring(0, 30)}{log.content?.length > 30 ? '...' : ''}</span>
                    )}

                    {log.type === 'RECEIVED' && (
                      <span>Recebido evento na tabela: {log.payload?.table}</span>
                    )}

                    {log.type === 'ERROR' && (
                      <span className="text-red-400">Erro: {String(log.error).substring(0, 50)}</span>
                    )}
                  </div>

                  {log.type === 'RECEIVED' && log.payload?.new && (
                    <pre className="mt-1 pl-12 text-slate-300 whitespace-pre-wrap text-[10px]">
                      {JSON.stringify(log.payload.new, null, 2)}
                    </pre>
                  )}

                  {log.type === 'SENT' && log.result && (
                    <pre className="mt-1 pl-12 text-slate-300 whitespace-pre-wrap text-[10px]">
                      {JSON.stringify(log.result, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      <div className="mt-4 text-xs text-muted-foreground">
        <div className="font-semibold">Como usar:</div>
        <ol className="list-decimal pl-4 space-y-1">
          <li>Verifique se o status da conexão está <strong>SUBSCRIBED</strong></li>
          <li>Envie uma mensagem de teste e observe os logs</li>
          <li>Verifique se os eventos realtime estão sendo recebidos</li>
          <li>Se não receber eventos, verifique as configurações do Supabase Realtime</li>
        </ol>
      </div>
    </div>
  );
}
