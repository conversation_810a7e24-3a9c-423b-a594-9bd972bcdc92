import { conversations } from './convo.json'

// Mensagem de um chat
export type Convo = {
  sender: string
  message: string
  timestamp: number
}

// Usuário de um chat
export type ChatUser = {
  id: string
  profile?: string
  username: string
  fullName: string
  title: string
  status?: string
  scheduledAt?: Date
  complaint?: string
  messages: Convo[]
}

// Consulta médica
export type Consultation = {
  id: string
  fullName: string
  username: string
  profile?: string
  title: string
  status: 'em_andamento' | 'agendada' | 'concluida'
  scheduledAt: Date
  complaint: string
  messages: Convo[]
}

// Usuário básico (para seleção no modal)
export type User = {
  id: string
  fullName: string
  username: string
  profile?: string
  title?: string
  status?: string
  scheduledAt?: Date
  complaint?: string
}
