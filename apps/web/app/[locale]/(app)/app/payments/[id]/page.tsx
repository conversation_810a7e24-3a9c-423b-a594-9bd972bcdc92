import { notFound } from "next/navigation";
import { db } from "database";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>geCheck, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Badge } from "@ui/components/badge";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { currentUser } from "@saas/auth/lib/current-user";

export const dynamic = "force-dynamic";

interface PageProps {
  params: {
    id: string;
  };
}

// Helper para obter o status do pagamento
const getPaymentStatus = (status: string) => {
  const styles: Record<string, string> = {
    PENDING: "bg-yellow-100 text-yellow-800",
    PAID: "bg-green-100 text-green-800",
    REFUNDED: "bg-purple-100 text-purple-800",
    FAILED: "bg-red-100 text-red-800",
  };

  const labels: Record<string, string> = {
    PENDING: "Pendente",
    PAID: "Pago",
    REFUNDED: "Reembolsado",
    FAILED: "Falha",
  };

  return (
    <Badge className={styles[status] || "bg-gray-100 text-gray-800"}>
      {labels[status] || status}
    </Badge>
  );
};

// Helper para obter o método de pagamento
const getPaymentMethod = (method: string) => {
  const labels: Record<string, string> = {
    CREDIT_CARD: "Cartão de Crédito",
    BOLETO: "Boleto",
    PIX: "Pix",
  };

  return labels[method] || method;
};

// Helper para formatar moeda
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
};

export default async function PaymentDetailPage({ params }: PageProps) {
  try {
    const { user } = await currentUser();

    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    // Verificar se o usuário é admin ou médico
    const isAdmin = user.role === "ADMIN";
    const isDoctor = user.role === "DOCTOR";

    if (!isAdmin && !isDoctor) {
      throw new Error("Acesso não autorizado");
    }

    const transaction = await db.transaction.findUnique({
      where: {
        id: params.id,
      },
      include: {
        appointment: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                    phone: true,
                  },
                },
              },
            },
            doctor: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                    phone: true,
                  },
                },
              },
            },
          },
        },
        invoice: true,
      },
    });

    if (!transaction) {
      return notFound();
    }

    // Se for médico, verificar se a transação é desse médico
    if (isDoctor) {
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
        select: { id: true }
      });

      if (!doctor || transaction.doctorId !== doctor.id) {
        throw new Error("Acesso não autorizado a esta transação");
      }
    }

    // Helper para formatar datas
    const formatDate = (date: Date | null) => {
      if (!date) return "-";
      return format(new Date(date), "dd 'de' MMMM 'de' yyyy, HH:mm", {
        locale: ptBR,
      });
    };

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/app/payments">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Detalhes do Pagamento</h1>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Informações da Transação */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Informações da Transação</span>
                {getPaymentStatus(transaction.status)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-500">ID da Transação</div>
                <div className="font-medium">{transaction.id}</div>

                <div className="text-gray-500">Valor Total</div>
                <div className="font-medium">{formatCurrency(Number(transaction.amount))}</div>

                <div className="text-gray-500">Taxa da Plataforma</div>
                <div className="font-medium">{formatCurrency(Number(transaction.platformFee))}</div>

                <div className="text-gray-500">Valor do Médico</div>
                <div className="font-medium">{formatCurrency(Number(transaction.doctorAmount))}</div>

                <div className="text-gray-500">Método de Pagamento</div>
                <div className="font-medium">{getPaymentMethod(transaction.paymentMethod)}</div>

                <div className="text-gray-500">Data de Vencimento</div>
                <div className="font-medium">{formatDate(transaction.dueDate)}</div>

                <div className="text-gray-500">Data de Pagamento</div>
                <div className="font-medium">{formatDate(transaction.paidAt)}</div>

                {transaction.refundedAt && (
                  <>
                    <div className="text-gray-500">Data de Reembolso</div>
                    <div className="font-medium">{formatDate(transaction.refundedAt)}</div>
                  </>
                )}

                <div className="text-gray-500">Data de Criação</div>
                <div className="font-medium">{formatDate(transaction.createdAt)}</div>
              </div>

              {transaction.invoice && (
                <div className="mt-6 pt-4 border-t">
                  <a
                    href={transaction.invoice.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-primary hover:underline"
                  >
                    <BadgeCheck className="h-4 w-4" />
                    Ver Nota Fiscal
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Informações da Consulta */}
          <Card>
            <CardHeader>
              <CardTitle>Informações da Consulta</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-500">ID da Consulta</div>
                <div className="font-medium">{transaction.appointment.id}</div>

                <div className="text-gray-500">Data Agendada</div>
                <div className="font-medium">{formatDate(transaction.appointment.scheduledAt)}</div>

                <div className="col-span-2 mt-4 mb-2">
                  <h3 className="text-md font-semibold">Informações do Paciente</h3>
                </div>

                <div className="text-gray-500">Nome</div>
                <div className="font-medium">{transaction.appointment.patient.user.name || "-"}</div>

                <div className="text-gray-500">E-mail</div>
                <div className="font-medium">{transaction.appointment.patient.user.email || "-"}</div>

                <div className="text-gray-500">Telefone</div>
                <div className="font-medium">{transaction.appointment.patient.user.phone || "-"}</div>

                <div className="col-span-2 mt-4 mb-2">
                  <h3 className="text-md font-semibold">Informações do Médico</h3>
                </div>

                <div className="text-gray-500">Nome</div>
                <div className="font-medium">{transaction.appointment.doctor.user.name || "-"}</div>

                <div className="text-gray-500">E-mail</div>
                <div className="font-medium">{transaction.appointment.doctor.user.email || "-"}</div>

                <div className="text-gray-500">Telefone</div>
                <div className="font-medium">{transaction.appointment.doctor.user.phone || "-"}</div>
              </div>

              <div className="mt-6 pt-4 border-t">
                <Link href={`/app/appointments/${transaction.appointment.id}`}>
                  <Button variant="outline" className="w-full">
                    Ver Detalhes da Consulta
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Erro ao carregar detalhes do pagamento:", error);

    return (
      <div className="container mx-auto p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Erro ao carregar detalhes do pagamento</h2>
        <p className="text-muted-foreground mb-6">
          Ocorreu um erro ao tentar carregar os detalhes deste pagamento.
          Por favor, tente novamente mais tarde.
        </p>

        <div className="bg-red-50 border border-red-200 p-4 rounded-md text-left mb-6">
          <p className="text-red-800 font-medium mb-2">Detalhes do erro:</p>
          <pre className="bg-white p-3 rounded overflow-auto text-sm">
            {error instanceof Error
              ? `${error.name}: ${error.message}\n\n${error.stack}`
              : JSON.stringify(error, null, 2)}
          </pre>
        </div>

        <div className="flex justify-center">
          <Link href="/app/payments">
            <Button>Voltar para Pagamentos</Button>
          </Link>
        </div>
      </div>
    );
  }
}
