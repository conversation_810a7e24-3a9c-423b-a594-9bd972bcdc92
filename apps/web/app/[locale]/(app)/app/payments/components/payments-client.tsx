"use client"

import { Search } from "lucide-react";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { Badge } from "@ui/components/badge";
import { formatCurrency } from "./format-utils";
import { PaymentDetailsModal } from "./payment-details-modal";

// Definição do tipo de transação comum para cliente e modal
export type TransactionData = {
  id: string;
  amount: number;
  platformFee: number;
  doctorAmount: number;
  status: string;
  paymentMethod: string;
  dueDate: Date;
  paidAt: Date | null;
  refundedAt: Date | null;
  createdAt: Date;
  invoice?: {
    url: string;
  } | null;
  appointment: {
    id: string;
    scheduledAt: Date;
    patient: {
      id: string;
      user: {
        name: string | null;
        email?: string | null;
        phone?: string | null;
      };
    };
    doctor: {
      id: string;
      user: {
        name: string | null;
        email?: string | null;
        phone?: string | null;
      };
    };
  };
};

interface PaymentClientProps {
  transactions: TransactionData[];
}

export function PaymentsClient({ transactions }: PaymentClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Construct URL with search parameter
    const params = new URLSearchParams();
    if (searchTerm) {
      params.set("search", searchTerm);
    }

    router.push(`/app/payments?${params.toString()}`);
  };

  const openTransactionDetails = (transaction: TransactionData) => {
    setSelectedTransaction(transaction);
    setIsModalOpen(true);
  };

  // Helper to get status badge color
  const getStatusBadge = (status: string) => {
    const styles: Record<string, string> = {
      PENDING: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
      PAID: "bg-green-100 text-green-800 hover:bg-green-100",
      REFUNDED: "bg-purple-100 text-purple-800 hover:bg-purple-100",
      FAILED: "bg-red-100 text-red-800 hover:bg-red-100",
    };

    const labels: Record<string, string> = {
      PENDING: "Pendente",
      PAID: "Pago",
      REFUNDED: "Reembolsado",
      FAILED: "Falha",
    };

    return (
      <Badge className={styles[status] || "bg-gray-100 text-gray-800"}>
        {labels[status] || status}
      </Badge>
    );
  };

  // Helper to get payment method label
  const getPaymentMethodLabel = (method: string) => {
    const labels: Record<string, string> = {
      CREDIT_CARD: "Cartão de Crédito",
      BOLETO: "Boleto",
      PIX: "Pix",
    };

    return labels[method] || method;
  };

  // Format date helper
  const formatDate = (date: Date | null) => {
    if (!date) return "-";
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short",
    }).format(new Date(date));
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <h1 className="text-2xl font-bold">Pagamentos</h1>

        <form onSubmit={handleSearch} className="flex w-full sm:w-auto gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Buscar pagamentos..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button type="submit">Buscar</Button>
        </form>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {transactions.length === 0 ? (
          <div className="md:col-span-2 lg:col-span-3 text-center py-12">
            <h3 className="text-lg font-medium">Nenhum pagamento encontrado</h3>
            <p className="text-gray-500 mt-2">
              Não foi possível encontrar pagamentos com os filtros atuais.
            </p>
          </div>
        ) : (
          transactions.map((transaction) => (
            <Card key={transaction.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">
                    {formatCurrency(transaction.amount)}
                  </CardTitle>
                  {getStatusBadge(transaction.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-gray-500">Paciente</div>
                    <div className="font-medium truncate">
                      {transaction?.appointment?.patient?.user?.name || "-"}
                    </div>

                    <div className="text-gray-500">Médico</div>
                    <div className="font-medium truncate">
                      {transaction?.appointment?.doctor?.user?.name || "-"}
                    </div>

                    <div className="text-gray-500">Forma de pagamento</div>
                    <div className="font-medium">
                      {getPaymentMethodLabel(transaction?.paymentMethod || "")}
                    </div>

                    <div className="text-gray-500">Data da consulta</div>
                    <div className="font-medium">
                      {formatDate(transaction?.appointment?.scheduledAt || null)}
                    </div>


                  </div>

                  <div className="pt-3 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => openTransactionDetails(transaction)}
                    >
                      Ver detalhes
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <PaymentDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        transaction={selectedTransaction}
      />
    </div>
  );
}
