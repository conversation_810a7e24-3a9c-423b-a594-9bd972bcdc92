"use client";

import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { BadgeCheck } from "lucide-react";
import Link from "next/link";
import { formatCurrency } from "./format-utils";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { TransactionData } from "./payments-client";

interface PaymentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: TransactionData | null;
}

export function PaymentDetailsModal({ isOpen, onClose, transaction }: PaymentDetailsModalProps) {
  if (!transaction) return null;

  // Helper para formatar datas
  const formatDate = (date: Date | null) => {
    if (!date) return "-";
    return format(new Date(date), "dd 'de' MMMM 'de' yyyy, HH:mm", {
      locale: ptBR,
    });
  };

  // Helper para obter o status do pagamento em português
  const getPaymentStatus = (status: string) => {
    const statusMap: Record<string, { label: string; color: string }> = {
      PENDING: { label: "Pendente", color: "bg-yellow-100 text-yellow-800" },
      PAID: { label: "Pago", color: "bg-green-100 text-green-800" },
      REFUNDED: { label: "Reembolsado", color: "bg-purple-100 text-purple-800" },
      FAILED: { label: "Falha", color: "bg-red-100 text-red-800" },
    };

    const paymentStatus = statusMap[status] || { label: status, color: "bg-gray-100 text-gray-800" };

    return (
      <Badge className={paymentStatus.color}>{paymentStatus.label}</Badge>
    );
  };

  // Helper para obter o método de pagamento em português
  const getPaymentMethod = (method: string) => {
    const methodMap: Record<string, string> = {
      CREDIT_CARD: "Cartão de Crédito",
      BOLETO: "Boleto",
      PIX: "Pix",
    };

    return methodMap[method] || method;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Detalhes do Pagamento</span>
            <div className="mr-4">
              {getPaymentStatus(transaction.status)}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-6 md:grid-cols-2 p-2">
          {/* Informações da Transação */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg border-b pb-2">Informações da Transação</h3>

            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="text-gray-500">ID da Transação</div>
              <div className="font-medium text-sm truncate">{transaction.id}</div>

              <div className="text-gray-500">Valor Total</div>
              <div className="font-medium">{formatCurrency(transaction.amount)}</div>

              <div className="text-gray-500">Taxa da Plataforma</div>
              <div className="font-medium">{formatCurrency(transaction.platformFee)}</div>

              <div className="text-gray-500">Valor do Médico</div>
              <div className="font-medium">{formatCurrency(transaction.doctorAmount)}</div>

              <div className="text-gray-500">Método de Pagamento</div>
              <div className="font-medium">{getPaymentMethod(transaction.paymentMethod)}</div>

              <div className="text-gray-500">Data de Vencimento</div>
              <div className="font-medium">{formatDate(transaction.dueDate)}</div>

              <div className="text-gray-500">Data de Pagamento</div>
              <div className="font-medium">{formatDate(transaction.paidAt)}</div>

              {transaction.refundedAt && (
                <>
                  <div className="text-gray-500">Data de Reembolso</div>
                  <div className="font-medium">{formatDate(transaction.refundedAt)}</div>
                </>
              )}

              <div className="text-gray-500">Data de Criação</div>
              <div className="font-medium">{formatDate(transaction.createdAt)}</div>
            </div>

            {transaction.invoice && (
              <div className="pt-4">
                <a
                  href={transaction.invoice.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 text-primary hover:underline"
                >
                  <BadgeCheck className="h-4 w-4" />
                  Ver Nota Fiscal
                </a>
              </div>
            )}
          </div>

          {/* Informações da Consulta */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg border-b pb-2">Informações da Consulta</h3>

            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="text-gray-500">ID da Consulta</div>
              <div className="font-medium text-sm truncate">{transaction.appointment.id}</div>

              <div className="text-gray-500">Data Agendada</div>
              <div className="font-medium">{formatDate(transaction.appointment.scheduledAt)}</div>
            </div>

            <h4 className="font-medium text-md mt-4 border-b pb-1">Informações do Paciente</h4>
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="text-gray-500">Nome</div>
              <div className="font-medium">{transaction?.appointment?.patient?.user?.name || "-"}</div>

              <div className="text-gray-500">E-mail</div>
              <div className="font-medium text-sm truncate">{transaction?.appointment?.patient?.user?.email || "-"}</div>

              <div className="text-gray-500">Telefone</div>
              <div className="font-medium">{transaction?.appointment?.patient?.user?.phone || "-"}</div>
            </div>

            <h4 className="font-medium text-md mt-4 border-b pb-1">Informações do Médico</h4>
            <div className="grid grid-cols-2 gap-x-4 gap-y-2">
              <div className="text-gray-500">Nome</div>
              <div className="font-medium">{transaction?.appointment?.doctor?.user?.name || "-"}</div>

              <div className="text-gray-500">E-mail</div>
              <div className="font-medium text-sm truncate">{transaction?.appointment?.doctor?.user?.email || "-"}</div>

              <div className="text-gray-500">Telefone</div>
              <div className="font-medium">{transaction.appointment.doctor.user.phone || "-"}</div>
            </div>

            <div className="pt-4">
              <Link href={`/app/appointments/${transaction.appointment.id}`}>
                <Button variant="outline" className="w-full">
                  Ver Detalhes da Consulta
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
