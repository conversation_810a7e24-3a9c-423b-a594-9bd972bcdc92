import { Suspense } from "react";
import { db } from "database";
import { PaginationButton } from "@ui/components/pagination-button";
import { PaymentsClient } from "./components/payments-client";
import Loading from "./loading";
import { currentUser } from "@saas/auth/lib/current-user";

// Definindo a página como dinâmica para evitar cache
export const dynamic = "force-dynamic";

interface PageProps {
  searchParams?: {
    page?: string;
    search?: string;
  };
}

export default async function PaymentsPage({
  searchParams,
}: PageProps = {}) {
  try {
    // Obter o usuário atual
    const { user } = await currentUser();

    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    // Parâmetros de paginação seguros
    const resolvedSearchParams = await searchParams;
    const page = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
    const pageSize = 9;
    const skip = Math.max(0, ((!isNaN(page) ? page : 1) - 1) * pageSize);
    const search = resolvedSearchParams?.search || "";

    // Construir condições de busca
    let whereCondition: any = {};

    // Filtragem baseada no papel do usuário
    if (user.role === "DOCTOR") {
      // Se for médico, mostrar apenas transações relacionadas a este médico
      const doctor = await db.doctor.findUnique({
        where: { userId: user.id },
        select: { id: true }
      });

      if (!doctor) {
        throw new Error("Médico não encontrado");
      }

      whereCondition.appointment = {
        doctorId: doctor.id
      };
    }

    // Adicionar busca por nome do paciente ou médico se houver termo de busca
    if (search) {
      whereCondition.OR = [
        {
          appointment: {
            patient: {
              user: {
                name: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
        },
        {
          appointment: {
            doctor: {
              user: {
                name: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            },
          },
        },
        // Busca pelo ID de transação
        {
          id: {
            contains: search,
          },
        },
      ];
    }

    // Buscar contagem total de transações com os filtros aplicados
    const totalTransactions = await db.transaction.count({
      where: whereCondition,
    });

    // Buscar transações
    const transactions = await db.transaction.findMany({
      where: whereCondition,
      skip,
      take: pageSize,
      orderBy: {
        createdAt: "desc", // Transações mais recentes primeiro
      },
      select: {
        id: true,
        amount: true,
        platformFee: true,
        doctorAmount: true,
        status: true,
        paymentMethod: true,
        dueDate: true,
        paidAt: true,
        refundedAt: true,
        createdAt: true,
        invoice: {
          select: {
            url: true,
          }
        },
        appointment: {
          select: {
            id: true,
            scheduledAt: true,
            patient: {
              select: {
                id: true,
                user: {
                  select: {
                    name: true,
                    email: true,
                    phone: true,
                  },
                },
              },
            },
            doctor: {
              select: {
                id: true,
                user: {
                  select: {
                    name: true,
                    email: true,
                    phone: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Converter valores Decimal para números
    const serializedTransactions = transactions.map(transaction => ({
      ...transaction,
      amount: Number(transaction.amount),
      platformFee: Number(transaction.platformFee),
      doctorAmount: Number(transaction.doctorAmount),
    }));

    // Calcular o total de páginas
    const totalPages = Math.ceil(totalTransactions / pageSize);

    return (
      <div className="flex flex-col gap-6">
        <Suspense fallback={<Loading />}>
          <PaymentsClient transactions={serializedTransactions} />
        </Suspense>

        {totalPages > 1 && (
          <div className="flex justify-center">
            <PaginationButton
              currentPage={!isNaN(page) ? page : 1}
              totalPages={totalPages}
              baseUrl="/app/payments"
              searchParams={search ? { search } : undefined}
            />
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error("Erro ao carregar pagamentos:", error);

    return (
      <div className="container mx-auto p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Erro ao carregar pagamentos</h2>
        <p className="text-muted-foreground mb-6">
          Ocorreu um erro ao tentar carregar a lista de pagamentos.
          Por favor, tente novamente mais tarde.
        </p>

        <div className="bg-red-50 border border-red-200 p-4 rounded-md text-left mb-6">
          <p className="text-red-800 font-medium mb-2">Detalhes do erro:</p>
          <pre className="bg-white p-3 rounded overflow-auto text-sm">
            {error instanceof Error
              ? `${error.name}: ${error.message}\n\n${error.stack}`
              : JSON.stringify(error, null, 2)}
          </pre>
        </div>

        <a href="/app/dashboard" className="text-primary hover:underline">
          Voltar para o Dashboard
        </a>
      </div>
    );
  }
}
