'use client';

import { useState, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@ui/components/tabs';
import {
  AreaChart,
  CircleUserRound,
  FileText,
  HeartPulse,
  Banknote,
} from 'lucide-react';

interface AdminTabsProps {
  defaultTab: string;
  overviewContent: ReactNode;
  doctorsContent: ReactNode;
  patientsContent: ReactNode;
  appointmentsContent: ReactNode;
  financeContent: ReactNode;
}

export function AdminTabs({
  defaultTab,
  overviewContent,
  doctorsContent,
  patientsContent,
  appointmentsContent,
  financeContent,
}: AdminTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab);

  return (
    <Tabs defaultValue={defaultTab} value={activeTab} onValueChange={setActiveTab} className="space-y-4">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="overview" className="flex items-center justify-center gap-2">
          <AreaChart className="h-4 w-4" />
          <span className="hidden sm:inline">Visão Geral</span>
        </TabsTrigger>
        <TabsTrigger value="doctors" className="flex items-center justify-center gap-2">
          <HeartPulse className="h-4 w-4" />
          <span className="hidden sm:inline">Médicos</span>
        </TabsTrigger>
        <TabsTrigger value="patients" className="flex items-center justify-center gap-2">
          <CircleUserRound className="h-4 w-4" />
          <span className="hidden sm:inline">Pacientes</span>
        </TabsTrigger>
        <TabsTrigger value="appointments" className="flex items-center justify-center gap-2">
          <FileText className="h-4 w-4" />
          <span className="hidden sm:inline">Consultas</span>
        </TabsTrigger>
        <TabsTrigger value="finance" className="flex items-center justify-center gap-2">
          <Banknote className="h-4 w-4" />
          <span className="hidden sm:inline">Financeiro</span>
        </TabsTrigger>
      </TabsList>
      <TabsContent value="overview" className="space-y-4">
        {overviewContent}
      </TabsContent>
      <TabsContent value="doctors" className="space-y-4">
        {doctorsContent}
      </TabsContent>
      <TabsContent value="patients" className="space-y-4">
        {patientsContent}
      </TabsContent>
      <TabsContent value="appointments" className="space-y-4">
        {appointmentsContent}
      </TabsContent>
      <TabsContent value="finance" className="space-y-4">
        {financeContent}
      </TabsContent>
    </Tabs>
  );
}
