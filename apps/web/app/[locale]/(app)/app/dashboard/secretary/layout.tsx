// apps/web/app/[locale]/(saas)/app/dashboard/secretary/layout.tsx
import { redirect } from "next/navigation";
import { currentUser } from "@saas/auth/lib/current-user";

export default async function SecretaryDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = await currentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Verificar se o usuário é SECRETARY
  if (user.role !== "SECRETARY") {
    // Redirecionar para o dashboard apropriado com base no papel do usuário
    if (user.role === "DOCTOR") {
      redirect("/app/dashboard/doctor");
    } else if (user.role === "USER" || user.role === "PATIENT") {
      redirect("/app/dashboard/user");
    } else if (user.role === "HOSPITAL" || user.role === "ADMIN") {
      redirect("/app/dashboard/admin");
    } else {
      redirect("/app/dashboard");
    }
  }

  return (
    <div className="mx-auto py-6">
      {children}
    </div>
  );
}
