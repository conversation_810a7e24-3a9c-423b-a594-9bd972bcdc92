'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Calendar,
  Clock,
  Users,
  FileText,
  Phone,
  Stethoscope,
  Calendar as CalendarIcon,
  Mail
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@ui/components/card';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { DashboardHeader } from '@ui/components/header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs';

// Dados mock para desenvolvimento - serão substituídos por dados reais
const mockAppointments = [
  {
    id: '1',
    patientName: '<PERSON>',
    patientAvatar: null,
    doctorName: 'Dr. <PERSON>',
    doctorSpecialty: 'Cardiologia',
    time: new Date(Date.now() + 1000 * 60 * 60 * 2), // 2 horas a partir de agora
    status: 'SCHEDULED',
    phone: '(11) 98765-4321'
  },
  {
    id: '2',
    patientName: '<PERSON>',
    patientAvatar: null,
    doctorName: 'Dra. Ana Souza',
    doctorSpecialty: 'Neurologia',
    time: new Date(Date.now() + 1000 * 60 * 60 * 4), // 4 horas a partir de agora
    status: 'SCHEDULED',
    phone: '(11) 91234-5678'
  },
  {
    id: '3',
    patientName: 'Pedro Santos',
    patientAvatar: null,
    doctorName: 'Dr. Carlos Mendes',
    doctorSpecialty: 'Cardiologia',
    time: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 dia a partir de agora
    status: 'SCHEDULED',
    phone: '(11) 99876-5432'
  }
];

const mockDoctors = [
  {
    id: '1',
    name: 'Dr. Carlos Mendes',
    specialty: 'Cardiologia',
    avatarUrl: null,
    status: 'ONLINE',
    appointmentsToday: 4
  },
  {
    id: '2',
    name: 'Dra. Ana Souza',
    specialty: 'Neurologia',
    avatarUrl: null,
    status: 'BUSY',
    appointmentsToday: 3
  },
  {
    id: '3',
    name: 'Dr. Roberto Almeida',
    specialty: 'Ortopedia',
    avatarUrl: null,
    status: 'OFFLINE',
    appointmentsToday: 0
  }
];

// Componente principal
const SecretaryDashboard = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('appointments');

  useEffect(() => {
    // Simular carregamento de dados
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.getHours().toString().padStart(2, '0') + ':' +
           date.getMinutes().toString().padStart(2, '0');
  };

  const formatDate = (date: Date) => {
    return date.getDate().toString().padStart(2, '0') + '/' +
           (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
           date.getFullYear();
  };

  // Verificar se a data é hoje
  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  // Total de consultas hoje
  const appointmentsToday = mockAppointments.filter(app => isToday(app.time)).length;

  // Data atual formatada
  const today = new Date();
  const dayOfWeek = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'][today.getDay()];
  const months = ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho', 'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'];
  const formattedToday = `${dayOfWeek}, ${today.getDate()} de ${months[today.getMonth()]}`;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Mensagem de saudação baseada na hora do dia
  const getGreeting = () => {
    const hour = today.getHours();
    if (hour < 12) return "Bom dia";
    if (hour < 18) return "Boa tarde";
    return "Boa noite";
  };

  return (
    <div className="container mx-auto px-4 space-y-6">
      <DashboardHeader
        heading={`${getGreeting()}, Secretária`}
        text={formattedToday}
      />

      {/* Resumo rápido */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Consultas Hoje</p>
                <h3 className="text-2xl font-bold">{appointmentsToday}</h3>
              </div>
              <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Calendar className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              Total do dia
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Médicos Ativos</p>
                <h3 className="text-2xl font-bold">{mockDoctors.filter(d => d.status === 'ONLINE').length}</h3>
              </div>
              <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Stethoscope className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              De {mockDoctors.length} médicos cadastrados
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Próxima Consulta</p>
                <h3 className="text-2xl font-bold">{formatTime(mockAppointments[0].time)}</h3>
              </div>
              <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Clock className="h-5 w-5 text-primary" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              Dr. {mockAppointments[0].doctorName.split(' ')[1]}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs de conteúdo */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="appointments">Consultas</TabsTrigger>
          <TabsTrigger value="doctors">Médicos</TabsTrigger>
          <TabsTrigger value="patients">Pacientes</TabsTrigger>
        </TabsList>

        {/* Tab de Consultas */}
        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Próximas Consultas</CardTitle>
              <CardDescription>
                Consultas agendadas para hoje e amanhã
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAppointments.map(appointment => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => router.push(`/app/appointments/${appointment.id}`)}
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={appointment.patientAvatar || undefined} />
                        <AvatarFallback>
                          {appointment.patientName.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{appointment.patientName}</p>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Stethoscope className="h-3 w-3 mr-1" />
                          {appointment.doctorName} • {appointment.doctorSpecialty}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <Badge className="bg-blue-100 text-blue-800 mb-1">
                        {isToday(appointment.time) ? `Hoje ${formatTime(appointment.time)}` : formatDate(appointment.time)}
                      </Badge>
                      <div className="flex space-x-1">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(`tel:${appointment.phone}`);
                          }}
                        >
                          <Phone className="h-3 w-3" />
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/app/messages?patient=${appointment.id}`);
                          }}
                        >
                          <Mail className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push('/app/agenda')}>
                Ver Agenda Completa
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Tab de Médicos */}
        <TabsContent value="doctors" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Status dos Médicos</CardTitle>
              <CardDescription>
                Disponibilidade atual e próximas consultas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockDoctors.map(doctor => (
                  <div
                    key={doctor.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => router.push(`/app/doctors/${doctor.id}`)}
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={doctor.avatarUrl || undefined} />
                        <AvatarFallback>
                          {doctor.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{doctor.name}</p>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <span>{doctor.specialty}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <Badge className={
                        doctor.status === 'ONLINE' ? 'bg-green-100 text-green-800' :
                        doctor.status === 'BUSY' ? 'bg-orange-100 text-orange-800' :
                        'bg-gray-100 text-gray-800'
                      }>
                        {doctor.status === 'ONLINE' ? 'Disponível' :
                        doctor.status === 'BUSY' ? 'Em Consulta' :
                        'Ausente'}
                      </Badge>
                      <span className="text-xs text-muted-foreground mt-1">
                        {doctor.appointmentsToday} consultas hoje
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push('/app/doctors')}>
                Gerenciar Médicos
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Tab de Pacientes */}
        <TabsContent value="patients" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Ações Rápidas</CardTitle>
              <CardDescription>
                Funções principais para gerenciamento de pacientes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => router.push('/app/patients/new')}
                >
                  <Users className="h-6 w-6 mb-2" />
                  <span>Cadastrar Paciente</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => router.push('/app/appointments/new')}
                >
                  <CalendarIcon className="h-6 w-6 mb-2" />
                  <span>Agendar Consulta</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => router.push('/app/patients')}
                >
                  <FileText className="h-6 w-6 mb-2" />
                  <span>Lista de Pacientes</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-auto py-6 flex flex-col items-center justify-center"
                  onClick={() => router.push('/app/messages')}
                >
                  <Mail className="h-6 w-6 mb-2" />
                  <span>Mensagens</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SecretaryDashboard;
