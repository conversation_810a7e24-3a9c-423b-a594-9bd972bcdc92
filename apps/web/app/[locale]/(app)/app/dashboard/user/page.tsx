// apps/web/app/[locale]/(saas)/app/dashboard/user/page.tsx
import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { DashboardHeader } from '@ui/components/header';
import { Skeleton } from '@ui/components/skeleton';
// import { getPatientDashboardData } from "@/actions/patients/get-dashboard-data";
import { UpcomingAppointments } from './components/upcoming-appointments';
import { PlantaoStatus } from './components/plantao-status';
import { RecentPrescriptions } from './components/recent-prescriptions';
import { HealthIndicators } from './components/health-indicators';
import { MedicalHistory } from './components/medical-history';
import { getPatientDashboardData } from './get-dashboard-data';
import { patientProfileExists } from '@lib/patient-utils';
import { currentUser } from '@saas/auth/lib/current-user';
// import { DashboardRefresh } from "./components/dashboard-refresh";

export default async function PatientDashboardPage() {
	// Verificar usuário e perfil de paciente
	const { user } = await currentUser();

	if (!user) {
		redirect('/auth/login');
	}

	// Verificar explicitamente se existe perfil de paciente
	const hasPatientProfile = await patientProfileExists(user.id);

	if (!hasPatientProfile) {
		redirect('/onboarding/patient');
	}

	// Continuar apenas se o perfil existir
	const response = await getPatientDashboardData();

	// Verificar se houve erro na busca de dados
	if (response.status === 'error') {
		// Se for um erro de autorização, redirecionar para login
		if (response.message === 'Não autenticado') {
			redirect('/auth/login');
		}

		// Para outros erros, exibir mensagem
		return (
			<div className='flex flex-col gap-6'>
				<DashboardHeader
					heading='Olá, Paciente 👋'
					text='Bem-vindo ao seu painel de controle da saúde.'
					className='mb-0 sm:mb-0'
				/>

				<Card>
					<CardContent className='p-6 text-center'>
						<p className='text-red-500'>
							{response.message ||
								'Ocorreu um erro ao carregar os dados do dashboard'}
						</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Garantir que response.data existe
	if (!response.data) {
		return (
			<div className='flex flex-col gap-6'>
				<DashboardHeader
					heading='Olá, Paciente 👋'
					text='Bem-vindo ao seu painel de controle da saúde.'
					className='mb-0 sm:mb-0'
				/>

				<Card>
					<CardContent className='p-6 text-center'>
						<p className='text-red-500'>Dados do dashboard não disponíveis</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Extrair dados da resposta bem-sucedida
	const {
		patient,
		upcomingAppointments,
		recentAppointments,
		activePrescriptions: rawPrescriptions,
	} = response.data;

	// Formatar as prescrições para o tipo esperado pelo componente
	const activePrescriptions = rawPrescriptions.map((prescription: any) => ({
		...prescription,
		createdAt: prescription.createdAt.toISOString(),
		appointment: {
			...prescription.appointment,
			doctor: {
				...prescription.appointment.doctor,
				specialty:
					prescription.appointment.doctor.specialties[0]?.name ||
					'Medicina Geral',
			},
		},
	}));

	// Preparar dados dos indicadores de saúde
	const healthData = {
		weight: patient.weight,
		height: patient.height,
		bmi: patient.bmi,
		bloodType: patient.bloodType,
		allergies: patient.allergies,
		chronicConditions: patient.chronicConditions,
	};

	// Obter o primeiro nome do usuário para saudação personalizada
	const firstName = user.name ? user.name.split(' ')[0] : 'Paciente';

	// Função para obter saudação baseada na hora do dia
	const getGreeting = () => {
		const hour = new Date().getHours();
		if (hour < 12) return 'Bom dia';
		if (hour < 18) return 'Boa tarde';
		return 'Boa noite';
	};

	return (
		<div className='flex flex-col gap-6'>
			<DashboardHeader
				heading={`${getGreeting()}, ${firstName} 👋`}
				text='Bem-vindo ao seu painel de controle da saúde.'
				className='mb-0 sm:mb-0'
			/>

			{/* Status de Plantão - sempre no topo se houver */}
			<div className='grid gap-6'>
				<Suspense fallback={<Skeleton className='h-[200px] w-full' />}>
					<PlantaoStatus appointments={upcomingAppointments} />
				</Suspense>
			</div>

			<div className='grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
				{/* Card de próximas consultas */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader>
						<CardTitle>Próximas Consultas</CardTitle>
						<CardDescription>Suas consultas agendadas</CardDescription>
					</CardHeader>
					<CardContent>
						<Suspense fallback={<AppointmentsSkeleton />}>
							<UpcomingAppointments appointments={upcomingAppointments.filter(apt => !apt.isOnDuty)} />
						</Suspense>
					</CardContent>
				</Card>

				{/* Card de indicadores de saúde */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader>
						<CardTitle>Indicadores de Saúde</CardTitle>
						<CardDescription>Seus dados de saúde</CardDescription>
					</CardHeader>
					<CardContent>
						<Suspense fallback={<Skeleton className='h-[200px] w-full' />}>
							<HealthIndicators healthData={healthData} />
						</Suspense>
					</CardContent>
				</Card>

				{/* Card de prescrições recentes */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader>
						<CardTitle>Prescrições Ativas</CardTitle>
						<CardDescription>
							Medicamentos prescritos atualmente
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Suspense fallback={<PrescriptionsSkeleton />}>
							<RecentPrescriptions prescriptions={activePrescriptions} />
						</Suspense>
					</CardContent>
				</Card>

				{/* Card de histórico médico */}
				<Card className='col-span-1 md:col-span-2 lg:col-span-3'>
					<CardHeader>
						<CardTitle>Histórico Médico Recente</CardTitle>
						<CardDescription>Suas últimas consultas médicas</CardDescription>
					</CardHeader>
					<CardContent>
						<Suspense fallback={<AppointmentsSkeleton />}>
							<MedicalHistory appointments={recentAppointments} />
						</Suspense>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}

// Componentes de Skeleton para carregamento
function AppointmentsSkeleton() {
	return (
		<div className='space-y-4'>
			{[1, 2, 3].map((i) => (
				<div key={i} className='flex items-center gap-4'>
					<Skeleton className='h-10 w-10 sm:h-12 sm:w-12 rounded-full' />
					<div className='space-y-2 flex-1'>
						<Skeleton className='h-3 sm:h-4 w-full max-w-[200px]' />
						<Skeleton className='h-3 sm:h-4 w-full max-w-[160px]' />
					</div>
				</div>
			))}
		</div>
	);
}

function PrescriptionsSkeleton() {
	return (
		<div className='space-y-4'>
			{[1, 2, 3].map((i) => (
				<div key={i}>
					<div className='flex items-center gap-3'>
						<Skeleton className='h-8 w-8 sm:h-10 sm:w-10 rounded-md' />
						<div className='space-y-2 flex-1'>
							<Skeleton className='h-3 sm:h-4 w-full max-w-[180px]' />
							<Skeleton className='h-3 sm:h-4 w-full max-w-[140px]' />
						</div>
					</div>
					<div className='mt-2 flex gap-2'>
						<Skeleton className='h-7 sm:h-8 w-full rounded-md' />
						<Skeleton className='h-7 sm:h-8 w-full rounded-md' />
					</div>
				</div>
			))}
		</div>
	);
}
