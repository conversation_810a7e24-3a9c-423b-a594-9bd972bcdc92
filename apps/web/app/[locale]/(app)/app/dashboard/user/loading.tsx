// apps/web/app/[locale]/(saas)/app/dashboard/patient/loading.tsx
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { DashboardHeader } from '@ui/components/header';
import { Skeleton } from '@ui/components/skeleton';

export default function PatientDashboardLoading() {
	return (
		<div className='flex flex-col gap-2 sm:gap-6'>
			<DashboardHeader
				heading='Olá, Paciente 👋'
				text='Bem-vindo ao seu painel de controle da saúde.'
				className='mb-0'
			/>

			<div className='grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
				{/* Skeleton para próximas consultas */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader className='px-3 sm:px-6 py-3 sm:py-4'>
						<CardTitle>Próximas Consultas</CardTitle>
						<CardDescription>Suas consultas agendadas</CardDescription>
					</CardHeader>
					<CardContent className='px-3 sm:px-6 py-2 sm:py-4'>
						<div className='space-y-4'>
							{[1, 2, 3].map((i) => (
								<div key={i} className='flex items-center gap-4'>
									<Skeleton className='h-10 w-10 sm:h-12 sm:w-12 rounded-full' />
									<div className='space-y-2 flex-1'>
										<Skeleton className='h-3 sm:h-4 w-full max-w-[200px]' />
										<Skeleton className='h-3 sm:h-4 w-full max-w-[160px]' />
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Skeleton para indicadores de saúde */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader className='px-3 sm:px-6 py-3 sm:py-4'>
						<CardTitle>Indicadores de Saúde</CardTitle>
						<CardDescription>Seus dados de saúde</CardDescription>
					</CardHeader>
					<CardContent className='px-3 sm:px-6 py-2 sm:py-4'>
						<div className='grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4'>
							<Skeleton className='h-16 sm:h-20 w-full rounded-md' />
							<Skeleton className='h-16 sm:h-20 w-full rounded-md' />
						</div>
						<Skeleton className='h-16 sm:h-20 w-full rounded-md mb-3 sm:mb-4' />
						<Skeleton className='h-5 sm:h-6 w-full' />
						<Skeleton className='h-5 sm:h-6 w-full mt-2' />
						<Skeleton className='h-5 sm:h-6 w-full mt-2' />
					</CardContent>
				</Card>

				{/* Skeleton para prescrições recentes */}
				<Card className='col-span-1 md:col-span-1 lg:col-span-1'>
					<CardHeader className='px-3 sm:px-6 py-3 sm:py-4'>
						<CardTitle>Prescrições Ativas</CardTitle>
						<CardDescription>
							Medicamentos prescritos atualmente
						</CardDescription>
					</CardHeader>
					<CardContent className='px-3 sm:px-6 py-2 sm:py-4'>
						<div className='space-y-4'>
							{[1, 2, 3].map((i) => (
								<div key={i}>
									<div className='flex items-center gap-3'>
										<Skeleton className='h-8 w-8 sm:h-10 sm:w-10 rounded-md' />
										<div className='space-y-2 flex-1'>
											<Skeleton className='h-3 sm:h-4 w-full max-w-[180px]' />
											<Skeleton className='h-3 sm:h-4 w-full max-w-[140px]' />
										</div>
									</div>
									<div className='mt-2 flex gap-2'>
										<Skeleton className='h-7 sm:h-8 w-full rounded-md' />
										<Skeleton className='h-7 sm:h-8 w-full rounded-md' />
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* Skeleton para histórico médico */}
				<Card className='col-span-1 md:col-span-2 lg:col-span-3'>
					<CardHeader className='px-3 sm:px-6 py-3 sm:py-4'>
						<CardTitle>Histórico Médico Recente</CardTitle>
						<CardDescription>Suas últimas consultas médicas</CardDescription>
					</CardHeader>
					<CardContent className='px-3 sm:px-6 py-2 sm:py-4'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4'>
							{[1, 2, 3, 4].map((i) => (
								<Skeleton
									key={i}
									className='h-[120px] sm:h-[150px] w-full rounded-lg'
								/>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
