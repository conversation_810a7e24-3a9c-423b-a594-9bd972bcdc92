// apps/web/app/[locale]/(saas)/app/dashboard/patient/components/health-indicators.tsx
"use client";

import { useRouter } from "next/navigation";
import { Weight, Ruler, Heart, AlertCircle, Dna } from "lucide-react";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@ui/components/tooltip";

interface HealthData {
  weight?: number | null;
  height?: number | null;
  bmi?: number | null;
  bloodType?: string | null;
  allergies?: string[];
  chronicConditions?: string[];
}

interface HealthIndicatorsProps {
  healthData: HealthData;
}

export function HealthIndicators({ healthData }: HealthIndicatorsProps) {
  const router = useRouter();

  // Função para classificar o IMC
  const getBmiCategory = (bmi: number) => {
    if (bmi < 18.5) return { label: "Abaixo do peso", color: "text-blue-500" };
    if (bmi < 25) return { label: "Peso normal", color: "text-green-500" };
    if (bmi < 30) return { label: "Sobrepeso", color: "text-yellow-500" };
    if (bmi < 35) return { label: "Obesidade grau 1", color: "text-orange-500" };
    if (bmi < 40) return { label: "Obesidade grau 2", color: "text-red-400" };
    return { label: "Obesidade grau 3", color: "text-red-600" };
  };

  // Verificar se há dados suficientes
  const hasData = healthData.weight || healthData.height || healthData.bloodType ||
                  (healthData.allergies && healthData.allergies.length > 0) ||
                  (healthData.chronicConditions && healthData.chronicConditions.length > 0);

  if (!hasData) {
    return (
      <div className="text-center py-4">
        <p className="text-muted-foreground mb-4">
          Seus dados de saúde ainda não foram registrados.
        </p>
        <Button
          variant="outline"
          onClick={() => router.push("/app/settings/profile")}
          className="w-full"
        >
          Completar Perfil Médico
        </Button>
      </div>
    );
  }

  // Classificação do IMC se disponível
  const bmiCategory = healthData.bmi ? getBmiCategory(healthData.bmi) : null;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        {/* Peso */}
        {healthData.weight && (
          <div className="flex flex-col items-center p-2 border rounded-md">
            <Weight className="h-5 w-5 text-primary mb-1" />
            <span className="text-xs text-muted-foreground">Peso</span>
            <span className="font-medium">{healthData.weight} kg</span>
          </div>
        )}

        {/* Altura */}
        {healthData.height && (
          <div className="flex flex-col items-center p-2 border rounded-md">
            <Ruler className="h-5 w-5 text-primary mb-1" />
            <span className="text-xs text-muted-foreground">Altura</span>
            <span className="font-medium">{healthData.height} cm</span>
          </div>
        )}
      </div>



      {/* Tipo Sanguíneo */}
      {healthData.bloodType && (
        <div className="flex items-center p-3 border rounded-md">
          <Heart className="h-5 w-5 text-primary mr-3" />
          <div>
            <span className="text-xs text-muted-foreground block">Tipo Sanguíneo</span>
            <span className="font-medium">{healthData.bloodType}</span>
          </div>
        </div>
      )}

      {/* Alergias */}
      {healthData.allergies && healthData.allergies.length > 0 && (
        <>
          <Separator />
          <div>
            <h5 className="text-sm font-medium mb-2 flex items-center gap-1">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              Alergias
            </h5>
            <ul className="text-sm list-disc list-inside space-y-1 text-muted-foreground">
              {healthData.allergies.slice(0, 3).map((allergy, index) => (
                <li key={index}>{allergy}</li>
              ))}
              {healthData.allergies.length > 3 && (
                <li className="text-primary cursor-pointer list-none" onClick={() => router.push("/app/settings/profile")}>
                  + {healthData.allergies.length - 3} mais
                </li>
              )}
            </ul>
          </div>
        </>
      )}

      {/* Condições crônicas */}
      {healthData.chronicConditions && healthData.chronicConditions.length > 0 && (
        <>
          <Separator />
          <div>
            <h5 className="text-sm font-medium mb-2 flex items-center gap-1">
              <Dna className="h-4 w-4 text-blue-500" />
              Condições Crônicas
            </h5>
            <ul className="text-sm list-disc list-inside space-y-1 text-muted-foreground">
              {healthData.chronicConditions.slice(0, 3).map((condition, index) => (
                <li key={index}>{condition}</li>
              ))}
              {healthData.chronicConditions.length > 3 && (
                <li className="text-primary cursor-pointer list-none" onClick={() => router.push("/app/settings/profile")}>
                  + {healthData.chronicConditions.length - 3} mais
                </li>
              )}
            </ul>
          </div>
        </>
      )}

      <Button
        variant="ghost"
        size="sm"
        className="w-full text-primary"
        onClick={() => router.push("/app/settings/patient/profile")}
      >
        Atualizar Informações
      </Button>
    </div>
  );
}
