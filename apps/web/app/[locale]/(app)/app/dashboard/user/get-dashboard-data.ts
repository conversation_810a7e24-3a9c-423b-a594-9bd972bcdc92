// apps/web/actions/patients/get-dashboard-data.ts
"use server";

import { db } from "database";
import { redirect } from "next/navigation";
import { createApiCaller } from "api/trpc/caller";
import { currentUser } from "@saas/auth/lib/current-user";

/**
 * Busca todos os dados necessários para o dashboard do paciente
 * Esta action é útil para atualizar os dados sem recarregar a página completa
 */
export async function getPatientDashboardData() {
  // Verificar autenticação
  const session = await currentUser();
  if (!session || !session.user) {
    redirect("/auth/login");
  }

  // Verificar se é um paciente
  if (session.user.role !== "USER" && session.user.role !== "PATIENT") {
    return {
      status: "error",
      message: "Apenas pacientes podem acessar este recurso",
    };
  }

  try {
    const apiCaller = await createApiCaller();

    // Buscar o perfil do paciente pelo ID do usuário
    const patient = await db.patient.findFirst({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        weight: true,
        height: true,
        bloodType: true,
        allergies: true,
        chronicConditions: true,
      },
    });

    if (!patient) {
      return {
        status: "error",
        message: "Perfil de paciente não encontrado",
      };
    }

    // Buscar as próximas consultas agendadas (incluindo plantões)
    const upcomingAppointments = await db.appointment.findMany({
      where: {
        patientId: patient.id,
        OR: [
          {
            // Consultas agendadas normais
            status: "SCHEDULED",
            isOnDuty: false,
            scheduledAt: {
              gte: new Date(),
            },
          },
          {
            // Plantões ativos (aguardando médico, aceitos ou em progresso)
            isOnDuty: true,
            status: {
              in: ["SCHEDULED", "IN_PROGRESS"]
            },
            paymentStatus: "PAID"
          }
        ]
      },
      select: {
        id: true,
        scheduledAt: true,
        duration: true,
        consultType: true,
        status: true,
        isOnDuty: true,
        urgencyLevel: true,
        symptoms: true,
        paymentStatus: true,
        createdAt: true,
        acceptedAt: true,
        doctor: {
          select: {
            id: true,
            specialties: {
              select: {
                name: true
              }
            },
            user: {
              select: {
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: [
        {
          isOnDuty: "desc" // Plantões primeiro
        },
        {
          scheduledAt: "asc",
        },
      ],
      take: 5, // Aumentar para mostrar mais
    });

    // Buscar histórico de consultas recentes
    const recentAppointments = await db.appointment.findMany({
      where: {
        patientId: patient.id,
        status: "COMPLETED",
      },
      select: {
        id: true,
        scheduledAt: true,
        duration: true,
        consultType: true,
        doctor: {
          select: {
            id: true,
            specialties: true,
            user: {
              select: {
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
        prescription: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        scheduledAt: "desc",
      },
      take: 3,
    });

    // Buscar prescrições ativas
    const activePrescriptions = await db.prescription.findMany({
      where: {
        appointment: {
          patientId: patient.id,
        },
        status: "active",
      },
      select: {
        id: true,
        createdAt: true,
        pdfUrl: true,
        status: true,
        appointment: {
          select: {
            id: true,
            doctor: {
              select: {
                specialties: true,
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    // Calcular IMC se houver peso e altura
    const bmi = patient.weight && patient.height
      ? Number(((patient.weight / Math.pow(patient.height / 100, 2)).toFixed(1)))
      : null;

    return {
      status: "success",
      data: {
        patient: {
          ...patient,
          bmi,
        },
        upcomingAppointments,
        recentAppointments,
        activePrescriptions,
      },
    };
  } catch (error) {
    console.error("Erro ao buscar dados do dashboard:", error);
    return {
      status: "error",
      message: "Ocorreu um erro ao buscar os dados do dashboard",
    };
  }
}

/**
 * Busca apenas as próximas consultas para atualização parcial do dashboard
 */
export async function getUpcomingAppointments() {
  const session = await currentUser();
  if (!session || !session.user) {
    return { status: "error", message: "Não autenticado" };
  }

  try {
    const patient = await db.patient.findFirst({
      where: { userId: session.user.id },
      select: { id: true },
    });

    if (!patient) {
      return { status: "error", message: "Paciente não encontrado" };
    }

    const appointments = await db.appointment.findMany({
      where: {
        patientId: patient.id,
        status: "SCHEDULED",
        scheduledAt: { gte: new Date() },
      },
      select: {
        id: true,
        scheduledAt: true,
        duration: true,
        consultType: true,
        doctor: {
          select: {
            specialties: true,
            user: {
              select: {
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: { scheduledAt: "asc" },
      take: 3,
    });

    return { status: "success", data: appointments };
  } catch (error) {
    console.error("Erro ao buscar consultas:", error);
    return { status: "error", message: "Erro ao buscar consultas" };
  }
}

/**
 * Busca apenas as prescrições ativas para atualização parcial do dashboard
 */
export async function getActivePrescriptions() {
  const session = await currentUser();
  if (!session || !session.user) {
    return { status: "error", message: "Não autenticado" };
  }

  try {
    const patient = await db.patient.findFirst({
      where: { userId: session.user.id },
      select: { id: true },
    });

    if (!patient) {
      return { status: "error", message: "Paciente não encontrado" };
    }

    const prescriptions = await db.prescription.findMany({
      where: {
        appointment: {
          patientId: patient.id,
        },
        status: "active",
      },
      select: {
        id: true,
        createdAt: true,
        pdfUrl: true,
        status: true,
        appointment: {
          select: {
            id: true,
            doctor: {
              select: {
                specialties: true,
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 5,
    });

    return { status: "success", data: prescriptions };
  } catch (error) {
    console.error("Erro ao buscar prescrições:", error);
    return { status: "error", message: "Erro ao buscar prescrições" };
  }
}
