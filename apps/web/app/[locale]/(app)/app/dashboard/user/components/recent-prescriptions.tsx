// apps/web/app/[locale]/(saas)/app/dashboard/patient/components/recent-prescriptions.tsx
"use client";

import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { FileText, Download, Calendar, PenLine } from "lucide-react";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";

interface Prescription {
  id: string;
  createdAt: string;
  pdfUrl?: string;
  content?: any;
  status: string;
  appointment: {
    id: string;
    doctor: {
      user: {
        name: string;
      };
      specialty: string;
    };
  };
}

interface RecentPrescriptionsProps {
  prescriptions: Prescription[];
}

export function RecentPrescriptions({ prescriptions }: RecentPrescriptionsProps) {
  const router = useRouter();

  // Função para baixar a prescrição
  const downloadPrescription = (url: string, filename: string) => {
    if (!url) return;

    // Criar um link temporário para download
    const a = document.createElement("a");
    a.href = url;
    a.download = filename || "prescricao.pdf";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Função para visualizar detalhes da prescrição
  const viewPrescription = (appointmentId: string) => {
    router.push(`/app/appointments/${appointmentId}`);
  };

  if (prescriptions.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground">Você não tem prescrições ativas no momento.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {prescriptions.map((prescription, index) => (
        <div key={prescription.id}>
          {index > 0 && <Separator className="my-3" />}

          <div className="flex items-start gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <FileText className="h-5 w-5 text-primary" />
            </div>

            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm truncate">
                Prescrição Dr. {prescription.appointment.doctor.user.name}
              </h4>

              <div className="flex items-center text-xs text-muted-foreground mt-1">
                <Calendar className="h-3 w-3 mr-1" />
                <span>
                  {format(new Date(prescription.createdAt), "dd/MM/yyyy", { locale: ptBR })}
                </span>

                <PenLine className="h-3 w-3 ml-2 mr-1" />
                <span className="truncate">{prescription.appointment.doctor.specialty}</span>
              </div>

              <div className="mt-2 flex gap-2">
                {prescription.pdfUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2 flex-1 gap-1"
                    onClick={() => downloadPrescription(
                      prescription.pdfUrl || "",
                      `prescricao_${format(new Date(prescription.createdAt), "dd-MM-yyyy")}.pdf`
                    )}
                  >
                    <Download className="h-3 w-3" />
                    <span className="text-xs">PDF</span>
                  </Button>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 flex-1 text-xs"
                  onClick={() => viewPrescription(prescription.appointment.id)}
                >
                  Detalhes
                </Button>
              </div>
            </div>
          </div>
        </div>
      ))}

      <Button
        variant="ghost"
        className="w-full text-primary"
        onClick={() => router.push("/app/prescriptions")}
      >
        Ver Todas as Prescrições
      </Button>
    </div>
  );
}
