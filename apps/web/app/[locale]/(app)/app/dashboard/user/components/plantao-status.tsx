"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Clock,
  Stethoscope,
  AlertCircle,
  AlertTriangle,
  Info,
  User<PERSON>heck,
  MessageSquare,
  Timer,
  CheckCircle
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { cn } from "@ui/lib";

interface PlantaoStatusProps {
  appointments: any[];
}

export function PlantaoStatus({ appointments }: PlantaoStatusProps) {
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Atualizar o tempo a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Filtrar apenas appointments de plantão
  const plantaoAppointments = appointments.filter(apt => apt.isOnDuty);

  if (plantaoAppointments.length === 0) {
    return null;
  }

  const getUrgencyIcon = (level: string) => {
    switch (level) {
      case 'HIGH':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'MEDIUM':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'LOW':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getUrgencyLabel = (level: string) => {
    switch (level) {
      case 'HIGH':
        return '🔴 Muito Urgente';
      case 'MEDIUM':
        return '🟡 Urgente';
      case 'LOW':
        return '🟢 Pouco Urgente';
      default:
        return '⚫ Sem classificação';
    }
  };

  const getStatusInfo = (appointment: any) => {
    if (appointment.status === 'SCHEDULED' && !appointment.doctorId) {
      return {
        label: 'Aguardando Médico',
        description: 'Médicos online estão sendo notificados',
        color: 'bg-blue-100 text-blue-800',
        icon: <Timer className="h-4 w-4" />,
        action: 'waiting'
      };
    }

    if (appointment.status === 'SCHEDULED' && appointment.doctorId) {
      return {
        label: 'Médico Atribuído',
        description: 'Aguardando início do atendimento',
        color: 'bg-green-100 text-green-800',
        icon: <UserCheck className="h-4 w-4" />,
        action: 'assigned'
      };
    }

    if (appointment.status === 'IN_PROGRESS') {
      return {
        label: 'Em Atendimento',
        description: 'Consulta em andamento',
        color: 'bg-emerald-100 text-emerald-800',
        icon: <MessageSquare className="h-4 w-4" />,
        action: 'active'
      };
    }

    return {
      label: 'Agendado',
      description: 'Aguardando horário',
      color: 'bg-gray-100 text-gray-800',
      icon: <Clock className="h-4 w-4" />,
      action: 'scheduled'
    };
  };

  const calculateWaitTime = (createdAt: string) => {
    const created = new Date(createdAt);
    const diffMs = currentTime.getTime() - created.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes} min`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}min`;
    }
  };

  const joinAppointment = (appointmentId: string) => {
    			router.push(`/app/zapchat?appointment=${appointmentId}`);
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Stethoscope className="h-5 w-5" />
          Plantão Médico
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {plantaoAppointments.map((appointment) => {
          const statusInfo = getStatusInfo(appointment);
          const waitTime = calculateWaitTime(appointment.createdAt);

          return (
            <div key={appointment.id} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={cn(statusInfo.color, "flex items-center gap-1")}>
                      {statusInfo.icon}
                      <span className="ml-1">{statusInfo.label}</span>
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      {getUrgencyIcon(appointment.urgencyLevel)}
                      {getUrgencyLabel(appointment.urgencyLevel)}
                    </Badge>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    {statusInfo.description}
                  </p>

                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>Tempo na fila: {waitTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>Criado em: {format(new Date(appointment.createdAt), "dd/MM 'às' HH:mm", { locale: ptBR })}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Informações do médico se já foi atribuído */}
              {appointment.doctor && (
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={appointment.doctor.user.avatarUrl || ""} />
                    <AvatarFallback>
                      {appointment.doctor.user.name?.substring(0, 2).toUpperCase() || "DR"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">Dr(a). {appointment.doctor.user.name}</h4>
                    <p className="text-muted-foreground text-xs">
                      {appointment.doctor.specialties?.map(s => s.name).join(', ') || 'Clínico Geral'}
                    </p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              )}

              {/* Ações baseadas no status */}
              <div className="flex gap-2">
                {statusInfo.action === 'waiting' && (
                  <div className="flex-1 text-center py-2 px-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-center gap-2 text-blue-700">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm font-medium">Procurando médico disponível...</span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Tempo estimado: {
                        appointment.urgencyLevel === 'HIGH' ? '5-10 min' :
                        appointment.urgencyLevel === 'MEDIUM' ? '15-25 min' :
                        '20-40 min'
                      }
                    </p>
                  </div>
                )}

                {statusInfo.action === 'assigned' && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => joinAppointment(appointment.id)}
                  >
                    Aguardar início
                  </Button>
                )}

                {statusInfo.action === 'active' && (
                  <Button
                    variant="default"
                    size="sm"
                    className="flex-1"
                    onClick={() => joinAppointment(appointment.id)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Continuar Atendimento
                  </Button>
                )}
              </div>

              {/* Sintomas/Motivo */}
              {appointment.symptoms && (
                <div className="pt-2 border-t">
                  <p className="text-xs text-muted-foreground">
                    <strong>Motivo:</strong> {appointment.symptoms}
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}
