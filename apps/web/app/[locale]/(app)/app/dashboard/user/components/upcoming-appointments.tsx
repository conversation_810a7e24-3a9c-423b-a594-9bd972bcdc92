// apps/web/app/[locale]/(saas)/app/dashboard/patient/components/upcoming-appointments.tsx
"use client";

import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, Clock, Video, Phone, MessageSquare } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";

// Mapeamento de ícones para tipos de consulta
const consultTypeIcons = {
  VIDEO: Video,
  AUDIO: Phone,
  CHAT: MessageSquare,
};

interface UpcomingAppointmentsProps {
  appointments: any[];
}

export function UpcomingAppointments({ appointments }: UpcomingAppointmentsProps) {
  const router = useRouter();

  // Função para entrar na sala de consulta
  const joinAppointment = (appointmentId: string) => {
    router.push(`/app/zapchat?appointment=${appointmentId}`);
  };

  // Função para exibir detalhes da consulta
  const viewAppointmentDetails = (appointmentId: string) => {
    router.push(`/app/zapchat?appointment=${appointmentId}`);
  };

  // Função para verificar se a consulta pode ser iniciada (15 minutos antes do horário)
  const canJoinAppointment = (scheduledAt: string) => {
    const appointmentTime = new Date(scheduledAt);
    const now = new Date();
    const diffMinutes = (appointmentTime.getTime() - now.getTime()) / (1000 * 60);
    return diffMinutes <= 15 && diffMinutes > -30; // Pode entrar 15min antes e até 30min depois
  };

  if (appointments.length === 0) {
    return (
      <div className="text-left py-6">
        <p className="text-muted-foreground">Você não tem consultas agendadas.</p>

      </div>
    );
  }

  return (
    <div className="space-y-4">
      {appointments.map((appointment) => {
        const ConsultIcon = consultTypeIcons[appointment.consultType] || Video;
        const canJoin = canJoinAppointment(appointment.scheduledAt);

        return (
          <div key={appointment.id} className="flex flex-col p-3 border rounded-lg hover:bg-slate-50">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={appointment.doctor.user.avatarUrl || ""} />
                <AvatarFallback>
                  {appointment.doctor.user.name?.substring(0, 2).toUpperCase() || "DR"}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <h4 className="font-medium text-sm">Dr. {appointment.doctor.user.name}</h4>
                <p className="text-muted-foreground text-xs">{appointment.doctor.specialty}</p>
              </div>

              <Badge className={`ml-auto flex items-center ${canJoin ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"}`}>
                <ConsultIcon className="h-3 w-3 mr-1" />
                CONSULTA
              </Badge>
            </div>

            <div className="mt-3 flex items-center text-xs text-muted-foreground">
              <Calendar className="h-3 w-3 mr-1" />
              <span>
                {format(new Date(appointment.scheduledAt), "dd 'de' MMMM', às' HH:mm", { locale: ptBR })}
              </span>
              <Clock className="h-3 w-3 ml-2 mr-1" />
              <span>{appointment.duration} min</span>
            </div>

            <div className="mt-3 flex gap-2">
              {canJoin ? (
                <Button
                  variant="default"
                  size="sm"
                  className="flex-1"
                  onClick={() => joinAppointment(appointment.id)}
                >
                  Entrar na Consulta
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => viewAppointmentDetails(appointment.id)}
                >
                  Acessar consulta
                </Button>
              )}
            </div>
          </div>
        );
      })}

      <Button
        variant="ghost"
        className="w-full text-primary"
        onClick={() => router.push("/app/appointments")}
      >
        Ver Todas as Consultas
      </Button>
    </div>
  );
}
