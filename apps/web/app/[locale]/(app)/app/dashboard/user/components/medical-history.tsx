// apps/web/app/[locale]/(saas)/app/dashboard/patient/components/medical-history.tsx
"use client";

import { useRouter } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { CheckCircle2, Video, Phone, MessageSquare, FileText, Calendar } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@ui/components/tooltip";

// Mapeamento de ícones para tipos de consulta
const consultTypeIcons = {
  VIDEO: Video,
  AUDIO: Phone,
  CHAT: MessageSquare,
};

interface MedicalHistoryProps {
  appointments: any[];
}

export function MedicalHistory({ appointments }: MedicalHistoryProps) {
  const router = useRouter();

  // Função para ver detalhes da consulta
  const viewAppointmentDetails = (appointmentId: string) => {
    router.push(`/app/appointments/${appointmentId}`);
  };

  if (appointments.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-muted-foreground">Você ainda não realizou nenhuma consulta.</p>

      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid md:grid-cols-2 gap-4">
        {appointments.map((appointment) => {
          const ConsultIcon = consultTypeIcons[appointment.consultType] || Video;
          const hasPrescription = !!appointment.prescription;

          return (
            <div key={appointment.id} className="flex flex-col p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={appointment.doctor.user.avatarUrl || ""} />
                  <AvatarFallback>
                    {appointment.doctor.user.name?.substring(0, 2).toUpperCase() || "DR"}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <h4 className="font-medium text-sm">Dr. {appointment.doctor.user.name}</h4>
                  <p className="text-muted-foreground text-xs">{appointment.doctor.specialty}</p>
                </div>

                <Badge variant="outline" className="ml-auto flex items-center gap-1">
                  <ConsultIcon className="h-3 w-3" />
                  <span>
                    {appointment.consultType === "VIDEO" ? "Vídeo" :
                     appointment.consultType === "AUDIO" ? "Áudio" : "Chat"}
                  </span>
                </Badge>
              </div>

              <div className="mt-3 flex items-center text-xs text-muted-foreground">
                <Calendar className="h-3 w-3 mr-1" />
                <span>
                  {format(new Date(appointment.scheduledAt), "dd/MM/yyyy", { locale: ptBR })}
                </span>
                <span className="mx-1">•</span>
                <span>
                  {formatDistanceToNow(new Date(appointment.scheduledAt), {
                    addSuffix: true,
                    locale: ptBR
                  })}
                </span>
              </div>

              <div className="mt-2 flex items-center text-xs">
                <CheckCircle2 className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-green-600">Consulta realizada</span>

                {hasPrescription && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="ml-2">
                        <FileText className="h-3 w-3 text-primary" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Prescrição disponível</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>

              <div className="mt-auto pt-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs"
                  onClick={() => viewAppointmentDetails(appointment.id)}
                >
                  Ver Detalhes
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      <Button
        variant="ghost"
        className="w-full text-primary"
        onClick={() => router.push("/app/appointments?status=COMPLETED")}
      >
        Ver Histórico Completo
      </Button>
    </div>
  );
}
