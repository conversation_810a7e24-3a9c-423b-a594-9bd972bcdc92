// apps/web/app/[locale]/(saas)/app/dashboard/user/layout.tsx
import { redirect } from "next/navigation";
import { currentUser } from "@saas/auth/lib/current-user";
import { patientProfileExists } from "@lib/patient-utils";

export default async function PatientDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  const { user } = await currentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Verificar se o usuário é USER ou PATIENT
  if (user.role !== "USER" && user.role !== "PATIENT") {
    // Redirecionar para o dashboard apropriado com base no papel do usuário
    if (user.role === "DOCTOR") {
      redirect("/app/dashboard/doctor");
    } else if (user.role === "HOSPITAL" || user.role === "ADMIN") {
      redirect("/app/dashboard/admin");
    } else {
      redirect("/app/dashboard");
    }
  }

  // NOVO: Redirecionar todos os pacientes para o fluxo específico de pacientes
  // Isso garante que pacientes não acessem mais o /app/ e sim o /patient/
  redirect("/patient/dashboard");
}
