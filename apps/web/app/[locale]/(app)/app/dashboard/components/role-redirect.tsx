'use client';

import { redirect } from 'next/navigation';
import { UserRoleSchema } from 'database';
import { useEffect, useState } from 'react';

export default function RoleRedirect({ role, locale, onboardingComplete = true }: { role: string; locale: string; onboardingComplete?: boolean }) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  // Special handling for users who haven't completed onboarding
  if (role === UserRoleSchema.Values.DOCTOR && onboardingComplete === false) {
    redirect(`/${locale}/onboarding/doctor`);
  }

  if ((role === UserRoleSchema.Values.USER || role === UserRoleSchema.Values.PATIENT) && onboardingComplete === false) {
    redirect(`/${locale}/patient/onboarding`);
  }

  switch (role) {
    case UserRoleSchema.Values.DOCTOR:
      redirect(`/${locale}/app/dashboard/doctor`);
    case UserRoleSchema.Values.USER:
    case UserRoleSchema.Values.PATIENT:
      // Redirecionar pacientes para o fluxo específico de pacientes
      redirect(`/${locale}/patient/dashboard`);
    case UserRoleSchema.Values.HOSPITAL:
      redirect(`/${locale}/app/dashboard/hospital`);
    case UserRoleSchema.Values.SECRETARY:
      redirect(`/${locale}/app/dashboard/secretary`);
    case UserRoleSchema.Values.ADMIN:
      return null;
    default:
      redirect(`/${locale}/app/unauthorized`);
  }
}
