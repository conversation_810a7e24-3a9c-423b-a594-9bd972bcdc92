'use client';

import { type DashboardMetrics } from "../../../../../../actions/admin/get-dashboard-metrics";
import {
	<PERSON>Chart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	Legend,
	BarChart,
	Bar,
	<PERSON>hart,
	Pie,
	Cell
} from 'recharts';
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";

// Chart visualization component that renders different charts based on data
export const Overview = ({ data }: { data: any }) => {
	// If no data is provided, show the placeholder
	if (!data || (Array.isArray(data) && data.length === 0)) {
		return (
			<div className="h-[300px] flex items-center justify-center text-muted-foreground">
				Sem dados disponíveis para visualização
			</div>
		);
	}

	// For appointment type data (pie chart)
	if (data[0]?.type) {
		const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

		return (
			<ResponsiveContainer width="100%" height={300}>
				<PieChart>
					<Pie
						data={data}
						cx="50%"
						cy="50%"
						labelLine={false}
						outerRadius={80}
						fill="#8884d8"
						dataKey="count"
						nameKey="type"
						label={({ type, count }) => `${type}: ${count}`}
					>
						{data.map((entry: any, index: number) => (
							<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
						))}
					</Pie>
					<Tooltip formatter={(value, name, props) => [value, props.payload.type]} />
					<Legend />
				</PieChart>
			</ResponsiveContainer>
		);
	}

	// For specialty data (bar chart)
	if (data[0]?.specialty) {
		return (
			<ResponsiveContainer width="100%" height={300}>
				<BarChart
					data={data}
					margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
				>
					<CartesianGrid strokeDasharray="3 3" />
					<XAxis dataKey="specialty" />
					<YAxis />
					<Tooltip />
					<Legend />
					<Bar dataKey="count" fill="#8884d8" name="Médicos" />
				</BarChart>
			</ResponsiveContainer>
		);
	}

	// For payment method or status data (pie chart)
	if (data[0]?.method || data[0]?.status) {
		const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
		const nameKey = data[0]?.method ? "method" : "status";

		return (
			<ResponsiveContainer width="100%" height={300}>
				<PieChart>
					<Pie
						data={data}
						cx="50%"
						cy="50%"
						labelLine={false}
						outerRadius={80}
						fill="#8884d8"
						dataKey="count"
						nameKey={nameKey}
						label={({ name, count }) => `${data[0]?.method ? name : name}: ${count}`}
					>
						{data.map((entry: any, index: number) => (
							<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
						))}
					</Pie>
					<Tooltip />
					<Legend />
				</PieChart>
			</ResponsiveContainer>
		);
	}

	// For time series data (line chart) - appointments by day
	if (data[0]?.date) {
		return (
			<ResponsiveContainer width="100%" height={300}>
				<LineChart
					data={data}
					margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
				>
					<CartesianGrid strokeDasharray="3 3" />
					<XAxis dataKey="date" />
					<YAxis />
					<Tooltip />
					<Legend />
					<Line type="monotone" dataKey="count" stroke="#8884d8" name="Consultas" />
				</LineChart>
			</ResponsiveContainer>
		);
	}

	// Default fallback
	return (
		<div className="h-[300px] flex items-center justify-center text-muted-foreground">
			Visualização não suportada para este tipo de dados
		</div>
	);
};

// Component to display recent appointments
export const RecentAppointments = ({ data }: { data?: DashboardMetrics['transactions']['recent'] }) => {
	// If no data or empty array, show placeholder
	if (!data || data.length === 0) {
		return (
			<div className="text-center py-4 text-muted-foreground">
				Não há consultas recentes para exibir
			</div>
		);
	}

	// Helper function to get badge color based on status
	const getStatusColor = (status: string) => {
		switch (status) {
			case 'COMPLETED':
			case 'PAID':
				return 'bg-green-100 text-green-800';
			case 'SCHEDULED':
			case 'PENDING':
				return 'bg-blue-100 text-blue-800';
			case 'CANCELED':
				return 'bg-red-100 text-red-800';
			case 'NO_SHOW':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	// Format currency
	const formatCurrency = (value: number): string => {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency: 'BRL'
		}).format(value);
	};

	return (
		<div className="space-y-4">
			{data.map((item, index) => (
				<div key={index} className="flex items-center justify-between p-3 border rounded-lg">
					<div className="flex items-center space-x-3">
						<Avatar className="h-9 w-9">
							<AvatarFallback>
								{item.patientName.substring(0, 2).toUpperCase()}
							</AvatarFallback>
						</Avatar>
						<div>
							<p className="font-medium text-sm">{item.patientName}</p>
							<p className="text-xs text-muted-foreground">Médico: {item.doctorName}</p>
						</div>
					</div>
					<div className="flex items-center space-x-2">
						<p className="text-sm font-medium">{formatCurrency(item.amount)}</p>
						<Badge className={getStatusColor(item.status)}>
							{item.status}
						</Badge>
					</div>
				</div>
			))}
		</div>
	);
};

export const TopPerformers = ({ type, data }: { type: string, data: any }) => {
	// If no data or empty array, show placeholder
	if (!data || data.length === 0) {
		return (
			<div className="text-center py-4 text-muted-foreground">
				{type === "doctors" ? "Não há médicos de destaque para exibir" : "Não há pacientes frequentes para exibir"}
			</div>
		);
	}

	// Format currency
	const formatCurrency = (value: number): string => {
		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency: 'BRL'
		}).format(value);
	};

	return (
		<div className="space-y-4">
			{data.map((item: any, index: number) => (
				<div key={index} className="flex items-center justify-between p-3 border rounded-lg">
					<div className="flex items-center space-x-3">
						<Avatar className="h-9 w-9">
							<AvatarFallback>
								{item.name.substring(0, 2).toUpperCase()}
							</AvatarFallback>
						</Avatar>
						<div>
							<p className="font-medium text-sm">{item.name}</p>
							<p className="text-xs text-muted-foreground">
								{type === "doctors" ?
									`${item.appointments} consultas` :
									`${item.appointments} visitas`}
							</p>
						</div>
					</div>
					{item.revenue !== undefined && (
						<div className="text-sm font-medium">
							{formatCurrency(item.revenue)}
						</div>
					)}
				</div>
			))}
		</div>
	);
};
