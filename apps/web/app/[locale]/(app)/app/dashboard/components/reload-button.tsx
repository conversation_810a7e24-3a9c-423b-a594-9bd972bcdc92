'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@ui/components/button';
import { RefreshCw } from 'lucide-react';

export function ReloadButton() {
  const router = useRouter();

  return (
    <Button
      onClick={() => {
        // Refresh the data by refreshing the current route
        router.refresh();
      }}
      variant="outline"
      className="w-full"
    >
      <RefreshCw className="mr-2 h-4 w-4" />
      Recarregar <PERSON>
    </Button>
  );
}
