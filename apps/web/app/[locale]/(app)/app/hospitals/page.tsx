import { Suspense } from "react";

import { PaginationButton } from "@ui/components/pagination-button";
import { db } from "database";
import { HospitalsClient } from "./components/hospitals-client";

interface PageProps {
	searchParams: {
		page?: string;
	};
}

export default async function HospitalsManagementPage({
	searchParams,
}: any) {
	const resolvedSearchParams = await searchParams;
	const page = Number(resolvedSearchParams.page) || 1;
	const pageSize = 9;

	const [hospitals, total] = await Promise.all([
		db.hospital.findMany({
			skip: (page - 1) * pageSize,
			take: pageSize,
			select: {
				id: true,
				name: true,
				address: true,
				logoUrl: true,
			},
		}),
		db.hospital.count(),
	]);

	const totalPages = Math.ceil(total / pageSize);

	return (
		<div className="flex flex-col gap-6">
			<Suspense>
				<HospitalsClient hospitals={hospitals} />
			</Suspense>

			{totalPages > 1 && (
				<div className="flex justify-center">
					<PaginationButton
						currentPage={page}
						totalPages={totalPages}
						baseUrl="/app/hospitals"
					/>
				</div>
			)}
		</div>
	);
}
