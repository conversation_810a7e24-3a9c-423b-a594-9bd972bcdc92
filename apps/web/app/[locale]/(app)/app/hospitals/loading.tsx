import { DashboardHeader } from "@ui/components/header";
import { PatientCardSkeleton } from "./components/patient-card-skeleton";

export default function PatientsLoading() {
	return (
		<>
			<DashboardHeader
				heading="Hospitais"
				text="Gerencie os hospitais cadastrados no sistema."
			/>
			<div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
				{Array.from({ length: 9 }).map((_, i) => (
					<PatientCardSkeleton key={i} />
				))}
			</div>
		</>
	);
}
