"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@ui/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { But<PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";

const formSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	address: z.string().min(1, "Endereço é obrigatório"),
	phone: z.string().min(1, "Telefone é obrigatório"),
	logoUrl: z.string().optional(),
	cnpj: z.string().min(14, "CNPJ deve ter 14 dígitos").optional(),
	contactEmail: z.string().email("Email inválido").optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface HospitalFormProps {
	hospital?: {
		id: string;
		name: string;
		address: string;
		phone: string;
		logoUrl?: string;
		cnpj?: string;
		contactEmail?: string;
	};
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function HospitalForm({
	hospital,
	open,
	onOpenChange,
}: HospitalFormProps) {
	const router = useRouter();
	const { toast } = useToast();
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: hospital?.name || "",
			address: hospital?.address || "",
			phone: hospital?.phone || "",
			logoUrl: hospital?.logoUrl || "",
			cnpj: hospital?.cnpj || "",
			contactEmail: hospital?.contactEmail || "",
		},
	});

	const onSubmit = async (data: FormValues) => {
		try {
			const response = await fetch(
				hospital ? `/api/hospitals/${hospital.id}` : "/api/hospitals",
				{
					method: hospital ? "PUT" : "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						...data,
						// Transforma o endereço em formato JSON
						address: { street: data.address },
					}),
				},
			);

			if (!response.ok) throw new Error();

			toast({
				variant: "success",
				title: hospital
					? "Hospital atualizado com sucesso!"
					: "Hospital criado com sucesso!",
			});
			onOpenChange(false);
			router.refresh();
		} catch {
			toast({
				variant: "error",
				title: hospital
					? "Erro ao atualizar hospital"
					: "Erro ao criar hospital",
			});
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>
						{hospital ? "Editar Hospital" : "Novo Hospital"}
					</DialogTitle>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Nome</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="address"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Endereço</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="phone"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Telefone</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="cnpj"
							render={({ field }) => (
								<FormItem>
									<FormLabel>CNPJ</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="contactEmail"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Email de Contato</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="logoUrl"
							render={({ field }) => (
								<FormItem>
									<FormLabel>URL do Logo</FormLabel>
									<FormControl>
										<Input {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button type="submit" className="w-full">
							{hospital ? "Salvar Alterações" : "Criar Hospital"}
						</Button>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
