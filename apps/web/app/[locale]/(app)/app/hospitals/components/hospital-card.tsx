import { Edit2, Trash2, Users } from "lucide-react";
import Image from "next/image";

import { useState } from "react";

import { Link } from "@i18n/routing";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";

interface HospitalCardProps {
	hospital: {
		id: string;
		name: string;
		cnpj: string;
		address: {
			city: string;
			country: string;
			state: string;
			street: string;
			zipCode: string;
			neighborhood: string;
			number: string;
			complement?: string;
		};
		phone: string;
		logoUrl?: string;
	};
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
}

export function HospitalCard({
	hospital,
	onEdit,
	onDelete,
}: HospitalCardProps) {
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const handleDelete = () => {
		onDelete(hospital.id);
		setIsDeleteDialogOpen(false);
	};

	return (
		<>
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<div className="flex items-center space-x-4">
						{hospital.logoUrl ? (
							<Image
								src={hospital.logoUrl}
								alt={hospital.name}
								width={40}
								height={40}
								className="rounded-full"
							/>
						) : (
							<div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
								<span className="font-semibold text-xl">
									{hospital.name.charAt(0)}
								</span>
							</div>
						)}
						<h3 className="text-lg font-semibold">{hospital.name}</h3>
					</div>
					<div className="flex space-x-2">
						<Button asChild variant="ghost" size="icon">
							<Link href={`/app/hospitals/${hospital.id}/team`}>
								<Users className="h-4 w-4" />
							</Link>
						</Button>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onEdit(hospital.id)}
						>
							<Edit2 className="h-4 w-4" />
						</Button>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => setIsDeleteDialogOpen(true)}
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				</CardHeader>
				<CardContent>
					<div className="space-y-2">
						{hospital.cnpj && (
							<p className="text-muted-foreground text-sm">
								<strong>CNPJ:</strong> {hospital.cnpj}
							</p>
						)}

						<p className="text-muted-foreground text-sm">
							<strong>Endereço:</strong> {hospital.address?.street},{" "}
							{hospital.address?.number}
							{hospital.address?.complement &&
								`, ${hospital.address.complement}`}
							{hospital.address?.neighborhood} {hospital.address?.city} -{" "}
							{hospital.address?.state}
						</p>
						{hospital.phone && (
							<p className="text-muted-foreground text-sm">
								<strong>Telefone:</strong> {hospital.phone}
							</p>
						)}
					</div>
				</CardContent>
			</Card>

			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Excluir Hospital</AlertDialogTitle>
						<AlertDialogDescription>
							Tem certeza que deseja excluir o hospital {hospital.name}? Esta
							ação não pode ser desfeita.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancelar</AlertDialogCancel>
						<AlertDialogAction
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleDelete}
						>
							Sim, excluir hospital
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
