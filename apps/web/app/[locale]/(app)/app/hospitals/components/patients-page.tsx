import { PaginationButton } from "@ui/components/pagination-button";
import { Suspense } from "react";
import { PatientsClient } from "./patients-client";



interface PatientsPageProps {
  patients: any[];
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  userRole: 'ADMIN' | 'DOCTOR' | 'HOSPITAL';
  userId?: string;
  hospitalId?: string;
}

export function PatientsPage({
  patients,
  currentPage,
  totalPages,
  baseUrl,
  userRole,
  userId,
  hospitalId,
}: PatientsPageProps) {
  return (
    <div className="flex flex-col gap-6">
      <Suspense>
        <PatientsClient
          patients={patients}
        />
      </Suspense>

      {totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationButton
            currentPage={currentPage}
            totalPages={totalPages}
            baseUrl={baseUrl}
          />
        </div>
      )}
    </div>
  );
}
