"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";

// Add the PatientFormProps interface
interface PatientFormProps {
	patient?: any;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

import {
	She<PERSON>,
	SheetContent,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { format } from "date-fns";
import { toast } from "sonner";
import {
	createPatient,
	updatePatient,
} from "../../../../../../actions/patients/manage-patient";

const patientFormSchema = z.object({
	// Required fields
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	phone: z.string().min(10, "Telefone inválido"),
	cpf: z.string().min(11, "CPF inválido").max(11, "CPF inválido"),
	birthDate: z.string().min(1, "Data de nascimento é obrigatória"),
	gender: z.enum(["M", "F", "O", "N"]),

	// Optional fields
	rg: z.string().optional(),
	bloodType: z
		.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", ""])
		.optional(),
	allergies: z.string().optional(),
	chronicConditions: z.string().optional(),

	// Optional address fields
	address: z
		.object({
			street: z.string().optional(),
			number: z.string().optional(),
			complement: z.string().optional(),
			neighborhood: z.string().optional(),
			city: z.string().optional(),
			state: z.string().optional(),
			zipCode: z.string().optional(),
		})
		.optional(),
});

export function PatientForm({ patient, open, onOpenChange }: PatientFormProps) {
	const router = useRouter();
	const [loading, setLoading] = useState(false);

	// In the defaultValues
	const form = useForm<z.infer<typeof patientFormSchema>>({
		resolver: zodResolver(patientFormSchema),
		defaultValues: {
			name: patient?.user?.name || "",
			email: patient?.user?.email || "",
			phone: patient?.user?.phone || "",
			cpf: patient?.cpf || "",
			birthDate: patient?.birthDate
				? format(new Date(patient.birthDate), "yyyy-MM-dd")
				: "",
			gender: patient?.gender || "N",
			rg: patient?.rg || "",
			bloodType: patient?.bloodType || "",
			allergies: patient?.allergies || "",
			chronicConditions: patient?.chronicConditions || "",
			address: {
				// ... address fields remain the same
			},
		},
	});

	// Reset form when patient changes
	useEffect(() => {
		if (patient) {
			form.reset({
				name: patient.user.name,
				email: patient.user.email,
				phone: patient.user.phone || "",
				cpf: patient.cpf,
				birthDate: format(new Date(patient.birthDate), "yyyy-MM-dd"),
				gender: patient.gender,
				rg: patient.rg || "",

				bloodType: patient.bloodType || "",
				allergies: patient.allergies || "",
				chronicConditions: patient.chronicConditions || "",
				address: {
					street: patient.address?.street || "",
					number: patient.address?.number || "",
					complement: patient.address?.complement || "",
					neighborhood: patient.address?.neighborhood || "",
					city: patient.address?.city || "",
					state: patient.address?.state || "",
					zipCode: patient.address?.zipCode || "",
				},
			});
		} else {
			form.reset({
				name: "",
				email: "",
				phone: "",
				cpf: "",
				birthDate: "",
				gender: "N",
				rg: "",
				bloodType: "",
				allergies: "",
				chronicConditions: "",
				address: {
					street: "",
					number: "",
					complement: "",
					neighborhood: "",
					city: "",
					state: "",
					zipCode: "",
				},
			});
		}
	}, [patient, form]);

	// Add the onSubmit function inside the PatientForm component
	async function onSubmit(data: z.infer<typeof patientFormSchema>) {
		try {
			setLoading(true);
			const response = patient
				? await updatePatient(patient.id, data)
				: await createPatient(data);

			if (response.status === "error") {
				throw new Error(response.message);
			}

			toast.success(
				patient
					? "Paciente atualizado com sucesso!"
					: "Paciente cadastrado com sucesso!",
			);

			// Force a complete refresh of the page
			router.push(`/app/patients?refresh=${Date.now()}`);
			onOpenChange(false);
		} catch (error) {
			toast.error(
				error instanceof Error
					? error.message
					: patient
						? "Erro ao atualizar paciente"
						: "Erro ao cadastrar paciente",
			);
		} finally {
			setLoading(false);
		}
	}

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full sm:max-w-2xl overflow-y-auto">
				<SheetHeader>
					<SheetTitle>{patient ? "Editar" : "Novo"} Paciente</SheetTitle>
				</SheetHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="mt-8 space-y-6"
					>
						{/* Required Fields Section */}
						<div className="space-y-4">
							<h3 className="font-semibold text-sm">Informações Essenciais</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nome*</FormLabel>
											<FormControl>
												<Input placeholder="Nome completo" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email*</FormLabel>
											<FormControl>
												<Input
													type="email"
													placeholder="<EMAIL>"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Telefone*</FormLabel>
											<FormControl>
												<Input placeholder="(00) 00000-0000" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="cpf"
									render={({ field }) => (
										<FormItem>
											<FormLabel>CPF*</FormLabel>
											<FormControl>
												<Input placeholder="00000000000" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="birthDate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Data de Nascimento*</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="gender"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Gênero*</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Selecione" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="M">Masculino</SelectItem>
													<SelectItem value="F">Feminino</SelectItem>
													<SelectItem value="O">Outro</SelectItem>
													<SelectItem value="N">
														Prefiro não informar
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Optional Health Information */}
						<div className="space-y-4">
							<h3 className="font-semibold text-sm">
								Informações de Saúde (Opcional)
							</h3>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<FormField
									control={form.control}
									name="bloodType"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Tipo Sanguíneo</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Selecione" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{[
														"A+",
														"A-",
														"B+",
														"B-",
														"AB+",
														"AB-",
														"O+",
														"O-",
													].map((type) => (
														<SelectItem key={type} value={type}>
															{type}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="allergies"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Alergias</FormLabel>
											<FormControl>
												<Input placeholder="Alergias conhecidas" {...field} />
											</FormControl>
										</FormItem>
									)}
								/>
							</div>
						</div>

						<div className="flex justify-end space-x-4 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
								disabled={loading}
							>
								Cancelar
							</Button>
							<Button type="submit" disabled={loading}>
								{loading ? "Salvando..." : patient ? "Salvar" : "Cadastrar"}
							</Button>
						</div>
					</form>
				</Form>
			</SheetContent>
		</Sheet>
	);
}
