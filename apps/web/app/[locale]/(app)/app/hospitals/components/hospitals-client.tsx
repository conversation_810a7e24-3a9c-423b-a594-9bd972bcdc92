"use client";

import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { Button } from "@ui/components/button";
import { DashboardHeader } from "@ui/components/header";
import { HospitalCard } from "./hospital-card";
import { HospitalForm } from "./hospital-form";

interface HospitalsClientProps {
	hospitals: any[];
}

export function HospitalsClient({ hospitals }: HospitalsClientProps) {
	const router = useRouter();
	const [formOpen, setFormOpen] = useState(false);
	const [selectedHospital, setSelectedHospital] = useState<any>(null);

	const handleEdit = (id: string) => {
		const hospital = hospitals.find((h) => h.id === id);
		if (hospital) {
			setSelectedHospital(hospital);
			setFormOpen(true);
		}
	};

	const handleDelete = async (id: string) => {
		if (!confirm("Tem certeza que deseja excluir este hospital?")) return;

		try {
			const response = await fetch(`/api/hospitals/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) throw new Error();

			toast.success("Hospital excluído com sucesso!");
			router.refresh();
		} catch {
			toast.error("Erro ao excluir hospital");
		}
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<DashboardHeader
					heading="Hospitais"
					text="Gerencie os hospitais cadastrados no sistema."
				/>
				<Button
					onClick={() => {
						setSelectedHospital(null);
						setFormOpen(true);
					}}
				>
					<Plus className="mr-2 h-4 w-4" />
					Novo Hospital
				</Button>
			</div>

			<div className="grid grid-cols-1 gap-4 px-4 md:grid-cols-2 lg:grid-cols-3">
				{hospitals.map((hospital) => (
					<HospitalCard
						key={hospital.id}
						hospital={hospital}
						onEdit={handleEdit}
						onDelete={handleDelete}
					/>
				))}
			</div>

			<HospitalForm
				hospital={selectedHospital}
				open={formOpen}
				onOpenChange={(open) => {
					setFormOpen(open);
					if (!open) setSelectedHospital(null);
				}}
			/>
		</div>
	);
}
