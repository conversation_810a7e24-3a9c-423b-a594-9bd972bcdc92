"use client";

import { Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@ui/components/button";

import { DashboardHeader } from "@ui/components/header";
import { PatientCard } from "./patient-card";
import { PatientForm } from "./patient-form";

interface PatientsClientProps {
	patients: any[];
}

export function PatientsClient({ patients }: PatientsClientProps) {
	const router = useRouter();
	const [formOpen, setFormOpen] = useState(false);
	const [selectedPatient, setSelectedPatient] = useState<any>(null);

	const handleEdit = (id: string) => {
		const patient = patients.find((p) => p.id === id);
		if (patient) {
			setSelectedPatient(patient);
			setFormOpen(true);
		}
	};

	const handleDelete = async (id: string) => {
		if (!confirm("Tem certeza que deseja excluir este paciente?")) return;

		try {
			const response = await fetch(`/api/patients/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) throw new Error();

			toast.success("Paciente excluído com sucesso!");
			router.refresh();
		} catch {
			toast.error("Erro ao excluir paciente");
		}
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<DashboardHeader
					heading="Pacientes"
					text="Gerencie os pacientes cadastrados no sistema."
				/>
				<Button
					onClick={() => {
						setSelectedPatient(null);
						setFormOpen(true);
					}}
				>
					<Plus className="mr-2 h-4 w-4" />
					Novo Paciente
				</Button>
			</div>

			<div className="grid grid-cols-1 gap-4 px-4 md:grid-cols-2 lg:grid-cols-3">
				{patients.map((patient) => (
					<PatientCard
						key={patient.id}
						patient={patient}
						onEdit={handleEdit}
						onDelete={handleDelete}
					/>
				))}
			</div>

			<PatientForm
				patient={selectedPatient}
				open={formOpen}
				onOpenChange={(open) => {
					setFormOpen(open);
					if (!open) setSelectedPatient(null);
				}}
			/>
		</div>
	);
}
