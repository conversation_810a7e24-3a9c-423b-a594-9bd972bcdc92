import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { InviteMemberForm } from "@saas/settings/components/InviteMemberForm";
import { TeamMembersBlock } from "@saas/settings/components/TeamMembersBlock";

import { DashboardHeader } from "@ui/components/header";
import { Separator } from "@ui/components/separator";
import { createApiCaller } from "api/trpc/caller";
import { getLocale } from "next-intl/server";

interface HospitalTeamPageProps {
	params: {
		id: string;
	};
}

export default async function HospitalTeamPage({
	params,
}: any) {
	const locale = await getLocale();
	const apiCaller = await createApiCaller();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Fetch hospital data
	const hospital = await apiCaller.hospitals.byId({
		id: params.id,
	});

	if (!hospital) {
		return redirect({ href: "/app/hospitals", locale });
	}

	// Fetch team memberships and invitations
	const memberships = await apiCaller.team.memberships({
		teamId: hospital.teamId,
	});

	const invitations = await apiCaller.team.invitations({
		teamId: hospital.teamId,
	});

	return (
		<div className="flex flex-col gap-6">
			<div className="flex flex-col gap-4">
				<DashboardHeader
					heading={hospital.name}
					text={`CNPJ: ${hospital.cnpj}`}
				/>
				<div className="text-muted-foreground text-sm">
					<p>
						{hospital.address.street}, {hospital.address.number}
						{hospital.address.complement && `, ${hospital.address.complement}`}
					</p>
					<p>
						{hospital.address.neighborhood}, {hospital.address.city} -{" "}
						{hospital.address.state}
					</p>
					<p>CEP: {hospital.address.zipCode}</p>
					{hospital.phone && <p>Telefone: {hospital.phone}</p>}
				</div>
			</div>

			<Separator />

			<div className="flex flex-col gap-4">
				<h2 className="font-semibold text-lg">Equipe</h2>
				<InviteMemberForm teamId={hospital.teamId} />
				<TeamMembersBlock memberships={memberships} invitations={invitations} />
			</div>
		</div>
	);
}
