// /apps/web/app/[locale]/(saas)/app/plantao/loading.tsx
import { Card, CardContent } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

export default function Loading() {
  return (
    <div className="space-y-4">
      {/* Skeleton para filtros */}
      <div className="space-y-3 filter-container">
        <div className="flex flex-col sm:flex-row gap-3">
          <Skeleton className="h-10 flex-1 skeleton-mobile" />
          <Skeleton className="h-10 flex-1 skeleton-mobile" />
          <Skeleton className="h-10 flex-1 skeleton-mobile" />
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Skeleton className="h-10 flex-1 skeleton-mobile" />
          <Skeleton className="h-12 w-24 skeleton-mobile" />
        </div>
      </div>

      {/* Skeleton para abas */}
      <div className="flex gap-2 tabs-mobile">
        <Skeleton className="h-12 w-1/2 skeleton-mobile" />
        <Skeleton className="h-12 w-1/2 skeleton-mobile" />
      </div>

      {/* Skeleton para cards de pacientes */}
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i} className="overflow-hidden patient-card-mobile">
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3 flex-1">
                <Skeleton className="h-12 w-12 rounded-full skeleton-mobile" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32 skeleton-mobile" />
                  <Skeleton className="h-3 w-24 skeleton-mobile" />
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <Skeleton className="h-6 w-20 skeleton-mobile" />
                <Skeleton className="h-4 w-16 skeleton-mobile" />
              </div>
            </div>

            <Skeleton className="h-12 w-full mb-3 skeleton-mobile" />
            <Skeleton className="h-16 w-full mb-3 skeleton-mobile" />

            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex gap-2">
                <Skeleton className="h-6 w-20 skeleton-mobile" />
                <Skeleton className="h-6 w-24 skeleton-mobile" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-8 w-20 skeleton-mobile" />
                <Skeleton className="h-8 w-20 skeleton-mobile" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
