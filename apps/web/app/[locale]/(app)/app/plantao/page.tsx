// /apps/web/app/[locale]/(app)/app/plantao/page.tsx
import { Suspense } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Stethoscope, Clock, Users, AlertCircle, UserCheck, UserPlus, MapPin } from "lucide-react";
import Loading from "./loading";
import { EnhancedPlantaoClient } from "./components/enhanced-plantao-client";
import { PlantaoHeader } from "./components/plantao-header";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { redirect } from "next/navigation";
import "./plantao-mobile.css";

export default async function PlantaoMedicoPage() {
  const { user } = await currentUser();

  if (!user) {
    redirect("/auth/login");
  }

  const doctor = await db.doctor.findUnique({
    where: { userId: user.id }
  });

  if (!doctor) {
    return (
      <div className="container mx-auto py-6 px-4">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Acesso Negado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Esta área é restrita apenas para médicos cadastrados na plataforma.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get queue statistics
  const availableAppointments = await db.appointment.count({
    where: {
      isOnDuty: true,
      status: "SCHEDULED",
      paymentStatus: "PAID",
      acceptedByDoctorId: null
    }
  });

  const doctorAppointments = await db.appointment.count({
    where: {
      isOnDuty: true,
      status: "IN_PROGRESS",
      acceptedByDoctorId: doctor.id
    }
  });

  const urgentAppointments = await db.appointment.count({
    where: {
      isOnDuty: true,
      status: "SCHEDULED",
      paymentStatus: "PAID",
      acceptedByDoctorId: null,
      urgencyLevel: "HIGH"
    }
  });

  // Get online doctors count
  const onlineDoctors = await db.doctor.count({
    where: {
      onlineStatus: "ONLINE"
    }
  });

  // Calculate average wait time by fetching appointments and calculating manually
  const appointmentsForWaitTime = await db.appointment.findMany({
    where: {
      isOnDuty: true,
      status: "SCHEDULED",
      paymentStatus: "PAID",
      acceptedByDoctorId: null
    },
    select: {
      createdAt: true
    }
  });

  // Calculate average wait time in minutes
  const now = new Date();
  const totalWaitTimeMinutes = appointmentsForWaitTime.reduce((total, appointment) => {
    const waitTimeMs = now.getTime() - appointment.createdAt.getTime();
    return total + Math.floor(waitTimeMs / (1000 * 60));
  }, 0);

  const avgWaitTimeMinutes = appointmentsForWaitTime.length > 0
    ? Math.round(totalWaitTimeMinutes / appointmentsForWaitTime.length)
    : 0;

  const queueStats = {
    total: availableAppointments,
    urgent: urgentAppointments,
    avgWaitTime: avgWaitTimeMinutes,
    onlineDoctors,
    change: 0 // This would need to be calculated from historical data
  };

  return (
    <div className="container mx-auto py-4 md:py-6 px-4 plantao-container">
      {/* Header Inteligente */}
      <PlantaoHeader
        queueStats={queueStats}
        doctorAppointments={doctorAppointments}
      />

      {/* Estatísticas Dinâmicas - Layout otimizado para mobile */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
        {/* Pacientes na fila */}
        <Card className="relative overflow-hidden patient-card-mobile">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Pacientes na Fila</p>
                <p className="text-2xl md:text-3xl font-bold text-gray-900">{queueStats.total}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {queueStats.change > 0 ? '+' : ''}{queueStats.change} na última hora
                </p>
              </div>
              <div className="h-10 w-10 md:h-12 md:w-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Users className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
              </div>
            </div>

            {/* Progress bar */}
            <div className="mt-3 md:mt-4">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>Capacidade</span>
                <span>{Math.round((queueStats.total / 100) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((queueStats.total / 100) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Casos urgentes */}
        <Card className="relative overflow-hidden patient-card-mobile">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Casos Urgentes</p>
                <p className="text-2xl md:text-3xl font-bold text-red-600">{queueStats.urgent}</p>
                <p className="text-xs text-red-500 mt-1">
                  {queueStats.urgent > 0 ? 'Ação imediata necessária' : 'Nenhum caso crítico'}
                </p>
              </div>
              <div className="h-10 w-10 md:h-12 md:w-12 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                <AlertCircle className="h-5 w-5 md:h-6 md:w-6 text-red-600" />
              </div>
            </div>

            {queueStats.urgent > 0 && (
              <div className="mt-3 md:mt-4 p-2 bg-red-50 rounded-lg">
                <p className="text-xs text-red-700 font-medium">
                  ⚠️ {queueStats.urgent} pacientes aguardando há mais de 30 min
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tempo médio de espera */}
        <Card className="patient-card-mobile">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Tempo Médio</p>
                <p className="text-2xl md:text-3xl font-bold text-gray-900">
                  {queueStats.avgWaitTime > 0 ? `${queueStats.avgWaitTime}min` : '0min'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Tempo médio na fila
                </p>
              </div>
              <div className="h-10 w-10 md:h-12 md:w-12 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Clock className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Seus atendimentos */}
        <Card className="patient-card-mobile">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">Seus Atendimentos</p>
                <p className="text-2xl md:text-3xl font-bold text-green-600">{doctorAppointments}</p>
                <p className="text-xs text-green-500 mt-1">
                  Em andamento
                </p>
              </div>
              <div className="h-10 w-10 md:h-12 md:w-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <UserCheck className="h-5 w-5 md:h-6 md:w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Suspense fallback={<Loading />}>
        <EnhancedPlantaoClient />
      </Suspense>
    </div>
  );
}


