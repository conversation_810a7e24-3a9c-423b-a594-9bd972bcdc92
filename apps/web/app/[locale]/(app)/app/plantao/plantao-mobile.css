/* Estilos personalizados para a página do plantão - Mobile First */

/* Melhorias de scroll para mobile */
.plantao-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Otimizações para cards em mobile */
.patient-card-mobile {
    transition: all 0.2s ease-in-out;
}

.patient-card-mobile:active {
    transform: scale(0.98);
}

/* Melhorias para badges de urgência */
.urgency-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.75rem;
    line-height: 1rem;
    white-space: nowrap;
}

/* Otimizações para botões em mobile */
.mobile-button {
    min-height: 2.5rem;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Melhorias para inputs e selects */
.mobile-input {
    -webkit-appearance: none;
    appearance: none;
    font-size: 16px;
    /* Previne zoom no iOS */
}

/* Otimizações para filtros */
.filter-container {
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.filter-container::-webkit-scrollbar {
    display: none;
}

/* Melhorias para abas */
.tabs-mobile {
    scroll-snap-align: start;
}

/* Otimizações para loading states */
.skeleton-mobile {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Melhorias para responsividade */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full-width {
        width: 100%;
    }

    .mobile-text-center {
        text-align: center;
    }
}

/* Melhorias para acessibilidade */
@media (prefers-reduced-motion: reduce) {
    .patient-card-mobile {
        transition: none;
    }

    .skeleton-mobile {
        animation: none;
    }
}

/* Melhorias para modo escuro (se aplicável) */
/* @media (prefers-color-scheme: dark) {
    .plantao-container {
        background-color: #1f2937;
        color: #f9fafb;
    }
} */

/* Otimizações para telas de alta densidade */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .urgency-badge {
        border-width: 0.5px;
    }
}
