import { Avatar } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { CardContent } from "@ui/components/card";
import {   Tabs,
    TabsList,
    TabsTrigger,
    TabsContent,



 } from "@ui/components/tabs";
import { useState } from "react";


// Mock data
const mockPatients = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON><PERSON> Fabiola Scandalora",
    waitTime: "15 minutos",
    urgencyLevel: "alta",
    hasWhatsapp: true,
    symptoms: "Dor abdominal intensa, febre alta",
    isNew: true
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    waitTime: "22 minutos",
    urgencyLevel: "alta",
    hasWhatsapp: true,
    symptoms: "Falta de ar, palpita<PERSON>es"
  },
  {
    id: "3",
    name: "<PERSON>",
    waitTime: "28 minutos",
    urgencyLevel: "media",
    hasWhatsapp: true,
    symptoms: "Dor de cab<PERSON> persistente",
    isNew: true
  },
  {
    id: "4",
    name: "<PERSON><PERSON>",
    waitTime: "35 minutos",
    urgencyLevel: "media",
    hasWhatsapp: true,
    symptoms: "Tontura e mal estar"
  },
  {
    id: "5",
    name: "Junia Liz Fernandes Silva",
    waitTime: "42 minutos",
    urgencyLevel: "baixa",
    hasWhatsapp: true,
    symptoms: "Dor de garganta"
  },
  {
    id: "6",
    name: "<PERSON> <PERSON>ez<PERSON>",
    waitTime: "55 minutos",
    urgencyLevel: "baixa",
    hasWhatsapp: false,
    symptoms: "Tosse seca persistente",
    isNew: true
  }
];

export default function PlantaoMedicoUI() {
  const [activeTab, setActiveTab] = useState("fila");
  const [inAttendance, setInAttendance] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter patients based on active tab and search term
  const filteredPatients = mockPatients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === "fila") {
      return !inAttendance.includes(patient.id) && matchesSearch;
    } else if (activeTab === "atendimento") {
      return inAttendance.includes(patient.id) && matchesSearch;
    }

    return matchesSearch;
  });

  // Sort patients by urgency level and wait time
  const sortedPatients = [...filteredPatients].sort((a, b) => {
    const urgencyOrder = { alta: 3, media: 2, baixa: 1 };
    const urgencyDiff = urgencyOrder[a.urgencyLevel as keyof typeof urgencyOrder] -
                        urgencyOrder[b.urgencyLevel as keyof typeof urgencyOrder];

    // If urgency levels are different, sort by urgency
    if (urgencyDiff !== 0) return -urgencyDiff;

    // If same urgency, sort by wait time
    const getMinutes = (time: string) => parseInt(time.split(" ")[0]);
    return getMinutes(b.waitTime) - getMinutes(a.waitTime);
  });

  const handleAttendPatient = (patientId: string) => {
    if (inAttendance.includes(patientId)) {
      setInAttendance(inAttendance.filter(id => id !== patientId));
    } else {
      setInAttendance([...inAttendance, patientId]);
    }
  };

  const waitingCount = mockPatients.filter(p => !inAttendance.includes(p.id)).length;
  const attendingCount = inAttendance.length;

  return (
    <div className="bg-gray-50 min-h-screen">
      <header className="bg-white border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">Plantão Médico</h1>
          <div className="text-sm">
            <span className="font-medium">Dr. Alysson Beckert</span>
            <span className="text-gray-500 ml-2">•</span>
            <span className="text-gray-500 ml-2">Online</span>
          </div>
        </div>
      </header>

      <main className="container mx-auto py-6 px-4">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="font-bold text-lg">Atendimentos</h2>
              <div className="flex items-center space-x-3 text-sm">
                <div className="flex items-center">
                  <span className="w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                  <span>Muito Urgente</span>
                </div>
                <div className="flex items-center">
                  <span className="w-3 h-3 rounded-full bg-orange-500 mr-1"></span>
                  <span>Urgente</span>
                </div>
                <div className="flex items-center">
                  <span className="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                  <span>Pouco Urgente</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <input
                type="text"
                placeholder="Pesquisar paciente..."
                className="pl-9 pr-4 py-2 border rounded-md w-64"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 absolute left-2 top-2.5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          <Tabs defaultValue="fila" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="fila">
                Fila de Atendimento <Badge className="ml-2 bg-blue-100 text-blue-800">{waitingCount}</Badge>
              </TabsTrigger>
              <TabsTrigger value="atendimento">
                Em Atendimento <Badge className="ml-2 bg-green-100 text-green-800">{attendingCount}</Badge>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="fila" className="space-y-4">
              {sortedPatients.length > 0 ? (
                sortedPatients.map((patient) => (
                  <PatientCard
                    key={patient.id}
                    patient={patient}
                    isAttending={false}
                    onAttend={() => handleAttendPatient(patient.id)}
                  />
                ))
              ) : (
                <div className="text-center py-10 text-gray-500">
                  Nenhum paciente na fila de atendimento.
                </div>
              )}
            </TabsContent>

            <TabsContent value="atendimento" className="space-y-4">
              {sortedPatients.length > 0 ? (
                sortedPatients.map((patient) => (
                  <PatientCard
                    key={patient.id}
                    patient={patient}
                    isAttending={true}
                    onAttend={() => handleAttendPatient(patient.id)}
                  />
                ))
              ) : (
                <div className="text-center py-10 text-gray-500">
                  Nenhum paciente em atendimento no momento.
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}

function PatientCard({ patient, isAttending, onAttend }) {
  // Map urgency levels to colors and labels
  const urgencyConfig = {
    alta: { color: "bg-red-500", label: "Muito Urgente" },
    media: { color: "bg-orange-500", label: "Urgente" },
    baixa: { color: "bg-green-500", label: "Pouco Urgente" }
  };

  const { color, label } = urgencyConfig[patient.urgencyLevel];

  return (
    <CardContent className={`p-4 border ${isAttending ? "border-blue-500 border-2" : ""}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Avatar className="h-12 w-12 bg-gray-200 text-gray-600">
              {patient.name.charAt(0)}
            </Avatar>
            <span className={`absolute -top-1 -right-1 w-4 h-4 rounded-full ${color} border-2 border-white`}></span>
          </div>

          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-medium">{patient.name}</h3>
              {patient.isNew && (
                <Badge className="bg-blue-100 text-blue-800 text-xs">Novo</Badge>
              )}
            </div>
            <div className="text-sm text-gray-500 flex items-center space-x-2">
              <span>Espera: {patient.waitTime}</span>
              <span>•</span>
              <span className="flex items-center">
                <span className={`inline-block w-2 h-2 rounded-full ${color} mr-1`}></span>
                {label}
              </span>
            </div>
            <div className="text-sm mt-1">{patient.symptoms}</div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {patient.hasWhatsapp && (
            <button className="p-2 rounded-full bg-green-50 text-green-600 hover:bg-green-100 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347z"/>
                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22c-5.523 0-10-4.477-10-10S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z"/>
              </svg>
            </button>
          )}

          <Button
            className={isAttending ? "bg-red-600 hover:bg-red-700" : ""}
            onClick={onAttend}
          >
            {isAttending ? "Finalizar" : "Atender"}
          </Button>
        </div>
      </div>
    </Card>
  );
}
