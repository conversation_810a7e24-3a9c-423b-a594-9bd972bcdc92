"use client";

import { useEffect, useState } from "react";
import { Clock, Users } from "lucide-react";

interface QueueStats {
  total: number;
  urgent: number;
  avgWaitTime: number;
  onlineDoctors: number;
  change: number;
}

interface PlantaoHeaderProps {
  queueStats: QueueStats;
  doctorAppointments: number;
}

export function PlantaoHeader({ queueStats, doctorAppointments }: PlantaoHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };


  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 md:p-6 rounded-lg mb-6">
      {/* Header Principal Simplificado */}
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-xl md:text-2xl font-bold text-gray-900">Plantão Médico</h1>
          <p className="text-sm md:text-base text-gray-600 mt-1">
            {formatDate(currentTime)}
          </p>
        </div>

        {/* Informações do plantão */}
        <div className="flex flex-col sm:flex-row gap-3 text-sm">
          <span className="flex items-center gap-2 text-blue-700">
            <Clock className="h-4 w-4" />
            <span className="font-medium">08:00 - 20:00</span>
          </span>
          <span className="flex items-center gap-2 text-green-700">
            <Users className="h-4 w-4" />
            <span className="font-medium">{queueStats.onlineDoctors} médicos online</span>
          </span>
        </div>
      </div>
    </div>
  );
}
