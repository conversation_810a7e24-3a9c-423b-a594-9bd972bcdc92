

// /apps/web/app/[locale]/(saas)/app/plantao/components/UrgencyBadge.tsx
// import { cn } from "@/lib/utils";

import { cn } from "@ui/lib";
import { AlertTriangle, AlertCircle, Info } from "lucide-react";

interface UrgencyBadgeProps {
  level: "baixa" | "media" | "alta" | "Nível 1 (Emergência)" | "Nível 2 (Urgência)" | "Nível 3 (Pouco Urgente)";
  className?: string;
  compact?: boolean;
}

export function UrgencyBadge({ level, className, compact = false }: UrgencyBadgeProps) {
  // Mapear níveis para configurações
  const getLevelConfig = (level: string) => {
    if (level.includes("Emergência") || level === "alta") {
      return {
        color: "bg-red-100 text-red-800 border-red-200",
        icon: <AlertTriangle className="h-3 w-3" />,
        label: compact ? "EMERGÊNCIA" : "NÍVEL 1 - EMERGÊNCIA"
      };
    } else if (level.includes("Urgência") || level === "media") {
      return {
        color: "bg-amber-100 text-amber-800 border-amber-200",
        icon: <AlertCircle className="h-3 w-3" />,
        label: compact ? "URGÊNCIA" : "NÍVEL 2 - URGÊNCIA"
      };
    } else {
      return {
        color: "bg-green-100 text-green-800 border-green-200",
        icon: <Info className="h-3 w-3" />,
        label: compact ? "NORMAL" : "NÍVEL 3 - NORMAL"
      };
    }
  };

  const config = getLevelConfig(level);

  return (
    <div className={cn(
      "inline-flex items-center gap-1.5 px-2.5 py-1.5 rounded-md border font-medium text-xs",
      config.color,
      compact ? "text-xs" : "text-sm",
      className
    )}>
      {config.icon}
      <span className="font-semibold">{config.label}</span>
    </div>
  );
}
