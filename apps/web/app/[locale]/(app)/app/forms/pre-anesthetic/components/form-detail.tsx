// apps/web/app/[locale]/(saas)/app/forms/pre-anesthetic/components/form-detail.tsx
"use client";

import { DocumentStatus } from "@prisma/client";
import { apiClient } from "@shared/lib/api-client";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import { Label } from "@ui/components/label";
import {
	Sheet,
	SheetContent,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
	AlertTriangle,
	CheckCircle,
	Clock,
	Download,
	Loader2,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

// Status do documento com tradução e cores
const STATUS_MAP = {
	PENDING: {
		label: "Pendente",
		color: "bg-yellow-100 text-yellow-800",
		icon: Clock,
	},
	APPROVED: {
		label: "Aprovado",
		color: "bg-green-100 text-green-800",
		icon: CheckCircle,
	},
	REJECTED: {
		label: "Rejeitado",
		color: "bg-red-100 text-red-800",
		icon: AlertTriangle,
	},
};

interface FormDetailSheetProps {
	form: any;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	userRole: string;
	userIsDoctor: boolean;
	userIsPatient: boolean;
	onFormUpdated?: (form: any) => void;
}

export function FormDetailSheet({
	form,
	open,
	onOpenChange,
	userRole,
	userIsDoctor,
	userIsPatient,
	onFormUpdated,
}: FormDetailSheetProps) {
	const [activeTab, setActiveTab] = useState("form");
	const [isApproving, setIsApproving] = useState(false);

	// Mutation para aprovar o formulário
	const updateFormMutation = apiClient.forms.preanesthetic.update.useMutation();

	// Verificar se o usuário pode aprovar o formulário (apenas médicos)
	const canApprove = userIsDoctor && form.status === DocumentStatus.PENDING;

	// Converter data para formato legível
	const formatDate = (date: string | Date) => {
		if (!date) return "N/A";
		return format(new Date(date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
	};

	// Aprovar o formulário
	const handleApproveForm = async () => {
		if (!canApprove) return;

		setIsApproving(true);
		try {
			const updatedForm = await updateFormMutation.mutateAsync({
				id: form.id,
				formData: form.clinicalEvaluation, // Manter os mesmos dados
				approve: true,
			});

			toast.success("Formulário aprovado com sucesso!");

			if (onFormUpdated) {
				onFormUpdated(updatedForm);
			}
		} catch (error) {
			toast.error("Erro ao aprovar formulário");
		} finally {
			setIsApproving(false);
		}
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full sm:max-w-3xl overflow-y-auto">
				<SheetHeader className="space-y-1">
					<SheetTitle>
						{form.template?.name || "Formulário Pré-Anestésico"}
					</SheetTitle>
					<div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
						<Badge className={STATUS_MAP[form.status]?.color || "bg-gray-100"}>
							{STATUS_MAP[form.status]?.label || form.status}
						</Badge>
						<span>•</span>
						<span>Hospital: {form.hospital?.name}</span>
						<span>•</span>
						<span>Criado em: {formatDate(form.createdAt)}</span>
					</div>
				</SheetHeader>

				<div className="mt-6">
					<Tabs value={activeTab} onValueChange={setActiveTab}>
						<TabsList className="grid grid-cols-2 mb-6">
							<TabsTrigger value="form">Formulário</TabsTrigger>
							<TabsTrigger value="info">Informações</TabsTrigger>
						</TabsList>

						<TabsContent value="form" className="space-y-6">
							{/* Dados do formulário pré-anestésico */}
							{form.template?.fields?.sections?.map(
								(section: any, sectionIndex: number) => (
									<Card key={section.id || sectionIndex}>
										<CardHeader>
											<CardTitle className="text-base">
												{section.title}
											</CardTitle>
											{section.description && (
												<p className="text-sm text-muted-foreground">
													{section.description}
												</p>
											)}
										</CardHeader>
										<CardContent>
											<div className="space-y-4">
												{section.fields.map(
													(field: any, fieldIndex: number) => {
														// Buscar o valor deste campo nos dados do formulário
														// Aqui precisamos identificar como o valor está armazenado no formulário
														// Assumindo que seja algo como clinicalEvaluation.fieldId ou por categoria
														const fieldValue = field.category
															? form[field.category]?.[field.id]
															: form.clinicalEvaluation?.[field.id];

														return (
															<div
																key={field.id || fieldIndex}
																className="space-y-2"
															>
																<Label>
																	{field.label}
																	{field.required && (
																		<span className="text-red-500 ml-1">*</span>
																	)}
																</Label>

																{/* Renderizar o valor do campo de acordo com seu tipo */}
																{field.type === "checkbox" ? (
																	<div className="flex items-center space-x-2">
																		<Checkbox checked={!!fieldValue} disabled />
																		<Label>{field.label}</Label>
																	</div>
																) : field.type === "radio" ||
																	field.type === "select" ? (
																	<div>
																		{field.options?.map(
																			(option: string, optIndex: number) => (
																				<div
																					key={optIndex}
																					className="flex items-center space-x-2"
																				>
																					<div
																						className={`w-4 h-4 rounded-full border ${fieldValue === option ? "bg-primary" : "bg-transparent"}`}
																					/>
																					<span>{option}</span>
																				</div>
																			),
																		)}
																	</div>
																) : (
																	<div className="p-2 border rounded">
																		{fieldValue || (
																			<span className="text-muted-foreground italic">
																				Não preenchido
																			</span>
																		)}
																	</div>
																)}
															</div>
														);
													},
												)}
											</div>
										</CardContent>
									</Card>
								),
							)}

							{/* Botões de ação no rodapé */}
							<div className="flex justify-between mt-6">
								<Button variant="outline" onClick={() => onOpenChange(false)}>
									Fechar
								</Button>

								{canApprove && (
									<Button onClick={handleApproveForm} disabled={isApproving}>
										{isApproving ? (
											<>
												<Loader2 className="mr-2 h-4 w-4 animate-spin" />
												Aprovando...
											</>
										) : (
											<>
												<CheckCircle className="mr-2 h-4 w-4" />
												Aprovar Formulário
											</>
										)}
									</Button>
								)}
							</div>
						</TabsContent>

						<TabsContent value="info" className="space-y-6">
							{/* Informações do paciente */}
							<Card>
								<CardHeader>
									<CardTitle className="text-base">Paciente</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="flex items-center space-x-4">
										<Avatar>
											<AvatarImage
												src={form.appointment?.patient?.user?.avatarUrl || ""}
											/>
											<AvatarFallback>
												{form.patientData?.name
													?.substring(0, 2)
													.toUpperCase() || "PT"}
											</AvatarFallback>
										</Avatar>
										<div>
											<p className="font-medium">
												{form.patientData?.name ||
													form.appointment?.patient?.user?.name}
											</p>
											<p className="text-sm text-muted-foreground">
												{form.patientData?.email ||
													form.appointment?.patient?.user?.email}
											</p>
										</div>
									</div>

									<div className="grid grid-cols-2 gap-4 mt-4 text-sm">
										<div>
											<p className="text-muted-foreground">CPF</p>
											<p>{form.patientData?.cpf || "Não informado"}</p>
										</div>
										<div>
											<p className="text-muted-foreground">
												Data de Nascimento
											</p>
											<p>
												{form.patientData?.birthDate
													? formatDate(form.patientData.birthDate)
													: "Não informado"}
											</p>
										</div>
										<div>
											<p className="text-muted-foreground">Gênero</p>
											<p>
												{form.patientData?.gender === "M"
													? "Masculino"
													: form.patientData?.gender === "F"
														? "Feminino"
														: form.patientData?.gender || "Não informado"}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Informações do médico */}
							<Card>
								<CardHeader>
									<CardTitle className="text-base">Médico</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="flex items-center space-x-4">
										<Avatar>
											<AvatarImage
												src={form.appointment?.doctor?.user?.avatarUrl || ""}
											/>
											<AvatarFallback>
												{form.appointment?.doctor?.user?.name
													?.substring(0, 2)
													.toUpperCase() || "MD"}
											</AvatarFallback>
										</Avatar>
										<div>
											<p className="font-medium">
												{form.appointment?.doctor?.user?.name ||
													"Não atribuído"}
											</p>
											<p className="text-sm text-muted-foreground">
												{form.appointment?.doctor?.crm
													? `CRM ${form.appointment.doctor.crm}-${form.appointment.doctor.crmState}`
													: ""}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Histórico de versões e aprovação */}
							<Card>
								<CardHeader>
									<CardTitle className="text-base">Auditoria</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										<div>
											<p className="text-muted-foreground">Versão</p>
											<p className="font-medium">{form.version}</p>
										</div>

										{form.status === DocumentStatus.APPROVED && (
											<div>
												<p className="text-muted-foreground">Aprovado por</p>
												<p className="text-muted-foreground">Aprovado por</p>
												<p className="font-medium">
													{form.signedBy || "Desconhecido"}
												</p>
												<p className="text-sm text-muted-foreground">
													{form.signedAt
														? formatDate(form.signedAt)
														: "Data desconhecida"}
												</p>
											</div>
										)}

										{form.history && form.history.length > 0 && (
											<div>
												<p className="text-muted-foreground mb-2">
													Histórico de alterações
												</p>
												<div className="space-y-2">
													{form.history.map(
														(historyItem: any, index: number) => (
															<div
																key={index}
																className="text-sm border-l-2 border-muted pl-3 py-1"
															>
																<p>Versão {historyItem.version}</p>
																<p className="text-muted-foreground">
																	{historyItem.updatedAt
																		? formatDate(historyItem.updatedAt)
																		: "Data desconhecida"}
																</p>
															</div>
														),
													)}
												</div>
											</div>
										)}
									</div>
								</CardContent>
							</Card>

							{/* Hospital */}
							<Card>
								<CardHeader>
									<CardTitle className="text-base">Hospital</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="flex items-center space-x-4">
										{form.hospital?.logoUrl ? (
											<img
												src={form.hospital.logoUrl}
												alt={form.hospital.name}
												className="h-10 w-10 object-contain"
											/>
										) : (
											<div className="h-10 w-10 bg-muted flex items-center justify-center rounded-md">
												<span className="text-muted-foreground font-bold">
													{form.hospital?.name?.substring(0, 1) || "H"}
												</span>
											</div>
										)}
										<div>
											<p className="font-medium">{form.hospital?.name}</p>
											<p className="text-sm text-muted-foreground">
												{form.hospital?.contactEmail || ""}
												{form.hospital?.contactPhone &&
													form.hospital?.contactEmail &&
													" • "}
												{form.hospital?.contactPhone || ""}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>

							{/* Botões de ação */}
							<div className="flex justify-between mt-6">
								<Button variant="outline" onClick={() => onOpenChange(false)}>
									Fechar
								</Button>

								<Button variant="outline">
									<Download className="mr-2 h-4 w-4" />
									Exportar PDF
								</Button>
							</div>
						</TabsContent>
					</Tabs>
				</div>
			</SheetContent>
		</Sheet>
	);
}
