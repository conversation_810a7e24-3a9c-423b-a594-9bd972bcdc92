// apps/web/app/[locale]/(saas)/app/appointments/[id]/pre-anesthetic/page.tsx
import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { DashboardHeader } from "@ui/components/header";
import { createApiCaller } from "api/trpc/caller";
import { FileText, Plus } from "lucide-react";
import { getLocale } from "next-intl/server";
import Link from "next/link";

export default async function PreAnestheticFormsPage() {
	const locale = await getLocale();
	const { user } = await currentUser();
	const apiCaller = await createApiCaller();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Get user's hospital ID based on role
	let hospitalId = "";
	if (user.role === "HOSPITAL") {
		hospitalId = user.id;
	} else if (user.role === "DOCTOR" && user.doctor) {
		const doctorHospital = await apiCaller.doctors.hospitals.list({
			doctorId: user.doctor.id,
		});
		if (doctorHospital[0]) {
			hospitalId = doctorHospital[0].hospitalId;
		}
	}

	// Fetch templates if we have a hospital ID
	const templates = hospitalId
		? await apiCaller.forms.templates.list({
				hospitalId,
				isActive: true,
			})
		: [];

	return (
		<div className="container mx-auto py-6">
			<DashboardHeader
				heading="Pre-Anesthetic Forms"
				text="Manage and create pre-anesthetic evaluation forms"
			>
				{(user.role === "ADMIN" || user.role === "HOSPITAL") && (
					<Link href="/app/forms/pre-anesthetic/templates/new">
						<Button>
							<Plus className="mr-2 h-4 w-4" />
							New Template
						</Button>
					</Link>
				)}
			</DashboardHeader>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				{templates.map((template) => (
					<Card key={template.id}>
						<CardHeader>
							<CardTitle>{template.name}</CardTitle>
							<CardDescription>{template.description}</CardDescription>
						</CardHeader>
						<CardContent>
							<Link href={`/app/forms/templates/${template.id}`}>
								<Button variant="secondary" className="w-full">
									<FileText className="mr-2 h-4 w-4" />
									View Template
								</Button>
							</Link>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
