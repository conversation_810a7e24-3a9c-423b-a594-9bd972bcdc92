// apps/web/app/[locale]/(saas)/app/forms/pre-anesthetic/components/forms-client.tsx
"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Calendar } from "@ui/components/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Label } from "@ui/components/label";
import { PaginationButton } from "@ui/components/pagination-button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {} from "@ui/components/sheet";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Eye, FileText, Filter, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { FormDetailSheet } from "./form-detail";

// Status do documento com tradução e cores
const STATUS_MAP = {
	PENDING: { label: "Pendente", color: "bg-yellow-100 text-yellow-800" },
	APPROVED: { label: "Aprovado", color: "bg-green-100 text-green-800" },
	REJECTED: { label: "Rejeitado", color: "bg-red-100 text-red-800" },
};

interface FormsClientProps {
	initialFormsData: any;
	availableHospitals: any[];
	filters: any;
	userRole: string;
	userIsDoctor: boolean;
	userIsPatient: boolean;
}

export function PreAnestheticFormsClient({
	initialFormsData,
	availableHospitals,
	filters,
	userRole,
	userIsDoctor,
	userIsPatient,
}: FormsClientProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [formsData, setFormsData] = useState(initialFormsData);
	const [selectedForm, setSelectedForm] = useState<any>(null);
	const [isDetailOpen, setIsDetailOpen] = useState(false);
	const [isFilterOpen, setIsFilterOpen] = useState(false);

	// Filtros
	const [activeFilters, setActiveFilters] = useState({
		hospitalId: filters.hospitalId || "",
		status: filters.status || "",
		startDate: filters.startDate ? new Date(filters.startDate) : undefined,
		endDate: filters.endDate ? new Date(filters.endDate) : undefined,
	});

	// Função para atualizar os filtros na URL
	const updateFilters = (newFilters: any) => {
		const params = new URLSearchParams(searchParams.toString());

		Object.entries(newFilters).forEach(([key, value]) => {
			if (value) {
				params.set(key, value as string);
			} else {
				params.delete(key);
			}
		});

		// Reset para página 1 quando os filtros mudam
		params.set("page", "1");

		router.push(`/app/forms/pre-anesthetic?${params.toString()}`);
	};

	// Função para limpar todos os filtros
	const clearFilters = () => {
		const params = new URLSearchParams();
		params.set("page", "1");
		router.push(`/app/forms/pre-anesthetic?${params.toString()}`);
	};

	// Formatar a data de uma forma legível
	const formatDate = (date: string | Date) => {
		if (!date) return "N/A";
		return format(new Date(date), "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
	};

	// Exibir detalhes do formulário
	const handleViewForm = (form: any) => {
		setSelectedForm(form);
		setIsDetailOpen(true);
	};

	// Verificar se tem formulários para mostrar
	if (formsData.forms.length === 0) {
		return (
			<div className="flex flex-col items-center justify-center p-8 border rounded-md bg-muted/20">
				<FileText className="h-12 w-12 text-muted-foreground mb-4" />
				<h3 className="text-lg font-medium mb-2">
					Nenhum formulário encontrado
				</h3>
				<p className="text-muted-foreground text-center mb-6">
					{Object.values(activeFilters).some((v) => v)
						? "Tente ajustar ou remover os filtros aplicados."
						: "Não há formulários pré-anestésicos disponíveis no momento."}
				</p>
				{Object.values(activeFilters).some((v) => v) && (
					<Button onClick={clearFilters} variant="outline">
						<X className="mr-2 h-4 w-4" />
						Limpar filtros
					</Button>
				)}
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Área de filtros */}
			<div className="flex flex-wrap justify-between gap-4">
				<div className="flex flex-wrap gap-2">
					{activeFilters.hospitalId && (
						<Badge variant="secondary" className="gap-1">
							Hospital:{" "}
							{availableHospitals.find((h) => h.id === activeFilters.hospitalId)
								?.name || "Desconhecido"}
							<button
								onClick={() =>
									updateFilters({ ...activeFilters, hospitalId: "" })
								}
							>
								<X className="h-3 w-3" />
							</button>
						</Badge>
					)}
					{activeFilters.status && (
						<Badge variant="secondary" className="gap-1">
							Status:{" "}
							{STATUS_MAP[activeFilters.status as keyof typeof STATUS_MAP]
								?.label || activeFilters.status}
							<button
								onClick={() => updateFilters({ ...activeFilters, status: "" })}
							>
								<X className="h-3 w-3" />
							</button>
						</Badge>
					)}
					{activeFilters.startDate && (
						<Badge variant="secondary" className="gap-1">
							A partir de: {format(activeFilters.startDate, "dd/MM/yyyy")}
							<button
								onClick={() =>
									updateFilters({ ...activeFilters, startDate: "" })
								}
							>
								<X className="h-3 w-3" />
							</button>
						</Badge>
					)}
					{activeFilters.endDate && (
						<Badge variant="secondary" className="gap-1">
							Até: {format(activeFilters.endDate, "dd/MM/yyyy")}
							<button
								onClick={() => updateFilters({ ...activeFilters, endDate: "" })}
							>
								<X className="h-3 w-3" />
							</button>
						</Badge>
					)}
					{Object.values(activeFilters).some((v) => v) && (
						<Button variant="ghost" size="sm" onClick={clearFilters}>
							Limpar todos
						</Button>
					)}
				</div>

				<Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
					<PopoverTrigger asChild>
						<Button variant="outline">
							<Filter className="mr-2 h-4 w-4" />
							Filtrar
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-80" align="end">
						<div className="space-y-4">
							<h4 className="font-medium">Filtros</h4>

							{availableHospitals.length > 0 && (
								<div className="space-y-2">
									<Label htmlFor="hospital-filter">Hospital</Label>
									<Select
										value={activeFilters.hospitalId}
										onValueChange={(value) =>
											setActiveFilters({ ...activeFilters, hospitalId: value })
										}
									>
										<SelectTrigger id="hospital-filter">
											<SelectValue placeholder="Todos os hospitais" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="">Todos os hospitais</SelectItem>
											{availableHospitals.map((hospital) => (
												<SelectItem key={hospital.id} value={hospital.id}>
													{hospital.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							)}

							<div className="space-y-2">
								<Label htmlFor="status-filter">Status</Label>
								<Select
									value={activeFilters.status}
									onValueChange={(value) =>
										setActiveFilters({ ...activeFilters, status: value })
									}
								>
									<SelectTrigger id="status-filter">
										<SelectValue placeholder="Todos os status" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="">Todos os status</SelectItem>
										<SelectItem value="PENDING">Pendente</SelectItem>
										<SelectItem value="APPROVED">Aprovado</SelectItem>
										<SelectItem value="REJECTED">Rejeitado</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div className="space-y-2">
								<Label>Período</Label>
								<div className="grid grid-cols-2 gap-2">
									<div>
										<Label htmlFor="start-date" className="text-xs">
											Data inicial
										</Label>
										<Popover>
											<PopoverTrigger asChild>
												<Button
													variant="outline"
													className="w-full justify-start text-left font-normal mt-1"
												>
													{activeFilters.startDate
														? format(activeFilters.startDate, "dd/MM/yyyy")
														: "Selecionar"}
												</Button>
											</PopoverTrigger>
											<PopoverContent className="w-auto p-0">
												<Calendar
													mode="single"
													selected={activeFilters.startDate}
													onSelect={(date) =>
														setActiveFilters({
															...activeFilters,
															startDate: date,
														})
													}
													initialFocus
												/>
											</PopoverContent>
										</Popover>
									</div>
									<div>
										<Label htmlFor="end-date" className="text-xs">
											Data final
										</Label>
										<Popover>
											<PopoverTrigger asChild>
												<Button
													variant="outline"
													className="w-full justify-start text-left font-normal mt-1"
												>
													{activeFilters.endDate
														? format(activeFilters.endDate, "dd/MM/yyyy")
														: "Selecionar"}
												</Button>
											</PopoverTrigger>
											<PopoverContent className="w-auto p-0">
												<Calendar
													mode="single"
													selected={activeFilters.endDate}
													onSelect={(date) =>
														setActiveFilters({
															...activeFilters,
															endDate: date,
														})
													}
													initialFocus
												/>
											</PopoverContent>
										</Popover>
									</div>
								</div>
							</div>

							<div className="flex justify-between pt-2">
								<Button variant="outline" size="sm" onClick={clearFilters}>
									Limpar
								</Button>
								<Button
									size="sm"
									onClick={() => {
										updateFilters({
											hospitalId: activeFilters.hospitalId,
											status: activeFilters.status,
											startDate: activeFilters.startDate
												?.toISOString()
												.split("T")[0],
											endDate: activeFilters.endDate
												?.toISOString()
												.split("T")[0],
										});
										setIsFilterOpen(false);
									}}
								>
									Aplicar Filtros
								</Button>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			</div>

			{/* Lista de formulários */}
			<div className="space-y-4">
				{formsData.forms.map((form: any) => (
					<Card
						key={form.id}
						className="overflow-hidden hover:shadow-md transition-shadow"
					>
						<CardHeader className="flex flex-row items-center justify-between pb-2">
							<div>
								<CardTitle className="text-base">
									{form.template?.name || "Formulário sem template"}
								</CardTitle>
								<p className="text-sm text-muted-foreground">
									{form.hospital?.name || "Hospital não definido"}
								</p>
							</div>
							<Badge
								className={STATUS_MAP[form.status]?.color || "bg-gray-100"}
							>
								{STATUS_MAP[form.status]?.label || form.status}
							</Badge>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
								<div>
									<p className="text-muted-foreground">Paciente</p>
									<p className="font-medium">
										{form.appointment?.patient?.user?.name ||
											form.patientData?.name}
									</p>
								</div>
								<div>
									<p className="text-muted-foreground">Médico</p>
									<p className="font-medium">
										{form.appointment?.doctor?.user?.name || "Não atribuído"}
									</p>
								</div>
								<div>
									<p className="text-muted-foreground">Data da Consulta</p>
									<p className="font-medium">
										{form.appointment?.scheduledAt
											? formatDate(form.appointment.scheduledAt)
											: "N/A"}
									</p>
								</div>
								<div>
									<p className="text-muted-foreground">Atualizado em</p>
									<p className="font-medium">{formatDate(form.updatedAt)}</p>
								</div>
							</div>
							<div className="mt-4 flex justify-end">
								<Button
									onClick={() => handleViewForm(form)}
									variant="outline"
									size="sm"
								>
									<Eye className="mr-2 h-4 w-4" />
									Visualizar
								</Button>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Paginação */}
			{formsData.pagination.pages > 1 && (
				<div className="flex justify-center mt-6">
					<PaginationButton
						currentPage={formsData.pagination.page}
						totalPages={formsData.pagination.pages}
						baseUrl={`/app/forms/pre-anesthetic?${new URLSearchParams({
							...(activeFilters.hospitalId
								? { hospitalId: activeFilters.hospitalId }
								: {}),
							...(activeFilters.status ? { status: activeFilters.status } : {}),
							...(activeFilters.startDate
								? {
										startDate: activeFilters.startDate
											.toISOString()
											.split("T")[0],
									}
								: {}),
							...(activeFilters.endDate
								? { endDate: activeFilters.endDate.toISOString().split("T")[0] }
								: {}),
						}).toString()}`}
					/>
				</div>
			)}

			{/* Sheet para visualização detalhada do formulário */}
			{selectedForm && (
				<FormDetailSheet
					form={selectedForm}
					open={isDetailOpen}
					onOpenChange={setIsDetailOpen}
					userRole={userRole}
					userIsDoctor={userIsDoctor}
					userIsPatient={userIsPatient}
					onFormUpdated={(updatedForm) => {
						// Atualizar o formulário na lista quando for aprovado/atualizado
						setFormsData({
							...formsData,
							forms: formsData.forms.map((f: any) =>
								f.id === updatedForm.id ? updatedForm : f,
							),
						});
					}}
				/>
			)}
		</div>
	);
}
