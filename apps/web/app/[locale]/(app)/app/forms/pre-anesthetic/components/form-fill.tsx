// apps/web/app/[locale]/(saas)/app/appointments/[id]/pre-anesthetic/components/form-fill.tsx
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { ArrowRight, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface PreAnestheticFormFillProps {
	appointment: any;
	templates: any[];
	userRole: string;
}

export function PreAnestheticFormFill({
	appointment,
	templates,
	userRole,
}: PreAnestheticFormFillProps) {
	const router = useRouter();
	const [selectedTemplate, setSelectedTemplate] = useState(templates[0]);
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Mutation para criar o formulário
	const createFormMutation = apiClient.forms.preanesthetic.create.useMutation();

	// Criar esquema de validação dinâmico baseado no template
	const createFormSchema = () => {
		if (!selectedTemplate) return z.object({});

		const schemaFields: Record<string, any> = {};

		selectedTemplate.fields.sections.forEach((section: any) => {
			section.fields.forEach((field: any) => {
				const fieldId = field.id;
				let schema: any = z.string();

				// Ajustar o schema baseado no tipo de campo
				if (field.type === "number") {
					schema = z.string().refine((value) => !isNaN(Number(value)), {
						message: "Deve ser um número válido",
					});
				} else if (field.type === "checkbox") {
					schema = z.boolean().optional();
				}

				// Marcar como obrigatório se necessário
				if (field.required) {
					schema = schema.min(1, { message: "Campo obrigatório" });
				} else {
					schema = schema.optional();
				}

				// Organizar por categoria se existir
				if (field.category) {
					if (!schemaFields[field.category]) {
						schemaFields[field.category] = {};
					}
					schemaFields[field.category][fieldId] = schema;
				} else {
					schemaFields[fieldId] = schema;
				}
			});
		});

		// Criar schema para cada categoria
		const finalSchema: Record<string, any> = {};
		Object.entries(schemaFields).forEach(([key, value]) => {
			if (typeof value === "object" && !z.ZodType.isZodType(value)) {
				finalSchema[key] = z.object(value);
			} else {
				finalSchema[key] = value;
			}
		});

		return z.object(finalSchema);
	};

	// Criar formulário React Hook Form
	const form = useForm({
		resolver: zodResolver(createFormSchema()),
		defaultValues: {},
	});

	// Mudar o template selecionado
	const handleTemplateChange = (templateId: string) => {
		const template = templates.find((t) => t.id === templateId);
		if (template) {
			setSelectedTemplate(template);
			form.reset();
		}
	};

	// Enviar formulário
	const onSubmit = async (data: any) => {
		setIsSubmitting(true);

		try {
			// Vamos organizar os dados por categoria para corresponder com a api
			const formData: Record<string, any> = {
				clinicalEvaluation: {},
				physicalExam: {},
				labTests: {},
				conclusion: {},
			};

			// Percorrer os campos do template e adicionar os valores nos grupos corretos
			selectedTemplate.fields.sections.forEach((section: any) => {
				section.fields.forEach((field: any) => {
					const fieldId = field.id;
					const value = data[field.category]?.[fieldId] || data[fieldId];

					if (field.category) {
						if (!formData[field.category]) {
							formData[field.category] = {};
						}
						formData[field.category][fieldId] = value;
					} else {
						// Se não tiver categoria, colocar no clinicalEvaluation por padrão
						formData.clinicalEvaluation[fieldId] = value;
					}
				});
			});

			// Criar o formulário na API
			await createFormMutation.mutateAsync({
				appointmentId: appointment.id,
				templateId: selectedTemplate.id,
				formData,
			});

			toast.success("Formulário preenchido com sucesso!");
			router.push(`/app/appointments/${appointment.id}`);
			router.refresh();
		} catch (error) {
			toast.error("Erro ao enviar formulário");
			console.error(error);
		} finally {
			setIsSubmitting(false);
		}
	};

	if (!selectedTemplate) {
		return (
			<div className="text-center p-6">
				Nenhum template disponível para preenchimento.
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{templates.length > 1 && (
				<div className="mb-6">
					<FormLabel>Selecione o modelo de formulário</FormLabel>
					<Select
						value={selectedTemplate.id}
						onValueChange={handleTemplateChange}
					>
						<SelectTrigger className="w-full md:w-80">
							<SelectValue placeholder="Selecione um modelo" />
						</SelectTrigger>
						<SelectContent>
							{templates.map((template) => (
								<SelectItem key={template.id} value={template.id}>
									{template.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			)}

			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
					{selectedTemplate.fields.sections.map(
						(section: any, sectionIndex: number) => (
							<Card key={section.id || sectionIndex}>
								<CardHeader>
									<CardTitle className="text-base">{section.title}</CardTitle>
									{section.description && (
										<p className="text-sm text-muted-foreground">
											{section.description}
										</p>
									)}
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										{section.fields.map((field: any, fieldIndex: number) => {
											// Determinar o path correto para o campo no formulário
											const formPath = field.category
												? `${field.category}.${field.id}`
												: field.id;

											return (
												<FormField
													key={field.id || fieldIndex}
													control={form.control}
													name={formPath}
													render={({ field: formField }) => (
														<FormItem>
															<FormLabel>
																{field.label}
																{field.required && (
																	<span className="text-red-500 ml-1">*</span>
																)}
															</FormLabel>

															{field.type === "text" && (
																<FormControl>
																	<Input
																		placeholder={field.placeholder}
																		{...formField}
																	/>
																</FormControl>
															)}

															{field.type === "textarea" && (
																<FormControl>
																	<Textarea
																		placeholder={field.placeholder}
																		{...formField}
																	/>
																</FormControl>
															)}

															{field.type === "number" && (
																<FormControl>
																	<Input
																		type="number"
																		placeholder={field.placeholder}
																		{...formField}
																	/>
																</FormControl>
															)}

															{field.type === "select" && (
																<FormControl>
																	<Select
																		value={formField.value}
																		onValueChange={formField.onChange}
																	>
																		<SelectTrigger>
																			<SelectValue
																				placeholder={
																					field.placeholder ||
																					"Selecione uma opção"
																				}
																			/>
																		</SelectTrigger>
																		<SelectContent>
																			{field.options?.map(
																				(option: string, optIndex: number) => (
																					<SelectItem
																						key={optIndex}
																						value={option}
																					>
																						{option}
																					</SelectItem>
																				),
																			)}
																		</SelectContent>
																	</Select>
																</FormControl>
															)}

															{field.type === "checkbox" && (
																<FormControl>
																	<div className="flex items-center space-x-2">
																		<Checkbox
																			checked={formField.value}
																			onCheckedChange={formField.onChange}
																		/>
																		<span className="text-sm text-muted-foreground">
																			{field.placeholder || "Marcar opção"}
																		</span>
																	</div>
																</FormControl>
															)}

															{field.type === "radio" && (
																<FormControl>
																	<RadioGroup
																		value={formField.value}
																		onValueChange={formField.onChange}
																		className="flex flex-col space-y-1"
																	>
																		{field.options?.map(
																			(option: string, optIndex: number) => (
																				<div
																					key={optIndex}
																					className="flex items-center space-x-2"
																				>
																					<RadioGroupItem
																						value={option}
																						id={`${field.id}-${optIndex}`}
																					/>
																					<label
																						htmlFor={`${field.id}-${optIndex}`}
																					>
																						{option}
																					</label>
																				</div>
																			),
																		)}
																	</RadioGroup>
																</FormControl>
															)}

															<FormDescription>
																{field.description}
															</FormDescription>
															<FormMessage />
														</FormItem>
													)}
												/>
											);
										})}
									</div>
								</CardContent>
							</Card>
						),
					)}

					<div className="flex justify-end">
						<Button type="submit" disabled={isSubmitting}>
							{isSubmitting ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Enviando...
								</>
							) : (
								<>
									Enviar formulário
									<ArrowRight className="ml-2 h-4 w-4" />
								</>
							)}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	);
}
