import { Card, CardContent, CardHeader } from "@ui/components/card";
// apps/web/app/[locale]/(saas)/app/forms/pre-anesthetic/loading.tsx
import { DashboardHeader } from "@ui/components/header";
import { Skeleton } from "@ui/components/skeleton";

export default function Loading() {
	return (
		<>
			<DashboardHeader
				heading="Formulários Pré-Anestésicos"
				text="Visualize e gerencie formulários pré-anestésicos preenchidos"
			/>

			<div className="flex flex-col gap-4 md:flex-row justify-between mb-6">
				<div className="flex flex-col md:flex-row gap-4">
					<Skeleton className="h-10 w-[250px]" />
					<Skeleton className="h-10 w-[200px]" />
					<Skeleton className="h-10 w-[150px]" />
				</div>
				<Skeleton className="h-10 w-[120px]" />
			</div>

			<div className="space-y-4">
				{Array.from({ length: 5 }).map((_, i) => (
					<Card key={i}>
						<CardHeader className="flex flex-row items-center justify-between pb-2">
							<Skeleton className="h-6 w-3/4" />
							<Skeleton className="h-8 w-24 rounded-md" />
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-4 w-full" />
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			<div className="flex justify-center mt-6">
				<Skeleton className="h-10 w-64" />
			</div>
		</>
	);
}
