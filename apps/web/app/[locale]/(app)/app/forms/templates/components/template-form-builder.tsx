// apps/web/app/[locale]/(saas)/app/forms/templates/components/template-form-builder.tsx
"use client";

import {
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@ui/components/accordion";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import { FormField } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { GripVertical, Layers, Plus, PlusCircle, Trash2 } from "lucide-react";
import { nanoid } from "nanoid";
import { useState } from "react";
import { type UseFormReturn, useFieldArray } from "react-hook-form";

interface TemplateFormBuilderProps {
	form: UseFormReturn<any>;
}

// Tipo de campo de formulário
const FIELD_TYPES = [
	{ value: "text", label: "Texto" },
	{ value: "textarea", label: "Texto longo" },
	{ value: "number", label: "Número" },
	{ value: "select", label: "Seleção" },
	{ value: "checkbox", label: "Checkbox" },
	{ value: "radio", label: "Múltipla escolha" },
];

// Categorias pré-definidas para campos
const FIELD_CATEGORIES = [
	{ value: "general", label: "Geral" },
	{ value: "history", label: "Histórico" },
	{ value: "medication", label: "Medicação" },
	{ value: "allergy", label: "Alergia" },
	{ value: "lab", label: "Exames" },
	{ value: "conclusion", label: "Conclusão" },
];

export function TemplateFormBuilder({ form }: TemplateFormBuilderProps) {
	const {
		fields: sections,
		append: appendSection,
		remove: removeSection,
		move: moveSection,
	} = useFieldArray({
		control: form.control,
		name: "sections",
	});

	const [expandedSections, setExpandedSections] = useState<string[]>([
		sections[0]?.id || "",
	]);
	const [expandedField, setExpandedField] = useState<string | null>(null);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [itemToDelete, setItemToDelete] = useState<{
		type: "section" | "field";
		sectionIndex: number;
		fieldIndex?: number;
	} | null>(null);

	// Configuração para drag and drop
	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	// Adicionar uma nova seção
	const handleAddSection = () => {
		const newSectionId = nanoid();
		appendSection({
			id: newSectionId,
			title: `Seção ${sections.length + 1}`,
			description: "",
			fields: [],
		});
		setExpandedSections([...expandedSections, newSectionId]);
	};

	// Adicionar um novo campo a uma seção
	const handleAddField = (sectionIndex: number) => {
		const section = sections[sectionIndex];
		const fieldsArray = useFieldArray({
			control: form.control,
			name: `sections.${sectionIndex}.fields`,
		});

		const newFieldId = nanoid();
		fieldsArray.append({
			id: newFieldId,
			type: "text",
			label: `Campo ${fieldsArray.fields.length + 1}`,
			required: false,
			placeholder: "",
			options: ["Opção 1", "Opção 2"],
			category: "general",
		});

		setExpandedField(newFieldId);
	};

	// Confirmar exclusão de item
	const confirmDelete = () => {
		if (!itemToDelete) return;

		const { type, sectionIndex, fieldIndex } = itemToDelete;

		if (type === "section") {
			removeSection(sectionIndex);
			setExpandedSections(
				expandedSections.filter((id) => id !== sections[sectionIndex].id),
			);
		} else if (fieldIndex !== undefined) {
			const fieldsArray = useFieldArray({
				control: form.control,
				name: `sections.${sectionIndex}.fields`,
			});
			fieldsArray.remove(fieldIndex);
		}

		setDeleteDialogOpen(false);
		setItemToDelete(null);
	};

	// Iniciar processo de exclusão com confirmação
	const handleDeleteItem = (
		type: "section" | "field",
		sectionIndex: number,
		fieldIndex?: number,
	) => {
		setItemToDelete({ type, sectionIndex, fieldIndex });
		setDeleteDialogOpen(true);
	};

	// Manipulador para final do drag and drop
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const activeId = String(active.id);
			const overId = String(over.id);

			// Verificar se são seções sendo movidas
			if (activeId.startsWith("section-") && overId.startsWith("section-")) {
				const activeIndex = Number.parseInt(activeId.replace("section-", ""));
				const overIndex = Number.parseInt(overId.replace("section-", ""));

				moveSection(activeIndex, overIndex);
			}
			// Aqui você pode adicionar lógica para mover campos dentro de uma seção
		}
	};

	// Verificar se temos seções para exibir
	if (sections.length === 0) {
		return (
			<div className="flex flex-col items-center justify-center p-8 border rounded-md">
				<p className="text-muted-foreground mb-4">Nenhuma seção adicionada</p>
				<Button onClick={handleAddSection}>
					<Plus className="mr-2 h-4 w-4" />
					Adicionar Seção
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-medium">Estrutura do formulário</h3>
				<Button onClick={handleAddSection} variant="outline" size="sm">
					<Plus className="mr-2 h-4 w-4" />
					Adicionar Seção
				</Button>
			</div>

			<DndContext
				sensors={sensors}
				collisionDetection={closestCenter}
				onDragEnd={handleDragEnd}
			>
				<SortableContext
					items={sections.map((_, idx) => `section-${idx}`)}
					strategy={verticalListSortingStrategy}
				>
					{sections.map((section, sectionIndex) => (
						<SortableSection
							key={section.id}
							id={`section-${sectionIndex}`}
							section={section}
							sectionIndex={sectionIndex}
							isExpanded={expandedSections.includes(section.id)}
							onExpand={() => {
								if (expandedSections.includes(section.id)) {
									setExpandedSections(
										expandedSections.filter((id) => id !== section.id),
									);
								} else {
									setExpandedSections([...expandedSections, section.id]);
								}
							}}
							onDelete={() => handleDeleteItem("section", sectionIndex)}
							onAddField={() => handleAddField(sectionIndex)}
							form={form}
							expandedField={expandedField}
							setExpandedField={setExpandedField}
							onDeleteField={(fieldIndex) =>
								handleDeleteItem("field", sectionIndex, fieldIndex)
							}
						/>
					))}
				</SortableContext>
			</DndContext>

			<AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
						<AlertDialogDescription>
							{itemToDelete?.type === "section"
								? "Tem certeza que deseja excluir esta seção? Todos os campos desta seção serão excluídos."
								: "Tem certeza que deseja excluir este campo?"}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancelar</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDelete}
							className="bg-destructive text-destructive-foreground"
						>
							Excluir
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}

// Componente de seção ordenável
function SortableSection({
	id,
	section,
	sectionIndex,
	isExpanded,
	onExpand,
	onDelete,
	onAddField,
	form,
	expandedField,
	setExpandedField,
	onDeleteField,
}) {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const { fields } = useFieldArray({
		control: form.control,
		name: `sections.${sectionIndex}.fields`,
	});

	return (
		<div ref={setNodeRef} style={style} className="mb-4">
			<Card>
				<CardHeader className="flex flex-row items-center p-4">
					<div className="flex-1 flex items-center">
						<div
							{...attributes}
							{...listeners}
							className="cursor-grab mr-2 opacity-50 hover:opacity-100"
						>
							<GripVertical className="h-5 w-5" />
						</div>
						<div className="flex flex-col">
							<FormField
								control={form.control}
								name={`sections.${sectionIndex}.title`}
								render={({ field }) => (
									<Input
										{...field}
										className="font-semibold bg-transparent border-none shadow-none focus:ring-0 p-0 h-auto text-base"
										placeholder="Título da seção"
									/>
								)}
							/>
							<FormField
								control={form.control}
								name={`sections.${sectionIndex}.description`}
								render={({ field }) => (
									<Input
										{...field}
										className="text-muted-foreground text-sm bg-transparent border-none shadow-none focus:ring-0 p-0 h-auto"
										placeholder="Descrição opcional"
									/>
								)}
							/>
						</div>
					</div>
					<div className="flex items-center space-x-2">
						<Button
							type="button"
							variant="ghost"
							size="icon"
							onClick={onExpand}
							className="h-8 w-8"
						>
							<Layers className="h-4 w-4" />
						</Button>
						<Button
							type="button"
							variant="ghost"
							size="icon"
							onClick={onDelete}
							className="h-8 w-8 text-destructive"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				</CardHeader>

				{isExpanded && (
					<CardContent className="px-4 pt-0 pb-4">
						<Accordion
							type="multiple"
							defaultValue={expandedField ? [expandedField] : []}
							className="space-y-2"
						>
							{fields.map((field, fieldIndex) => (
								<FormField
									key={field.id}
									control={form.control}
									name={`sections.${sectionIndex}.fields.${fieldIndex}`}
									render={({ field: formField }) => (
										<AccordionItem
											value={field.id}
											className="border rounded-md p-2"
										>
											<div className="flex items-center">
												<AccordionTrigger className="py-1 flex-1">
													<div className="flex flex-col items-start">
														<span className="font-medium">
															{formField.value.label || "Campo sem título"}
														</span>
														<span className="text-xs text-muted-foreground">
															{FIELD_TYPES.find(
																(t) => t.value === formField.value.type,
															)?.label || formField.value.type}
															{formField.value.required && " (Obrigatório)"}
														</span>
													</div>
												</AccordionTrigger>
												<Button
													type="button"
													variant="ghost"
													size="icon"
													onClick={(e) => {
														e.stopPropagation();
														onDeleteField(fieldIndex);
													}}
													className="h-8 w-8 text-destructive"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
											<AccordionContent className="pt-2 space-y-4">
												<div className="grid grid-cols-2 gap-4">
													<div>
														<Label htmlFor={`field-label-${field.id}`}>
															Rótulo do Campo
														</Label>
														<Input
															id={`field-label-${field.id}`}
															value={formField.value.label}
															onChange={(e) => {
																const newValue = {
																	...formField.value,
																	label: e.target.value,
																};
																formField.onChange(newValue);
															}}
															className="mt-1"
														/>
													</div>
													<div>
														<Label htmlFor={`field-type-${field.id}`}>
															Tipo de Campo
														</Label>
														<Select
															value={formField.value.type}
															onValueChange={(value) => {
																const newValue = {
																	...formField.value,
																	type: value,
																};
																formField.onChange(newValue);
															}}
														>
															<SelectTrigger
																id={`field-type-${field.id}`}
																className="mt-1"
															>
																<SelectValue placeholder="Selecione o tipo" />
															</SelectTrigger>
															<SelectContent>
																{FIELD_TYPES.map((type) => (
																	<SelectItem
																		key={type.value}
																		value={type.value}
																	>
																		{type.label}
																	</SelectItem>
																))}
															</SelectContent>
														</Select>
													</div>
												</div>

												<div>
													<Label htmlFor={`field-placeholder-${field.id}`}>
														Texto de Exemplo
													</Label>
													<Input
														id={`field-placeholder-${field.id}`}
														value={formField.value.placeholder || ""}
														onChange={(e) => {
															const newValue = {
																...formField.value,
																placeholder: e.target.value,
															};
															formField.onChange(newValue);
														}}
														className="mt-1"
													/>
												</div>

												<div className="grid grid-cols-2 gap-4">
													<div>
														<Label htmlFor={`field-category-${field.id}`}>
															Categoria
														</Label>
														<Select
															value={formField.value.category || "general"}
															onValueChange={(value) => {
																const newValue = {
																	...formField.value,
																	category: value,
																};
																formField.onChange(newValue);
															}}
														>
															<SelectTrigger
																id={`field-category-${field.id}`}
																className="mt-1"
															>
																<SelectValue placeholder="Selecione a categoria" />
															</SelectTrigger>
															<SelectContent>
																{FIELD_CATEGORIES.map((category) => (
																	<SelectItem
																		key={category.value}
																		value={category.value}
																	>
																		{category.label}
																	</SelectItem>
																))}
															</SelectContent>
														</Select>
													</div>
													<div className="flex items-center">
														<div className="flex items-center space-x-2 mt-6">
															<Switch
																id={`field-required-${field.id}`}
																checked={formField.value.required || false}
																onCheckedChange={(checked) => {
																	const newValue = {
																		...formField.value,
																		required: checked,
																	};
																	formField.onChange(newValue);
																}}
															/>
															<Label htmlFor={`field-required-${field.id}`}>
																Campo obrigatório
															</Label>
														</div>
													</div>
												</div>

												{(formField.value.type === "select" ||
													formField.value.type === "radio") && (
													<div>
														<Label>Opções</Label>
														<div className="space-y-2 mt-1">
															{(formField.value.options || []).map(
																(option, optionIndex) => (
																	<div
																		key={optionIndex}
																		className="flex items-center space-x-2"
																	>
																		<Input
																			value={option}
																			onChange={(e) => {
																				const newOptions = [
																					...(formField.value.options || []),
																				];
																				newOptions[optionIndex] =
																					e.target.value;
																				const newValue = {
																					...formField.value,
																					options: newOptions,
																				};
																				formField.onChange(newValue);
																			}}
																		/>
																		<Button
																			type="button"
																			variant="ghost"
																			size="icon"
																			onClick={() => {
																				const newOptions = [
																					...(formField.value.options || []),
																				];
																				newOptions.splice(optionIndex, 1);
																				const newValue = {
																					...formField.value,
																					options: newOptions,
																				};
																				formField.onChange(newValue);
																			}}
																			className="h-8 w-8 text-destructive"
																		>
																			<Trash2 className="h-4 w-4" />
																		</Button>
																	</div>
																),
															)}
															<Button
																type="button"
																variant="outline"
																size="sm"
																onClick={() => {
																	const newOptions = [
																		...(formField.value.options || []),
																		"Nova opção",
																	];
																	const newValue = {
																		...formField.value,
																		options: newOptions,
																	};
																	formField.onChange(newValue);
																}}
																className="w-full"
															>
																<Plus className="mr-2 h-4 w-4" />
																Adicionar Opção
															</Button>
														</div>
													</div>
												)}
											</AccordionContent>
										</AccordionItem>
									)}
								/>
							))}
						</Accordion>

						{fields.length === 0 ? (
							<div className="mt-4 flex flex-col items-center justify-center rounded-md border p-4">
								<p className="mb-2 text-muted-foreground">
									Nenhum campo aficionado
								</p>
								<Button onClick={onAddField} size="sm">
									<Plus className="mr-2 h-4 w-4" />
									Adicionar Campo
								</Button>
							</div>
						) : (
							<Button
								onClick={onAddField}
								variant="outline"
								size="sm"
								className="mt-4 w-full"
							>
								<PlusCircle className="mr-2 h-4 w-4" />
								Adicionar Campo
							</Button>
						)}
					</CardContent>
				)}
			</Card>
		</div>
	);
}
