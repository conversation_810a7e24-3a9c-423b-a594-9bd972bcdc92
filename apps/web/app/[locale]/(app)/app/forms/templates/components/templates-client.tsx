"use client";

import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Plus, Search } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
// apps/web/app/[locale]/(saas)/app/forms/templates/components/templates-client.tsx
import { useState } from "react";
import { toast } from "sonner";
import { TemplateCard } from "./template-card";
import { TemplateFormDialog } from "./template-form-dialog";

interface Hospital {
	id: string;
	name: string;
	logoUrl?: string;
}

interface Template {
	id: string;
	name: string;
	description?: string;
	isActive: boolean;
	fields: any;
	createdAt: Date;
	updatedAt: Date;
}

interface TemplatesClientProps {
	initialTemplates: Template[];
	hospitals: Hospital[];
	selectedHospitalId: string;
	searchTerm: string;
	userRole: string;
}

export function TemplatesClient({
	initialTemplates,
	hospitals,
	selectedHospitalId,
	searchTerm: initialSearchTerm,
	userRole,
}: TemplatesClientProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [templates, setTemplates] = useState<Template[]>(initialTemplates);
	const [isFormOpen, setIsFormOpen] = useState(false);
	const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
		null,
	);
	const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
	const [isSearching, setIsSearching] = useState(false);

	// Verificar se o usuário pode criar/editar templates
	const canManageTemplates = ["ADMIN", "HOSPITAL"].includes(userRole);

	// Template query
	const templatesQuery = apiClient.forms.templates.list.useQuery(
		{ hospitalId: selectedHospitalId, searchTerm },
		{ initialData: initialTemplates, enabled: false },
	);

	// Mutations
	const deleteTemplateMutation =
		apiClient.forms.templates.delete_.useMutation();

	const handleHospitalChange = (hospitalId: string) => {
		const params = new URLSearchParams(searchParams.toString());
		params.set("hospitalId", hospitalId);

		// Reset page when changing hospital
		if (params.has("page")) {
			params.delete("page");
		}

		router.push(`/app/forms/templates?${params.toString()}`);
	};

	const handleSearch = async () => {
		setIsSearching(true);
		try {
			const result = await templatesQuery.refetch();
			if (result.data) {
				setTemplates(result.data);
			}
		} catch (error) {
			toast.error("Erro ao buscar templates");
		} finally {
			setIsSearching(false);
		}
	};

	const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSearch();
		}
	};

	const handleEdit = (template: Template) => {
		setSelectedTemplate(template);
		setIsFormOpen(true);
	};

	const handleDelete = async (id: string) => {
		if (!confirm("Tem certeza que deseja excluir este template?")) return;

		try {
			await deleteTemplateMutation.mutateAsync({ id });
			setTemplates(templates.filter((t) => t.id !== id));
			toast.success("Template excluído com sucesso");
		} catch (error) {
			toast.error("Erro ao excluir template");
		}
	};

	const handleFormClose = (templateCreated?: Template) => {
		setIsFormOpen(false);
		setSelectedTemplate(null);

		if (templateCreated) {
			// Se foi criado um novo template, adicioná-lo à lista
			setTemplates((prev) => [templateCreated, ...prev]);
		} else {
			// Caso contrário, recarregar a lista para obter atualizações
			templatesQuery.refetch();
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex flex-col md:flex-row justify-between gap-4">
				<div className="flex flex-col md:flex-row gap-4">
					<Select
						value={selectedHospitalId}
						onValueChange={handleHospitalChange}
						disabled={hospitals.length <= 1}
					>
						<SelectTrigger className="w-full md:w-[250px]">
							<SelectValue placeholder="Selecione o hospital" />
						</SelectTrigger>
						<SelectContent>
							{hospitals.map((hospital) => (
								<SelectItem key={hospital.id} value={hospital.id}>
									{hospital.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					<div className="relative">
						<Input
							placeholder="Buscar templates..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							onKeyDown={handleSearchKeyDown}
							className="w-full pr-10"
						/>
						<Button
							variant="ghost"
							size="icon"
							className="absolute right-0 top-0 h-full"
							onClick={handleSearch}
							disabled={isSearching}
						>
							<Search className="h-4 w-4" />
						</Button>
					</div>
				</div>

				{canManageTemplates && (
					<Button onClick={() => setIsFormOpen(true)}>
						<Plus className="mr-2 h-4 w-4" />
						Novo Template
					</Button>
				)}
			</div>

			{templates.length === 0 ? (
				<div className="flex flex-col items-center justify-center h-64 text-center p-4 border rounded-md bg-muted/20">
					<p className="text-lg font-medium mb-2">Nenhum template encontrado</p>
					<p className="text-muted-foreground mb-6">
						{searchTerm
							? "Tente ajustar seus filtros de busca."
							: "Crie seu primeiro template para começar."}
					</p>
					{canManageTemplates && (
						<Button onClick={() => setIsFormOpen(true)}>
							<Plus className="mr-2 h-4 w-4" />
							Criar Template
						</Button>
					)}
				</div>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{templates.map((template) => (
						<TemplateCard
							key={template.id}
							template={template}
							canEdit={canManageTemplates}
							onEdit={() => handleEdit(template)}
							onDelete={() => handleDelete(template.id)}
						/>
					))}
				</div>
			)}

			{canManageTemplates && (
				<TemplateFormDialog
					open={isFormOpen}
					onOpenChange={setIsFormOpen}
					template={selectedTemplate}
					hospitalId={selectedHospitalId}
					onSuccess={handleFormClose}
				/>
			)}
		</div>
	);
}
