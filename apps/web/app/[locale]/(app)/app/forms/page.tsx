import { Card } from "@ui/components/card";
import { FileText, Stethoscope } from "lucide-react";
import Link from "next/link";

export default function FormsPage() {
	return (
		<div className="container py-8">
			<h1 className="text-3xl font-bold mb-6">Formulários</h1>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<Link href="/app/forms/pre-anesthetic">
					<Card className="p-6 hover:bg-muted/50 transition-colors cursor-pointer">
						<div className="flex items-center gap-4">
							<div className="p-3 bg-primary/10 rounded-lg">
								<FileText className="w-6 h-6 text-primary" />
							</div>
							<div>
								<h2 className="text-xl font-semibold">Pré-Anestésico</h2>
								<p className="text-muted-foreground">
									Gerenciar formulários de avaliação pré-anestésica
								</p>
							</div>
						</div>
					</Card>
				</Link>

				<Link href="/app/forms/anamnesis">
					<Card className="p-6 hover:bg-muted/50 transition-colors cursor-pointer">
						<div className="flex items-center gap-4">
							<div className="p-3 bg-primary/10 rounded-lg">
								<Stethoscope className="w-6 h-6 text-primary" />
							</div>
							<div>
								<h2 className="text-xl font-semibold">Anamnese</h2>
								<p className="text-muted-foreground">
									Gerenciar formulários de anamnese médica
								</p>
							</div>
						</div>
					</Card>
				</Link>
			</div>
		</div>
	);
}
