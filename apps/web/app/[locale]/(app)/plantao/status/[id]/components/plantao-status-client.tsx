"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@ui/components/card";
import { RefreshCw, Wifi, WifiOff } from "lucide-react";
import { Badge } from "@ui/components/badge";

interface PlantaoStatusClientProps {
  appointmentId: string;
  initialStatus: {
    label: string;
    description: string;
    action: string;
    progress: number;
  };
}

export function PlantaoStatusClient({ appointmentId, initialStatus }: PlantaoStatusClientProps) {
  const [status, setStatus] = useState(initialStatus);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isOnline, setIsOnline] = useState(true);
  const [updateCount, setUpdateCount] = useState(0);

  useEffect(() => {
    // Check online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/appointments/${appointmentId}/status`);
        if (response.ok) {
          const data = await response.json();

          // Atualizar status se mudou
          if (data.status !== status.action || data.hasDoctor !== (status.action === 'assigned' || status.action === 'active')) {
            let newStatus = { ...initialStatus };

            if (data.status === 'SCHEDULED' && !data.hasDoctor) {
              newStatus = {
                label: 'Procurando Médico',
                description: 'Médicos online estão sendo notificados sobre seu caso',
                action: 'waiting',
                progress: 25
              };
            } else if (data.status === 'SCHEDULED' && data.hasDoctor) {
              newStatus = {
                label: 'Médico Encontrado',
                description: 'Um médico aceitou seu atendimento e iniciará em breve',
                action: 'assigned',
                progress: 75
              };
            } else if (data.status === 'IN_PROGRESS') {
              newStatus = {
                label: 'Em Atendimento',
                description: 'Sua consulta está em andamento',
                action: 'active',
                progress: 100
              };
            }

            if (newStatus.action !== status.action) {
              setStatus(newStatus);
              setUpdateCount(prev => prev + 1);
            }
          }

          setLastUpdated(new Date());
        }
      } catch (error) {
        console.error('Erro ao verificar status:', error);
      }
    };

    // Verificar status imediatamente
    checkStatus();

    // Configurar polling - mais frequente no início
    const intervals = [
      setTimeout(() => checkStatus(), 5000),    // 5s
      setTimeout(() => checkStatus(), 15000),   // 15s
      setTimeout(() => checkStatus(), 30000),   // 30s
    ];

    // Polling regular a cada 30s
    const regularInterval = setInterval(checkStatus, 30000);

    return () => {
      intervals.forEach(clearTimeout);
      clearInterval(regularInterval);
    };
  }, [appointmentId, status.action]);

  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSecs = Math.floor(diffMs / 1000);

    if (diffSecs < 60) {
      return 'agora há pouco';
    } else if (diffSecs < 3600) {
      const mins = Math.floor(diffSecs / 60);
      return `há ${mins} min`;
    } else {
      return lastUpdated.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              {isOnline ? (
                <>
                  <Wifi className="h-4 w-4 text-green-600" />
                  <span className="text-green-600">Online</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-600" />
                  <span className="text-red-600">Offline</span>
                </>
              )}
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <RefreshCw className="h-3 w-3" />
              <span className="text-xs">
                Atualizado {formatLastUpdated()}
              </span>
            </div>
          </div>

          {/* Status Updates */}
          {updateCount > 0 && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="animate-pulse h-2 w-2 bg-blue-600 rounded-full"></div>
                <span className="text-sm text-blue-800 font-medium">
                  Status atualizado {updateCount} {updateCount === 1 ? 'vez' : 'vezes'}
                </span>
              </div>
              <p className="text-xs text-blue-700 mt-1">
                Esta página é atualizada automaticamente quando há mudanças no seu atendimento.
              </p>
            </div>
          )}

          {/* Auto-refresh info */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              🔄 Esta página se atualiza automaticamente a cada 30 segundos
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Mantenha-a aberta para receber atualizações em tempo real
            </p>
          </div>

          {/* Tips based on status */}
          {status.action === 'waiting' && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-800 mb-1">💡 Enquanto você aguarda:</h4>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• Prepare uma lista dos seus sintomas</li>
                <li>• Tenha seus documentos e exames em mãos</li>
                <li>• Mantenha o WhatsApp aberto</li>
                <li>• Esteja em um local com boa conexão</li>
              </ul>
            </div>
          )}

          {status.action === 'assigned' && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="text-sm font-medium text-green-800 mb-1">🎉 Médico encontrado!</h4>
              <p className="text-xs text-green-700">
                Você receberá uma mensagem no WhatsApp com o link para iniciar o atendimento.
                Fique atento às notificações!
              </p>
            </div>
          )}

          {status.action === 'active' && (
            <div className="p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
              <h4 className="text-sm font-medium text-emerald-800 mb-1">🩺 Atendimento em andamento</h4>
              <p className="text-xs text-emerald-700">
                Se você saiu da sala de atendimento, verifique seu WhatsApp para o link de acesso.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
