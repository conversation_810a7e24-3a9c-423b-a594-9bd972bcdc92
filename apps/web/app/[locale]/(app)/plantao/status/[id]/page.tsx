import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@ui/components/card';
import {
  Clock,
  Stethoscope,
  AlertCircle,
  AlertTriangle,
  Info,
  UserCheck,
  MessageSquare,
  Timer,
  CheckCircle,
  User,
  Phone
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { db } from 'database';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { PlantaoStatusClient } from './components/plantao-status-client';

interface PlantaoStatusPageProps {
  params: {
    id: string;
  };
}

async function getAppointmentStatus(appointmentId: string) {
  try {
    const appointment = await db.appointment.findUnique({
      where: {
        id: appointmentId,
        isOnDuty: true, // Apenas plantões
        paymentStatus: 'PAID' // Apenas pagos
      },
      select: {
        id: true,
        status: true,
        urgencyLevel: true,
        symptoms: true,
        createdAt: true,
        scheduledAt: true,
        acceptedAt: true,
        amount: true,
        duration: true,
        patient: {
          select: {
            user: {
              select: {
                name: true,
                phone: true
              }
            }
          }
        },
        doctor: {
          select: {
            user: {
              select: {
                name: true,
                avatarUrl: true
              }
            },
            specialties: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    return appointment;
  } catch (error) {
    console.error('Erro ao buscar status do appointment:', error);
    return null;
  }
}

export default async function PlantaoStatusPage({ params }: PlantaoStatusPageProps) {
  const appointment = await getAppointmentStatus(params.id);

  if (!appointment) {
    return notFound();
  }

  const getUrgencyIcon = (level: string) => {
    switch (level) {
      case 'HIGH':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'MEDIUM':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'LOW':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getUrgencyLabel = (level: string) => {
    switch (level) {
      case 'HIGH':
        return '🔴 Muito Urgente';
      case 'MEDIUM':
        return '🟡 Urgente';
      case 'LOW':
        return '🟢 Pouco Urgente';
      default:
        return '⚫ Sem classificação';
    }
  };

  const getStatusInfo = () => {
    if (appointment.status === 'SCHEDULED' && !appointment.doctor) {
      return {
        label: 'Procurando Médico',
        description: 'Médicos online estão sendo notificados sobre seu caso',
        color: 'bg-blue-100 text-blue-800',
        icon: <Timer className="h-5 w-5" />,
        action: 'waiting',
        progress: 25
      };
    }

    if (appointment.status === 'SCHEDULED' && appointment.doctor) {
      return {
        label: 'Médico Encontrado',
        description: 'Um médico aceitou seu atendimento e iniciará em breve',
        color: 'bg-green-100 text-green-800',
        icon: <UserCheck className="h-5 w-5" />,
        action: 'assigned',
        progress: 75
      };
    }

    if (appointment.status === 'IN_PROGRESS') {
      return {
        label: 'Em Atendimento',
        description: 'Sua consulta está em andamento',
        color: 'bg-emerald-100 text-emerald-800',
        icon: <MessageSquare className="h-5 w-5" />,
        action: 'active',
        progress: 100
      };
    }

    return {
      label: 'Agendado',
      description: 'Aguardando processamento',
      color: 'bg-gray-100 text-gray-800',
      icon: <Clock className="h-5 w-5" />,
      action: 'scheduled',
      progress: 10
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50/50">
      <div className="container max-w-2xl py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <Stethoscope className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold">Status do Plantão</h1>
          <p className="text-muted-foreground mt-2">
            Acompanhe em tempo real o status do seu atendimento
          </p>
        </div>

        {/* Status Card */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                {statusInfo.icon}
                {statusInfo.label}
              </CardTitle>
              <Badge className={statusInfo.color}>
                {statusInfo.label}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progresso do Atendimento</span>
                <span>{statusInfo.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-500"
                  style={{ width: `${statusInfo.progress}%` }}
                ></div>
              </div>
            </div>

            <p className="text-muted-foreground">
              {statusInfo.description}
            </p>

            {/* Informações do Paciente */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-medium text-sm text-gray-600">Paciente</h4>
                <p className="font-medium">{appointment.patient.user.name}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Urgência</h4>
                <div className="flex items-center gap-2">
                  {getUrgencyIcon(appointment.urgencyLevel)}
                  <span className="text-sm">{getUrgencyLabel(appointment.urgencyLevel)}</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">ID do Atendimento</h4>
                <p className="text-sm font-mono">{appointment.id.slice(-8)}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Valor</h4>
                <p className="font-medium">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(Number(appointment.amount))}
                </p>
              </div>
            </div>

            {/* Informações do Médico se disponível */}
            {appointment.doctor && (
              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                <h4 className="font-medium text-green-800 mb-3 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Seu Médico
                </h4>
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={appointment.doctor.user.avatarUrl || ""} />
                    <AvatarFallback>
                      <User className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Dr(a). {appointment.doctor.user.name}</p>
                    <p className="text-sm text-green-700">
                      {appointment.doctor.specialties?.map(s => s.name).join(', ') || 'Clínico Geral'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Motivo da consulta */}
            {appointment.symptoms && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Motivo da Consulta</h4>
                <p className="text-blue-700">{appointment.symptoms}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Live Updates Component */}
        <PlantaoStatusClient appointmentId={appointment.id} initialStatus={statusInfo} />

        {/* Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <h3 className="font-medium">Próximos Passos</h3>

              {statusInfo.action === 'waiting' && (
                <div className="space-y-3">
                  <div className="flex items-center justify-center gap-2 text-blue-700">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">Aguarde, estamos conectando você com um médico...</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Tempo estimado: {
                      appointment.urgencyLevel === 'HIGH' ? '5-10 minutos' :
                      appointment.urgencyLevel === 'MEDIUM' ? '15-25 minutos' :
                      '20-40 minutos'
                    }
                  </p>
                </div>
              )}

              {statusInfo.action === 'assigned' && (
                <div className="space-y-3">
                  <p className="text-green-700">
                    ✅ Médico encontrado! Você receberá uma mensagem no WhatsApp com o link para iniciar o atendimento.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Mantenha o WhatsApp aberto para receber a notificação.
                  </p>
                </div>
              )}

              {statusInfo.action === 'active' && (
                <div className="space-y-3">
                  <p className="text-emerald-700">
                    🩺 Seu atendimento está em andamento. Se você saiu da sala por acidente, verifique seu WhatsApp para o link.
                  </p>
                </div>
              )}

              <div className="pt-4 border-t">
                <p className="text-xs text-muted-foreground mb-4">
                  Precisa de ajuda? Entre em contato com nosso suporte.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/">
                      Voltar ao Início
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/auth/login">
                      Fazer Login
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
