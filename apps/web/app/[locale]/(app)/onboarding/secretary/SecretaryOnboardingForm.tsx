'use client';

import { <PERSON><PERSON> } from "@ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { useToast } from "@ui/hooks/use-toast";

// Função para formatar o telefone no padrão brasileiro: (XX) XXXXX-XXXX
const formatPhone = (value: string) => {
  if (!value) return "";

  // Remove tudo que não for número
  const phoneNumber = value.replace(/\D/g, "");

  // Aplica a formatação
  if (phoneNumber.length <= 2) {
    return phoneNumber.replace(/^(\d{0,2})/, "($1");
  } else if (phoneNumber.length <= 7) {
    return phoneNumber.replace(/^(\d{2})(\d{0,5})/, "($1) $2");
  } else if (phoneNumber.length <= 11) {
    return phoneNumber.replace(/^(\d{2})(\d{5})(\d{0,4})/, "($1) $2-$3");
  } else {
    // Limita a 11 dígitos (DDD + 9 dígitos)
    return phoneNumber.slice(0, 11).replace(/^(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  }
};

// Função para remover a formatação do telefone
const unformatPhone = (value: string) => {
  return value.replace(/\D/g, "");
};

// Schema para validação
const secretaryFormSchema = z.object({
  phone: z.string().min(10, "Telefone inválido"),
  hospitalId: z.string().min(1, "Hospital é obrigatório"),
  address: z.object({
    street: z.string().min(3, "Endereço inválido"),
    number: z.string(),
    complement: z.string().optional(),
    neighborhood: z.string(),
    city: z.string().min(2, "Cidade inválida"),
    state: z.string().length(2, "Estado inválido"),
    zipCode: z.string().min(8, "CEP inválido"),
  }),
  bio: z.string().optional(),
});

type SecretaryFormValues = z.infer<typeof secretaryFormSchema>;

export function SecretaryOnboardingForm({ userId }: { userId: string }) {
  const [isLoading, setIsLoading] = useState(false);
  const [hospitals, setHospitals] = useState<{ id: string; name: string }[]>([]);
  const { toast } = useToast();
  const router = useRouter();

  // Buscar hospitais
  useEffect(() => {
    const loadHospitals = async () => {
      try {
        const response = await fetch('/api/hospitals');
        if (response.ok) {
          const data = await response.json();
          setHospitals(data);
        } else {
          throw new Error(`Failed to load hospitals: ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error loading hospitals:', error);
        toast({
          title: "Erro ao carregar hospitais",
          description: "Não foi possível carregar a lista de hospitais",
        });
      }
    };

    loadHospitals();
  }, [toast]);

  const form = useForm<SecretaryFormValues>({
    resolver: zodResolver(secretaryFormSchema),
    defaultValues: {
      phone: "",
      hospitalId: "",
      bio: "",
      address: {
        street: "",
        number: "",
        complement: "",
        neighborhood: "",
        city: "",
        state: "",
        zipCode: "",
      },
    },
  });

  async function onSubmit(data: SecretaryFormValues) {
    setIsLoading(true);
    console.log("[FORM] Iniciando envio do formulário:", data);

    try {
      // Preparar dados para envio
      const formData = {
        phone: unformatPhone(data.phone),
        hospitalId: data.hospitalId,
        bio: data.bio || null,
        address: {
          ...data.address,
          zipCode: data.address.zipCode.replace(/\D/g, ""),
        },
        userId,
      };

      console.log("[FORM] Enviando dados:", formData);

      // Enviar para API
      const response = await fetch('/api/secretary/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
      });

      console.log("[FORM] Status da resposta:", response.status);

      // Ler resposta como texto para diagnóstico
      const responseText = await response.text();
      console.log("[FORM] Resposta bruta:", responseText);

      // Tentar fazer parse da resposta
      let responseData;
      try {
        responseData = responseText ? JSON.parse(responseText) : {};
      } catch (e) {
        console.error("[FORM] Erro ao fazer parse da resposta:", e);
        throw new Error("Erro ao processar resposta do servidor");
      }

      // Verificar se houve erro
      if (!response.ok) {
        const errorMessage = responseData?.error || `Erro no servidor (${response.status})`;
        console.error("[FORM] Erro na API:", errorMessage, responseData);
        throw new Error(errorMessage);
      }

      // Processar sucesso
      console.log("[FORM] Perfil criado com sucesso:", responseData);

      // Completar onboarding
      try {
        const onboardingResponse = await fetch('/api/user/complete-onboarding', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (onboardingResponse.ok) {
          console.log("[FORM] Onboarding concluído");
        } else {
          console.warn("[FORM] Falha ao completar onboarding:", onboardingResponse.status);
        }
      } catch (onboardingError) {
        // Não interromper o fluxo se falhar apenas o complete-onboarding
        console.warn("[FORM] Erro ao completar onboarding:", onboardingError);
      }

      // Exibir mensagem de sucesso
      toast({
        title: "Cadastro realizado com sucesso!",
        description: "Você será redirecionado para o dashboard.",
      });

      // Redirecionar para dashboard
      router.push('/app/dashboard/secretary');
    } catch (error: any) {
      console.error("[FORM] Erro no cadastro:", error);

      toast({
        title: "Erro no cadastro",
        description: error?.message || "Ocorreu um erro ao cadastrar seus dados. Tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Telefone</FormLabel>
              <FormControl>
                <Input
                  placeholder="(00) 00000-0000"
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(formatPhone(e.target.value));
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="hospitalId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Hospital</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um hospital" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hospitals.map((hospital) => (
                    <SelectItem key={hospital.id} value={hospital.id}>
                      {hospital.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notas Profissionais (opcional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Informações adicionais relevantes para sua função"
                  className="resize-none"
                  rows={3}
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="border p-4 rounded-md">
          <h3 className="font-medium mb-4">Endereço</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="address.street"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rua</FormLabel>
                  <FormControl>
                    <Input placeholder="Rua das Flores" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="address.number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número</FormLabel>
                    <FormControl>
                      <Input placeholder="123" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.complement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Complemento</FormLabel>
                    <FormControl>
                      <Input placeholder="Apto 101" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <FormField
              control={form.control}
              name="address.neighborhood"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bairro</FormLabel>
                  <FormControl>
                    <Input placeholder="Centro" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address.zipCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CEP</FormLabel>
                  <FormControl>
                    <Input placeholder="00000-000" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <FormField
              control={form.control}
              name="address.city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cidade</FormLabel>
                  <FormControl>
                    <Input placeholder="São Paulo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address.state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Estado</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="AC">AC</SelectItem>
                      <SelectItem value="AL">AL</SelectItem>
                      <SelectItem value="AP">AP</SelectItem>
                      <SelectItem value="AM">AM</SelectItem>
                      <SelectItem value="BA">BA</SelectItem>
                      <SelectItem value="CE">CE</SelectItem>
                      <SelectItem value="DF">DF</SelectItem>
                      <SelectItem value="ES">ES</SelectItem>
                      <SelectItem value="GO">GO</SelectItem>
                      <SelectItem value="MA">MA</SelectItem>
                      <SelectItem value="MT">MT</SelectItem>
                      <SelectItem value="MS">MS</SelectItem>
                      <SelectItem value="MG">MG</SelectItem>
                      <SelectItem value="PA">PA</SelectItem>
                      <SelectItem value="PB">PB</SelectItem>
                      <SelectItem value="PR">PR</SelectItem>
                      <SelectItem value="PE">PE</SelectItem>
                      <SelectItem value="PI">PI</SelectItem>
                      <SelectItem value="RJ">RJ</SelectItem>
                      <SelectItem value="RN">RN</SelectItem>
                      <SelectItem value="RS">RS</SelectItem>
                      <SelectItem value="RO">RO</SelectItem>
                      <SelectItem value="RR">RR</SelectItem>
                      <SelectItem value="SC">SC</SelectItem>
                      <SelectItem value="SP">SP</SelectItem>
                      <SelectItem value="SE">SE</SelectItem>
                      <SelectItem value="TO">TO</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Salvando..." : "Completar Cadastro"}
        </Button>
      </form>
    </Form>
  );
}
