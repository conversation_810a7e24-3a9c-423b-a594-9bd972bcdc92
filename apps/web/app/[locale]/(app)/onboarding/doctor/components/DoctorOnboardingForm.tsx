// DoctorOnboardingForm.tsx
'use client';

import { But<PERSON> } from '@ui/components/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@ui/components/form';
import { Input } from '@ui/components/input';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@ui/components/select';
import { Textarea } from '@ui/components/textarea';
import { toast } from 'sonner';
import { Checkbox } from '@ui/components/checkbox';

// Função para formatar o telefone no padrão brasileiro: (XX) XXXXX-XXXX
const formatPhone = (value: string) => {
	if (!value) return '';

	// Remove tudo que não for número
	const phoneNumber = value.replace(/\D/g, '');

	// Aplica a formatação
	if (phoneNumber.length <= 2) {
		return phoneNumber.replace(/^(\d{0,2})/, '($1');
	} else if (phoneNumber.length <= 7) {
		return phoneNumber.replace(/^(\d{2})(\d{0,5})/, '($1) $2');
	} else if (phoneNumber.length <= 11) {
		return phoneNumber.replace(/^(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
	} else {
		// Limita a 11 dígitos (DDD + 9 dígitos)
		return phoneNumber
			.slice(0, 11)
			.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
	}
};

// Função para remover a formatação do telefone
const unformatPhone = (value: string) => {
	return value.replace(/\D/g, '');
};

// Schema para validação
// Modificar o schema para tornar hospitalId opcional
const doctorFormSchema = z.object({
	name: z
		.string()
		.min(3, 'Nome completo é obrigatório')
		.refine((val) => val.trim().split(/\s+/).length >= 2, {
			message: 'Por favor, forneça nome e sobrenome',
		}),
	crm: z.string().min(4, 'CRM inválido'),
	crmState: z.string().length(2, 'Estado de registro inválido'),
	phone: z
		.string()
		.min(14, 'Telefone inválido')
		.refine((val) => val.replace(/\D/g, '').length >= 10, {
			message: 'Telefone deve ter pelo menos 10 dígitos (incluindo DDD)',
		}),
	specialtyIds: z
		.array(z.string())
		.min(1, 'Selecione pelo menos uma especialidade'),
	consultationPrice: z.coerce.number().min(0, 'O preço não pode ser negativo'),
	consultationDuration: z.coerce
		.number()
		.min(15, 'A duração mínima é de 15 minutos'),
	biography: z
		.string()
		.min(10, 'Por favor, adicione uma breve biografia profissional')
		.max(1000, 'A biografia deve ter no máximo 1000 caracteres'),
	hospitalId: z.string().optional(), // Tornando opcional
	setupDefaultSchedule: z.boolean().default(true),
	scheduleConfig: z
		.object({
			startTime: z.string().default('08:00'),
			endTime: z.string().default('18:00'),
			lunchBreak: z.boolean().default(true),
			lunchStartTime: z.string().default('12:00'),
			lunchEndTime: z.string().default('14:00'),
			workDays: z.array(z.number()).default([1, 2, 3, 4, 5]), // Segunda a sexta por padrão
		})
		.optional(),
});

type DoctorFormValues = z.infer<typeof doctorFormSchema>;

// Tipo para o médico existente
type ExistingDoctor = {
	id: string;
	crm: string;
	crmState: string;
	specialties: { id: string; name?: string }[];
	hospitals: { hospitalId: string; hospital: { name: string } }[];
	user: { name: string; phone: string | null };
	consultationDuration?: number;
	biography?: string | null;
} | null;

// Adicione esta interface (ou importe se já existir em outro lugar)
interface ScheduleInputItem {
	weekDay: number;
	startTime: string;
	endTime: string;
	isEnabled: boolean;
	isBreak: boolean;
}

export function DoctorOnboardingForm({
	userId,
	existingDoctor,
}: {
	userId: string;
	existingDoctor?: ExistingDoctor;
}) {
	const [isLoading, setIsLoading] = useState(false);
	const [specialties, setSpecialties] = useState<
		{ id: string; name: string }[]
	>([]);
	const [hospitals, setHospitals] = useState<{ id: string; name: string }[]>(
		[]
	);
	const [currentStep, setCurrentStep] = useState(1);
	const [useDefaultSchedule, setUseDefaultSchedule] = useState(true);
	const router = useRouter();
	// Define totalSteps to fix the ReferenceError
	const totalSteps = 3;

	// Get specialties and hospitals
	useEffect(() => {
		const loadData = async () => {
			try {
				// Use a simple GET request to avoid tRPC errors
				const [specialtiesResponse, hospitalsResponse] = await Promise.all([
					fetch('/api/specialties'),
					fetch('/api/hospitals'),
				]);

				// Process specialties
				if (specialtiesResponse.ok) {
					const specialtyData = await specialtiesResponse.json();
					setSpecialties(specialtyData);
					console.log('Specialties loaded:', specialtyData);
				} else {
					console.error(
						'Failed to load specialties:',
						specialtiesResponse.statusText
					);
					toast.error('Não foi possível carregar as especialidades médicas');
				}

				// Process hospitals
				if (hospitalsResponse.ok) {
					const hospitalData = await hospitalsResponse.json();
					setHospitals(hospitalData);
					console.log('Hospitals loaded:', hospitalData);

					// Auto-selecionar o hospital se existir algum - MODIFICADO PARA GARANTIR SELEÇÃO
					if (hospitalData.length > 0) {
						const currentHospitalId = form.getValues().hospitalId;
						if (!currentHospitalId || currentHospitalId === '') {
							form.setValue('hospitalId', hospitalData[0].id);
							console.log('Auto-selected hospital:', hospitalData[0].id);
						}
					} else {
						console.warn('No hospitals available to select');
						toast.warning(
							'Não há hospitais disponíveis para seleção. Entre em contato com o suporte.'
						);
					}
				} else {
					console.error(
						'Failed to load hospitals:',
						hospitalsResponse.statusText
					);
					toast.error('Não foi possível carregar a lista de hospitais');
				}
			} catch (error) {
				console.error('Error loading data:', error);
				toast.error(
					'Erro ao carregar dados. Verifique sua conexão e tente novamente.'
				);
			}
		};

		loadData();
	}, []);

	// Preparar valores iniciais com base no médico existente
	const defaultValues = {
		name: existingDoctor?.user.name || '',
		crm: existingDoctor?.crm || '',
		crmState: existingDoctor?.crmState || '',
		phone: existingDoctor?.user.phone
			? formatPhone(existingDoctor.user.phone)
			: '',
		specialtyIds: existingDoctor?.specialties.map((s) => s.id) || [],
		// Usar o valor existente se disponível, caso contrário usar 100 como padrão
		consultationPrice: existingDoctor?.consultationPrice
			? Number(existingDoctor.consultationPrice)
			: 100,
		consultationDuration: existingDoctor?.consultationDuration || 30,
		biography: existingDoctor?.biography || '',
		hospitalId: existingDoctor?.hospitals.length
			? existingDoctor.hospitals[0].hospitalId
			: '',
		setupDefaultSchedule: true,
		scheduleConfig: {
			startTime: '08:00',
			endTime: '18:00',
			lunchBreak: true,
			lunchStartTime: '12:00',
			lunchEndTime: '14:00',
			workDays: [1, 2, 3, 4, 5],
		},
	};

	const form = useForm<DoctorFormValues>({
		resolver: zodResolver(doctorFormSchema),
		defaultValues,
	});

	// Update form when existingDoctor changes or when specialties/hospitals are loaded
	useEffect(() => {
		if (existingDoctor) {
			form.reset({
				...defaultValues,
				hospitalId:
					existingDoctor.hospitals.length > 0
						? existingDoctor.hospitals[0].hospitalId
						: '',
			});
		} else if (hospitals.length > 0 && form.getValues().hospitalId === '') {
			// Set default hospital when hospitals are loaded and no hospital is selected
			form.setValue('hospitalId', hospitals[0].id);
		}
	}, [existingDoctor, hospitals, form]);

	async function onSubmit(data: DoctorFormValues) {
		setIsLoading(true);
		console.log('[FORM] Iniciando envio do formulário:', data);

		// Verificar se hospitalId está presente
		if (!data.hospitalId || data.hospitalId === '') {
			if (hospitals.length > 0) {
				data.hospitalId = hospitals[0].id;
				console.log(
					'[FORM] Auto-selecionando hospital no momento do envio:',
					data.hospitalId
				);
			} else {
				toast.error(
					'Não foi possível selecionar um hospital. Por favor, tente novamente mais tarde.'
				);
				setIsLoading(false);
				return;
			}
		}

		try {
			// Preparar dados para envio (perfil)
			const profileFormData = {
				name: data.name,
				crm: data.crm,
				crmState: data.crmState.toUpperCase(),
				phone: unformatPhone(data.phone),
				specialtyIds: data.specialtyIds,
				hospitalId: data.hospitalId, // Agora garantimos que este valor existe
				consultationPrice: Number(data.consultationPrice),
				consultationDuration: Number(data.consultationDuration),
				biography: data.biography || null,
				// Não enviar scheduleConfig aqui, será tratado separadamente
			};

			console.log('[FORM] Enviando dados do perfil:', profileFormData);

			// Enviar para API simplificada
			const response = await fetch('/api/doctor/simple-create', {
				// Usando a rota existente
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(profileFormData),
			});

			console.log('[FORM] Status da resposta do perfil:', response.status);

			// Check if response is OK
			if (!response.ok) {
				// Read error response
				const errorText = await response.text();
				console.error(
					'[FORM] Erro na API de criação/atualização de perfil:',
					errorText
				);

				let errorMessage = 'Erro ao processar cadastro do perfil';
				try {
					// Try to parse as JSON for structured error
					const errorJson = JSON.parse(errorText);
					errorMessage = errorJson.error || errorJson.message || errorMessage;
				} catch {
					// If not JSON, use as plain text
					errorMessage = errorText || errorMessage;
				}

				throw new Error(errorMessage);
			}

			// Parse success response
			const responseData = await response.json();
			console.log('[FORM] Perfil criado/atualizado com sucesso:', responseData);
			const doctorId = responseData.doctor?.id;

			if (!doctorId) {
				console.error('[FORM] ID do médico não retornado pela API de perfil.');
				throw new Error(
					'Falha ao obter ID do médico após criação/atualização.'
				);
			}

			// Se o médico marcou a opção de configurar horários padrão
			if (data.setupDefaultSchedule) {
				try {
					console.log(
						`[FORM] Configurando horários padrão para o médico: ${doctorId}`
					);

					// Preparar os horários baseados na configuração
					const scheduleItems: ScheduleInputItem[] = [];
					const config = data.scheduleConfig || defaultValues.scheduleConfig; // Usar config ou default
					const {
						workDays,
						startTime,
						endTime,
						lunchBreak,
						lunchStartTime,
						lunchEndTime,
					} = config;

					// Criar os horários para cada dia de trabalho
					for (const weekDay of workDays) {
						if (lunchBreak && lunchStartTime && lunchEndTime) {
							// Verificar se horários de almoço existem
							// Adicionar horário da manhã (se início < início almoço)
							if (startTime < lunchStartTime) {
								scheduleItems.push({
									weekDay,
									startTime,
									endTime: lunchStartTime,
									isEnabled: true,
									isBreak: false,
								});
							}

							// Adicionar intervalo de almoço
							scheduleItems.push({
								weekDay,
								startTime: lunchStartTime,
								endTime: lunchEndTime,
								isEnabled: true, // Intervalo também precisa estar "enabled" para ser considerado
								isBreak: true,
							});

							// Adicionar horário da tarde (se fim almoço < fim expediente)
							if (lunchEndTime < endTime) {
								scheduleItems.push({
									weekDay,
									startTime: lunchEndTime,
									endTime,
									isEnabled: true,
									isBreak: false,
								});
							}
						} else {
							// Adicionar jornada completa sem intervalo (se início < fim)
							if (startTime < endTime) {
								scheduleItems.push({
									weekDay,
									startTime,
									endTime,
									isEnabled: true,
									isBreak: false,
								});
							}
						}
					}
					console.log(
						'[FORM] Horários padrão a serem enviados:',
						scheduleItems
					);

					// Enviar os horários para a API /api/doctor/schedule
					const scheduleResponse = await fetch('/api/doctor/schedule', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							doctorId: doctorId, // Usar o ID retornado pela API de perfil
							schedule: scheduleItems,
						}),
					});

					if (scheduleResponse.ok) {
						const scheduleResult = await scheduleResponse.json();
						console.log(
							'[FORM] Horários configurados com sucesso:',
							scheduleResult
						);
					} else {
						const scheduleErrorText = await scheduleResponse.text();
						console.warn(
							`[FORM] Erro ao configurar horários (Status: ${scheduleResponse.status}):`,
							scheduleErrorText
						);
						// Não bloquear o fluxo principal, mas informar o usuário
						toast.warning(
							'Seu perfil foi salvo, mas houve um problema ao configurar os horários padrão. Você pode ajustá-los nas configurações.'
						);
					}
				} catch (scheduleError) {
					console.error(
						'[FORM] Erro interno ao tentar configurar horários:',
						scheduleError
					);
					toast.error('Erro ao tentar configurar os horários padrão.');
					// Não interromper o fluxo principal
				}
			} else {
				console.log(
					'[FORM] Usuário optou por não configurar horários padrão agora.'
				);
			}

			// Marcar onboarding como completo (se necessário, a API simple-create já pode fazer isso)
			// Se a API /api/doctor/simple-create já marca como completo, esta chamada pode ser redundante.
			// Verifique a lógica da API simple-create. Se ela já faz isso, remova o bloco abaixo.
			try {
				console.log(
					'[FORM] Tentando marcar onboarding como completo (pode ser redundante)'
				);
				const onboardingResponse = await fetch(
					'/api/user/complete-onboarding',
					{
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						// Não precisa de body se a API usa o usuário logado
					}
				);

				if (onboardingResponse.ok) {
					console.log(
						'[FORM] Onboarding marcado como concluído (via API separada).'
					);
				} else {
					console.warn(
						'[FORM] Falha ao marcar onboarding como concluído (via API separada):',
						onboardingResponse.status
					);
				}
			} catch (onboardingError) {
				console.warn(
					'[FORM] Erro ao tentar marcar onboarding como concluído (via API separada):',
					onboardingError
				);
			}

			// Exibir mensagem de sucesso
			toast.success(
				existingDoctor
					? 'Perfil atualizado com sucesso!'
					: 'Cadastro realizado com sucesso!'
			);

			// Redirecionar para dashboard
			router.push('/app/dashboard/doctor'); // Ajuste a rota se necessário
		} catch (error: any) {
			console.error('[FORM] Erro geral no onSubmit:', error);
			toast.error(
				error?.message ||
					'Ocorreu um erro ao processar sua solicitação. Tente novamente.'
			);
		} finally {
			setIsLoading(false);
		}
	}

	const nextStep = () => {
		const currentValues = form.getValues();
		if (currentStep === 1) {
			// Validar campos do primeiro passo
			const stepOneFields = [
				'name',
				'crm',
				'crmState',
				'phone',
				'specialtyIds',
				'hospitalId',
			];
			const stepOneErrors = stepOneFields.some((field) => {
				const fieldError = form.getFieldState(field as any).error;
				return fieldError;
			});

			if (stepOneErrors) {
				form.trigger(stepOneFields as any);
				return;
			}
		} else if (currentStep === 2) {
			// Validar campos do segundo passo
			const stepTwoFields = [
				'consultationPrice',
				'consultationDuration',
				'biography',
			];
			const stepTwoErrors = stepTwoFields.some((field) => {
				const fieldError = form.getFieldState(field as any).error;
				return fieldError;
			});

			if (stepTwoErrors) {
				form.trigger(stepTwoFields as any);
				return;
			}
		}
		setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
	};

	const prevStep = () => {
		setCurrentStep((prev) => Math.max(prev - 1, 1));
	};

	const weekDays = [
		{ value: 0, label: 'Domingo' },
		{ value: 1, label: 'Segunda-feira' },
		{ value: 2, label: 'Terça-feira' },
		{ value: 3, label: 'Quarta-feira' },
		{ value: 4, label: 'Quinta-feira' },
		{ value: 5, label: 'Sexta-feira' },
		{ value: 6, label: 'Sábado' },
	];

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
				{currentStep === 1 && (
					<div className='space-y-6'>
						<FormField
							control={form.control}
							name='name'
							render={({ field }) => (
								<FormItem>
									<FormLabel>Nome Completo</FormLabel>
									<FormControl>
										<Input placeholder='Seu nome completo' {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<FormField
								control={form.control}
								name='crm'
								render={({ field }) => (
									<FormItem>
										<FormLabel>CRM</FormLabel>
										<FormControl>
											<Input placeholder='Número do CRM' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name='crmState'
								render={({ field }) => (
									<FormItem>
										<FormLabel>Estado de Registro</FormLabel>
										<FormControl>
											<Select
												value={field.value}
												onValueChange={field.onChange}
											>
												<SelectTrigger>
													<SelectValue placeholder='Selecione' />
												</SelectTrigger>
												<SelectContent>
													{[
														'AC',
														'AL',
														'AP',
														'AM',
														'BA',
														'CE',
														'DF',
														'ES',
														'GO',
														'MA',
														'MT',
														'MS',
														'MG',
														'PA',
														'PB',
														'PR',
														'PE',
														'PI',
														'RJ',
														'RN',
														'RS',
														'RO',
														'RR',
														'SC',
														'SP',
														'SE',
														'TO',
													].map((state) => (
														<SelectItem key={state} value={state}>
															{state}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name='phone'
							render={({ field }) => (
								<FormItem>
									<FormLabel>Telefone</FormLabel>
									<FormControl>
										<Input
											placeholder='(00) 00000-0000'
											value={field.value}
											onChange={(e) => {
												field.onChange(formatPhone(e.target.value));
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name='specialtyIds'
							render={({ field }) => (
								<FormItem>
									<FormLabel>Especialidades</FormLabel>
									<div className='space-y-2'>
										{specialties.length === 0 ? (
											<div className='text-sm text-muted-foreground'>
												Carregando especialidades...
											</div>
										) : (
											specialties.map((specialty) => (
												<div
													key={specialty.id}
													className='flex items-center space-x-2'
												>
													<Checkbox
														id={`specialty-${specialty.id}`}
														checked={field.value?.includes(specialty.id)}
														onCheckedChange={(checked) => {
															if (checked) {
																field.onChange([...field.value, specialty.id]);
															} else {
																field.onChange(
																	field.value?.filter(
																		(id) => id !== specialty.id
																	)
																);
															}
														}}
													/>
													<label
														htmlFor={`specialty-${specialty.id}`}
														className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
													>
														{specialty.name}
													</label>
												</div>
											))
										)}
									</div>
									{field.value.length > 0 && (
										<div className='mt-2 text-sm text-muted-foreground'>
											{field.value.length} especialidade(s) selecionada(s)
										</div>
									)}
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Hospital field removed - it will be auto-selected in the background */}

						<div className='flex justify-end'>
							<Button type='button' onClick={nextStep}>
								Próximo
							</Button>
						</div>
					</div>
				)}

				{currentStep === 2 && (
					<div className='space-y-6'>
						<div className='bg-blue-50 p-4 rounded-lg mb-6'>
							<h3 className='text-lg font-medium text-blue-800 mb-2'>
								Detalhes da Consulta
							</h3>
							<p className='text-blue-700'>
								Agora, vamos configurar os detalhes das suas consultas e seu
								perfil profissional.
							</p>
						</div>

						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<FormField
								control={form.control}
								name='consultationPrice'
								render={({ field }) => (
									<FormItem>
										<FormLabel>Preço da Consulta (R$)</FormLabel>
										<FormControl>
											<div className='relative'>
												<span className='absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground'>
													R$
												</span>
												<Input
													type='text'
													inputMode='numeric'
													pattern='[0-9]*'
													placeholder='0,00'
													className='pl-10'
													value={field.value}
													onChange={(e) => {
														// Allow only numbers
														const value = e.target.value.replace(/[^0-9]/g, '');
														// Convert to number for the form
														field.onChange(value === '' ? 0 : Number(value));
													}}
												/>
											</div>
										</FormControl>
										<FormMessage />
										<p className='text-xs text-muted-foreground mt-1'>
											Este é o valor que será cobrado por consulta na
											plataforma.
										</p>
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name='consultationDuration'
								render={({ field }) => (
									<FormItem>
										<FormLabel>Duração da Consulta (minutos)</FormLabel>
										<Select
											value={field.value.toString()}
											onValueChange={(value) => field.onChange(parseInt(value))}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder='Selecione a duração' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{[15, 20, 30, 40, 45, 50, 60].map((duration) => (
													<SelectItem
														key={duration}
														value={duration.toString()}
													>
														{duration} minutos
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
										<p className='text-xs text-muted-foreground mt-1'>
											Tempo reservado para cada atendimento.
										</p>
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name='biography'
							render={({ field }) => (
								<FormItem>
									<FormLabel>Biografia Profissional</FormLabel>
									<FormControl>
										<Textarea
											placeholder='Conte um pouco sobre sua formação, experiência e áreas de interesse...'
											className='min-h-[120px]'
											{...field}
										/>
									</FormControl>
									<div className='flex justify-between mt-1'>
										<FormMessage />
										<p
											className={`text-xs ${
												field.value.length > 1000
													? 'text-destructive'
													: 'text-muted-foreground'
											}`}
										>
											{field.value.length}/1000 caracteres
										</p>
									</div>
									<p className='text-xs text-muted-foreground mt-1'>
										Esta biografia será exibida no seu perfil público e ajudará
										os pacientes a conhecerem você melhor.
									</p>
								</FormItem>
							)}
						/>

						<div className='flex justify-between'>
							<Button type='button' variant='outline' onClick={prevStep}>
								Voltar
							</Button>
							<Button type='button' onClick={nextStep}>
								Próximo
							</Button>
						</div>
					</div>
				)}

				{currentStep === 3 && (
					<div className='space-y-6'>
						<div className='bg-blue-50 p-4 rounded-lg mb-6'>
							<h3 className='text-lg font-medium text-blue-800 mb-2'>
								Configuração de Horários
							</h3>
							<p className='text-blue-700 mb-4'>
								Por último, vamos configurar seus horários de atendimento para
								que pacientes possam agendar consultas com você.
							</p>

							<FormField
								control={form.control}
								name='setupDefaultSchedule'
								render={({ field }) => (
									<FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-white'>
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className='space-y-1 leading-none'>
											<FormLabel>Configurar horários padrão</FormLabel>
											<p className='text-sm text-muted-foreground'>
												Usaremos horários padrão de atendimento para iniciar.
												Você poderá ajustá-los depois.
											</p>
										</div>
									</FormItem>
								)}
							/>

							{form.watch('setupDefaultSchedule') && (
								<div className='mt-4 space-y-4 bg-white p-4 rounded-md border'>
									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<FormField
											control={form.control}
											name='scheduleConfig.startTime'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Horário de início</FormLabel>
													<FormControl>
														<Input type='time' {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name='scheduleConfig.endTime'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Horário de término</FormLabel>
													<FormControl>
														<Input type='time' {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<FormField
										control={form.control}
										name='scheduleConfig.lunchBreak'
										render={({ field }) => (
											<FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
												<FormControl>
													<Checkbox
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
												<div className='space-y-1 leading-none'>
													<FormLabel>Incluir intervalo para almoço</FormLabel>
													<p className='text-sm text-muted-foreground'>
														Defina um período em que você não estará disponível
														para consultas.
													</p>
												</div>
											</FormItem>
										)}
									/>

									{form.watch('scheduleConfig.lunchBreak') && (
										<div className='grid grid-cols-1 md:grid-cols-2 gap-4 pl-4 border-l-2 border-blue-100'>
											<FormField
												control={form.control}
												name='scheduleConfig.lunchStartTime'
												render={({ field }) => (
													<FormItem>
														<FormLabel>Início do intervalo</FormLabel>
														<FormControl>
															<Input type='time' {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name='scheduleConfig.lunchEndTime'
												render={({ field }) => (
													<FormItem>
														<FormLabel>Fim do intervalo</FormLabel>
														<FormControl>
															<Input type='time' {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									)}

									<FormField
										control={form.control}
										name='scheduleConfig.workDays'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Dias de atendimento</FormLabel>
												<div className='space-y-2'>
													{weekDays.map((day) => (
														<div
															key={day.value}
															className='flex items-center space-x-2'
														>
															<Checkbox
																id={`day-${day.value}`}
																checked={field.value?.includes(day.value)}
																onCheckedChange={(checked) => {
																	if (checked) {
																		field.onChange([...field.value, day.value]);
																	} else {
																		field.onChange(
																			field.value?.filter(
																				(d) => d !== day.value
																			)
																		);
																	}
																}}
															/>
															<label
																htmlFor={`day-${day.value}`}
																className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
															>
																{day.label}
															</label>
														</div>
													))}
												</div>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>

						<div className='flex justify-between'>
							<Button type='button' variant='outline' onClick={prevStep}>
								Voltar
							</Button>
							<Button type='submit' disabled={isLoading}>
								{isLoading
									? 'Salvando...'
									: existingDoctor
									? 'Atualizar Perfil'
									: 'Completar Cadastro'}
							</Button>
						</div>
					</div>
				)}
			</form>
		</Form>
	);
}
