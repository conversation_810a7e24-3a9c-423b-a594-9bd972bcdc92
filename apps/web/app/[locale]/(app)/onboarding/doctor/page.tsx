import { Link } from "@i18n/routing";
import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserContextProvider } from "@saas/auth/lib/user-context";
import { Footer } from "@saas/shared/components/Footer";
import { Logo } from "@shared/components/Logo";
import { getLocale, getTranslations } from "next-intl/server";
import { DoctorOnboardingWizard } from "./components/DoctorOnboardingWizard";
import { doctorProfileExists } from "@lib/doctor-utils";
import { db } from "database";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title:  "Cadastro de Médico",
	};
}

export default async function DoctorOnboardingPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Verificar se o perfil está completo e se o usuário já completou o onboarding
	// Redirecionar apenas se ambas as condições forem verdadeiras
	if (user.onboardingComplete) {
		const doctorProfile = await db.doctor.findUnique({
			where: { userId: user.id },
			select: {
				id: true,
				crm: true,
				crmState: true,
				specialties: { select: { id: true } }
			}
		});

		// Se o perfil existir e tiver informações completas, redirecionar para o dashboard
		if (doctorProfile && doctorProfile.crm && doctorProfile.crmState && doctorProfile.specialties.length > 0) {
			return redirect({ href: "/app/dashboard/doctor", locale });
		}
	}

	// Verificar se já existe um perfil de médico para pré-carregar os dados
	const existingDoctor = await db.doctor.findUnique({
		where: { userId: user.id },
		include: {
			specialties: true,
			hospitals: { include: { hospital: true } },
			user: { select: { name: true, phone: true } }
		}
	});

	return (
		<div className="min-h-screen bg-gray-50">
			<div className="w-full mx-auto">
				<UserContextProvider initialUser={user}>
					<DoctorOnboardingWizard
						userId={user.id}
						existingDoctor={existingDoctor}
					/>
				</UserContextProvider>
			</div>
		</div>
	);
}
