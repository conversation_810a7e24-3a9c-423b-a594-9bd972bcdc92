import { Link } from "@i18n/routing";
import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { UserContextProvider } from "@saas/auth/lib/user-context";
import { Footer } from "@saas/shared/components/Footer";
import { Logo } from "@shared/components/Logo";
import { getLocale, getTranslations } from "next-intl/server";
import { PatientOnboardingForm } from "./components/PatientOnboardingForm";
import { patientProfileExists } from "@lib/patient-utils";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("onboarding.patient.title") || "Cadastro de Paciente",
	};
}

export default async function PatientOnboardingPage() {
	const locale = await getLocale();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	if (user.onboardingComplete && await patientProfileExists(user.id)) {
		return redirect({ href: "/app/dashboard/user", locale });
	}

	return (
		<UserContextProvider initialUser={user}>
			<div className="flex min-h-screen w-full bg-card p-8">
				<div className="flex w-full flex-col items-center justify-between">
					<div className="container">
						<div className="flex items-center justify-between">
							<Link href="/" className="block">
								<Logo />
							</Link>

							<div className="flex items-center justify-end gap-2"></div>
						</div>
					</div>

					<div className="container w-full max-w-2xl">
						<div className="mb-8 text-center">
							<h1 className="text-3xl font-bold">Cadastro de Paciente</h1>
							<p className="text-muted-foreground mt-2">
								Complete seu cadastro para continuar
							</p>
						</div>
						<PatientOnboardingForm userId={user.id} />
					</div>

					<Footer />
				</div>
			</div>
		</UserContextProvider>
	);
}
