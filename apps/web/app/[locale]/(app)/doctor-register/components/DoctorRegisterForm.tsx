"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useToast } from "@ui/hooks/use-toast";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { BRPhoneInput } from "../../../(checkout)/checkout/components/phone-input";
import { User, Phone, Mail, FileText, MapPin, CheckCircle } from "lucide-react";

const BRAZILIAN_STATES = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  { value: "AP", label: "Amapá" },
  { value: "AM", label: "Amazonas" },
  { value: "BA", label: "Bahia" },
  { value: "CE", label: "Ceará" },
  { value: "DF", label: "Distrito Federal" },
  { value: "ES", label: "Espírito Santo" },
  { value: "GO", label: "Goiás" },
  { value: "MA", label: "Maranhão" },
  { value: "MT", label: "Mato Grosso" },
  { value: "MS", label: "Mato Grosso do Sul" },
  { value: "MG", label: "Minas Gerais" },
  { value: "PA", label: "Pará" },
  { value: "PB", label: "Paraíba" },
  { value: "PR", label: "Paraná" },
  { value: "PE", label: "Pernambuco" },
  { value: "PI", label: "Piauí" },
  { value: "RJ", label: "Rio de Janeiro" },
  { value: "RN", label: "Rio Grande do Norte" },
  { value: "RS", label: "Rio Grande do Sul" },
  { value: "RO", label: "Rondônia" },
  { value: "RR", label: "Roraima" },
  { value: "SC", label: "Santa Catarina" },
  { value: "SP", label: "São Paulo" },
  { value: "SE", label: "Sergipe" },
  { value: "TO", label: "Tocantins" },
];

const formSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	phone: z.string().min(10, "Telefone inválido").regex(/^\d{10,11}$/, "Formato de celular inválido"),
	crm: z.string().min(4, "CRM inválido"),
	crmState: z.string().length(2, "Estado deve ter 2 caracteres"),
});

type FormValues = z.infer<typeof formSchema>;

export function DoctorRegisterForm() {
	const router = useRouter();
	const { toast } = useToast();
	const [isLoading, setIsLoading] = useState(false);
	const [isRegistered, setIsRegistered] = useState(false);
	const registerMutation = apiClient.auth.registerDoctor.useMutation();

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			email: "",
			phone: "",
			crm: "",
			crmState: "",
		},
	});

	async function onSubmit(values: FormValues) {
		setIsLoading(true);
		try {
			await registerMutation.mutateAsync(values);

			setIsRegistered(true);

			toast({
				title: "Cadastro realizado com sucesso!",
				description: "Seu cadastro foi recebido e está em análise pela nossa equipe.",
				variant: "success",
			});

			// Não redirecionamos mais para a página de login
			// router.push("/auth/login");
		} catch (error) {
			toast({
				title: "Erro no cadastro",
				description: "Ocorreu um erro ao processar seu cadastro. Por favor, tente novamente.",
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	}

	return (
		<div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-primary/20 relative z-10">
			<div className="container mx-auto px-4 py-6 md:py-8">
				<div className="max-w-4xl mx-auto">
					{/* Header Section */}
					<div className="text-center mb-6 md:mb-8">
						<div className="flex justify-center mb-4 md:mb-6">
							<Image
								src="/images/logo-zapvida.png"
								alt="Zapvida Logo"
								width={180}
								height={60}
								className="h-10 md:h-16 w-auto"
							/>
						</div>
						<h1 className="text-2xl md:text-4xl font-bold text-foreground mb-3 md:mb-4 px-4">
							{isRegistered
								? "Cadastro realizado com sucesso!"
								: "Cadastro de Profissional de Saúde"}
						</h1>
						<p className="text-base md:text-lg text-muted-foreground max-w-2xl mx-auto px-4">
							{isRegistered
								? "Seu cadastro foi recebido e está em análise pela nossa equipe."
								: "Junte-se à nossa plataforma de telemedicina e ajude a transformar vidas através da tecnologia em saúde."}
						</p>
					</div>

					{/* Main Content */}
					<div className="grid lg:grid-cols-2 gap-8 items-start">
						{/* Left Side - Benefits/Info */}
						<div className="hidden lg:block space-y-6">
							<div className="bg-white/50 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
								<h3 className="text-2xl font-semibold mb-6 text-foreground">
									Por que escolher a Zapvida?
								</h3>
								<div className="space-y-4">
									<div className="flex items-start gap-3">
										<CheckCircle className="h-6 w-6 text-primary mt-0.5 flex-shrink-0" />
										<div>
											<h4 className="font-medium text-foreground">Plataforma Completa</h4>
											<p className="text-sm text-muted-foreground">
												Ferramentas integradas para consultas, agendamentos e gestão de pacientes
											</p>
										</div>
									</div>
									<div className="flex items-start gap-3">
										<CheckCircle className="h-6 w-6 text-primary mt-0.5 flex-shrink-0" />
										<div>
											<h4 className="font-medium text-foreground">Flexibilidade Total</h4>
											<p className="text-sm text-muted-foreground">
												Atenda de onde estiver, no horário que preferir
											</p>
										</div>
									</div>
									<div className="flex items-start gap-3">
										<CheckCircle className="h-6 w-6 text-primary mt-0.5 flex-shrink-0" />
										<div>
											<h4 className="font-medium text-foreground">Suporte Especializado</h4>
											<p className="text-sm text-muted-foreground">
												Equipe dedicada para ajudar você a crescer na plataforma
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Right Side - Form */}
						<div className="w-full">
							{isRegistered ? (
								<Card className="bg-white/80 backdrop-blur-sm shadow-xl border-0">
									<CardContent className="p-8 text-center space-y-6">
										<div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
											<CheckCircle className="h-8 w-8 text-green-600" />
										</div>
										<div className="space-y-2">
											<h3 className="text-xl font-semibold text-foreground">
												Cadastro realizado com sucesso!
											</h3>
											<p className="text-muted-foreground">
												Enviamos um e-mail com instruções de acesso para você.
											</p>
											<p className="text-sm text-muted-foreground">
												Verifique sua caixa de entrada para acessar a plataforma.
											</p>
										</div>
										<Button
											onClick={() => router.push("/")}
											className="w-full h-11 font-medium"
											size="lg"
										>
											Voltar para a página inicial
										</Button>
									</CardContent>
								</Card>
							) : (
								<Card className="bg-white/80 backdrop-blur-sm shadow-xl border-0">
									<CardHeader className="p-4 md:p-6 pb-4">
										<div className="flex items-center gap-2">
											<User className="h-5 w-5 text-primary" />
											<CardTitle className="text-lg md:text-xl">Informações Profissionais</CardTitle>
										</div>
										<p className="text-sm text-muted-foreground">
											Preencha seus dados para se juntar à nossa rede de especialistas
										</p>
									</CardHeader>
									<CardContent className="p-4 md:p-6 pt-0">
										<Form {...form}>
											<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
												<div className="grid gap-4 md:grid-cols-2">
													<FormField
														control={form.control}
														name="name"
														render={({ field }) => (
															<FormItem className="md:col-span-2">
																<FormLabel className="text-sm font-medium flex items-center gap-2">
																	<User className="h-4 w-4" />
																	Nome completo
																</FormLabel>
																<FormControl>
																	<Input
																		type="text"
																		inputMode="text"
																		autoComplete="name"
																		placeholder="Digite seu nome completo"
																		className="h-11"
																		{...field}
																	/>
																</FormControl>
																<FormMessage className="text-xs" />
															</FormItem>
														)}
													/>
													<FormField
														control={form.control}
														name="email"
														render={({ field }) => (
															<FormItem className="md:col-span-2">
																<FormLabel className="text-sm font-medium flex items-center gap-2">
																	<Mail className="h-4 w-4" />
																	E-mail
																</FormLabel>
																<FormControl>
																	<Input
																		type="email"
																		inputMode="email"
																		autoComplete="email"
																		placeholder="<EMAIL>"
																		className="h-11"
																		{...field}
																	/>
																</FormControl>
																<FormMessage className="text-xs" />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name="phone"
														render={({ field }) => (
															<FormItem className="md:col-span-2">
																<FormLabel className="text-sm font-medium flex items-center gap-2">
																	<Phone className="h-4 w-4" />
																	Celular
																</FormLabel>
																<FormControl>
																	<BRPhoneInput
																		value={field.value}
																		onChange={field.onChange}
																		onBlur={field.onBlur}
																		placeholder="(00) 00000-0000"
																		type="tel"
																		inputMode="tel"
																		autoComplete="tel"
																		className="h-11 input-tel-no-zoom"
																		error={!!form.formState.errors.phone}
																	/>
																</FormControl>
																<FormMessage className="text-xs" />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name="crm"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium flex items-center gap-2">
																	<FileText className="h-4 w-4" />
																	CRM
																</FormLabel>
																<FormControl>
																	<Input
																		type="text"
																		inputMode="numeric"
																		pattern="[0-9]*"
																		autoComplete="off"
																		placeholder="Digite seu número de CRM"
																		className="h-11"
																		{...field}
																	/>
																</FormControl>
																<FormMessage className="text-xs" />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name="crmState"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium flex items-center gap-2">
																	<MapPin className="h-4 w-4" />
																	Estado do CRM
																</FormLabel>
																<Select
																	onValueChange={field.onChange}
																	defaultValue={field.value}
																>
																	<FormControl>
																		<SelectTrigger className="h-11">
																			<SelectValue placeholder="Selecione" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent className="max-h-[200px] overflow-y-auto">
																		{BRAZILIAN_STATES.map((state) => (
																			<SelectItem key={state.value} value={state.value}>
																				{state.value} - {state.label}
																			</SelectItem>
																		))}
																	</SelectContent>
																</Select>
																<FormMessage className="text-xs" />
															</FormItem>
														)}
													/>
												</div>

												<Button
													type="submit"
													className="w-full h-12 md:h-11 font-medium text-base md:text-sm touch-friendly-button"
													size="lg"
													loading={isLoading}
												>
													{isLoading ? "Cadastrando..." : "Cadastrar"}
												</Button>
											</form>
										</Form>
									</CardContent>
								</Card>
							)}

							{/* Footer */}
							{!isRegistered && (
								<div className="text-center mt-6">
									<p className="text-sm text-muted-foreground">
										Já possui uma conta?{" "}
										<Link href="/auth/login" className="font-medium text-primary hover:underline">
											Fazer login
										</Link>
									</p>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
