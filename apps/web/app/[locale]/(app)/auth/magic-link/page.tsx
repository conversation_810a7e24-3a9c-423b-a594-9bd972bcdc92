"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { LoaderIcon } from "lucide-react";
import { Button } from "@ui/components/button";
import { apiClient } from "@shared/lib/api-client";
import { useUser } from "@saas/auth/hooks/use-user";

export default function MagicLinkPage() {
  const t = useTranslations();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const { reloadUser } = useUser();

  const token = searchParams.get("token");
  const redirect = searchParams.get("redirect");

  const verifyTokenMutation = apiClient.auth.verifyToken.useMutation();

  useEffect(() => {
    if (!token) {
      setError("Token não fornecido");
      setLoading(false);
      return;
    }

    (async () => {
      try {
        await verifyTokenMutation.mutateAsync({ token });
        await reloadUser();

        if (redirect) {
          router.push(redirect);
        } else {
          router.push("/app");
        }
      } catch (err) {
        setError("Token inválido ou expirado");
      } finally {
        setLoading(false);
      }
    })();
  }, [token, redirect, router, reloadUser, verifyTokenMutation]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoaderIcon className="size-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <h1 className="font-bold text-3xl md:text-4xl">
          {t("auth.invalidToken.title")}
        </h1>
        <p className="mt-2 mb-4 text-muted-foreground">
          {error}
        </p>
        <Button
          className="w-full"
          onClick={() => router.push("/auth/login")}
        >
          {t("auth.invalidToken.backToLogin")}
        </Button>
      </div>
    );
  }

  return null;
}
