import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { createApiCaller } from "api/trpc/caller";
import { getLocale } from "next-intl/server";
import { logger } from "logs";
import { db } from "database";
import { generateSessionToken, createSession } from "auth/lib/sessions";
import { cookies } from "next/headers";
import { createSessionCookie } from "auth/lib/cookies";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
	const locale = await getLocale();
	const url = new URL(request.url);
	const code = url.searchParams.get("code");
	const cookieStore = await cookies();

	if (!code) {
		logger.warn("No invitation code provided");
		return redirect({ href: "/", locale });
	}

	try {
		// Create API caller for invitation lookup only
		const apiCaller = await createApiCaller();

		// Try to get current user, but don't require them to be logged in
		const { user } = await currentUser();

		// Get the invitation details first
		const invitation = await apiCaller.team.invitationById({
			id: code,
		});

		if (!invitation) {
			logger.warn(`Invitation ${code} not found or expired`);
			return redirect({ href: "/", locale });
		}

		logger.info(`Found invitation with role: ${invitation.role} for email: ${invitation.email}`);

		// If user is already logged in and it's the correct user, proceed with accepting
		if (user && user.email.toLowerCase() === invitation.email.toLowerCase()) {
			logger.info(`User ${user.id} already logged in, accepting invitation with role ${invitation.role}`);

			try {
				// Accept the invitation (this will update the user's role if appropriate)
				const team = await apiCaller.team.acceptInvitation({
					id: code,
				});

				// Force a fresh user lookup to ensure we have the updated user
				const updatedUser = await db.user.findUnique({
					where: { id: user.id },
					select: { role: true, onboardingComplete: true }
				});

				logger.info(`Invitation accepted. User role updated to: ${updatedUser?.role}`);

				// If doctor and onboarding not complete, redirect to onboarding
				if (updatedUser?.role === "DOCTOR" && updatedUser.onboardingComplete === false) {
					return Response.redirect(new URL(`/${locale}/onboarding/doctor`, request.url));
				}

				// Redirecionamento silencioso
				return Response.redirect(new URL(`/${locale}/app/dashboard`, request.url));
			} catch (error: any) {
				if (error?.name === "NEXT_REDIRECT") {
					// Isso é esperado, o Next.js está redirecionando
					logger.info("Redirecting after successful invitation acceptance");
					throw error; // Re-lançar para permitir que o Next.js gerencie o redirecionamento
				}

				logger.error("Error accepting invitation:", error);
				return Response.redirect(new URL(`/${locale}/app`, request.url));
			}
		}

		// Check if a user with this email already exists
		const existingUser = await db.user.findUnique({
			where: { email: invitation.email.toLowerCase() },
			select: { id: true, role: true, onboardingComplete: true },
		});

		if (existingUser) {
			// User exists but isn't logged in - create a session for them
			logger.info(`Creating session for existing user ${existingUser.id} with role ${existingUser.role}`);

			// Create a session token and log user in
			const sessionToken = generateSessionToken();
			await createSession(sessionToken, existingUser.id);
			cookieStore.set(createSessionCookie(sessionToken));

			try {
				// Create a new API caller with the new session
				const authenticatedApiCaller = await createApiCaller();

				// Accept the invitation (this will update the user's role if needed)
				await authenticatedApiCaller.team.acceptInvitation({
					id: code,
					userId: existingUser.id,  // Explicitly provide the user ID
				});

				// Force a fresh user lookup to ensure we have the updated role
				const updatedUser = await db.user.findUnique({
					where: { id: existingUser.id },
					select: { role: true, onboardingComplete: true }
				});

				logger.info(`Logged in existing user and accepted invitation. Role updated to: ${updatedUser?.role}`);

				// If doctor and onboarding not complete, redirect to onboarding
				if (updatedUser?.role === "DOCTOR" && updatedUser.onboardingComplete === false) {
					return Response.redirect(new URL(`/${locale}/onboarding/doctor`, request.url));
				}

				// Redirecionamento silencioso
				return Response.redirect(new URL(`/${locale}/app/dashboard`, request.url));
			} catch (error: any) {
				if (error?.name === "NEXT_REDIRECT") {
					logger.info("Redirecting after successful invitation acceptance");
					throw error;
				}

				logger.error("Error accepting invitation for existing user:", error);

				// Despite errors, redirect user appropriately
				if (existingUser.role === "DOCTOR" && existingUser.onboardingComplete === false) {
					logger.info("Despite invitation error, redirecting doctor to onboarding");
					return Response.redirect(new URL(`/${locale}/onboarding/doctor`, request.url));
				}

				return Response.redirect(new URL(`/${locale}/app`, request.url));
			}
		} else {
			// Need to create a new user
			try {
				logger.info(`Creating new user for email ${invitation.email} with role ${invitation.role}`);

				// Create the user with the correct role from the invitation
				const newUser = await db.user.create({
					data: {
						email: invitation.email.toLowerCase(),
						emailVerified: true, // Verify email automatically since it's from an invitation
						role: invitation.role === "DOCTOR" ? "DOCTOR" :
							  invitation.role === "ADMIN" ? "ADMIN" :
							  invitation.role === "SECRETARY" ? "SECRETARY" : "USER", // Map TeamMemberRole to UserRole
						onboardingComplete: false,
					},
				});

				logger.info(`Created new user ${newUser.id} with role ${newUser.role}`);

				// Create a session token and log user in
				const sessionToken = generateSessionToken();
				await createSession(sessionToken, newUser.id);
				cookieStore.set(createSessionCookie(sessionToken));

				try {
					// Create a new API caller with the new session
					const authenticatedApiCaller = await createApiCaller();

					// Accept the invitation using direct database access to avoid permission issues
					// when the user role was just created
					try {
						// First update the user role directly if needed
						if (invitation.role === "DOCTOR" || invitation.role === "ADMIN" || invitation.role === "SECRETARY") {
							logger.info(`Ensuring user has correct role: ${invitation.role}`);
							await db.user.update({
								where: { id: newUser.id },
								data: { role: invitation.role }
							});
						}

						// Then create the team membership directly
						await db.teamMembership.create({
							data: {
								teamId: invitation.teamId,
								userId: newUser.id,
								role: invitation.role,
							}
						});

						// Mark invitation as used
						await db.teamInvitation.delete({
							where: { id: code }
						});

						logger.info(`Manually processed invitation acceptance for new user ${newUser.id}`);
					} catch (dbError: any) {
						logger.error("Database error during manual invitation acceptance:", dbError);
						// Continue to redirect even if this fails, as the user is created and logged in
					}

					// Determine where to redirect based on the role
					let redirectPath = "/app/dashboard";

					// If user needs to complete onboarding, redirect to onboarding
					if (invitation.role === "DOCTOR") {
						redirectPath = "/onboarding/doctor";
					} else if (invitation.role === "ADMIN") {
						// For admins, we can go straight to dashboard
						redirectPath = "/app/dashboard";
					}

					logger.info(`Redirecting new user to ${redirectPath}`);
					return Response.redirect(new URL(`/${locale}${redirectPath}`, request.url));
				} catch (error: any) {
					if (error?.name === "NEXT_REDIRECT") {
						logger.info("Redirecting after successful invitation acceptance");
						throw error;
					}

					logger.error("Error accepting invitation for new user:", error);

					// For doctors and other roles that need onboarding, we'll still redirect to the appropriate onboarding
					if (invitation.role === "DOCTOR") {
						logger.info("Despite invitation error, redirecting doctor to onboarding");
						return Response.redirect(new URL(`/${locale}/onboarding/doctor`, request.url));
					}

					return Response.redirect(new URL(`/${locale}/app`, request.url));
				}
			} catch (error: any) {
				if (error?.name === "NEXT_REDIRECT") {
					logger.info("Redirecting after successful user creation");
					throw error;
				}

				logger.error("Error during auto-registration:", error);
				// If auto-registration fails, redirect to traditional signup
				return Response.redirect(new URL(`/${locale}/auth/signup?invitationCode=${invitation.id}&email=${encodeURIComponent(invitation.email)}`, request.url));
			}
		}
	} catch (error: any) {
		if (error?.name === "NEXT_REDIRECT") {
			// Isso é esperado, apenas propagar
			throw error;
		}

		logger.error("Unexpected error in invitation processing:", error);
		return Response.redirect(new URL(`/${locale}/`, request.url));
	}
}
