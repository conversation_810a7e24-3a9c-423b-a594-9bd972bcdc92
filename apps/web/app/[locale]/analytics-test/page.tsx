'use client';

import { useState } from 'react';
import { useAnalytics } from '../../../modules/analytics/provider/google';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';

/**
 * Página de teste para verificar se o Google Analytics está funcionando corretamente
 * Acesse: /analytics-test
 * 
 * Esta página permite testar:
 * 1. Se o gtag está carregado
 * 2. Rastreamento de eventos personalizados
 * 3. Rastreamento de conversões
 * 4. Rastreamento de compras
 */
export default function AnalyticsTestPage() {
  const { trackEvent, trackConversion, trackPurchase } = useAnalytics();
  const [eventName, setEventName] = useState('test_event');
  const [eventData, setEventData] = useState('{"category": "test", "value": 1}');
  const [conversionValue, setConversionValue] = useState('80');
  const [transactionId, setTransactionId] = useState('TEST_' + Date.now());
  const [purchaseValue, setPurchaseValue] = useState('150');
  const [status, setStatus] = useState<string[]>([]);

  const addStatus = (message: string) => {
    setStatus(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const checkGtagStatus = () => {
    if (typeof window !== 'undefined') {
      const hasGtag = typeof (window as any).gtag === 'function';
      const hasDataLayer = Array.isArray((window as any).dataLayer);

      addStatus(`gtag function: ${hasGtag ? '✅ Disponível' : '❌ Não encontrado'}`);
      addStatus(`dataLayer: ${hasDataLayer ? '✅ Disponível' : '❌ Não encontrado'}`);

      if (hasDataLayer) {
        addStatus(`dataLayer items: ${(window as any).dataLayer.length}`);
      }

      // Verificar se os IDs estão corretos
      const gaId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;
      const adsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID;
      addStatus(`GA4 ID: ${gaId || '❌ Não configurado'}`);
      addStatus(`Google Ads ID: ${adsId || '❌ Não configurado'}`);

      // Verificar se os scripts estão carregados no DOM
      const gtagScript = document.querySelector('script[src*="googletagmanager.com/gtag/js"]');
      const configScript = document.querySelector('script:not([src]):not([id])');

      addStatus(`Script gtag.js: ${gtagScript ? '✅ Carregado' : '❌ Não encontrado'}`);
      addStatus(`Script config: ${configScript ? '✅ Carregado' : '❌ Não encontrado'}`);

      // Verificar se está no head
      const headScripts = document.head.querySelectorAll('script[src*="googletagmanager.com"]');
      addStatus(`Scripts no <head>: ${headScripts.length > 0 ? '✅ Sim' : '❌ Não'}`);

      // Verificar URL atual
      addStatus(`URL atual: ${window.location.href}`);
    }
  };

  const testCustomEvent = () => {
    try {
      const data = JSON.parse(eventData);
      trackEvent(eventName, data);
      addStatus(`✅ Evento '${eventName}' enviado com sucesso`);
    } catch (error) {
      addStatus(`❌ Erro ao enviar evento: ${error}`);
    }
  };

  const testConversion = () => {
    try {
      trackConversion({
        value: parseFloat(conversionValue),
        currency: 'BRL',
        transaction_id: transactionId
      });
      addStatus(`✅ Conversão enviada: R$ ${conversionValue} (${transactionId})`);
    } catch (error) {
      addStatus(`❌ Erro ao enviar conversão: ${error}`);
    }
  };

  const testPurchase = () => {
    try {
      trackPurchase(transactionId, parseFloat(purchaseValue), 'BRL');
      addStatus(`✅ Compra enviada: R$ ${purchaseValue} (${transactionId})`);
    } catch (error) {
      addStatus(`❌ Erro ao enviar compra: ${error}`);
    }
  };

  const clearStatus = () => {
    setStatus([]);
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Teste do Google Analytics</h1>
        <p className="text-muted-foreground mt-2">
          Use esta página para verificar se o Google Analytics está funcionando corretamente
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Status Check */}
        <Card>
          <CardHeader>
            <CardTitle>Status do Sistema</CardTitle>
            <CardDescription>
              Verificar se o Google Analytics está carregado corretamente
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={checkGtagStatus} className="w-full">
              Verificar Status
            </Button>
          </CardContent>
        </Card>

        {/* Custom Event Test */}
        <Card>
          <CardHeader>
            <CardTitle>Teste de Evento Personalizado</CardTitle>
            <CardDescription>
              Enviar um evento personalizado para o Google Analytics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="eventName">Nome do Evento</Label>
              <Input
                id="eventName"
                value={eventName}
                onChange={(e) => setEventName(e.target.value)}
                placeholder="test_event"
              />
            </div>
            <div>
              <Label htmlFor="eventData">Dados do Evento (JSON)</Label>
              <Textarea
                id="eventData"
                value={eventData}
                onChange={(e) => setEventData(e.target.value)}
                placeholder='{"category": "test", "value": 1}'
                rows={3}
              />
            </div>
            <Button onClick={testCustomEvent} className="w-full">
              Enviar Evento
            </Button>
          </CardContent>
        </Card>

        {/* Conversion Test */}
        <Card>
          <CardHeader>
            <CardTitle>Teste de Conversão</CardTitle>
            <CardDescription>
              Testar o rastreamento de conversões do Google Ads
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="conversionValue">Valor da Conversão (R$)</Label>
              <Input
                id="conversionValue"
                type="number"
                value={conversionValue}
                onChange={(e) => setConversionValue(e.target.value)}
                placeholder="80"
              />
            </div>
            <div>
              <Label htmlFor="transactionId">ID da Transação</Label>
              <Input
                id="transactionId"
                value={transactionId}
                onChange={(e) => setTransactionId(e.target.value)}
                placeholder="TEST_123"
              />
            </div>
            <Button onClick={testConversion} className="w-full">
              Enviar Conversão
            </Button>
          </CardContent>
        </Card>

        {/* Purchase Test */}
        <Card>
          <CardHeader>
            <CardTitle>Teste de Compra</CardTitle>
            <CardDescription>
              Testar o rastreamento de compras/vendas
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="purchaseValue">Valor da Compra (R$)</Label>
              <Input
                id="purchaseValue"
                type="number"
                value={purchaseValue}
                onChange={(e) => setPurchaseValue(e.target.value)}
                placeholder="150"
              />
            </div>
            <Button onClick={testPurchase} className="w-full">
              Enviar Compra
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Status Log */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Log de Status</CardTitle>
            <CardDescription>
              Histórico de testes e verificações
            </CardDescription>
          </div>
          <Button variant="outline" onClick={clearStatus}>
            Limpar
          </Button>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-4 rounded-lg max-h-64 overflow-y-auto">
            {status.length === 0 ? (
              <p className="text-muted-foreground text-sm">
                Nenhum teste executado ainda. Clique nos botões acima para começar.
              </p>
            ) : (
              <div className="space-y-1">
                {status.map((message, index) => (
                  <div key={index} className="text-sm font-mono">
                    {message}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Como Verificar no Google Analytics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm space-y-2">
            <p><strong>1. Tempo Real:</strong></p>
            <p className="ml-4">• Acesse Google Analytics → Relatórios → Tempo real</p>
            <p className="ml-4">• Você deve ver esta página sendo visitada</p>
            
            <p><strong>2. Eventos:</strong></p>
            <p className="ml-4">• Vá para Relatórios → Engajamento → Eventos</p>
            <p className="ml-4">• Os eventos enviados devem aparecer aqui</p>
            
            <p><strong>3. Conversões:</strong></p>
            <p className="ml-4">• Acesse Google Ads → Conversões</p>
            <p className="ml-4">• As conversões devem ser registradas lá</p>
            
            <p><strong>4. Debug:</strong></p>
            <p className="ml-4">• Abra o DevTools (F12) → Console</p>
            <p className="ml-4">• Verifique se não há erros relacionados ao gtag</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
