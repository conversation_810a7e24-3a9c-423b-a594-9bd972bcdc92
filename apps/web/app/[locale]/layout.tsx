import { Toaster } from '@ui/components/toaster';
import { cn } from '@ui/lib';
import { Provider as <PERSON><PERSON>Provider } from 'jotai';
import type { Metadata } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { Poppins } from 'next/font/google';
import NextTopLoader from 'nextjs-toploader';

import { ApiClientProvider } from '@shared/components/ApiClientProvider';
import { GradientBackgroundWrapper } from '@shared/components/GradientBackgroundWrapper';
import { ThemeProvider } from 'next-themes';
import './globals.css';
import '@livekit/components-styles';
import '@livekit/components-styles/prefabs';

import { config } from '@config';
import { AnalyticsScriptGoogle } from '../../modules/analytics/provider/google';
import { PostHogProvider } from '../../components/providers/PostHogProvider';
import { MedicalBusinessSchema } from '../../components/structured-data/MedicalBusinessSchema';

export const metadata: Metadata = {
	title: {
		absolute: 'Consulta Online WhatsApp | Telemedicina 24h | ZapVida',
		default: 'Consulta Online WhatsApp | Telemedicina 24h | ZapVida',
		template: '%s | ZapVida - Consulta Online WhatsApp',
	},
	description:
		'Consulte médicos online via WhatsApp 24h. Telemedicina segura, prescrição digital e atendimento imediato. Assinantes têm descontos especiais. Mais de 30.000 pacientes atendidos.',
	openGraph: {
		type: 'website',
		locale: 'pt_BR',
		url: 'https://zapvida.com',
		title: 'Consulta Online WhatsApp | Telemedicina 24h | ZapVida',
		description:
			'Consulte médicos online via WhatsApp 24h. Telemedicina segura, prescrição digital e atendimento imediato. Assinantes têm descontos especiais.',
		siteName: 'ZapVida',
		images: [
			{
				url: 'https://zapvida.com/images/og-image.png',
				width: 1200,
				height: 630,
				alt: 'Consulta Online WhatsApp - Telemedicina 24h ZapVida',
			},
		],
	},
	twitter: {
		creator: '@zapvida',
		card: 'summary_large_image',
	},
	metadataBase: new URL('https://zapvida.com'),
	verification: {
		google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
	},
	alternates: {
		canonical: 'https://zapvida.com',
	},
};

const sansFont = Poppins({
	subsets: ['latin'],
	weight: ['400', '500', '600', '700'],
	variable: '--font-sans',
});

export default async function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const locale = await getLocale();
	const messages = await getMessages();

	return (
		<html lang={locale} suppressHydrationWarning>
			<head>
				<AnalyticsScriptGoogle />
				<MedicalBusinessSchema />
			</head>
			<body
				className={cn(
					'min-h-screen bg-background font-sans text-foreground antialiased',
					sansFont.variable
				)}
			>
				<NextTopLoader color='var(--colors-ring)' />
				<NextIntlClientProvider locale={locale} messages={messages}>
					<PostHogProvider>
						<ThemeProvider
							attribute='class'
							disableTransitionOnChange
							enableSystem
							defaultTheme={config.ui.defaultTheme}
							themes={config.ui.enabledThemes}
						>
							<ApiClientProvider>
								<JotaiProvider>
									<GradientBackgroundWrapper>
										{children}
									</GradientBackgroundWrapper>
								</JotaiProvider>
							</ApiClientProvider>
						</ThemeProvider>
						<Toaster />
					</PostHogProvider>
				</NextIntlClientProvider>
			</body>
		</html>
	);
}
