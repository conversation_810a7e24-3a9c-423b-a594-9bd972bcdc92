import { constructMetadata } from '@lib/utils';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Confirmação de Consulta | ZapVida',
    description: 'Confirme sua consulta médica online na ZapVida. Atendimento 24 horas, prescrição digital e muito mais.',
    canonical: 'https://zapvida.com/confirmacao-consulta',
  });
}

export default function ConfirmacaoConsultaPage() {
  // Redireciona para a página de login, onde o usuário poderá acessar suas consultas
  redirect('/auth/login?redirect=/app/appointments');
  
  // Este código nunca será executado devido ao redirecionamento
  return null;
}
