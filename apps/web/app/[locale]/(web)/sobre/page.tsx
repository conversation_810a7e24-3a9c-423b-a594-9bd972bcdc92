/* eslint-disable @next/next/no-img-element */
'use client';

import React from 'react';
import Link from 'next/link';
import {
	Calendar,
	ChevronRight,
	MessageCircle,
	MessageCircleCode,
	ShieldCheck,
	Stethoscope,
	HeartPulse,
	FileText,
	Clock,
	Users,
	Lock,
	Globe,
} from 'lucide-react';
import { FaWhatsapp, FaInstagram, FaFacebookMessenger } from 'react-icons/fa';

import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@ui/components/accordion';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { CtaFooter } from '@marketing/home/<USER>/cta-footer';
import { CallToActionButton } from '@shared/components/CallToActionButton';

export default function SobrePage() {
	return (
		<div className='min-h-screen bg-background'>
			{/* Hero Section */}
			<section className='relative flex h-[600px] items-center justify-center overflow-hidden'>
				<div className='absolute inset-0 z-0'>
					<img
						src='/images/young-happy-positive-woman.webp'
						// src='/images/doctor-medical-worker.webp'
						alt='ZapVida Telemedicina'
						className='h-full w-full object-cover brightness-50'
					/>
				</div>
				<div className='relative z-10 mx-auto max-w-3xl px-4 text-center'>
					<Badge className='mb-4 bg-ring text-white hover:bg-ring/90'>
						Telemedicina pelo WhatsApp
					</Badge>
					<h1 className='mb-6 text-4xl font-bold text-white md:text-5xl'>
						Sobre a ZapVida: Sua saúde a um clique de distância
					</h1>
					<p className='mb-8 text-lg text-gray-200'>
						Conectamos você a médicos qualificados 24h/dia através do WhatsApp,
						Instagram ou Facebook
					</p>

                    <div className='flex justify-center'>
                        <CallToActionButton href='/pay/plantao'>
							FALE COM UM MÉDICO AGORA
						</CallToActionButton>
					</div>
				</div>
			</section>

			{/* About Section */}
			<section className='bg-background py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Sobre a ZapVida</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Telemedicina acessível, rápida e humana para todos os brasileiros
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-2'>
						<div className='space-y-6'>
							<h3 className='text-2xl font-semibold'>Quem Somos</h3>
							<p className='text-muted-foreground'>
								A ZapVida é uma plataforma de telemedicina dedicada a tornar o
								acesso à saúde mais rápido, simples e seguro. Conectamos
								pacientes a médicos qualificados por meio de consultas online,
								sem filas ou burocracias, diretamente pelo WhatsApp, Instagram
								ou Facebook.
							</p>
							<p className='text-muted-foreground'>
								Nosso compromisso é oferecer atendimento humanizado, ético e
								eficiente, 24 horas por dia, onde quer que você esteja.
							</p>

							<div className='flex flex-wrap gap-4'>
								<div className='flex items-center rounded-full bg-primary/10 px-4 py-2'>
									<FaWhatsapp className='mr-2 h-5 w-5 text-primary' />
									<span>WhatsApp</span>
								</div>
								<div className='flex items-center rounded-full bg-primary/10 px-4 py-2'>
									<FaInstagram className='mr-2 h-5 w-5 text-primary' />
									<span>Instagram</span>
								</div>
								<div className='flex items-center rounded-full bg-primary/10 px-4 py-2'>
									<FaFacebookMessenger className='mr-2 h-5 w-5 text-primary' />
									<span>Facebook</span>
								</div>
							</div>
						</div>
						<div className='rounded-lg overflow-hidden'>
							<img
								src='/images/doctor-medical-worker.webp'
								// src='/images/young-happy-positive-woman.webp'
								alt='Consulta médica online pela ZapVida'
								className='w-full h-full object-cover'
							/>
						</div>
					</div>
				</div>
			</section>

			{/* Mission, Vision, Values Section */}
			<section className='bg-[#0eadbb8b]/10 py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Nossa Identidade</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Conheça nossos pilares e o que nos move a transformar a saúde
							digital no Brasil
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-3'>
						{/* Mission */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<HeartPulse className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>Missão</CardTitle>
							</CardHeader>
							<CardContent>
								<p className='text-muted-foreground'>
									Facilitar o acesso à saúde, sendo a opção mais rápida do
									Brasil para consultas médicas e resolução de necessidades de
									saúde.
								</p>
							</CardContent>
						</Card>

						{/* Vision */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<Globe className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>Visão</CardTitle>
							</CardHeader>
							<CardContent>
								<p className='text-muted-foreground'>
									Ser líder em telemedicina no Brasil, reconhecida pela
									excelência no atendimento, inovação tecnológica e impacto
									positivo na vida das pessoas.
								</p>
							</CardContent>
						</Card>

						{/* Values */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<Users className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>Valores</CardTitle>
							</CardHeader>
							<CardContent>
								<p className='text-muted-foreground'>
									<strong>Qualidade:</strong> Atendimento de alto padrão com
									profissionais capacitados.
									<br />
									<br />
									<strong>Acessibilidade:</strong> Saúde ao alcance de todos, a
									qualquer hora e lugar.
									<br />
									<br />
									<strong>Humanização:</strong> Cuidado com respeito, empatia e
									confiança.
									<br />
									<br />
									<strong>Segurança:</strong> Proteção total dos dados, em
									conformidade com a LGPD.
								</p>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Benefits Section */}
			<section className='bg-background py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Por que escolher a ZapVida?
						</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Vantagens exclusivas da nossa plataforma de telemedicina
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
						{/* Benefit 1 */}
						<Card className='bg-card'>
							<CardHeader>
								<Clock className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Atendimento 24/7</CardTitle>
								<CardDescription>
									Médicos disponíveis a qualquer hora do dia ou da noite
								</CardDescription>
							</CardHeader>
						</Card>

						{/* Benefit 2 */}
						<Card className='bg-card'>
							<CardHeader>
								<Stethoscope className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Médicos Qualificados</CardTitle>
								<CardDescription>
									Profissionais especializados e verificados pelo CFM
								</CardDescription>
							</CardHeader>
						</Card>

						{/* Benefit 3 */}
						<Card className='bg-card'>
							<CardHeader>
								<FileText className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Prescrição Digital</CardTitle>
								<CardDescription>
									Receitas e atestados enviados diretamente para seu dispositivo
								</CardDescription>
							</CardHeader>
						</Card>

						{/* Benefit 4 */}
						<Card className='bg-card'>
							<CardHeader>
								<Lock className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Privacidade Garantida</CardTitle>
								<CardDescription>
									Dados protegidos e consultas confidenciais
								</CardDescription>
							</CardHeader>
						</Card>
					</div>
				</div>
			</section>

			{/* How We Work Section */}
			<section className='bg-[#0eadbb8b]/10 py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Como Funcionamos</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Conheça o processo simples da ZapVida para cuidar da sua saúde
							online
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-3'>
						{/* Step 1 */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<MessageCircle className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>1. Inicie o Contato</CardTitle>
								<CardDescription>
									Comece a conversa pelo WhatsApp, Instagram ou Facebook
								</CardDescription>
							</CardHeader>
							<CardContent className='space-y-4'>
								<ul className='space-y-2 text-sm text-muted-foreground'>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Escolha sua rede social preferida
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Descreva seu problema de saúde
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Envie fotos ou documentos se necessário
									</li>
								</ul>
							</CardContent>
						</Card>

						{/* Step 2 */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<Calendar className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>2. Consulta Online</CardTitle>
								<CardDescription>
									Atendimento rápido com médicos especialistas
								</CardDescription>
							</CardHeader>
							<CardContent className='space-y-4'>
								<ul className='space-y-2 text-sm text-muted-foreground'>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Comunicação via texto ou áudio
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Avaliação médica completa
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Orientações personalizadas
									</li>
								</ul>
							</CardContent>
						</Card>

						{/* Step 3 */}
						<Card className='relative border-2 border-primary/10 transition-all hover:border-primary/20'>
							<CardHeader>
								<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
									<MessageCircleCode className='h-6 w-6 text-primary' />
								</div>
								<CardTitle>3. Solução Imediata</CardTitle>
								<CardDescription>
									Receba prescrições, atestados e orientações no seu dispositivo
								</CardDescription>
							</CardHeader>
							<CardContent className='space-y-4'>
								<ul className='space-y-2 text-sm text-muted-foreground'>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Receitas médicas digitais
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Atestados quando necessário
									</li>
									<li className='flex items-center'>
										<ChevronRight className='mr-2 h-4 w-4 text-primary' />
										Acompanhamento pós-consulta
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* FAQ Section */}
			<section className='bg-background py-20'>
				<div className='mx-auto max-w-6xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Perguntas Frequentes</h2>
						<p className='text-muted-foreground'>
							Tire suas dúvidas sobre a ZapVida
						</p>
					</div>

					<div className='space-y-4'>
						<Card>
							<CardHeader>
								<CardTitle className='text-xl'>
									Encontre respostas para as principais dúvidas
								</CardTitle>
							</CardHeader>
							<CardContent>
								<Accordion type='single' collapsible className='w-full'>
									<AccordionItem value='item-1'>
										<AccordionTrigger>
											O que é a ZapVida e como funciona?
										</AccordionTrigger>
										<AccordionContent>
											A ZapVida é uma plataforma de telemedicina que conecta
											pacientes a médicos qualificados através do WhatsApp,
											Instagram ou Facebook. Nosso serviço permite que você
											tenha consultas médicas online, receba prescrições
											digitais e orientações de saúde sem sair de casa, 24 horas
											por dia, 7 dias por semana.
										</AccordionContent>
									</AccordionItem>

									<AccordionItem value='item-2'>
										<AccordionTrigger>
											As consultas online são seguras?
										</AccordionTrigger>
										<AccordionContent>
											Sim, todas as consultas na ZapVida são realizadas em
											ambiente digital seguro, com criptografia de ponta a ponta
											para proteger suas informações. Nossos médicos são
											verificados e registrados no Conselho Federal de Medicina
											(CFM), garantindo atendimento ético e de qualidade. Além
											disso, seguimos rigorosamente a Lei Geral de Proteção de
											Dados (LGPD) para garantir a privacidade dos seus dados.
										</AccordionContent>
									</AccordionItem>

									<AccordionItem value='item-3'>
										<AccordionTrigger>
											Que tipos de problemas médicos podem ser resolvidos pela
											telemedicina?
										</AccordionTrigger>
										<AccordionContent>
											A telemedicina é eficaz para diversos problemas de saúde,
											como:
											<ul className='list-disc pl-5 mt-2'>
												<li>
													Sintomas leves a moderados (gripes, alergias,
													infecções)
												</li>
												<li>Dúvidas sobre medicamentos e tratamentos</li>
												<li>Orientações sobre doenças crônicas</li>
												<li>Análise de resultados de exames</li>
												<li>Renovação de receitas</li>
												<li>Emissão de atestados médicos</li>
												<li>Encaminhamentos para especialistas</li>
											</ul>
											Em casos de emergência ou que necessitem de exame físico
											detalhado, orientamos a buscar atendimento presencial.
										</AccordionContent>
									</AccordionItem>

									<AccordionItem value='item-4'>
										<AccordionTrigger>
											Como recebo as prescrições médicas?
										</AccordionTrigger>
										<AccordionContent>
											Após a consulta, o médico enviará a receita médica digital
											diretamente para seu WhatsApp, Instagram ou Facebook. A
											prescrição é válida em todo o território nacional e pode
											ser apresentada em farmácias em formato digital ou
											impressa. As receitas digitais são assinadas
											eletronicamente pelos médicos, garantindo sua
											autenticidade e validade legal.
										</AccordionContent>
									</AccordionItem>

									<AccordionItem value='item-5'>
										<AccordionTrigger>
											Qual o horário de funcionamento da ZapVida?
										</AccordionTrigger>
										<AccordionContent>
											A ZapVida funciona 24 horas por dia, 7 dias por semana,
											incluindo feriados. Nosso objetivo é oferecer acesso à
											saúde a qualquer momento que você precisar, sem restrições
											de horário.
										</AccordionContent>
									</AccordionItem>
								</Accordion>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
			<CtaFooter />
		</div>
	);
}
