'use client';

import Link from 'next/link';
import {
	ArrowR<PERSON>,
	CalendarX2Icon,
	Shield,
	Stethoscope,
	Timer,
} from 'lucide-react';

import { Button } from '@ui/components/button';
import { Card, CardContent } from '@ui/components/card';
import { PlantaoHero } from '@marketing/home/<USER>/plantao/plantao-hero';
import MaxWidthWrapper from '@marketing/shared/components/MaxWidthWrapper';
import { TestimonialsSection } from '@marketing/home/<USER>/home-sections/testimonials-section';
import { CallToActionButton } from '@shared/components/CallToActionButton';
import { ON_DUTY_DOCTORS } from 'api/constants/on-duty';

export default function OnCallPage() {
	const conditions = [
		'Gripes e Resfriados',
		'Dores de Garganta',
		'Dores de Cabeça',
		'Febres',
		'Alergias',
		'Problemas Digestivos',
		'Infecções Urinárias',
		'Problemas de Pele',
		'Dores Musculares',
		'Ansiedade',
		'Insônia',
		'Pressão Alta',
	];

	const benefits = [
		{
			icon: <Timer className='h-6 w-6 text-primary' />,
			title: 'Consulte sem demora',
			description: 'Atendimento médico imediato, sem agendamento e sem espera.',
		},
		{
			icon: <Stethoscope className='h-6 w-6 text-primary' />,
			title: 'Consulta 100% segura',
			description: 'Atendimento sigiloso, seguindo as normas da LGPD.',
		},
		{
			icon: <Shield className='h-6 w-6 text-primary' />,
			title: 'Médicos especialistas',
			description: 'Atendimento com médicos verificados e experientes.',
		},
		{
			icon: <CalendarX2Icon className='h-6 w-6 text-primary' />,
			title: 'Retorno garantido',
			description:
				'Se precisar, consulte novamente dentro de 24h, sem custo adicional.',
		},
	];

	return (
		<div className='flex min-h-screen flex-col '>
			{/* Hero Section */}
			<PlantaoHero />

			{/* Benefits Section */}
			<section className='py-16 '>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='text-3xl font-bold'>Benefícios do Plantão Médico</h2>
					</div>
					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
						{benefits.map((benefit, index) => (
							<Card
								key={index}
								className='border-2 border-muted bg-background/50'
							>
								<CardContent className='flex flex-col items-center p-6 text-center'>
									<div className='mb-4'>{benefit.icon}</div>
									<h3 className='mb-2 font-semibold'>{benefit.title}</h3>
									<p className='text-sm text-muted-foreground'>
										{benefit.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* Steps Section */}
			<section className='py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Como funciona?</h2>
						<p className='text-muted-foreground'>
							Processo simples e rápido para seu atendimento
						</p>
					</div>
					<div className='grid gap-8 md:grid-cols-3'>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								1
							</div>
							<h3 className='mb-2 font-semibold'>Escolha Plantão ou Agende</h3>
							<p className='text-sm text-muted-foreground'>
								Atendimento imediato ou consulta agendada – você escolhe o que
								precisa!
							</p>
						</div>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								2
							</div>
							<h3 className='mb-2 font-semibold'>Confirmação instantânea</h3>
							<p className='text-sm text-muted-foreground'>
								Após o pagamento, você e o médico recebem a confirmação no
								WhatsApp e e-mail. 🔗 Vocês são automaticamente conectados para
								iniciar a consulta.
							</p>
						</div>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								3
							</div>
							<h3 className='mb-2 font-semibold'>Início da consulta</h3>
							<p className='text-sm text-muted-foreground'>
								Seu médico já está pronto para atender! 🩺 Consulta por
								chat/áudio, ligação ou vídeo. O médico ajudará no diagnóstico e
								conduta. Após, encaminha os documentos médicos necessários
								(prescrição, atestado, pedido de exame).
							</p>
						</div>
					</div>
				</MaxWidthWrapper>

				<TestimonialsSection />
			</section>

			{/* Conditions Section */}
			<section className='bg-muted/50 py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Problemas que Podemos Resolver
						</h2>
						<p className='text-muted-foreground'>
							Problemas de saúde? Consulte um médico agora!
						</p>
					</div>
					<div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4'>
						{conditions.map((condition, index) => (
							<div
								key={index}
								className='flex items-center rounded-lg border bg-background p-4'
							>
								<div className='mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-secondary'>
									<Stethoscope className='h-4 w-4 text-white' />
								</div>
								<span className='text-sm'>{condition}</span>
							</div>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* CTA Section */}
			<section className='bg-primary py-16 text-primary-foreground'>
				<MaxWidthWrapper className='text-center'>
					<h2 className='mb-6 text-3xl font-bold'>
						Atendimento médico sem complicações,{' '}
						<span className='text-ring md:block'>quando e onde precisar.</span>
					</h2>
					<p className='mb-8 text-lg'>
						Receba atendimento médico de qualidade sem sair de casa
					</p>
                    <div className='mt-8 text-center'>
                        <Link href={`/pay/plantao`}>
							<CallToActionButton className='px-8' icon={false}>
								Inicie sua consulta agora{' '}
								<ArrowRight className='ml-2 h-4 w-4' />
							</CallToActionButton>
						</Link>
					</div>
				</MaxWidthWrapper>
			</section>
		</div>
	);
}
