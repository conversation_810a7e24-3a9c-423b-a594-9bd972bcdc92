import { constructMetadata } from '@lib/utils';
import { Metadata } from 'next';
import { SubscriptionHero } from './components/subscription-hero';
import { PlansComparison } from './components/plans-comparison';
import { BenefitsSection } from './components/benefits-section';
import { FAQSection } from './components/faq-section';
import { CTASection } from './components/cta-section';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Consulta Online | Planos de Assinatura | ZapVida',
    description: 'Assinaturas ZapVida: consulte médicos online via WhatsApp com descontos. Telemedicina 24h, prescrição digital e atendimento prioritário incluídos.',
    canonical: 'https://zapvida.com/assinaturas',
  });
}

export default function AssinaturasPage() {
  return (
    <div className="min-h-screen bg-background">
      <SubscriptionHero />
      <div id="plans-section">
        <PlansComparison />
      </div>
      <BenefitsSection />
      <FAQSection />
      <CTASection />
    </div>
  );
}
