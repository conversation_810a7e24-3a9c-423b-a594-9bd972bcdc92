'use client';

import { Card, CardContent } from '@ui/components/card';
import {
  Clock,
  Shield,
  Users,
  FileText,
  Video,
  Smartphone
} from 'lucide-react';

const benefits = [
  {
    icon: Clock,
    title: 'Atendimento Rápido',
    description: 'Consultas médicas em poucos minutos, sem filas ou esperas desnecessárias.'
  },
  {
    icon: Shield,
    title: 'Segurança Total',
    description: 'Plataforma segura e certificada, com dados protegidos e consultas confidenciais.'
  },
  {
    icon: Users,
    title: 'Especialistas Qualificados',
    description: 'Médicos certificados e especializados em diversas áreas da medicina.'
  },
  {
    icon: FileText,
    title: 'Receitas Digitais',
    description: 'Receitas médicas digitais válidas em todo Brasil, enviadas diretamente para você.'
  },
  {
    icon: Video,
    title: 'Consulta por Vídeo',
    description: 'Atendimento humanizado através de videochamada com qualidade HD.'
  },
  {
    icon: Smartphone,
    title: 'Acesso Mobile',
    description: 'Consulte médicos pelo celular, tablet ou computador, onde você estiver.'
  }
];

export function BenefitsSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Por que escolher a ZapVida?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Oferecemos a melhor experiência em telemedicina com tecnologia de ponta e cuidado humanizado
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center mt-12">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <p className="text-gray-600">Disponível todos os dias</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">5min</div>
                <p className="text-gray-600">Tempo médio de atendimento</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">98%</div>
                <p className="text-gray-600">Satisfação dos pacientes</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
