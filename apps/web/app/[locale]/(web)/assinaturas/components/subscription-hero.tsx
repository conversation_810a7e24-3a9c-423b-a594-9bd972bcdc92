'use client';

import Image from 'next/image';
import Link from 'next/link';
import { CheckCircle2, ArrowRight } from 'lucide-react';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { CallToActionButton } from '@shared/components/CallToActionButton';

export function SubscriptionHero() {
  return (
    <div className='relative overflow-hidden bg-primary pb-12 pt-24 md:pb-20 lg:pb-28 lg:pt-11'>
      <div className='relative mx-auto flex max-w-7xl flex-col items-center gap-4 px-4 sm:px-6 lg:px-8'>
        {/* Background Effects */}
        <div className='absolute inset-0 overflow-hidden'>
          <div className='absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-white/10 blur-3xl' />
        </div>

        {/* Content Grid */}
        <div className='relative grid w-full gap-8 pt-0 lg:grid-cols-2 lg:gap-12 lg:pt-16'>
          {/* Left Column */}
          <div className='flex flex-col justify-center'>
            <div className='flex flex-col gap-6'>
              {/* Badge */}
              <Badge className='w-fit bg-white/20 text-white hover:bg-white/30'>
                Planos de assinatura
              </Badge>

              {/* Main Text */}
              <h1 className='font-urban text-4xl font-bold tracking-tight text-white sm:text-4xl md:text-5xl'>
                Comece sua jornada de saúde hoje mesmo
              </h1>

              <p className='max-w-xl text-lg text-white/80'>
                Com o <b>ZapVida Sempre</b>, você tem <b>2 consultas médicas por mês</b> com especialistas qualificados.
                Atendimento online, rápido e seguro, quando você precisar.
              </p>

              {/* Features List */}
              <div className='flex flex-col gap-3'>
                <div className='flex items-center gap-2 text-sm text-white/80'>
                  <CheckCircle2 className='h-5 w-5 text-green-500' />
                  <span>2 consultas médicas por mês</span>
                </div>
                <div className='flex items-center gap-2 text-sm text-white/80'>
                  <CheckCircle2 className='h-5 w-5 text-green-500' />
                  <span>Especialistas qualificados e experientes</span>
                </div>
                <div className='flex items-center gap-2 text-sm text-white/80'>
                  <CheckCircle2 className='h-5 w-5 text-green-500' />
                  <span>Atendimento rápido em poucos minutos</span>
                </div>
                <div className='flex items-center gap-2 text-sm text-white/80'>
                  <CheckCircle2 className='h-5 w-5 text-green-500' />
                  <span>Dados 100% protegidos e confidenciais</span>
                </div>
              </div>

              {/* Price Card */}
              <div className='w-full rounded-xl bg-white/10 p-10 backdrop-blur-sm'>
                <p className='mb-1 text-sm font-medium text-white/80'>
                  Plano mensal a partir de
                </p>
                <div className='mb-3 text-4xl font-bold text-white'>R$ 49</div>

                <div className='flex flex-col sm:flex-row gap-4'>
                  <CallToActionButton
                    className='w-full md:w-auto'
                    href='/pay/assinatura'
                  >
                    COMEÇAR AGORA
                  </CallToActionButton>

                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/20 text-white hover:bg-white/10 bg-white/5 backdrop-blur-sm"
                    onClick={() => {
                      const plansSection = document.getElementById('plans-section');
                      plansSection?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Ver planos
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Images */}
          <div className='relative hidden max-h-[400px] w-full lg:block'>
            {/* Main Image */}
            <div className='relative aspect-[3/4] overflow-hidden rounded-xl'>
              <Image
                src='/images/young-happy-positive-woman.webp'
                alt='Assinaturas Médicas ZapVida'
                fill
                className='object-cover'
                priority
              />
            </div>

            {/* Subscription Badge */}
            <div className='absolute right-8 top-6 flex items-center gap-2 rounded-full bg-white px-4 py-2 shadow-lg'>
              <div className='h-2.5 w-2.5 animate-pulse rounded-full bg-primary' />
              <span className='text-sm font-medium'>Planos Ativos</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
