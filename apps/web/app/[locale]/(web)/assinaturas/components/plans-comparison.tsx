'use client';

import Link from 'next/link';
import { Button } from '@ui/components/button';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@ui/components/card';
import { Check } from 'lucide-react';

const plans = [
  {
    id: 'avulsa-80',
    name: 'Consulta Avulsa',
    price: 80,
    description: 'Consulta única quando precisar',
    features: [
      '1 consulta médica',
      'Atendimento por vídeo',
      'Receita digital',
      'Sem compromisso mensal'
    ],
    buttonText: 'Agendar consulta',
    href: '/plantao',
    popular: false
  },
  {
    id: 'zapvida-sempre',
    name: 'ZapVida Sempre',
    price: 49,
    originalPrice: 160,
    description: '2 consultas por mês',
    features: [
      '2 consultas médicas por mês',
      'Atendimento por vídeo ou chat',
      'Acesso a especialistas',
      'Receitas digitais',
      'Histórico médico completo',
      'Suporte prioritário'
    ],
    buttonText: 'Assinar agora',
    href: '/pay/assinatura?plan=zapvida-sempre',
    popular: true,
    savings: 'Economize R$ 111/mês'
  },
  {
    id: 'plantao',
    name: 'Plantão Médico',
    price: 120,
    description: 'Atendimento de urgência 24h',
    features: [
      'Atendimento imediato',
      'Médicos de plantão',
      'Receita digital',
      'Suporte emergencial'
    ],
    buttonText: 'Falar com médico',
    href: '/plantao',
    popular: false
  }
];

export function PlansComparison() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Escolha o plano ideal para você
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Compare nossos planos e encontre a opção que melhor se adapta às suas necessidades
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${plan.popular ? 'border-primary border-2 shadow-xl' : 'border-gray-200'}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold">
                    Mais popular
                  </span>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl font-bold text-gray-900">
                  {plan.name}
                </CardTitle>
                <div className="mt-4">
                  <div className="flex items-center justify-center">
                    <span className="text-4xl font-bold text-gray-900">
                      R$ {plan.price}
                    </span>
                    {plan.id === 'zapvida-sempre' && (
                      <span className="text-gray-600 ml-2">/mês</span>
                    )}
                  </div>
                  {plan.originalPrice && (
                    <div className="text-center mt-2">
                      <span className="text-gray-500 line-through text-lg">
                        R$ {plan.originalPrice}/mês
                      </span>
                      <div className="text-green-600 font-semibold text-sm mt-1">
                        {plan.savings}
                      </div>
                    </div>
                  )}
                </div>
                <p className="text-gray-600 mt-2">{plan.description}</p>
              </CardHeader>

              <CardContent className="space-y-4">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </CardContent>

              <CardFooter>
                <Button
                  asChild
                                    className={`w-full ${
                    plan.popular
                      ? 'bg-primary hover:bg-primary/90 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                  }`}
                >
                  <Link href={plan.href}>
                    {plan.buttonText}
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600">
            Todos os planos incluem atendimento seguro e receitas digitais válidas em todo Brasil
          </p>
        </div>
      </div>
    </section>
  );
}
