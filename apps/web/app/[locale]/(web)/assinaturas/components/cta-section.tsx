'use client';

import Link from 'next/link';
import { But<PERSON> } from '@ui/components/button';
import { <PERSON>, ShieldChe<PERSON>, BadgeCheck, Smartphone } from 'lucide-react';

export function CTASection() {
  return (
    <section className="py-20 bg-primary">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">
            Comece sua jornada de saúde hoje mesmo
          </h2>

                     <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
            Junte-se a milhares de pessoas que já cuidam da saúde de forma prática e acessível
          </p>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 max-w-2xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center text-white">
                <Check className="h-5 w-5 text-green-400 mr-3 flex-shrink-0" />
                <span>2 consultas por mês (ZapVida Sempre)</span>
              </div>
              <div className="flex items-center text-white">
                <Check className="h-5 w-5 text-green-400 mr-3 flex-shrink-0" />
                <span>Atendimento 24/7</span>
              </div>
              <div className="flex items-center text-white">
                <Check className="h-5 w-5 text-green-400 mr-3 flex-shrink-0" />
                <span>Receitas digitais válidas</span>
              </div>
              <div className="flex items-center text-white">
                <Check className="h-5 w-5 text-green-400 mr-3 flex-shrink-0" />
                <span>Cancele quando quiser</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              className="bg-white text-primary hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
            >
              <Link href="/pay/assinatura?plan=zapvida-sempre">
                Assinar por R$ 49/mês
              </Link>
            </Button>

            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white/10 px-8 py-4 text-lg"
            >
              <Link href="/plantao">
                Fazer consulta avulsa
              </Link>
            </Button>
          </div>

          {/* Trust bar */}
          <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-3xl mx-auto">
            <div className="flex items-center justify-center gap-3 text-white/90">
              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-white/15">
                <ShieldCheck className="h-5 w-5" />
              </div>
              <div>
                <p className="text-sm font-semibold leading-tight">Pagamento seguro</p>
                <p className="text-xs text-white/70">Criptografia e antifraude</p>
              </div>
            </div>
            <div className="flex items-center justify-center gap-3 text-white/90">
              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-white/15">
                <BadgeCheck className="h-5 w-5" />
              </div>
              <div>
                <p className="text-sm font-semibold leading-tight">Sem fidelidade</p>
                <p className="text-xs text-white/70">Cancele quando quiser</p>
              </div>
            </div>
            <div className="flex items-center justify-center gap-3 text-white/90">
              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-white/15">
                <Smartphone className="h-5 w-5" />
              </div>
              <div>
                <p className="text-sm font-semibold leading-tight">Acesso imediato</p>
                <p className="text-xs text-white/70">Use do celular ou computador</p>
              </div>
            </div>
          </div>


        </div>
      </div>
    </section>
  );
}
