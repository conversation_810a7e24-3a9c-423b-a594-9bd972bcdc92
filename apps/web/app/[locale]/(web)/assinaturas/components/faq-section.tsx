'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { Card, CardContent } from '@ui/components/card';

const faqs = [
  {
    question: 'Como funciona a assinatura?',
    answer: 'Com a assinatura ZapVida Sempre, você tem direito a 2 consultas médicas por mês por apenas R$ 49. As consultas não utilizadas não acumulam para o próximo mês.'
  },
  {
    question: 'Posso cancelar a qualquer momento?',
    answer: 'Sim! Você pode cancelar sua assinatura a qualquer momento sem multa ou taxa de cancelamento. O cancelamento é efetivo no final do período de cobrança atual.'
  },
  {
    question: 'As receitas são válidas?',
    answer: 'Sim, todas as receitas emitidas pelos nossos médicos são digitais e válidas em todo território nacional, conforme regulamentação do CFM (Conselho Federal de Medicina).'
  },
  {
    question: 'Que tipos de consulta posso fazer?',
    answer: 'Você pode fazer consultas médicas gerais, consultas de retorno, orientações sobre sintomas, renovação de receitas e muito mais. Para casos de emergência, recomendamos procurar um pronto-socorro.'
  },
  {
    question: 'Como é feito o atendimento?',
    answer: 'O atendimento é feito através de videochamada ou chat de texto, conforme sua preferência. Nossos médicos estão disponíveis 24 horas por dia, 7 dias por semana.'
  },
  {
    question: 'E se eu precisar de mais consultas?',
    answer: 'Caso você precise de mais consultas além das 2 incluídas na assinatura, pode agendar consultas avulsas ou usar o plantão médico com valores especiais para assinantes.'
  },
  {
    question: 'Meus dados estão seguros?',
    answer: 'Sim, utilizamos criptografia de ponta a ponta e seguimos todas as normas da LGPD (Lei Geral de Proteção de Dados). Seus dados médicos são completamente confidenciais.'
  },
  {
    question: 'Posso escolher o médico?',
    answer: 'Para consultas de plantão, o sistema direciona para o próximo médico disponível. Para consultas agendadas, você pode escolher entre os especialistas disponíveis.'
  }
];

export function FAQSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Perguntas Frequentes
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Tire suas dúvidas sobre nossos planos de assinatura
          </p>
        </div>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="border border-gray-200">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform flex-shrink-0 ${
                      openIndex === index ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>


      </div>
    </section>
  );
}
