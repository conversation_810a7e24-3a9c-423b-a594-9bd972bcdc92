/* eslint-disable @next/next/no-img-element */
'use client';

import React from 'react';
import { HelpCircle, Search, CheckCircle, BookOpen } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { CtaFooter } from '@marketing/home/<USER>/cta-footer';
import { CallToActionButton } from '@shared/components/CallToActionButton';
import { FaqSection } from '@marketing/home/<USER>/FaqSection';

export default function FAQPage() {
	return (
		<div className='min-h-screen bg-background'>
			{/* Hero Section */}
			<section className='relative flex h-[400px] items-center justify-center overflow-hidden'>
				<div className='absolute inset-0 z-0'>
					<img
						src='/images/young-happy-positive-woman.webp'
						alt='Perguntas Frequentes ZapVida'
						className='h-full w-full object-cover brightness-50'
					/>
				</div>
				<div className='relative z-10 mx-auto max-w-3xl px-4 text-center'>
					<Badge className='mb-4 bg-ring text-white hover:bg-ring/90'>
						Dúvidas e Respostas
					</Badge>
					<h1 className='mb-6 text-4xl font-bold text-white md:text-5xl'>
						Perguntas Frequentes
					</h1>
					<p className='mb-8 text-lg text-gray-200'>
						Encontre respostas rápidas para as dúvidas mais comuns sobre a
						ZapVida e nossa telemedicina
					</p>
				</div>
			</section>

			{/* Quick Help Cards */}
			<section className='py-12 bg-background'>
				<div className='container max-w-7xl'>
					<div className='grid gap-6 md:grid-cols-3'>
						<Card className='bg-primary/5 border-primary/10'>
							<CardHeader className='pb-3'>
								<div className='flex items-center gap-3'>
									<HelpCircle className='h-6 w-6 text-primary' />
									<CardTitle className='text-lg'>Precisa de ajuda?</CardTitle>
								</div>
							</CardHeader>
							<CardContent>
								<p className='text-sm text-muted-foreground mb-4'>
									Se você não encontrar a resposta que procura, nossa equipe
									está pronta para ajudar.
								</p>
								<Button className='w-full' variant='default' size='sm' asChild>
									<a
										href='https://wa.me/5547997708518'
										target='_blank'
										rel='noopener noreferrer'
										className='flex items-center gap-2'
									>
										<FaWhatsapp className='h-4 w-4' />
										<span>Falar com suporte</span>
									</a>
								</Button>
							</CardContent>
						</Card>

						<Card className='bg-primary/5 border-primary/10'>
							<CardHeader className='pb-3'>
								<div className='flex items-center gap-3'>
									<CheckCircle className='h-6 w-6 text-primary' />
									<CardTitle className='text-lg'>Primeira Consulta</CardTitle>
								</div>
							</CardHeader>
							<CardContent>
								<p className='text-sm text-muted-foreground mb-4'>
									Nunca usou telemedicina antes? Realize sua primeira consulta
									com um médico online.
								</p>
                                <Button className='w-full' variant='default' size='sm' asChild>
                                    <a href='/pay/plantao'>Consultar médico agora</a>
                                </Button>
							</CardContent>
						</Card>

						<Card className='bg-primary/5 border-primary/10'>
							<CardHeader className='pb-3'>
								<div className='flex items-center gap-3'>
									<BookOpen className='h-6 w-6 text-primary' />
									<CardTitle className='text-lg'>Sobre Telemedicina</CardTitle>
								</div>
							</CardHeader>
							<CardContent>
								<p className='text-sm text-muted-foreground mb-4'>
									Saiba mais sobre como funciona o atendimento médico online da
									ZapVida.
								</p>
								<Button className='w-full' variant='default' size='sm' asChild>
									<a href='/sobre'>Conhecer mais</a>
								</Button>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Main FAQ Section */}
			<FaqSection className='pt-4' />

			{/* Additional Info */}
			<section className='py-20 bg-background'>
				<div className='container max-w-7xl'>
					<div className='grid gap-10 md:grid-cols-2'>
						<div>
							<h2 className='text-2xl font-bold mb-6'>Ainda com dúvidas?</h2>
							<p className='text-muted-foreground mb-6'>
								Não encontrou o que procurava em nossas perguntas frequentes?
								Entre em contato conosco para obter ajuda personalizada. Nossa
								equipe está disponível 24 horas por dia, 7 dias por semana para
								responder suas dúvidas.
							</p>
							<div className='flex gap-4'>
								<Button asChild>
									<a href='https://wa.me/5547997708518'>Falar no WhatsApp</a>
								</Button>
								<Button variant='outline' asChild>
									<a href='/contato'>Ver mais contatos</a>
								</Button>
							</div>
						</div>
						<div>
							<h2 className='text-2xl font-bold mb-6'>Médicos online</h2>
							<p className='text-muted-foreground mb-6'>
								Na ZapVida, você tem acesso a médicos qualificados 24h por dia.
								Nossas consultas médicas online são acessíveis, rápidas e
								convenientes. Ideal para renovar receitas, obter orientações
								médicas ou resolver questões de saúde sem sair de casa.
							</p>
                            <CallToActionButton href='/pay/plantao'>
								AGENDE SUA CONSULTA AGORA
							</CallToActionButton>
						</div>
					</div>
				</div>
			</section>

			<CtaFooter />
		</div>
	);
}
