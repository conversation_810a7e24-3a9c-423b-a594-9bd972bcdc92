"use client";

import Image from "next/image";
import { useEffect } from "react";
import {
  BadgeCheck,
  Calendar,
  CheckCircle2,
  CreditCard,
  FileText,
  MessagesSquare,
  Stethoscope,
  Users,
} from "lucide-react";

import { Card, CardContent, CardHeader } from "@ui/components/card";
import { HeaderSection } from "@marketing/home/<USER>/home-sections/header-section";
import MaxWidthWrapper from "@marketing/shared/components/MaxWidthWrapper";

const benefits = [
  {
    title: "Conheça a Plataforma",
    description:
      "Veja na prática como funciona nossa solução de telemedicina.",
    icon: Stethoscope,
  },
  {
    title: "Tire Suas Dúvidas",
    description:
      "Converse com nossos especialistas sobre suas necessidades específicas.",
    icon: MessagesSquare,
  },
  {
    title: "Esclareça Aspectos Financeiros",
    description: "Entenda nosso modelo de negócio e possibilidades de ganhos.",
    icon: CreditCard,
  },
  {
    title: "Descubra Oportunidades",
    description: "Saiba como expandir sua atuação para novos pacientes.",
    icon: Users,
  },
  {
    title: "Entenda a Documentação",
    description:
      "Veja como funcionam nossas prescrições digitais e prontuários eletrônicos.",
    icon: FileText,
  },
  {
    title: "Conheça Nossa Agenda",
    description:
      "Explore nosso sistema de agendamento inteligente e gestão de horários.",
    icon: Calendar,
  },
];

export default function ScheduleDemoPage() {
  // Script to adjust iframe height
  useEffect(() => {
    const script = document.createElement('script');
    script.src = "https://api.gsh.digital/js/form_embed.js";
    script.type = "text/javascript";
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-secondary pb-16 pt-[4.5rem] md:pb-16 lg:pb-16 lg:pt-[4.75rem]">
        <div className="relative mx-auto flex max-w-7xl flex-col items-center gap-4 px-4 sm:px-6 lg:px-8">
          {/* Background Effects */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-secondary blur-3xl" />
          </div>

          {/* Content */}
          <div className="relative w-full max-w-3xl text-center pt-16">
            {/* Badge */}
            <div className="mx-auto w-fit rounded-full bg-white/10 px-4 py-1 text-sm text-white backdrop-blur-sm mb-6">
              Agende uma demonstração exclusiva
            </div>

            {/* Main Text */}
            <h1 className="font-urban text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl mb-6">
              Agende uma Demo Personalizada
            </h1>

            <p className="max-w-2xl mx-auto text-lg text-white/80 mb-8">
              Conheça em detalhes como nossa plataforma pode transformar sua prática médica e expandir seu alcance através da telemedicina.
            </p>


          </div>
        </div>
      </div>

      {/* Form Section */}
      <section className="py-12 bg-white">
        <MaxWidthWrapper className="max-w-4xl">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold">Escolha o melhor horário para sua demonstração</h2>
            <p className="text-muted-foreground mt-2">Nossa equipe está disponível para atendê-lo</p>
          </div>

          <div className="bg-white shadow-md rounded-xl overflow-hidden">
            <iframe
              src="https://api.gsh.digital/widget/booking/oeaUt0foM43TYEDIdxQJ"
              style={{width: '100%', border: 'none', overflow: 'hidden', minHeight: '600px'}}
              scrolling="no"
              id="Xj0lOye2negG1bR86v7R_1743184125745"
            />
          </div>
        </MaxWidthWrapper>
      </section>

      {/* Why Schedule a Demo Section */}
      <section className="py-20 bg-slate-50">
        <MaxWidthWrapper>
          <HeaderSection
            label="Por que agendar uma demo?"
            title="Descubra como nossa plataforma pode ajudar você"
            subtitle="Uma demonstração personalizada para entender todos os benefícios e recursos disponíveis para médicos"
          />

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {benefits.map((benefit, index) => (
              <Card
                key={index}
                className="relative border-2 transition-all hover:border-primary"
              >
                <CardHeader className="space-y-4 pb-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                    <benefit.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-heading text-xl font-semibold">
                    {benefit.title}
                  </h3>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </MaxWidthWrapper>
      </section>

      {/* Stats Section */}
      <section className="border-y bg-white py-20">
        <MaxWidthWrapper>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">+100</div>
              <p className="mt-2 text-muted-foreground">Médicos Ativos</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">+2.000</div>
              <p className="mt-2 text-muted-foreground">Consultas Realizadas</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">98%</div>
              <p className="mt-2 text-muted-foreground">
                Satisfação dos Médicos
              </p>
            </div>
          </div>
        </MaxWidthWrapper>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-slate-50">
        <MaxWidthWrapper>
          <div className="text-center">
            <h2 className="mb-12 text-3xl font-bold">Perguntas Frequentes</h2>

            <div className="mx-auto max-w-3xl space-y-6 text-left">
              <div>
                <h3 className="mb-2 text-xl font-semibold">Quanto tempo dura a demonstração?</h3>
                <p className="text-muted-foreground">Nossa demonstração personalizada dura aproximadamente 30 minutos, com tempo adicional para responder todas as suas dúvidas.</p>
              </div>

              <div>
                <h3 className="mb-2 text-xl font-semibold">Preciso ter conhecimento técnico?</h3>
                <p className="text-muted-foreground">Não é necessário. Nossa plataforma é intuitiva e guiaremos você em cada etapa durante a demonstração.</p>
              </div>

              <div>
                <h3 className="mb-2 text-xl font-semibold">Posso convidar outros colegas para a demonstração?</h3>
                <p className="text-muted-foreground">Sim! Encorajamos que convide outros médicos ou profissionais de sua equipe para conhecer a plataforma.</p>
              </div>

              <div>
                <h3 className="mb-2 text-xl font-semibold">Terei acesso à plataforma após a demonstração?</h3>
                <p className="text-muted-foreground">Sim, podemos oferecer um acesso temporário para que você teste a plataforma por conta própria após a demonstração.</p>
              </div>
            </div>
          </div>
        </MaxWidthWrapper>
      </section>
    </div>
  );
}
