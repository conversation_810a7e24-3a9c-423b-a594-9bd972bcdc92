'use client';

import Link from 'next/link';
import Image from 'next/image';
import {
	ArrowRight,
	CalendarX2Icon,
	Shield,
	Stethoscope,
	Timer,
	CheckCircle2,
} from 'lucide-react';

import { Button } from '@ui/components/button';
import { Card, CardContent } from '@ui/components/card';
import MaxWidthWrapper from '@marketing/shared/components/MaxWidthWrapper';
import { TestimonialsSection } from '@marketing/home/<USER>/home-sections/testimonials-section';
import { CallToActionButton } from '@shared/components/CallToActionButton';
import { ON_DUTY_DOCTORS } from 'api/constants/on-duty';

export default function FarmaciaPartnershipPage() {
	const conditions = [
		'Gripes e Resfriados',
		'Dores de Garganta',
		'Dores de Cabeça',
		'Febres',
		'Alergias',
		'Problemas Digestivos',
		'Infecções Urinárias',
		'Problemas de Pele',
		'Dores Musculares',
		'Ansiedade',
		'Insônia',
		'Pressão Alta',
	];

	const benefits = [
		{
			icon: <Timer className='h-6 w-6 text-primary' />,
			title: 'Atendimento garantido em até 20 minutos',
			description: 'Atendimento médico imediato, sem agendamento e sem espera.',
		},
		{
			icon: <Stethoscope className='h-6 w-6 text-primary' />,
			title: 'Consulta 100% segura',
			description: 'Atendimento sigiloso, seguindo as normas da LGPD.',
		},
		{
			icon: <Shield className='h-6 w-6 text-primary' />,
			title: 'Médicos especialistas',
			description: 'Atendimento com médicos verificados e experientes.',
		},
		{
			icon: <CalendarX2Icon className='h-6 w-6 text-primary' />,
			title: 'Retorno garantido',
			description:
				'Se precisar, consulte novamente dentro de 24h, sem custo adicional.',
		},
	];

	return (
		<div className='flex min-h-screen flex-col'>
			{/* Hero Section */}
			<div className='relative overflow-hidden bg-secondary pb-12 pt-24 md:pb-20 lg:pb-28 lg:pt-11'>
				<div className='relative mx-auto flex max-w-7xl flex-col items-center gap-4 px-4 sm:px-6 lg:px-8'>
					{/* Background Effects */}
					<div className='absolute inset-0 overflow-hidden'>
						<div className='absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-[#00A3E0]/20 blur-3xl' />
					</div>

					{/* Content Grid */}
					<div className='relative grid w-full gap-8 pt-0 lg:grid-cols-2 lg:gap-12 lg:pt-16'>
						{/* Left Column */}
						<div className='flex flex-col justify-center'>
							<div className='flex flex-col gap-6'>
								{/* Badge */}
								<div className='w-fit rounded-full bg-white/10 px-4 py-1 text-sm text-white backdrop-blur-sm'>
									Parceria exclusiva Farmacia
								</div>

								{/* Main Text */}
								<h1 className='font-urban text-4xl font-bold tracking-tight text-white sm:text-4xl md:text-5xl'>
									Seu médico disponível sem filas, sem espera. Em poucos
									cliques!
								</h1>

								<p className='max-w-xl text-lg text-white/80'>
									Atendimento médico confiável e <b>em poucos minutos</b>.
									Consulte-se agora sem sair de casa.
								</p>

								{/* Features List */}
								<div className='flex flex-col gap-3'>
									<div className='flex items-center gap-2 text-sm text-white/80'>
										<CheckCircle2 className='h-5 w-5 text-green-500' />
										<span>Mais de 30.000 Pacientes Atendidos</span>
									</div>
									<div className='flex items-center gap-2 text-sm text-white/80'>
										<CheckCircle2 className='h-5 w-5 text-green-500' />
										<span>
											Prescrição digital de documentos médicos pertinentes
										</span>
									</div>
									<div className='flex items-center gap-2 text-sm text-white/80'>
										<CheckCircle2 className='h-5 w-5 text-green-500' />
										<span>Atendimento garantido em até 20 minutos</span>
									</div>

									<div className='flex items-center gap-2 text-sm text-white/80'>
										<CheckCircle2 className='h-5 w-5 text-green-500' />
										<span>Consulta rápida, segura e sigilosa</span>
									</div>
								</div>

								{/* Price Card */}
								<div className='w-full rounded-xl bg-white/10 p-10 backdrop-blur-sm'>
									<p className='mb-1 text-sm font-medium text-white/80'>
										Consulta por apenas
									</p>
									<div className='mb-3 text-4xl font-bold text-white'>
										R$ 69
									</div>

									<CallToActionButton
										className='w-full md:w-auto'
										href={`/checkout/${ON_DUTY_DOCTORS.DEFAULT}?partner=farmacia`}
									>
										SEJA ATENDIDO AGORA!
									</CallToActionButton>
								</div>
							</div>
						</div>

						{/* Right Column - Images */}
						<div className='relative hidden max-h-[400px] w-full lg:block'>
							{/* Main Image */}
							<div className='relative aspect-[3/4] overflow-hidden rounded-xl'>
								<Image
									src='/images/doctor-medical-worker.webp'
									alt='Plantão Médico ZapVida'
									fill
									className='object-cover'
									priority
								/>
							</div>

							{/* Online Status */}
							<div className='absolute right-8 top-6 flex items-center gap-2 rounded-full bg-white px-4 py-2 shadow-lg'>
								<div className='h-2.5 w-2.5 animate-pulse rounded-full bg-green-500' />
								<span className='text-sm font-medium'>Médicos Online</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Benefits Section */}
			<section className='py-16 '>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='text-3xl font-bold'>Benefícios do Plantão Médico</h2>
						<p className='mt-4 text-muted-foreground'>
							Em parceria com Farmacia, oferecemos atendimento médico confiável
							por apenas R$69.
						</p>
					</div>
					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
						{benefits.map((benefit, index) => (
							<Card
								key={index}
								className='border-2 border-muted bg-background/50'
							>
								<CardContent className='flex flex-col items-center p-6 text-center'>
									<div className='mb-4'>{benefit.icon}</div>
									<h3 className='mb-2 font-semibold'>{benefit.title}</h3>
									<p className='text-sm text-muted-foreground'>
										{benefit.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* Steps Section */}
			<section className='py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Como funciona?</h2>
						<p className='text-muted-foreground'>
							Processo simples e rápido para seu atendimento
						</p>
					</div>
					<div className='grid gap-8 md:grid-cols-3'>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								1
							</div>
							<h3 className='mb-2 font-semibold'>Escolha Plantão ou Agende</h3>
							<p className='text-sm text-muted-foreground'>
								Atendimento imediato ou consulta agendada – você escolhe o que
								precisa!
							</p>
						</div>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								2
							</div>
							<h3 className='mb-2 font-semibold'>Confirmação instantânea</h3>
							<p className='text-sm text-muted-foreground'>
								Após o pagamento, você e o médico recebem a confirmação no
								WhatsApp e e-mail. 🔗 Vocês são automaticamente conectados para
								iniciar a consulta.
							</p>
						</div>
						<div className='text-center'>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white'>
								3
							</div>
							<h3 className='mb-2 font-semibold'>Início da consulta</h3>
							<p className='text-sm text-muted-foreground'>
								Seu médico já está pronto para atender! 🩺 Consulta por
								chat/áudio, ligação ou vídeo. O médico ajudará no diagnóstico e
								conduta. Após, encaminha os documentos médicos necessários
								(prescrição, atestado, pedido de exame).
							</p>
						</div>
					</div>
				</MaxWidthWrapper>

				<TestimonialsSection />
			</section>

			{/* Conditions Section */}
			<section className='bg-muted/50 py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Problemas que Podemos Resolver
						</h2>
						<p className='text-muted-foreground'>
							Problemas de saúde? Consulte um médico agora!
						</p>
					</div>
					<div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4'>
						{conditions.map((condition, index) => (
							<div
								key={index}
								className='flex items-center rounded-lg border bg-background p-4'
							>
								<div className='mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-secondary'>
									<Stethoscope className='h-4 w-4 text-white' />
								</div>
								<span className='text-sm'>{condition}</span>
							</div>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* CTA Section */}
			<section className='bg-primary py-16 text-primary-foreground'>
				<MaxWidthWrapper className='text-center'>
					<h2 className='mb-6 text-3xl font-bold'>
						Atendimento médico sem complicações,{' '}
						<span className='text-ring md:block'>quando e onde precisar.</span>
					</h2>
					<p className='mb-8 text-lg'>
						Consulta por apenas R$69 - Atendimento garantido em até 20 minutos
					</p>
					<div className='mt-8 text-center'>
						<Link
							href={`/checkout/${ON_DUTY_DOCTORS.DEFAULT}?partner=farmacia`}
						>
							<CallToActionButton className='px-8' icon={false}>
								Inicie sua consulta agora{' '}
								<ArrowRight className='ml-2 h-4 w-4' />
							</CallToActionButton>
						</Link>
					</div>
				</MaxWidthWrapper>
			</section>
		</div>
	);
}
