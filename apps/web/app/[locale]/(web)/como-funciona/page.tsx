/* eslint-disable @next/next/no-img-element */
"use client";

import React from "react";
import Link from "next/link";
import {
  Calendar,
  ChevronRight,
  CreditCard,
  FileText,
  HeartPulse,
  MessageCircle,
  MessageCircleCode,
  PhoneCall,
  ShieldCheck,
  Stethoscope,
  Video,
} from "lucide-react";
import { FaPhone } from "react-icons/fa";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@ui/components/accordion";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";
import { CtaFooter } from "@marketing/home/<USER>/cta-footer";
import { CallToActionButton } from "@shared/components/CallToActionButton";


export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative flex h-[600px] items-center justify-center overflow-hidden">
        <div className="absolute inset-0  z-0">
          <img
            src="/images/como-funciona-telemedicina.jpeg"
            alt="Telemedicina"
            className="h-full w-full object-cover brightness-50"
          />
        </div>
        <div className="relative z-10 mx-auto max-w-3xl px-4 text-center">
          <Badge className="mb-4 bg-ring text-white hover:bg-ring/90">
            Telemedicina Simplificada
          </Badge>
          <h1 className="mb-6 text-4xl font-bold text-white md:text-5xl">
            Consultas médicas online em 3 passos simples
          </h1>
          <p className="mb-8 text-lg text-gray-200">
            Cuidar da sua saúde nunca foi tão fácil e seguro. Conecte-se com
            médicos especialistas sem sair de casa.
          </p>

          <div className="flex justify-center">
          						<CallToActionButton href="/plantao">
							SEJA ATENDIDO AGORA!
						</CallToActionButton>
          </div>
        </div>
      </section>

      {/* Steps Section */}
      <section className="bg-background py-20">
        <div className="mx-auto max-w-7xl px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold">Como funciona</h2>
            <p className="mx-auto max-w-2xl text-muted-foreground">
              Nossa plataforma foi desenvolvida para tornar o atendimento médico
              online simples, seguro e eficiente para todos.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            {/* Step 1 */}
            <Card className="relative border-2 border-primary/10 transition-all hover:border-primary/20">
              <CardHeader>
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>1. Escolha e Agende</CardTitle>
                <CardDescription>
                  Selecione o especialista ideal e escolha o melhor horário para
                  você
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Busque por especialidade
                  </li>
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Compare perfis
                  </li>
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Agende sua consulta
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Step 2 */}
            <Card className="relative border-2 border-primary/10 transition-all hover:border-primary/20">
              <CardHeader>
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>2. Confirme e Pague</CardTitle>
                <CardDescription>
                  Processo de pagamento seguro e diversos métodos disponíveis
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Ecolha como quer pagar
                  </li>
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Cartão de crédito ou PIX
                  </li>
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Receba o link da consulta
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Step 3 */}
            <Card className="relative border-2 border-primary/10 transition-all hover:border-primary/20">
              <CardHeader>
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                  <MessageCircleCode className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>3. Realize sua Consulta</CardTitle>
                <CardDescription>
                  Atendimento online de qualidade com toda segurança
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Atendimento 100% online
                  </li>
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Receba prescrições digitais
                  </li>{" "}
                  <li className="flex items-center">
                    <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                    Médicos Verificados
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-[#0eadbb8b]/10 py-20">
        <div className="mx-auto max-w-7xl px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold">
              Por que escolher nossa plataforma?
            </h2>
            <p className="mx-auto max-w-2xl text-muted-foreground">
              Desenvolvida com foco em qualidade, segurança e praticidade para
              médicos e pacientes
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {/* Benefit 1 */}
            <Card className="bg-card">
              <CardHeader>
                <ShieldCheck className="mb-4 h-8 w-8 text-primary" />
                <CardTitle className="text-lg">Segurança Total</CardTitle>
                <CardDescription>
                  Plataforma certificada com criptografia de ponta a ponta
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Benefit 2 */}
            <Card className="bg-card">
              <CardHeader>
                <Stethoscope className="mb-4 h-8 w-8 text-primary" />
                <CardTitle className="text-lg">Médicos Verificados</CardTitle>
                <CardDescription>
                  Profissionais com registro ativo e validado
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Benefit 3 */}
            <Card className="bg-card">
              <CardHeader>
                <FileText className="mb-4 h-8 w-8 text-primary" />
                <CardTitle className="text-lg">Prescrição Digital</CardTitle>
                <CardDescription>
                  Receitas e atestados com validade legal
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Benefit 4 */}
            <Card className="bg-card">
              <CardHeader>
                <MessageCircle className="mb-4 h-8 w-8 text-primary" />
                <CardTitle className="text-lg">Suporte 24/7</CardTitle>
                <CardDescription>
                  Assistência eficiente e humanizada
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-background py-20">
        <div className="mx-auto max-w-6xl px-4">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold">Dúvidas Frequentes</h2>
            <p className="text-muted-foreground">Faq sobre nossa plataforma</p>
          </div>

          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">
                  Encontre respostas para as principais dúvidas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>
                      O que é Telemedicina e como funciona?
                    </AccordionTrigger>
                    <AccordionContent>
                      A Telemedicina é o uso de tecnologias de comunicação para
                      prestar serviços de saúde à distância. No ZapVida,
                      oferecemos consultas online com médicos qualificados,
                      através de chat, em consultas do tipo assíncronas, ou
                      seja, conversas com tempo e resposta de chat. Você pode
                      realizar sua consulta através de nosso site.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-2">
                    <AccordionTrigger>
                      Quais são os benefícios da Telemedicina?
                    </AccordionTrigger>
                    <AccordionContent>
                      <ul>
                        <li>
                          <strong>Praticidade:</strong> Você consulta um médico
                          de onde estiver, sem precisar se deslocar ou enfrentar
                          filas.
                        </li>
                        <li>
                          <strong>Agilidade:</strong> Realizamos sua consulta
                          rapidamente e você recebe o atendimento no conforto de
                          casa.
                        </li>
                        <li>
                          <strong>Acessibilidade:</strong> Consultas com preços
                          acessíveis e o plano ZapVida Sempre com 2 consultas por mês por R$ 49 que cabe no seu
                          bolso.
                        </li>
                        <li>
                          <strong>Segurança:</strong> Nossas plataformas são
                          seguras e protegem seus dados de acordo com a LGPD.
                        </li>
                        <li>
                          <strong>Comodidade:</strong> Você recebe a receita
                          médica eletronicamente e pode comprar os medicamentos
                          online com desconto.
                        </li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-3">
                    <AccordionTrigger>
                      Como funciona a consulta assíncrona por chat e áudio na
                      telemedicina?
                    </AccordionTrigger>
                    <AccordionContent>
                      A consulta assíncrona por chat e áudio na telemedicina
                      oferece uma maneira flexível e conveniente de se conectar
                      com um profissional de saúde, utilizando ferramentas de
                      comunicação textuais e de voz para realizar consultas
                      remotas.
                      <br />
                      <br />
                      <strong>Como funciona:</strong>
                      <ul>
                        <li>
                          Comunicação: Mensagens de texto e/ou chamadas de áudio
                          para descrever sintomas, fazer perguntas e receber
                          orientações.
                        </li>
                        <li>
                          Recursos adicionais: Compartilhamento de fotos, vídeos
                          e resultados de exames.
                        </li>
                        <li>
                          Prescrições e atestados: Receitas médicas e atestados
                          eletrônicos.
                        </li>
                      </ul>
                      <br />
                      <strong>Vantagens:</strong>
                      <ul>
                        <li>
                          Acessibilidade: Facilita o acesso à saúde para pessoas
                          em áreas remotas.
                        </li>
                        <li>
                          Praticidade: Sem deslocamentos ou longas esperas.
                        </li>
                        <li>
                          Eficiência: Otimiza o tempo de médicos e pacientes.
                        </li>
                        <li>
                          Redução de custos: Evita gastos com transporte e
                          deslocamento.
                        </li>
                        <li>
                          Segurança: Proteção da privacidade dos pacientes.
                        </li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-4">
                    <AccordionTrigger>
                      Como funciona a consulta por chat?
                    </AccordionTrigger>
                    <AccordionContent>
                      Digite suas mensagens para o médico e ele responderá em
                      tempo real. Você também pode enviar fotos e exames pelo
                      chat. Recebe sua prescrição médica via chat, além de
                      documentos pertinentes, tais como: atestado médico,
                      laudos, encaminhamentos e pedidos de check-up médico.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-5">
                    <AccordionTrigger>
                      Como recebo a receita médica?
                    </AccordionTrigger>
                    <AccordionContent>
                      Após a consulta, o médico enviará a receita médica
                      eletronicamente para seu chat, e-mail ou app. Você poderá
                      imprimir a receita ou apresentá-la em formato digital na
                      farmácia. Todas as farmácias aceitam a receita médica
                      eletrônica diretamente no app.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-6">
                    <AccordionTrigger>
                      Como funciona a emissão de nota fiscal?
                    </AccordionTrigger>
                    <AccordionContent>
                      A nota fiscal da sua consulta é emitida automaticamente e
                      enviada para o seu e-mail. Você também pode acessar a nota
                      fiscal no seu perfil.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-7">
                    <AccordionTrigger>Segurança e LGPD</AccordionTrigger>
                    <AccordionContent>
                      No 1Doc1Min, levamos a sério a segurança dos seus dados.
                      Todas as nossas plataformas são seguras e protegem seus
                      dados de acordo com a Lei Geral de Proteção de Dados
                      Pessoais (LGPD).
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      <CtaFooter />
    </div>
  );
}
