/* eslint-disable @next/next/no-img-element */
'use client';

import React from 'react';
import {
	MapPin,
	Mail,
	Phone,
	MessageCircle,
	ChevronRight,
	FileText,
	Clock,
	Calendar,
} from 'lucide-react';
import { FaWhatsapp, FaInstagram, FaFacebookMessenger } from 'react-icons/fa';

import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	CardFooter,
} from '@ui/components/card';
import { Separator } from '@ui/components/separator';
import { CtaFooter } from '@marketing/home/<USER>/cta-footer';
import { CallToActionButton } from '@shared/components/CallToActionButton';

export default function ContatoPage() {
	return (
		<div className='min-h-screen bg-background'>
			{/* Hero Section */}
			<section className='relative flex h-[500px] items-center justify-center overflow-hidden'>
				<div className='absolute inset-0 z-0'>
					<img
						src='/images/doctor-medical-worker.webp'
						alt='Contato ZapVida'
						className='h-full w-full object-cover brightness-50'
					/>
				</div>
				<div className='relative z-10 mx-auto max-w-3xl px-4 text-center'>
					<Badge className='mb-4 bg-ring text-white hover:bg-ring/90'>
						Estamos aqui para ajudar
					</Badge>
					<h1 className='mb-6 text-4xl font-bold text-white md:text-5xl'>
						Entre em contato com a ZapVida
					</h1>
					<p className='mb-8 text-lg text-gray-200'>
						Tire suas dúvidas, envie sugestões ou solicite suporte a qualquer
						momento
					</p>

					<div className='flex justify-center'>
						<CallToActionButton href='https://wa.me/5547997708518'>
							FALE COM UM MÉDICO AGORA
						</CallToActionButton>
					</div>
				</div>
			</section>

			{/* Contact Info Section */}
			<section className='bg-background py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Como podemos ajudar?</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Nossa equipe está pronta para atender você e responder todas as
							suas dúvidas sobre telemedicina e nossos serviços
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-2'>
						{/* Contact Information */}
						<Card className='h-full'>
							<CardHeader>
								<CardTitle>Informações de Contato</CardTitle>
								<CardDescription>
									Fale conosco através dos nossos canais de atendimento
								</CardDescription>
							</CardHeader>
							<CardContent className='space-y-6'>
								<div className='flex items-start'>
									<div className='mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
										<Mail className='h-5 w-5 text-primary' />
									</div>
									<div>
										<h3 className='text-sm font-medium'>Email</h3>
										<p className='text-sm text-muted-foreground'>
											<EMAIL>
										</p>
									</div>
								</div>

								<div className='flex items-start'>
									<div className='mr-4 flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
										<FaWhatsapp className='h-5 w-5 text-primary' />
									</div>
									<div>
										<h3 className='text-sm font-medium'>WhatsApp</h3>
										<p className='text-sm text-muted-foreground'>
											(47) 99770-8518
										</p>
										<p className='mt-1'>
											<Button size='sm' variant='outline' asChild>
												<a
													href='https://wa.me/5547997708518'
													target='_blank'
													rel='noopener noreferrer'
												>
													Iniciar conversa
												</a>
											</Button>
										</p>
									</div>
								</div>

								<Separator />

								<div>
									<h3 className='mb-3 text-sm font-medium'>Redes Sociais</h3>
									<div className='flex flex-wrap gap-4'>
										<Button
											variant='outline'
											size='sm'
											className='flex items-center gap-2'
											asChild
										>
											<a
												href='https://wa.me/5547997708518'
												target='_blank'
												rel='noopener noreferrer'
											>
												<FaWhatsapp className='h-5 w-5 text-primary' />
												<span>WhatsApp</span>
											</a>
										</Button>
										<Button
											variant='outline'
											size='sm'
											className='flex items-center gap-2'
											asChild
										>
											<a
												href='https://instagram.com/zapvida'
												target='_blank'
												rel='noopener noreferrer'
											>
												<FaInstagram className='h-5 w-5 text-primary' />
												<span>Instagram</span>
											</a>
										</Button>
										<Button
											variant='outline'
											size='sm'
											className='flex items-center gap-2'
											asChild
										>
											<a
												href='https://m.me/zapvida'
												target='_blank'
												rel='noopener noreferrer'
											>
												<FaFacebookMessenger className='h-5 w-5 text-primary' />
												<span>Facebook</span>
											</a>
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className='h-full'>
							<CardHeader>
								<CardTitle>Horário de Atendimento</CardTitle>
								<CardDescription>
									Nossa equipe médica está disponível 24 horas por dia
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='space-y-3'>
									<div className='flex justify-between border-b pb-2'>
										<span>Segunda - Domingo</span>
										<span className='font-medium'>24 horas</span>
									</div>
									<div className='flex justify-between border-b pb-2'>
										<span>Feriados</span>
										<span className='font-medium'>24 horas</span>
									</div>
								</div>

								<div className='mt-8 p-4 bg-primary/5 rounded-lg'>
									<h3 className='text-sm font-medium mb-2 flex items-center'>
										<Clock className='mr-2 h-4 w-4 text-primary' /> Atendimento
										imediato
									</h3>
									<p className='text-sm text-muted-foreground mb-4'>
										Temos médicos de plantão prontos para atender você a
										qualquer momento, através do WhatsApp ou do nosso site.
									</p>
									<Button className='w-full' asChild>
										<a
											href='https://wa.me/5547997708518'
											target='_blank'
											rel='noopener noreferrer'
										>
											Falar com um médico agora
										</a>
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Services Section */}
			<section className='bg-[#0eadbb8b]/10 py-20'>
				<div className='mx-auto max-w-7xl px-4'>
					<div className='mb-16 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>Nossos Serviços</h2>
						<p className='mx-auto max-w-2xl text-muted-foreground'>
							Conheça os principais serviços disponíveis na ZapVida
						</p>
					</div>

					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-3'>
						<Card className='bg-card'>
							<CardHeader>
								<Calendar className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Consultas 24/7</CardTitle>
								<CardDescription>
									Atendimento médico disponível a qualquer hora do dia
								</CardDescription>
							</CardHeader>
							<CardFooter>
								<Button variant='link' className='px-0' asChild>
									<a href='https://wa.me/5547997708518'>
										Agendar agora <ChevronRight className='ml-1 h-4 w-4' />
									</a>
								</Button>
							</CardFooter>
						</Card>

						<Card className='bg-card'>
							<CardHeader>
								<MessageCircle className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Suporte via WhatsApp</CardTitle>
								<CardDescription>
									Atendimento rápido e direto pelo seu aplicativo preferido
								</CardDescription>
							</CardHeader>
							<CardFooter>
								<Button variant='link' className='px-0' asChild>
									<a href='https://wa.me/5547997708518'>
										Falar agora <ChevronRight className='ml-1 h-4 w-4' />
									</a>
								</Button>
							</CardFooter>
						</Card>

						<Card className='bg-card'>
							<CardHeader>
								<FileText className='mb-4 h-8 w-8 text-primary' />
								<CardTitle className='text-lg'>Prescrições Digitais</CardTitle>
								<CardDescription>
									Receba suas receitas diretamente no seu celular
								</CardDescription>
							</CardHeader>
							<CardFooter>
								<Button variant='link' className='px-0' asChild>
									<a href='https://wa.me/5547997708518'>
										Saiba mais <ChevronRight className='ml-1 h-4 w-4' />
									</a>
								</Button>
							</CardFooter>
						</Card>
					</div>
				</div>
			</section>

			<CtaFooter />
		</div>
	);
}
