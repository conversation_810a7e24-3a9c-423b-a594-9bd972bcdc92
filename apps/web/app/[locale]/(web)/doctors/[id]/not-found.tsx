"use client";

import { useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@ui/components/button";
import { useParams } from "next/navigation";

export default function DoctorNotFound() {
  const params = useParams();
  const locale = params.locale || 'pt';

  useEffect(() => {
    console.error("[NOT_FOUND] DoctorNotFound component renderizado");
    console.error("[NOT_FOUND] ID que causou o 404:", params.id);
  }, [params.id]);

  return (
    <div className="container mx-auto flex flex-col items-center justify-center py-20">
      <h1 className="mb-6 text-4xl font-bold">Médico não encontrado</h1>
      <p className="mb-8 text-center text-lg text-muted-foreground">
        O médico que você está procurando não foi encontrado.<br />
        Pode ser que o link esteja incorreto ou que o médico não esteja mais disponível.
      </p>
      <Button asChild>
        <Link href={`/${locale}/doctors`}>
          Ver todos os médicos
        </Link>
      </Button>
    </div>
  );
}
