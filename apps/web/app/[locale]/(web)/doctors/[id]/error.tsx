"use client";

import { useEffect } from "react";
import Link from "next/link";
import { Button } from "@ui/components/button";
import { useParams } from "next/navigation";

export default function DoctorError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const params = useParams();
  const locale = params.locale || 'pt';

  useEffect(() => {
    console.error("[ERROR] Doctor error:", error);
    console.error("[ERROR] ID que causou o erro:", params.id);
  }, [error, params.id]);

  return (
    <div className="container mx-auto flex flex-col items-center justify-center py-20">
      <h1 className="mb-6 text-4xl font-bold">Algo deu errado</h1>
      <p className="mb-8 text-center text-lg text-muted-foreground">
        Ocorreu um erro ao carregar as informações do médico.<br />
        Por favor, tente novamente mais tarde.
      </p>
      <div className="flex gap-4">
        <Button variant="outline" onClick={() => reset()}>
          Tentar novamente
        </Button>
        <Button asChild>
          <Link href={`/${locale}/doctors`}>
            Ver todos os médicos
          </Link>
        </Button>
      </div>
    </div>
  );
}
