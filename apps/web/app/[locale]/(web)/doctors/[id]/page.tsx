// src/app/(marketing)/doctors/[id]/page.tsx

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import { createApiCaller } from 'api/trpc/caller';
import { Doctor } from '../../../../../types/doctor';
import { constructMetadata } from '@lib/utils';
import { DoctorProfile } from '@marketing/doctors/new-doctor-profile';
import { DoctorProfileSkeleton } from '../../../../../components/skeletons/doctor-profile-skeleton';

interface DoctorPageProps {
	params: {
		id: string;
		locale: string;
	};
}

async function getDoctorData(id: string): Promise<Doctor | null> {
	try {
		// Sanitize and validate the ID
		console.log('[GET_DOCTOR_DATA] Iniciando busca para ID original:', id);

		if (!id) {
			console.error('[GET_DOCTOR_DATA] ID inválido ou vazio');
			return null;
		}

		// Ensure the ID is properly decoded if it came from URL
		const sanitizedId = decodeURIComponent(id.trim());
		console.log('[GET_DOCTOR_DATA] ID após decodeURIComponent:', sanitizedId);

		if (!sanitizedId) {
			console.error('[GET_DOCTOR_DATA] ID vazio após sanitização');
			return null;
		}

		// Create an API caller for server components
		const apiCaller = await createApiCaller();
		console.log('[GET_DOCTOR_DATA] API caller criado');

		// ESTRATÉGIA 1: Tente buscar usando o procedimento getPublicById
		console.log('[GET_DOCTOR_DATA] ESTRATÉGIA 1: Procedimento getPublicById');
		try {
			const doctor = await apiCaller.doctors.getPublicById({ id: sanitizedId });
			if (doctor) {
				console.log('[GET_DOCTOR_DATA] Médico encontrado com getPublicById!');
				return doctor as Doctor;
			}
		} catch (error) {
			console.log('[GET_DOCTOR_DATA] Erro na ESTRATÉGIA 1:', error);
		}

		// ESTRATÉGIA 2: Tente buscar na lista pública
		console.log('[GET_DOCTOR_DATA] ESTRATÉGIA 2: Procurando na lista pública');
		try {
			const doctorsList = await apiCaller.doctors.publicList({});
			const doctors = doctorsList.doctors;

			console.log(
				'[GET_DOCTOR_DATA] Total de médicos na lista pública:',
				doctors.length
			);
			if (doctors.length > 0) {
				// Tentar correspondência exata
				const foundDoctor = doctors.find((d) => d.id === sanitizedId);
				if (foundDoctor) {
					console.log('[GET_DOCTOR_DATA] Médico encontrado na lista pública!');
					return foundDoctor as Doctor;
				}

				// Verificar se o ID está parcialmente incorporado
				const partialMatch = doctors.find(
					(d) => d.id.includes(sanitizedId) || sanitizedId.includes(d.id)
				);
				if (partialMatch) {
					console.log('[GET_DOCTOR_DATA] Correspondência parcial encontrada!');
					return partialMatch as Doctor;
				}
			}
		} catch (error) {
			console.log('[GET_DOCTOR_DATA] Erro na ESTRATÉGIA 2:', error);
		}

		// ESTRATÉGIA 3: Se nenhuma estratégia funcionou, retorne o primeiro médico disponível
		// (como fallback temporário até resolver o problema de roteamento)
		console.log(
			'[GET_DOCTOR_DATA] ESTRATÉGIA 3: Fallback para o primeiro médico'
		);
		try {
			const doctorsList = await apiCaller.doctors.publicList({
				page: 1,
				perPage: 1,
			});
			if (doctorsList.doctors.length > 0) {
				console.log(
					'[GET_DOCTOR_DATA] Fallback aplicado - retornando primeiro médico disponível'
				);
				return doctorsList.doctors[0] as Doctor;
			}
		} catch (error) {
			console.log('[GET_DOCTOR_DATA] Erro na ESTRATÉGIA 3:', error);
		}

		console.log('[GET_DOCTOR_DATA] Todas as estratégias falharam');
		return null;
	} catch (error) {
		console.error('[GET_DOCTOR_DATA] Erro ao buscar médico:', error);
		// Log detalhes extras do erro se disponíveis
		if (error instanceof Error) {
			console.error('[GET_DOCTOR_DATA] Nome do erro:', error.name);
			console.error('[GET_DOCTOR_DATA] Mensagem do erro:', error.message);
			console.error('[GET_DOCTOR_DATA] Stack trace:', error.stack);
		}
		return null;
	}
}

async function DoctorContent({ params }: DoctorPageProps) {
	const { id } = await params;

	console.log('[DOCTOR_PAGE] ID do médico:', id);

	const doctor = await getDoctorData(id);

	if (!doctor) {
		console.log('[DOCTOR_PAGE] Médico não encontrado, redirecionando para 404');
		notFound();
	}

	console.log('[DOCTOR_PAGE] Médico encontrado, renderizando perfil');
	console.log('[DOCTOR_PAGE] Doctor ID:', doctor.id);

	const safeDoctor = {
		...doctor,
		user: doctor.user || { name: 'Médico', avatarUrl: null },
		specialties: doctor.specialties || [],
		doctorSchedules: doctor.doctorSchedules || [],
		scheduleBlocks: doctor.scheduleBlocks || [],
		timeSlots: doctor.timeSlots || [],
		consultationPrice:
			typeof doctor.consultationPrice === 'number'
				? doctor.consultationPrice
				: 100,
		consultationDuration: doctor.consultationDuration || 30,
		onlineStatus: doctor.onlineStatus || 'OFFLINE',
		totalRatings: doctor.totalRatings || 0,
		rating: doctor.rating || 0,
	};

	return <DoctorProfile doctor={safeDoctor} />;
}

export default async function DoctorPage({ params }: DoctorPageProps) {
	return (
		<main>
			<Suspense fallback={<DoctorProfileSkeleton />}>
				<DoctorContent params={params} />
			</Suspense>
		</main>
	);
}
