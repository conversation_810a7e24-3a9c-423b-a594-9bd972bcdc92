// src/app/(marketing)/doctors/page.tsx
import { Suspense } from 'react';
import { Metadata } from 'next';
import { Skeleton } from '@ui/components/skeleton';
import { getTranslations } from 'next-intl/server';
import { fetchDoctors } from '../../../../actions/doctors/fetch-doctors';
import { constructMetadata } from '@lib/utils';
import DoctorsListPage from '@marketing/doctors/doctors-list';
import { DoctorsListSkeleton } from '../../../../components/skeletons/doctors-list-skeleton';

interface DoctorsPageProps {
	searchParams: {
		query?: string;
		specialty?: string;
		online?: string;
		page?: string;
	};
}

export async function generateMetadata(): Promise<Metadata> {
	return constructMetadata({
		title: 'Médicos Online WhatsApp | Consulta Online | ZapVida',
		description:
			'Consulte médicos online via WhatsApp 24h. Telemedicina com especialistas qualificados. Assinantes têm descontos em consultas. Prescrição digital incluída.',
	});
}

async function DoctorsContent({ searchParams }: DoctorsPageProps) {
	const { query, specialty, online, page } = await searchParams;

	try {
		const response = await fetchDoctors({
			query: query,
			specialtyId: specialty,
			onlineOnly: online === 'true',
			page: page ? parseInt(page) : 1,
		});

		if (response.status === 'error' || !response.data) {
			throw new Error(response.message || 'Erro ao carregar médicos');
		}

		const { doctors, specialties, total, onlineCount } = response.data;

		return (
			<DoctorsListPage
				initialDoctors={doctors}
				specialties={specialties}
				total={total}
				onlineCount={onlineCount}
			/>
		);
	} catch (error) {
		console.error('Error loading doctors page:', error);
		return <div>Erro ao carregar médicos</div>;
	}
}

export default async function DoctorsPage({ searchParams }: DoctorsPageProps) {
	return (
		<div className="min-h-screen bg-gray-50">
			<Suspense fallback={<DoctorsListSkeleton />}>
				<DoctorsContent searchParams={searchParams} />
			</Suspense>
		</div>
	);
}



// Página de erro
export function ErrorBoundary() {
	return (
		<div className='flex min-h-[400px] flex-col items-center justify-center'>
			<h2 className='text-xl font-bold'>Algo deu errado</h2>
			<p className='text-muted-foreground'>
				Não foi possível carregar os médicos. Por favor, tente novamente mais
				tarde.
			</p>
		</div>
	);
}
