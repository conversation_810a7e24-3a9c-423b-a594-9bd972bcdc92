"use client";

import Image from "next/image";
import Link from "next/link";
import {
  ArrowR<PERSON>,
  BadgeCheck,
  Calendar,
  CheckCircle2,
  CreditCard,
  FileText,
  MessagesSquare,
  Stethoscope,
  Users,
} from "lucide-react";

import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import MaxWidthWrapper from "@marketing/shared/components/MaxWidthWrapper";
import { HeaderSection } from "@marketing/home/<USER>/home-sections/header-section";
// import { HeaderSection } from "@/components/shared/header-section";

const features = [
  {
    title: "Atenda de Qualquer Lugar",
    description:
      "Faça consultas online com flexibilidade de horário e localização.",
    icon: Stethoscope,
  },
  {
    title: "Gestão Simplificada",
    description:
      "Gerencie sua agenda, prontuários e prescrições em uma única plataforma.",
    icon: Calendar,
  },
  {
    title: "Recebimento Garantido",
    description: "Receba seus pagamentos de forma segura e automatizada.",
    icon: CreditCard,
  },
  {
    title: "Aumente sua Base",
    description: "Alcance mais pacientes e expanda sua prática médica.",
    icon: Users,
  },
  {
    title: "Prescrição Digital",
    description:
      "Emita prescrições digitais com validade legal em todo o Brasil.",
    icon: FileText,
  },
  {
    title: "Suporte Prioritário",
    description:
      "Conte com nossa equipe para auxiliar em qualquer necessidade.",
    icon: MessagesSquare,
  },
];

export default function DoctorLandingPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-primary pb-16 pt-[4.5rem] md:pb-20 lg:pb-28 lg:pt-[4.75rem]">
        <div className="relative mx-auto flex max-w-7xl flex-col items-center gap-4 px-4 sm:px-6 lg:px-8">
          {/* Background Effects */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-[#00A3E0]/20 blur-3xl" />
          </div>

          {/* Content Grid */}
          <div className="relative grid w-full gap-8 pt-16 lg:grid-cols-2 lg:gap-12">
            {/* Left Column */}
            <div className="flex flex-col justify-center">
              <div className="flex flex-col gap-6">
                {/* Badge */}
                <div className="w-fit rounded-full bg-white/10 px-4 py-1 text-sm text-white backdrop-blur-sm">
                  Plataforma líder em telemedicina
                </div>

                {/* Main Text */}
                <h1 className="font-urban text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl">
                  Transforme sua Prática Médica com Telemedicina
                </h1>

                <p className="max-w-xl text-lg text-white/80">
                  Junte-se a milhares de médicos que já estão expandindo seus
                  horizontes profissionais através da telemedicina.
                </p>

                {/* Features List */}
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-2 text-sm text-white/80">
                    <CheckCircle2 className="h-5 w-5 text-white" />
                    <span>Plataforma aprovada pelo CFM</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-white/80">
                    <CheckCircle2 className="h-5 w-5 text-white" />
                    <span>Mais de 100 médicos ativos</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-white/80">
                    <CheckCircle2 className="h-5 w-5 text-white" />
                    <span>Mais de 2.000 consultas realizadas</span>
                  </div>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                  <Link href="/doctors/register">
                    <Button
                      size="lg"
                      className="w-full bg-white text-primary hover:bg-white/90 sm:w-auto"
                    >
                      Cadastre-se Gratuitamente
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full border-white text-white sm:w-auto"
                  >
                    Agende uma Demo
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Column - Images */}
            <div className="relative hidden lg:block">
              {/* Main Image */}
              <div className="relative aspect-[4/3] overflow-hidden rounded-xl">
                <Image
                  src="/images/telemedicina-brasil.png"
                  alt="Médico realizando teleconsulta"
                  fill
                  className="object-cover"
                  priority
                />
              </div>

              {/* Stats Card */}
              <div className="absolute -bottom-6 -right-6 rounded-lg border-4 border-white bg-white p-6 shadow-lg">
                <div className="flex flex-col gap-2">
                  <div className="text-3xl font-bold text-primary">98%</div>
                  <div className="text-sm text-muted-foreground">
                    dos médicos satisfeitos
                  </div>
                </div>
              </div>

              {/* Badge */}
              <div className="absolute right-8 top-6 flex items-center gap-2 rounded-full bg-white px-4 py-2 shadow-lg">
                <BadgeCheck className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Aprovado pelo CFM</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section className="py-20">
        <MaxWidthWrapper>
          <HeaderSection
            label="Recursos"
            title="Tudo que você precisa em um só lugar"
            subtitle="Uma plataforma completa para modernizar seu consultório e expandir sua prática médica"
          />

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="relative border-2 transition-all hover:border-primary"
              >
                <CardHeader className="space-y-4 pb-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-heading text-xl font-semibold">
                    {feature.title}
                  </h3>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </MaxWidthWrapper>
      </section>

      {/* Stats Section */}
      <section className="border-y bg-muted/50 py-20">
        <MaxWidthWrapper>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">+100</div>
              <p className="mt-2 text-muted-foreground">Médicos Ativos</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">+2.000</div>
              <p className="mt-2 text-muted-foreground">Consultas Realizadas</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary">98%</div>
              <p className="mt-2 text-muted-foreground">
                Satisfação dos Médicos
              </p>
            </div>
          </div>
        </MaxWidthWrapper>
      </section>

      {/* CTA Section */}
      <section className="bg-primary py-20">
        <MaxWidthWrapper className="text-center">
          <h2 className="mb-4 text-3xl font-bold text-white">
            Comece a atender online hoje mesmo
          </h2>
          <p className="mb-8 text-lg text-white/90">
            Processo de cadastro simplificado e suporte completo para começar
          </p>
          <Link href="/doctors/register">
            <Button
              size="lg"
              className="bg-white text-primary hover:bg-white/90"
            >
              Cadastre-se Gratuitamente
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </MaxWidthWrapper>
      </section>
    </div>
  );
}
