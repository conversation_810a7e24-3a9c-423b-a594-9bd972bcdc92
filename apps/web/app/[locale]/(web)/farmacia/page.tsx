'use client';

import Image from 'next/image';
import {
	Shield,
	Stethoscope,
	Timer,
	QrCode,
	Users,
	Headphones,
	Check,
} from 'lucide-react';

import { Card, CardContent } from '@ui/components/card';
import MaxWidthWrapper from '@marketing/shared/components/MaxWidthWrapper';
import { CallToActionButton } from '@shared/components/CallToActionButton';
import { FarmaciaHero } from '@marketing/home/<USER>/farmacia/farmacia-hero';
import { ON_DUTY_DOCTORS } from 'api/constants/on-duty';

export default function FarmaciaPage() {
	const whatsappLink = 'https://wa.me/5547992593851';

	const benefitsPharma = [
		'Aumento do Fluxo de Clientes',
		'Sem Custos para a Farmácia',
		'Diferenciação no Mercado',
		'Fidelização de Clientes',
		'Melhoria na Imagem da Farmácia',
		'Promoção de Saúde Preventiva',
	];

	const benefitsPatients = [
		'Acesso Rápido a Consultas Médicas',
		'Conveniência',
		'Redução de Tempo de Espera',
		'Atendimento Especializado',
		'Receitas e Prescrições Imediatas',
		'Segurança e Privacidade',
	];

	const howItWorks = [
		{
			icon: <QrCode className='h-6 w-6 text-primary' />,
			title: 'Exponha o QR Code',
			description:
				'O farmacêutico mostra ao cliente a oportunidade de teleconsulta, indicando o QR Code.',
		},
		{
			icon: <Users className='h-6 w-6 text-primary' />,
			title: 'Cliente escaneia',
			description:
				'Agora, a pessoa a ser consultada, acessa a nossa plataforma, de forma ágil e com segurança.',
		},
		{
			icon: <Timer className='h-6 w-6 text-primary' />,
			title: 'Atendimento rápido',
			description:
				'Após o pagamento, o cliente recebe atendimento médico rapidamente.',
		},
		{
			icon: <Stethoscope className='h-6 w-6 text-primary' />,
			title: 'Documentos emitidos',
			description: 'O médico envia receitas, atestados e exames necessários.',
		},
	];

	const implementation = [
		{
			icon: <Users className='h-6 w-6 text-primary' />,
			title: 'Visita da equipe de vendas',
			description:
				'Nossa equipe visita sua farmácia para explicar a parceria.*',
		},
		{
			icon: <QrCode className='h-6 w-6 text-primary' />,
			title: 'Configuração do QR Code',
			description: 'Fornecemos o material necessário (banner e QR Code).',
		},
		{
			icon: <Shield className='h-6 w-6 text-primary' />,
			title: 'Treinamento',
			description: 'Treinamos sua equipe para orientar os clientes.',
		},
		{
			icon: <Headphones className='h-6 w-6 text-primary' />,
			title: 'Suporte continuado',
			description:
				'Oferecemos suporte contínuo para garantir o sucesso da parceria.',
		},
	];

	return (
		<div className='flex min-h-screen flex-col'>
			{/* Hero Section */}
			<FarmaciaHero whatsappLink={whatsappLink} />

			{/* How it Works Section */}
			<section className='py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='text-3xl font-bold'>Como funciona a Parceria</h2>
					</div>
					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
						{howItWorks.map((item, index) => (
							<Card
								key={index}
								className='border-2 border-muted bg-background/50'
							>
								<CardContent className='flex flex-col items-center p-6 text-center'>
									<div className='mb-4'>{item.icon}</div>
									<h3 className='mb-2 font-semibold'>{item.title}</h3>
									<p className='text-sm text-muted-foreground'>
										{item.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* Implementation Steps Section */}
			<section className='bg-muted/50 py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Como implementar na sua Farmácia
						</h2>
						<p className='text-muted-foreground'>
							Veja como é simples começar a oferecer consultas médicas em sua
							farmácia
						</p>
					</div>
					<div className='grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
						{implementation.map((item, index) => (
							<Card
								key={index}
								className='border-2 border-muted bg-background/50'
							>
								<CardContent className='flex flex-col items-center p-6 text-center'>
									<div className='mb-4'>{item.icon}</div>
									<h3 className='mb-2 font-semibold'>{item.title}</h3>
									<p className='text-sm text-muted-foreground'>
										{item.description}
									</p>
								</CardContent>
							</Card>
						))}
					</div>
					<div className='mt-6 text-center text-sm text-muted-foreground'>
						*Todo o processo de implantação também pode ser feito de forma
						on-line, por ser simples e prático.
					</div>
					<div className='mt-8 text-center'>
						<CallToActionButton
							href={whatsappLink}
							target='_blank'
							className='px-8'
						>
							FALE COM NOSSO FARMACÊUTICO
						</CallToActionButton>
					</div>
				</MaxWidthWrapper>
			</section>

			{/* QR Code Section */}
			<section className='py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Escaneie o QR code da sua farmácia
						</h2>
						<p className='text-muted-foreground'>
							Acesse a teleconsulta de forma rápida e prática
						</p>
					</div>
					<div className='grid md:grid-cols-2 gap-12 items-center'>
						<div>
							<div className='p-8 border-2 border-dashed border-primary rounded-lg flex flex-col items-center justify-center h-[300px]'>
								<div className='w-[200px] h-[200px] bg-gray-200 rounded-lg flex items-center justify-center'>
									{/* <p className='text-center'>Placeholder para QR Code</p>
									 */}
									<Image
										src='/images/home/<USER>'
										alt='QR Code'
										width={200}
										height={200}
										className='w-[200px] h-[200px]'
									/>
								</div>
								<p className='mt-4 text-muted-foreground text-center'>
									QR Code único para sua farmácia
								</p>
							</div>
						</div>
						<div>
							<h3 className='text-2xl font-bold mb-4'>Como funciona?</h3>
							<ol className='space-y-4'>
								<li className='flex gap-3'>
									<div className='flex h-7 w-7 items-center justify-center rounded-full bg-primary text-primary-foreground'>
										1
									</div>
									<div>
										<p className='font-medium'>Escaneie o QR code</p>
										<p className='text-muted-foreground'>
											O cliente escaneia o QR code disponibilizado na farmácia
										</p>
									</div>
								</li>
								<li className='flex gap-3'>
									<div className='flex h-7 w-7 items-center justify-center rounded-full bg-primary text-primary-foreground'>
										2
									</div>
									<div>
										<p className='font-medium'>Realize o pagamento</p>
										<p className='text-muted-foreground'>
											Efetue o pagamento de R$69 pela consulta médica
										</p>
									</div>
								</li>
								<li className='flex gap-3'>
									<div className='flex h-7 w-7 items-center justify-center rounded-full bg-primary text-primary-foreground'>
										3
									</div>
									<div>
										<p className='font-medium'>Consulta imediata</p>
										<p className='text-muted-foreground'>
											Seja atendido em até 20 minutos por um médico especialista
										</p>
									</div>
								</li>
							</ol>
						</div>
					</div>
				</MaxWidthWrapper>
			</section>

			{/* CTA Section */}
			<section className='bg-primary py-16 text-primary-foreground'>
				<MaxWidthWrapper className='text-center'>
					<h2 className='mb-6 text-3xl font-bold'>
						Ofereça conforto e suporte clínico aos seus clientes
					</h2>
					<p className='mb-8 text-lg'>
						Nossa equipe médica quer ser um apoio para sua farmácia e ajudar
						seus clientes no alívio de seus sintomas, a partir de um diagnóstico
						individual e do pronto atendimento online.
					</p>
					<div className='mt-8 text-center'>
						<CallToActionButton
							href={`/checkout/${ON_DUTY_DOCTORS.DEFAULT}?partner=farmacia`}
							target='_blank'
							className='px-8 bg-white text-primary hover:bg-white/90'
						>
							FALE COM NOSSO FARMACÊUTICO
						</CallToActionButton>
					</div>
				</MaxWidthWrapper>
			</section>

			{/* Benefits for Pharmacy Section */}
			<section className='py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Vantagens para a Farmácia Parceira
						</h2>
					</div>
					<div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
						{benefitsPharma.map((benefit, index) => (
							<div
								key={index}
								className='flex items-center rounded-lg border bg-background p-4'
							>
								<div className='mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-secondary'>
									<Check className='h-4 w-4 text-white' />
								</div>
								<span className='text-sm'>{benefit}</span>
							</div>
						))}
					</div>
				</MaxWidthWrapper>
			</section>

			{/* Benefits for Patients Section */}
			<section className='bg-muted/50 py-16'>
				<MaxWidthWrapper>
					<div className='mb-12 text-center'>
						<h2 className='mb-4 text-3xl font-bold'>
							Vantagens para os Pacientes
						</h2>
					</div>
					<div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
						{benefitsPatients.map((benefit, index) => (
							<div
								key={index}
								className='flex items-center rounded-lg border bg-background p-4'
							>
								<div className='mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-secondary'>
									<Check className='h-4 w-4 text-white' />
								</div>
								<span className='text-sm'>{benefit}</span>
							</div>
						))}
					</div>
					<div className='mt-8 text-center'>
						<CallToActionButton
							href={whatsappLink}
							target='_blank'
							className='px-8'
						>
							FALE COM NOSSO FARMACÊUTICO
						</CallToActionButton>
					</div>
				</MaxWidthWrapper>
			</section>

			{/* No App Section */}
			<section className='py-16'>
				<MaxWidthWrapper className='flex flex-col md:flex-row items-center justify-between gap-8'>
					<div className='md:w-1/2'>
						<h2 className='text-3xl font-bold mb-4'>
							Não há necessidade de aplicativos
						</h2>
						<p className='mb-6 text-muted-foreground'>
							Não é necessário baixar nenhum tipo de aplicativo adicional.
							Nossas consultas podem ser feitas de forma simples e conveniente,
							diretamente pelo navegador do seu dispositivo.
						</p>
						<CallToActionButton
							href={whatsappLink}
							target='_blank'
							className='px-8'
						>
							FALE COM NOSSO FARMACÊUTICO
						</CallToActionButton>
					</div>
					<div className='md:w-1/2 flex justify-center'>
						<div className='relative w-[400px] h-[400px]  rounded-3xl flex items-center justify-center'>
							{/* <div className='text-center p-4'>
								<p className='font-bold text-lg mb-2'>Smartphone Placeholder</p>
								<p className='text-sm'>(Mostrando a consulta online)</p>
							</div> */}
							{/* Replace with actual image when available */}
							<Image
								src='/images/home/<USER>'
								alt='Aplicativo ZapVida'
								fill
								className='object-contain'
							/>
						</div>
					</div>
				</MaxWidthWrapper>
			</section>
		</div>
	);
}
