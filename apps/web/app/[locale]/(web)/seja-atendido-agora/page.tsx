import { constructMetadata } from '@lib/utils';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Consulta Online WhatsApp | Atendimento Imediato | ZapVida',
    description: 'Consulte médicos online via WhatsApp agora! Atendimento 24h imediato, prescrição digital e sem filas. Assinantes têm descontos especiais.',
    canonical: 'https://zapvida.com/seja-atendido-agora',
  });
}

export default function SejaAtendidoAgoraPage() {
  // Redireciona para a página de médicos disponíveis
  redirect('/doctors?online=true');

  // Este código nunca será executado devido ao redirecionamento
  return null;
}
