@tailwind base;
@tailwind components;
@tailwind utilities;

/* ZapChat V2 - Enhanced Styles */

/* ZapChat Custom Styles */
.chat-transitioning {
    transition: all 0.3s ease-in-out;
}

/* Custom scrollbar for chat */
.chat-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Message animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-enter {
    animation: messageSlideIn 0.3s ease-out;
}

/* Typing indicator */
.typing-indicator {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #9ca3af;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {

    0%,
    80%,
    100% {
        transform: scale(0.8);
        opacity: 0.5;
    }

    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Mobile optimizations */
@media (max-width: 1024px) {
    .chat-mobile-full {
        width: 100vw;
        height: 100vh;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .chat-dark {
        @apply bg-gray-900 text-white;
    }

    .chat-dark .bg-white {
        @apply bg-gray-800;
    }

    .chat-dark .border-gray-200 {
        @apply border-gray-700;
    }
}

/* Patient Appointments Mobile Optimizations */
@media (max-width: 768px) {

    /* Action Cards Mobile Enhancements */
    .action-card-mobile {
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .action-card-mobile:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-1px);
    }

    /* Badge Mobile Optimizations */
    .badge-mobile {
        padding: 6px 12px;
        font-size: 11px;
        line-height: 1.2;
        border-radius: 12px;
    }

    /* Button Mobile Optimizations */
    .btn-mobile-primary {
        padding: 12px 20px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 12px;
        min-height: 48px;
    }

    .btn-mobile-secondary {
        padding: 10px 16px;
        font-size: 13px;
        border-radius: 10px;
        min-height: 44px;
    }

    /* Card Mobile Optimizations */
    .card-mobile {
        border-radius: 16px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    }

    .card-mobile:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Text Mobile Optimizations */
    .text-mobile-title {
        font-size: 18px;
        line-height: 1.3;
        font-weight: 700;
    }

    .text-mobile-subtitle {
        font-size: 14px;
        line-height: 1.4;
        color: #6b7280;
    }

    .text-mobile-body {
        font-size: 13px;
        line-height: 1.5;
    }

    /* Spacing Mobile Optimizations */
    .space-mobile-y {
        margin-top: 16px;
        margin-bottom: 16px;
    }

    .space-mobile-y>*+* {
        margin-top: 16px;
    }

    /* Icon Mobile Optimizations */
    .icon-mobile {
        width: 20px;
        height: 20px;
    }

    .icon-mobile-lg {
        width: 24px;
        height: 24px;
    }

    .icon-mobile-xl {
        width: 28px;
        height: 28px;
    }
}

/* Touch-friendly interactions for mobile */
@media (hover: none) and (pointer: coarse) {
    .touch-friendly {
        min-height: 44px;
        min-width: 44px;
    }

    .touch-friendly-card {
        transition: transform 0.15s ease-out;
    }

    .touch-friendly-card:active {
        transform: scale(0.98);
    }

    .touch-friendly-button {
        transition: all 0.15s ease-out;
    }

    .touch-friendly-button:active {
        transform: scale(0.95);
        opacity: 0.9;
    }
}

/* Enhanced mobile shadows and depth */
@media (max-width: 768px) {
    .mobile-shadow-sm {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    }

    .mobile-shadow-md {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }

    .mobile-shadow-lg {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.16);
    }
}

/* Mobile-specific animations */
@media (max-width: 768px) {
    .mobile-fade-in {
        animation: mobileFadeIn 0.3s ease-out;
    }

    @keyframes mobileFadeIn {
        from {
            opacity: 0;
            transform: translateY(8px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .mobile-slide-up {
        animation: mobileSlideUp 0.25s ease-out;
    }

    @keyframes mobileSlideUp {
        from {
            opacity: 0;
            transform: translateY(12px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}
