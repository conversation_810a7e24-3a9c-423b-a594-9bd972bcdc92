import baseConfig from "tailwind-config";
import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    presets: [baseConfig],
	content: ["./app/**/*.tsx", "./modules/**/*.tsx"],
	safelist: ["ml-2", "ml-4", "ml-6", "ml-8", "ml-10"],
    theme: {
    	extend: {
    		colors: {
    			sidebar: {
    				DEFAULT: 'hsl(var(--sidebar-background))',
    				foreground: 'hsl(var(--sidebar-foreground))',
    				primary: 'hsl(var(--sidebar-primary))',
    				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
    				accent: 'hsl(var(--sidebar-accent))',
    				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
    				border: 'hsl(var(--sidebar-border))',
    				ring: 'hsl(var(--sidebar-ring))'
    			}
    		}
    	}
    }
} satisfies Config;
