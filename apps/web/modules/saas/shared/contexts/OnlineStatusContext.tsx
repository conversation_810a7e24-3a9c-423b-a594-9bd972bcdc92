// Check if this file exists and has the correct implementation
// If not, you might need to create it with the following content:

'use client';

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { useUser } from '@saas/auth/hooks/use-user';

interface OnlineStatusContextType {
	isOnline: boolean;
	setIsOnline: (status: boolean) => void;
	isUpdatingStatus: boolean;
	setIsUpdatingStatus: (updating: boolean) => void;
}

const OnlineStatusContext = createContext<OnlineStatusContextType | undefined>(
	undefined
);

export function OnlineStatusProvider({
	children,
}: {
	children: React.ReactNode;
}) {
	const { user } = useUser();
	const [isOnline, setIsOnlineState] = useState<boolean>(
		user?.role === 'DOCTOR' && user?.onlineStatus === 'ONLINE'
	);
	const [isUpdatingStatus, setIsUpdatingStatus] = useState<boolean>(false);

	// Update the initial state when user data changes
	useEffect(() => {
		if (user?.role === 'DOCTOR') {
			console.log(
				'OnlineStatusProvider - User data changed, updating isOnline to:',
				user.onlineStatus === 'ONLINE'
			);
			setIsOnlineState(user.onlineStatus === 'ONLINE');
		}
	}, [user]);

	// Log when isOnline changes
	useEffect(() => {
		console.log('OnlineStatusProvider - isOnline state changed to:', isOnline);
	}, [isOnline]);

	// Create a stable setIsOnline function
	const setIsOnline = (status: boolean) => {
		console.log('setIsOnline called with:', status);
		setIsOnlineState(status);
	};

	// Use useMemo to ensure the context value only changes when its dependencies change
	const value = useMemo(
		() => ({
			isOnline,
			setIsOnline,
			isUpdatingStatus,
			setIsUpdatingStatus,
		}),
		[isOnline, isUpdatingStatus]
	);

	return (
		<OnlineStatusContext.Provider value={value}>
			{children}
		</OnlineStatusContext.Provider>
	);
}

export function useOnlineStatus() {
	const context = useContext(OnlineStatusContext);
	if (context === undefined) {
		throw new Error(
			'useOnlineStatus must be used within an OnlineStatusProvider'
		);
	}
	return context;
}
