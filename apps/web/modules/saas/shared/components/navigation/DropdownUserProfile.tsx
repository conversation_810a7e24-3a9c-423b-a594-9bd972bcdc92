'use client';

import { config } from '@config';
import { Link } from '@i18n/routing';
import { useUser } from '@saas/auth/hooks/use-user';
import { UserAvatar } from '@shared/components/UserAvatar';
import { apiClient } from '@shared/lib/api-client';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';
import { useToast } from '@ui/hooks/use-toast';
import {
	EllipsisVertical,
	HardDriveIcon,
	LoaderIcon,
	LogOutIcon,
	MoonIcon,
	SettingsIcon,
	SunIcon,
	UserRoundXIcon,
} from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import React, { useState } from 'react';
import { useOnlineStatus } from '@saas/shared/contexts/OnlineStatusContext';

export type DropdownUserProfileProps = {
	children: React.ReactNode;
	align?: 'center' | 'start' | 'end';
};

export function DropdownUserProfile({
	children,
	align = 'start',
}: DropdownUserProfileProps) {
	const { user, logout, reloadUser } = useUser();
	const { toast } = useToast();
	const currentLocale = useLocale();
	const t = useTranslations();
	const [locale, setLocale] = useState<string>(currentLocale);
	const { setTheme: setCurrentTheme, theme: currentTheme } = useTheme();
	const [theme, setTheme] = useState<string>(currentTheme ?? 'system');
	const unimpersonateMutation = apiClient.admin.unimpersonate.useMutation();
	const updateOnlineStatusMutation =
		apiClient.doctors.updateOnlineStatus.useMutation();
	// Use the shared context instead of local state
	const { isOnline, setIsOnline, isUpdatingStatus, setIsUpdatingStatus } =
		useOnlineStatus();

	// Add useEffect to log when isOnline changes
	React.useEffect(() => {
		console.log('DropdownUserProfile - isOnline changed:', isOnline);
	}, [isOnline]);

	if (!user) {
		return null;
	}

	const { name, email, avatarUrl, role } = user;
	// Usar as traduções para os roles
	const translatedRole = role ? t(('dashboard.roles.' + role) as any) : role;

	const toggleOnlineStatus = async () => {
		try {
			setIsUpdatingStatus(true);
			const newStatus = !isOnline;
			console.log('Toggling status from', isOnline, 'to', newStatus);

			// Atualizar status no backend
			await updateOnlineStatusMutation.mutateAsync({
				isOnline: newStatus,
			});

			// Update shared state BEFORE reloading user to ensure UI updates immediately
			setIsOnline(newStatus);
			console.log('Status updated in state to:', newStatus);
			
			await reloadUser();

			toast({
				title: `Status atualizado para ${newStatus ? 'Online' : 'Offline'}`,
				variant: 'default',
			});
		} catch (error) {
			console.error('Erro ao atualizar status:', error);
			toast({
				title: 'Erro ao atualizar status',
				description: 'Não foi possível atualizar seu status online/offline',
				variant: 'error',
			});
		} finally {
			setIsUpdatingStatus(false);
		}
	};

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>

			<DropdownMenuContent align={align}>
				<DropdownMenuLabel>
					{name}
					<span className='block font-normal text-xs opacity-70'>{email}</span>
					{role && (
						<span className='block font-normal text-xs opacity-70'>
							{translatedRole}
						</span>
					)}
				</DropdownMenuLabel>

				<DropdownMenuSeparator />

				{role === 'DOCTOR' && (
					<DropdownMenuItem
						onClick={toggleOnlineStatus}
						disabled={isUpdatingStatus}
						className='relative'
					>
						<span
							className={`mr-2 inline-block h-2 w-2 rounded-full ${
								isOnline ? 'bg-green-500' : 'bg-gray-500'
							}`}
						></span>
						<span className='flex items-center gap-2'>
							{isUpdatingStatus ? (
								<>
									<LoaderIcon className='size-3 animate-spin' />
									Atualizando...
								</>
							) : isOnline ? (
								'Ficar Offline'
							) : (
								'Ficar Online'
							)}
						</span>
					</DropdownMenuItem>
				)}

				<DropdownMenuItem asChild>
					<Link href='/app/settings/account/general'>
						<SettingsIcon className='mr-2 size-4' />
						{t('dashboard.userMenu.accountSettings')}
					</Link>
				</DropdownMenuItem>

				{user.impersonatedBy && (
					<DropdownMenuItem
						onClick={async () => {
							const { dismiss } = toast({
								variant: 'loading',
								title: t('admin.users.impersonation.unimpersonating'),
							});
							await unimpersonateMutation.mutateAsync();
							dismiss();
							window.location.reload();
						}}
					>
						<UserRoundXIcon className='mr-2 size-4' />
						{t('dashboard.userMenu.unimpersonate')}
					</DropdownMenuItem>
				)}

				<DropdownMenuItem onClick={logout}>
					<LogOutIcon className='mr-2 size-4' />
					{t('dashboard.userMenu.logout')}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
