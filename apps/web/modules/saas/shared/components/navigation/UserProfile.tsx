'use client';

import { useUser } from '@saas/auth/hooks/use-user';
import { UserAvatar } from '@shared/components/UserAvatar';
import { ChevronsUpDown, User } from 'lucide-react';
import { DropdownUserProfile } from './DropdownUserProfile';
import { cx } from './utils';
import { Button } from '@ui/components/button';
import React from 'react';
import { apiClient } from '@shared/lib/api-client';
import { useToast } from '@ui/hooks/use-toast';
import { useTranslations } from 'next-intl';
import { useOnlineStatus } from '@saas/shared/contexts/OnlineStatusContext';

interface UserProfileDesktopProps {
	isCollapsed?: boolean;
}

export const UserProfileDesktop = ({
	isCollapsed,
}: UserProfileDesktopProps) => {
	const { user, reloadUser } = useUser();
	const { toast } = useToast();
	const updateOnlineStatusMutation =
		apiClient.doctors.updateOnlineStatus.useMutation();
	// Use the shared context instead of local state
	const { isOnline, setIsOnline, isUpdatingStatus, setIsUpdatingStatus } =
		useOnlineStatus();

	if (!user) return null;

	const { name, email, avatarUrl, role } = user;
	const initials =
		name
			?.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase() || '';

	const toggleOnlineStatus = async () => {
		try {
			setIsUpdatingStatus(true);
			const newStatus = !isOnline;

			await updateOnlineStatusMutation.mutateAsync({
				isOnline: newStatus,
			});

			// Update shared state
			setIsOnline(newStatus);
			await reloadUser();

			toast({
				title: `Status atualizado para ${newStatus ? 'Online' : 'Offline'}`,
				variant: 'default',
			});
		} catch (error) {
			console.error('Erro ao atualizar status:', error);
			toast({
				title: 'Erro ao atualizar status',
				description: 'Não foi possível atualizar seu status online/offline',
				variant: 'error',
			});
		} finally {
			setIsUpdatingStatus(false);
		}
	};

	return (
		<DropdownUserProfile>
			{isCollapsed ? (
				<Button
					aria-label='User settings'
					variant='ghost'
					className='group flex justify-center w-full items-center rounded-md px-1 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200/50 data-[state=open]:bg-gray-200/50 hover:dark:bg-gray-800/50 data-[state=open]:dark:bg-gray-900'
				>
					<UserAvatar
						name={name ?? ''}
						avatarUrl={avatarUrl}
						className='size-8 shrink-0'
					/>
				</Button>
			) : (
				<Button
					aria-label='User settings'
					variant='ghost'
					className='group flex w-full items-center justify-between rounded-md px-2 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200/50 data-[state=open]:bg-gray-200/50 hover:dark:bg-gray-800/50 data-[state=open]:dark:bg-gray-900'
				>
					<div className='flex items-center gap-2'>
						<UserAvatar
							name={name ?? ''}
							avatarUrl={avatarUrl}
							className='size-8 shrink-0'
						/>
						<div className='text-left min-w-0'>
							{' '}
							{/* Adicionado min-w-0 para permitir truncate funcionar */}
							<div className='flex items-center gap-2'>
								<p className='font-medium truncate max-w-[120px]'>
									{name ? name.split(' ')[0] : 'Paciente'}
								</p>{' '}
								{/* Adicionado truncate e max-w */}
								{role === 'DOCTOR' && (
									<span
										onClick={(e) => {
											e.stopPropagation();
											toggleOnlineStatus();
										}}
										className={`inline-block h-2 w-2 rounded-full cursor-pointer ${
											isOnline ? 'bg-green-500' : 'bg-gray-500'
										} ${isUpdatingStatus ? 'animate-pulse' : ''}`}
										title={
											isOnline
												? 'Online (click to go offline)'
												: 'Offline (click to go online)'
										}
									></span>
								)}
							</div>
							<p className='text-xs text-muted-foreground truncate max-w-[140px]'>
								{role === 'DOCTOR' && 'Médico'}
								{role === 'PATIENT' && 'Paciente'}
								{role === 'SECRETARY' && 'Secretária'}
								{role === 'ADMIN' && 'Administrador'}
							</p>
							{/* Adicionado truncate e max-w */}
						</div>
					</div>
					<ChevronsUpDown className='size-4 opacity-50' />
				</Button>
			)}
		</DropdownUserProfile>
	);
};

export const UserProfileMobile = () => {
	const { user, reloadUser } = useUser();
	const { toast } = useToast();
	const updateOnlineStatusMutation =
		apiClient.doctors.updateOnlineStatus.useMutation();
	// Use the shared context instead of local state
	const { isOnline, setIsOnline, isUpdatingStatus, setIsUpdatingStatus } =
		useOnlineStatus();

	if (!user) return null;

	const { name, avatarUrl, role } = user;

	const toggleOnlineStatus = async () => {
		// Same implementation as in UserProfileDesktop
		try {
			setIsUpdatingStatus(true);
			const newStatus = !isOnline;

			await updateOnlineStatusMutation.mutateAsync({
				isOnline: newStatus,
			});

			setIsOnline(newStatus);
			await reloadUser();

			toast({
				title: `Status atualizado para ${newStatus ? 'Online' : 'Offline'}`,
				variant: 'default',
			});
		} catch (error) {
			console.error('Erro ao atualizar status:', error);
			toast({
				title: 'Erro ao atualizar status',
				description: 'Não foi possível atualizar seu status online/offline',
				variant: 'error',
			});
		} finally {
			setIsUpdatingStatus(false);
		}
	};

	return (
		<DropdownUserProfile align='end'>
			<div className='flex items-center gap-2'>
				<Button
					aria-label='User settings'
					variant='ghost'
					className={cx(
						'group flex items-center rounded-md p-0.5 sm:p-1 text-sm font-medium text-gray-900 hover:bg-gray-200/50 data-[state=open]:bg-gray-200/50 hover:dark:bg-gray-800/50 data-[state=open]:dark:bg-gray-800/50'
					)}
				>
					<UserAvatar
						name={name ?? ''}
						avatarUrl={avatarUrl}
						className='size-8 sm:size-7 shrink-0'
					/>
					{role === 'DOCTOR' && (
						<span
							onClick={(e) => {
								e.stopPropagation();
								toggleOnlineStatus();
							}}
							className={`absolute top-0 right-0 hidden md:inline-block h-2 w-2 rounded-full ${
								isOnline ? 'bg-green-500' : 'bg-gray-500'
							} ${isUpdatingStatus ? 'animate-pulse' : ''}`}
						></span>
					)}
				</Button>
			</div>
		</DropdownUserProfile>
	);
};
