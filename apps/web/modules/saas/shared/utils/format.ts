/**
 * Formata um valor numérico para moeda brasileira (R$)
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Formata uma data para o formato brasileiro
 */
export function formatDate(date: Date | string): string {
  if (!date) return '';
  return new Intl.DateTimeFormat('pt-BR').format(
    typeof date === 'string' ? new Date(date) : date
  );
}

/**
 * Formata uma data e hora para o formato brasileiro
 */
export function formatDateTime(date: Date | string): string {
  if (!date) return '';
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(typeof date === 'string' ? new Date(date) : date);
}

/**
 * Formata um CPF (xxx.xxx.xxx-xx)
 */
export function formatCPF(cpf: string): string {
  if (!cpf) return '';

  // Remove caracteres não numéricos
  const cpfClean = cpf.replace(/\D/g, '');

  // Aplica a máscara
  return cpfClean.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Formata um número de telefone ((xx) xxxxx-xxxx)
 */
export function formatPhone(phone: string): string {
  if (!phone) return '';

  // Remove caracteres não numéricos
  const phoneClean = phone.replace(/\D/g, '');

  // Aplica a máscara de acordo com o comprimento
  if (phoneClean.length === 11) {
    return phoneClean.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (phoneClean.length === 10) {
    return phoneClean.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }

  return phoneClean;
}
