import { CURRENT_TEAM_ID_COOKIE_NAME } from "@saas/shared/constants";
import { createApiCaller } from "api/trpc/caller";
import { cookies } from "next/headers";
import "server-only";

export const currentUser = async () => {
	console.log('=== DEBUG currentUser ===');

	const apiCaller = await createApiCaller();
	console.log('API caller created');

	const user = await apiCaller.auth.user();
	console.log('User from API:', JSON.stringify(user, null, 2));

	if (!user) {
		console.log('No user returned from API');
		return {
			user: null,
			team: null,
		};
	}

	console.log('User found, getting team memberships');
	const currentTeamId =
		(await cookies()).get(CURRENT_TEAM_ID_COOKIE_NAME)?.value ?? null;

	const { teamMemberships } = user;

	const teamMembership =
		teamMemberships?.find(
			(membership) => membership.team.id === currentTeamId,
		) ?? teamMemberships?.[0];

	const team = teamMembership?.team || null;

	console.log('Returning user and team:', { userId: user.id, userRole: user.role, teamId: team?.id });
	return {
		user,
		team,
		teamMembership,
	};
};
