"use client";

import { But<PERSON> } from "@ui/components/button";
import { useTranslations } from "next-intl";
import type { JSXElementConstructor } from "react";
import type React from "react";
import { FcGoogle } from "react-icons/fc";


type IconProps = {
	className?: string;
};

export const oAuthProviders: Record<
	string,
	{
		name: string;
		icon: JSXElementConstructor<React.SVGProps<SVGSVGElement>>;
	}
> = {
	google: {
		name: "Google",
		icon: ({ ...props }: IconProps) => (
			<FcGoogle />
		),
	},

};

export function SocialSigninButton({
	provider,
	className,
}: {
	provider: keyof typeof oAuthProviders;
	className?: string;
}) {
	const t = useTranslations();
	const providerData = oAuthProviders[provider];

	return (
		<Button asChild variant="outline" type="button" className={className}>
			<a href={`/api/oauth/${provider}`}>
				{providerData.icon && (
					<i className="mr-2 opacity-70">
						<providerData.icon className="size-5" />
					</i>
				)}
				{t("auth.continueWithProvider", { provider: providerData.name })}
			</a>
		</Button>
	);
}
