"use client";

import { apiClient } from "@shared/lib/api-client";
import { LoaderIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useUser } from "../hooks/use-user";

export function VerifyTokenView() {
	const t = useTranslations();
	const [loading, setLoading] = useState(true);
	const searchParams = useSearchParams();
	const router = useRouter();
	const { reloadUser } = useUser();
	const [error, setError] = useState<string | null>(null);

	const token = searchParams.get("token") ?? "";
	const redirect = searchParams.get("redirect") ?? "/app/dashboard";

	const verifyTokenMutation = apiClient.auth.verifyToken.useMutation();

	const verifyToken = async (token: string) => {
		try {
			setLoading(true);
			console.log("Verificando token:", token);

			const response = await verifyTokenMutation.mutateAsync({
				token,
				redirect,
			});
			console.log("Resposta da verificação:", response);

			await reloadUser();
			console.log("Usuário recarregado");

			const redirectTo = response?.redirectUrl || "/app";
			console.log("Redirecionando para:", redirectTo);

			router.push(redirectTo);
		} catch (error) {
			console.error("Erro na verificação:", error);
			setLoading(false);
			setError(
				error instanceof Error
					? error.message
					: "An error occurred while verifying your token",
			);
		}
	};

	useEffect(() => {
		if (!token) {
			setLoading(false);
			return;
		}

		(async () => {
			await verifyToken(token);
		})();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center py-8">
				<LoaderIcon className="size-8 animate-spin" />
			</div>
		);
	}

	return (
		<div>
			<h1 className="font-bold text-3xl md:text-4xl">
				{t("auth.invalidToken.title")}
			</h1>
			<p className="mt-2 mb-4 text-muted-foreground">
				{t("auth.invalidToken.message")}
			</p>
		</div>
	);
}
