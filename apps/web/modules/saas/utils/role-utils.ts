import type { TeamMemberRoleType } from "database";

/**
 * Converts a role type to a human-readable label
 */
export function getR<PERSON>Label(role: TeamMemberRoleType): string {
  switch (role) {
    case "ADMIN":
      return "Administrador";
    case "DOCTOR":
      return "Médico";
    case "SECRETARY":
      return "Secretário(a)";
    case "OWNER":
      return "Proprietário";
    case "MEMBER":
      return "Membro";
    default:
      return role;
  }
}
