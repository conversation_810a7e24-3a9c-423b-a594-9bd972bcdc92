import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import type { TeamMemberRoleType } from "database";
import { useTranslations } from "next-intl";
import { getRoleLabel } from "@saas/utils/role-utils";

export function TeamRoleSelect({
	value,
	onSelect,
	disabled,
}: {
	value: TeamMemberRoleType;
	onSelect: (value: TeamMemberRoleType) => void;
	disabled?: boolean;
}) {
	const t = useTranslations();

	// Define role options with clear, unique values
	const roleOptions = [
		{
			label: "Proprietário",
			value: "OWNER",
			description: "Acesso completo ao sistema"
		},
		{
			label: "Administrador",
			value: "ADMIN",
			description: "Acesso administrativo"
		},
		{
			label: "Médico",
			value: "DOCTOR",
			description: "Acesso a consultas médicas"
		},
		{
			label: "Secretário(a)",
			value: "SECRETARY",
			description: "Acesso à agenda e pacientes"
		},
		{
			label: "Membro",
			value: "MEMBER",
			description: "Acesso básico"
		}
	];

	// Find the display label for the current value
	const currentLabel = roleOptions.find(option => option.value === value)?.label || getRoleLabel(value);

	return (
		<Select value={value} onValueChange={onSelect} disabled={disabled}>
			<SelectTrigger>
				<div className="truncate">{currentLabel}</div>
			</SelectTrigger>
			<SelectContent>
				{roleOptions.map((option) => (
					<SelectItem key={option.value} value={option.value}>
						{option.label}
					</SelectItem>
				))}
			</SelectContent>
		</Select>
	);
}
