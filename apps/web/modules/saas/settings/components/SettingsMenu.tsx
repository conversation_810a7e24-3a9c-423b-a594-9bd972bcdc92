"use client";

import { Link, usePathname } from "@i18n/routing";
import { cn } from "@ui/lib";
import { ChevronDown, ChevronRight, CreditCard, Settings, User, Users } from "lucide-react";
import type React from "react";
import { useState } from "react";

const getMenuIcon = (href: string) => {
	if (href.includes("general")) return Settings;
	if (href.includes("members")) return Users;
	if (href.includes("billing")) return CreditCard;
	return User;
};

// Definir interface para itens do menu com submenu opcional
interface MenuItem {
	title: string;
	href?: string;
	icon?: React.ReactNode;
	submenu?: MenuItem[];
}

interface MenuProps {
	menuItems: {
		title: string;
		avatar: React.ReactNode;
		items: MenuItem[];
	}[];
}

export function SettingsMenu({ menuItems }: MenuProps) {
	const pathname = usePathname();
	// Estado para controlar quais submenus estão expandidos
	const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

	const isActiveMenuItem = (href?: string) => {
		if (!href) return false;
		return pathname.includes(href);
	};

	const isActiveSubmenu = (item: MenuItem) => {
		if (item.href && isActiveMenuItem(item.href)) return true;
		if (item.submenu) {
			return item.submenu.some(subitem =>
				(subitem.href && isActiveMenuItem(subitem.href)) ||
				(subitem.submenu && isActiveSubmenu(subitem))
			);
		}
		return false;
	};

	const toggleSubmenu = (title: string) => {
		setExpandedMenus(prev => ({
			...prev,
			[title]: !prev[title]
		}));
	};

	// Renderizar um item do menu (pode ser um link ou um submenu)
	const renderMenuItem = (item: MenuItem, index: number) => {
		// Se tem submenu, renderiza expandível
		if (item.submenu) {
			const isActive = isActiveSubmenu(item);
			// Auto expandir se algum item do submenu estiver ativo
			if (isActive && !expandedMenus[item.title]) {
				toggleSubmenu(item.title);
			}

			return (
				<li key={index}>
					<div
						onClick={() => toggleSubmenu(item.title)}
						className={cn(
							"flex items-center justify-between rounded-md px-2 py-3 text-sm transition-colors cursor-pointer",
							isActive
								? "bg-primary/10 text-primary font-medium"
								: "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800",
						)}
					>
						<div className="flex items-center gap-2">
							{item.icon || <Settings className="h-4 w-4" />}
							{item.title}
						</div>
						{expandedMenus[item.title] ? (
							<ChevronDown className="h-4 w-4" />
						) : (
							<ChevronRight className="h-4 w-4" />
						)}
					</div>

					{expandedMenus[item.title] && (
						<ul className="ml-4 mt-1 space-y-1 border-l pl-2">
							{item.submenu.map((subitem, subIndex) => (
								renderMenuItem(subitem, `${index}-${subIndex}`)
							))}
						</ul>
					)}
				</li>
			);
		}

		// Item regular com link
		const isActive = item.href && isActiveMenuItem(item.href);
		return (
			<li key={index}>
				<Link
					href={item.href || "#"}
					className={cn(
						"flex items-center gap-2 rounded-md px-2 py-3 text-sm transition-colors",
						isActive
							? "bg-primary text-primary-foreground"
							: "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800",
					)}
				>
					{item.icon || (item.href ? getMenuIcon(item.href) : <User className="h-4 w-4" />)}
					{item.title}
				</Link>
			</li>
		);
	};

	return (
		<div className="w-full max-w-[280px] ">
			<nav className="sticky top-0 py-6 pr-4">
				<div className="space-y-6">
					{menuItems.map((item, i) => (
						<div key={i}>
							<div className="mb-6 flex items-center gap-2">
								{item.avatar}
								<h2 className="font-semibold text-gray-900 text-sm dark:text-gray-100">
									Sua Conta
								</h2>
							</div>

							<ul className="space-y-1">
								{item.items.map((subitem, k) => renderMenuItem(subitem, k))}
							</ul>
						</div>
					))}
				</div>
			</nav>
		</div>
	);
}
