"use client";

import { useUser } from "@saas/auth/hooks/use-user";
import { UserAvatar } from "@shared/components/UserAvatar";
import { apiClient } from "@shared/lib/api-client";
import { LoaderIcon } from "lucide-react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { v4 as uuid } from "uuid";

export function UserAvatarUpload({
	onSuccess,
	onError,
}: {
	onSuccess: () => void;
	onError: () => void;
}) {
	const { user, updateUser } = useUser();
	const [uploading, setUploading] = useState(false);

	const getSignedUploadUrlMutation =
		apiClient.uploads.signedUploadUrl.useMutation();
	const updateUserMutation = apiClient.auth.update.useMutation();

	const { getRootProps, getInputProps } = useDropzone({
		onDrop: async (acceptedFiles) => {
			if (acceptedFiles.length === 0) return;

			const file = acceptedFiles[0];
			handleImageUpload(file);
		},
		accept: {
			"image/png": [".png"],
			"image/jpeg": [".jpg", ".jpeg"],
		},
		multiple: false,
		maxSize: 5242880, // 5MB
	});

	const handleImageUpload = async (file: File) => {
		if (!user) return;

		setUploading(true);
		try {
			const path = `${user.id}-${uuid()}.${file.name.split('.').pop() || 'png'}`;
			const uploadUrl = await getSignedUploadUrlMutation.mutateAsync({
				path,
				bucket: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME as string,
			});

			const response = await fetch(uploadUrl, {
				method: "PUT",
				body: file,
				headers: {
					"Content-Type": file.type,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to upload image");
			}

			const updatedUser = await updateUserMutation.mutateAsync({
				avatarUrl: path,
			});

			updateUser({
				avatarUrl: updatedUser.avatarUrl,
			});

			onSuccess();
		} catch (e) {
			onError();
		} finally {
			setUploading(false);
		}
	};

	return (
		<div
			className="relative bg-white rounded-full hover:opacity-90 transition-opacity group"
			{...getRootProps()}
		>
			<input {...getInputProps()} />
			<UserAvatar
				className="size-24 cursor-pointer text-xl border-2 border-transparent group-hover:border-primary"
				avatarUrl={user?.avatarUrl}
				name={user?.name ?? ""}
			/>

			<div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
				<div className="bg-primary/20 rounded-full size-24 flex items-center justify-center">
					<span className="text-xs text-white font-medium">Alterar foto</span>
				</div>
			</div>

			{uploading && (
				<div className="absolute inset-0 z-20 flex items-center justify-center bg-card/90 rounded-full">
					<LoaderIcon className="size-6 animate-spin text-primary" />
				</div>
			)}
		</div>
	);
}
