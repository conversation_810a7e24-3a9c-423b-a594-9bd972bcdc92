"use client";

import { ActionBlock } from "@saas/shared/components/ActionBlock";
import { useToast } from "@ui/hooks/use-toast";
import { useTranslations } from "next-intl";
import { UserAvatarUpload } from "./UserAvatarUpload";

export function UserAvatarForm() {
	const { toast } = useToast();
	const t = useTranslations();

	return (
		<ActionBlock title={t("settings.account.avatar.title")}>
			<div className="flex flex-col sm:flex-row items-center gap-6">
				<UserAvatarUpload
					onSuccess={() => {
						toast({
							variant: "success",
							title: t("settings.account.avatar.notifications.updateSuccess"),
						});
					}}
					onError={() => {
						toast({
							variant: "error",
							title: t("settings.account.avatar.notifications.updateFailed"),
						});
					}}
				/>

				<div className="text-center sm:text-left">
					<p className="text-muted-foreground mb-2">{t("settings.account.avatar.description")}</p>
					<p className="text-xs text-muted-foreground">Clique na imagem para atualizar</p>
				</div>
			</div>
		</ActionBlock>
	);
}
