"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslations } from "next-intl";

export function CropImageDialog({
	image,
	open,
	onOpenChange,
	onCrop,
}: {
	image: File | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onCrop: (croppedImage: Blob | null) => void;
}) {
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const imageRef = useRef<HTMLImageElement>(null);
	const t = useTranslations();
	const [imageLoaded, setImageLoaded] = useState(false);

	const imageSrc = useMemo(() => image && URL.createObjectURL(image), [image]);

	// Limpa a URL do objeto quando o componente é desmontado ou a imagem muda
	useEffect(() => {
		return () => {
			if (imageSrc) {
				URL.revokeObjectURL(imageSrc);
			}
		};
	}, [image, imageSrc]);

	// Desenha a imagem no canvas quando ela é carregada
	useEffect(() => {
		if (!imageLoaded || !canvasRef.current || !imageRef.current) return;

		const canvas = canvasRef.current;
		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		const img = imageRef.current;
		
		// Determinar o tamanho do canvas mantendo a proporção da imagem
		let width = img.naturalWidth;
		let height = img.naturalHeight;
		const maxSize = 300;
		
		if (width > height) {
			if (width > maxSize) {
				height = height * (maxSize / width);
				width = maxSize;
			}
		} else {
			if (height > maxSize) {
				width = width * (maxSize / height);
				height = maxSize;
			}
		}
		
		// Definir o tamanho do canvas
		canvas.width = width;
		canvas.height = height;
		
		// Limpar o canvas e desenhar a imagem
		ctx.clearRect(0, 0, canvas.width, canvas.height);
		ctx.drawImage(img, 0, 0, width, height);
	}, [imageLoaded]);

	const handleSave = async () => {
		if (!canvasRef.current) return;
		
		try {
			// Criar um canvas quadrado para o avatar
			const squareCanvas = document.createElement('canvas');
			const size = 256; // Tamanho final do avatar
			squareCanvas.width = size;
			squareCanvas.height = size;
			
			const ctx = squareCanvas.getContext('2d');
			if (!ctx) return;
			
			const canvas = canvasRef.current;
			
			// Determinar a área quadrada para recorte (centralizada)
			const minDimension = Math.min(canvas.width, canvas.height);
			const sourceX = (canvas.width - minDimension) / 2;
			const sourceY = (canvas.height - minDimension) / 2;
			
			// Desenhar a área recortada no canvas quadrado
			ctx.drawImage(
				canvas, 
				sourceX, sourceY, minDimension, minDimension, // área de origem
				0, 0, size, size // área de destino
			);
			
			// Converter para blob
			const blob = await new Promise<Blob | null>((resolve) => {
				squareCanvas.toBlob((blob) => resolve(blob), 'image/png', 0.9);
			});
			
			onCrop(blob);
			onOpenChange(false);
		} catch (error) {
			console.error("Erro ao processar imagem:", error);
			onCrop(null);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle>Ajustar imagem</DialogTitle>
				</DialogHeader>
				<div className="mt-2 flex flex-col items-center">
					{imageSrc && (
						<>
							<img 
								ref={imageRef}
								src={imageSrc}
								alt="Imagem original"
								className="hidden"
								onLoad={() => setImageLoaded(true)}
							/>
							<canvas 
								ref={canvasRef} 
								className="max-w-full border rounded-md"
							/>
							<p className="text-xs text-muted-foreground mt-2 text-center">
								A imagem será recortada como um quadrado a partir do centro.
							</p>
						</>
					)}
				</div>
				<DialogFooter className="mt-4">
					<Button
						onClick={handleSave}
						disabled={!imageLoaded}
					>
						Salvar
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
