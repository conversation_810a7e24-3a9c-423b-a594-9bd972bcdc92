"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  addDays,
  format,
  isAfter,
  isBefore,
  setHours,
  setMinutes,
} from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  BadgeCheck,
  Calendar,
  CalendarDays,
  Clock,
  Mail,
  Medal,
  MoveRight,
  Star,
  Timer,
  Video,
} from "lucide-react";
import { toast } from "sonner";

// Components
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Calendar as CalendarComponent } from "@ui/components/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@ui/components/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { ScrollArea } from "@ui/components/scroll-area";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { UserAvatar } from "@marketing/shared/components/UserAvatar";
import { Doctor } from "../../../types/doctor";
import { cn } from "@ui/lib";
import { formatCurrency } from "../../../lib/utils";

interface DoctorProfileProps {
  doctor: Doctor;
}

export function DoctorProfile({ doctor }: DoctorProfileProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date>(new Date());
  const [selectedTime, setSelectedTime] = React.useState<string>();
  const [isBookingModalOpen, setIsBookingModalOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  // Type assertion for the doctor object to include web app specific properties
  const doctorWithSchedules = doctor as unknown as {
    doctorSchedules: Array<{
      id: string;
      week_day: number;
      is_enabled: boolean;
      start_time: string;
      end_time: string;
    }>;
    specialties: Array<{
      id: string;
      name: string;
      description?: string;
    }>;
  };

  const getAvailableSlots = (date: Date) => {
    const dayOfWeek = date.getDay() + 1; // Ajusta para corresponder ao week_day do banco (1-7)
    const daySchedule = doctorWithSchedules.doctorSchedules.find(
      (schedule) => schedule.week_day === dayOfWeek && schedule.is_enabled,
    );

    if (!daySchedule) {
      return [];
    }

    const slots: Date[] = [];
    const [startHour, startMinute] = daySchedule.start_time
      .split(":")
      .map(Number);
    const [endHour, endMinute] = daySchedule.end_time.split(":").map(Number);

    let currentSlot = setMinutes(setHours(date, startHour), startMinute);
    const endTime = setMinutes(setHours(date, endHour), endMinute);

    while (isBefore(currentSlot, endTime)) {
      const isInBreak = doctor.scheduleBlocks?.some((block) => {
        const breakStart = new Date(block.start_time);
        const breakEnd = new Date(block.end_time);
        return (
          !isBefore(currentSlot, breakStart) && isBefore(currentSlot, breakEnd)
        );
      });

      if (!isInBreak && isAfter(currentSlot, new Date())) {
        slots.push(currentSlot);
      }

      currentSlot = new Date(currentSlot);
      currentSlot.setMinutes(
        currentSlot.getMinutes() + doctor.consultationDuration,
      );
    }

    return slots;
  };

  const availableSlots = getAvailableSlots(selectedDate);

  const router = useRouter();

  const handleBooking = () => {
    const params = new URLSearchParams();
    params.set("doctorId", doctor.id);
    params.set("date", selectedDate.toISOString());
    if (selectedTime) {
      params.set("time", selectedTime);
    }

    router.push(`/checkout?${params.toString()}`);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="grid gap-6 md:grid-cols-3">
        {/* Left Column - Doctor Info */}
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <UserAvatar user={doctor.user} className="size-20" />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold">{doctor.user.name}</h1>
                    <Badge
                      status={
                        doctor.onlineStatus === "ONLINE"
                          ? "success"
                          : "secondary"
                      }
                      className="ml-2"
                    >
                      {doctor.onlineStatus === "ONLINE" ? "Online" : "Offline"}
                    </Badge>
                  </div>

                  <div className="mt-1 flex flex-wrap gap-1">
                    {doctorWithSchedules.specialties.map((specialty) => (
                      <Badge key={specialty.id} status="info" className="bg-blue-100 text-blue-800">
                        {specialty.name}
                      </Badge>
                    ))}
                  </div>

                  <div className="mt-2 flex items-center gap-2">
                    <Star className="size-4 fill-primary text-primary" />
                    <span>
                      {doctor.rating?.toFixed(1)} ({doctor.totalRatings}{" "}
                      avaliações)
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4 border-t pt-6">
                <div className="text-center">
                  <Timer className="mx-auto mb-2 size-5 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Duração</p>
                  <p className="font-medium">
                    {doctor.consultationDuration}min
                  </p>
                </div>
                <div className="text-center">
                  <Video className="mx-auto mb-2 size-5 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Consulta</p>
                  <p className="font-medium">
                    {formatCurrency(doctor.consultationPrice)}
                  </p>
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="font-semibold">CRM</h3>
                  <Badge status="info" variant="outline">
                    {doctor.crm} - {doctor.crmState}
                  </Badge>
                </div>

                <h3 className="mb-2 font-semibold">Sobre o médico</h3>
                <p className="text-sm text-muted-foreground">
                  {doctor.biography}
                </p>

                {/* {doctor.returnPeriod && (
                  <div className="mt-4 rounded-lg bg-primary/10 p-4">
                    <div className="flex items-center gap-2 text-sm">
                      <BadgeCheck className="size-4 text-blue-500" />
                      <span>Conta Verificada</span>
                    </div>
                  </div>
                )} */}
              </div>
            </CardContent>
          </Card>

          {/* Experience Card */}
          <Card>
            <CardHeader>
              <CardTitle>Experiência</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <BadgeCheck className="size-8 text-primary" />
                <div>
                  <p className="font-medium">Documentação Verificada</p>
                  <p className="text-sm text-muted-foreground">
                    CRM ativo e documentos validados
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Medal className="size-8 text-primary" />
                <div>
                  <p className="font-medium">
                    Especialista em {doctorWithSchedules.specialties[0]?.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Mais de 15 anos de experiência
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Schedule & Evaluations */}
        <div className="md:col-span-2">
          <div className="space-y-6">
            {/* Schedule Card */}
            <div className="order-first md:order-none">
              {doctorWithSchedules.doctorSchedules?.length > 0 ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Agendar Consulta</CardTitle>
                    <CardDescription>
                      Consulta{" "}
                      {doctor.onlineStatus === "ONLINE" ? "online" : "agendada"}{" "}
                      por {formatCurrency(doctor.consultationPrice)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="calendar">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="calendar">
                          <Calendar className="mr-2 size-4" />
                          Calendário
                        </TabsTrigger>
                        <TabsTrigger value="list">
                          <CalendarDays className="mr-2 size-4" />
                          Próximos 7 dias
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="calendar" className="mt-4">
                        <div className="grid gap-6 md:grid-cols-2">
                          <CalendarComponent
                            mode="single"
                            selected={selectedDate}
                            onSelect={(date) => date && setSelectedDate(date)}
                            locale={ptBR}
                            disabled={(date) => {
                              if (isBefore(date, new Date())) return true;
                              const maxDate = addDays(new Date(), 30);
                              if (isAfter(date, maxDate)) return true;
                              const slots = getAvailableSlots(date);
                              return slots.length === 0;
                            }}
                          />

                          <div className="space-y-4">
                            <h4 className="font-medium">
                              Horários Disponíveis
                            </h4>
                            <div className="grid grid-cols-3 gap-2">
                              {availableSlots.map((slot) => (
                                <Button
                                  key={slot.toISOString()}
                                  variant={
                                    selectedTime === format(slot, "HH:mm")
                                      ? "default"
                                      : "outline"
                                  }
                                  className="w-full"
                                  onClick={() =>
                                    setSelectedTime(format(slot, "HH:mm"))
                                  }
                                >
                                  {format(slot, "HH:mm")}
                                </Button>
                              ))}
                            </div>

                            {selectedTime && (
                              <Button
                                className="mt-4 w-full"
                                onClick={() => setIsBookingModalOpen(true)}
                              >
                                Agendar para{" "}
                                {format(selectedDate, "PP", { locale: ptBR })}{" "}
                                às {selectedTime}{" "}
                                <MoveRight className="ml-1 size-5" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="list" className="mt-4">
                        <ScrollArea className="h-[400px]">
                          {Array.from({ length: 7 }).map((_, i) => {
                            const date = addDays(new Date(), i);
                            const slots = getAvailableSlots(date);

                            if (slots.length === 0) return null;

                            return (
                              <div key={i} className="mb-6 last:mb-0">
                                <h4 className="mb-2 font-medium">
                                  {format(date, "EEEE, dd 'de' MMMM", {
                                    locale: ptBR,
                                  })}
                                </h4>
                                <div className="grid grid-cols-4 gap-2">
                                  {slots.map((slot) => (
                                    <Button
                                      key={slot.toISOString()}
                                      variant={
                                        selectedDate.toDateString() ===
                                          date.toDateString() &&
                                        selectedTime === format(slot, "HH:mm")
                                          ? "default"
                                          : "outline"
                                      }
                                      className="w-full"
                                      onClick={() => {
                                        setSelectedDate(date);
                                        setSelectedTime(format(slot, "HH:mm"));
                                      }}
                                    >
                                      {format(slot, "HH:mm")}
                                    </Button>
                                  ))}
                                </div>
                              </div>
                            );
                          })}
                        </ScrollArea>

                        {selectedTime && (
                          <Button
                            className="mt-4 w-full"
                            onClick={() => setIsBookingModalOpen(true)}
                          >
                            Agendar para{" "}
                            {format(selectedDate, "PP", { locale: ptBR })} às{" "}
                            {selectedTime} <MoveRight className="ml-1 size-5" />
                          </Button>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Horários Indisponíveis</CardTitle>
                    <CardDescription>
                      Este médico ainda não configurou sua agenda de
                      atendimento.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex flex-col items-center py-8">
                    <Calendar className="size-12 text-muted-foreground" />
                    <p className="mt-4 text-center text-muted-foreground">
                      Entre em contato conosco para mais informações sobre
                      disponibilidade.
                    </p>
                    <Button className="mt-4" variant="outline">
                      <Mail className="mr-2 size-4" />
                      Entrar em Contato
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Evaluations Card */}
            {doctor.evaluations.length ? (
              <Card>
                <CardHeader>
                  <CardTitle>Avaliações Recentes</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px] pr-4">
                    {doctor.evaluations.length > 0 ? (
                      doctor.evaluations.map((evaluation) => (
                        <div
                          key={evaluation.id}
                          className="mb-4 border-b pb-4 last:border-0"
                        >
                          <div className="flex items-center gap-2">
                            <UserAvatar
                              user={evaluation.patient.user}
                              className="size-8"
                            />
                            <div>
                              <p className="font-medium">
                                {evaluation.patient.user.name}
                              </p>
                              <div className="flex items-center gap-1">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <Star
                                    key={i}
                                    className={cn(
                                      "size-4",
                                      i < evaluation.rating
                                        ? "fill-primary text-primary"
                                        : "fill-muted text-muted",
                                    )}
                                  />
                                ))}
                              </div>
                            </div>
                            <span className="ml-auto text-sm text-muted-foreground">
                              {format(new Date(evaluation.createdAt), "PP", {
                                locale: ptBR,
                              })}
                            </span>
                          </div>
                          {evaluation.comment && (
                            <p className="mt-2 text-sm text-muted-foreground">
                              {evaluation.comment}
                            </p>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="flex flex-col items-center justify-center py-8">
                        <Star className="size-12 text-muted-foreground" />
                        <p className="mt-4 text-center text-muted-foreground">
                          Este médico ainda não possui avaliações
                        </p>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            ) : null}
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      <Dialog open={isBookingModalOpen} onOpenChange={setIsBookingModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Agendamento</DialogTitle>
            <DialogDescription>
              Você está agendando uma consulta com:
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4">
            <div className="flex items-center gap-4">
              <UserAvatar user={doctor.user} className="size-12" />
              <div>
                <p className="font-medium">Dr. {doctor.user.name}</p>
                <p className="text-sm text-muted-foreground">
                  {doctorWithSchedules.specialties.map((s) => s.name).join(", ")}
                </p>
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Data</p>
                  <p className="font-medium">
                    {format(selectedDate, "PP", { locale: ptBR })}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Horário</p>
                  <p className="font-medium">{selectedTime}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Duração</p>
                  <p className="font-medium">
                    {doctor.consultationDuration} minutos
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Valor</p>
                  <p className="font-medium">
                    {formatCurrency(doctor.consultationPrice)}
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-muted p-4">
              <h4 className="mb-2 font-medium">Importante</h4>
              <ul className="list-inside list-disc space-y-2 text-sm text-muted-foreground">
                <li>
                  Entre na sala virtual 5 minutos antes do horário agendado
                </li>
                <li>Tenha uma conexão estável de internet</li>
                <li>Escolha um local silencioso e bem iluminado</li>
                <li>Retorno gratuito em até {doctor.returnPeriod} dias</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsBookingModalOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button onClick={handleBooking} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Clock className="mr-2 size-4 animate-spin" />
                  Agendando...
                </>
              ) : (
                "Confirmar"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
