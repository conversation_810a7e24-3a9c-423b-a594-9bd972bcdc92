"use client";

import React, { useState } from 'react';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Calendar } from '@ui/components/calendar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@ui/components/dialog';
import { format, isBefore, isAfter, addDays, isToday } from 'date-fns';
import { pt } from 'date-fns/locale';
import { SignedAvatar } from '../../../components/shared/signed-avatar';
import {
  Star,
  MapPin,
  CheckCircle,
  Calendar as CalendarIcon,
  Clock,
  ChevronLeft,
  ChevronRight,
  Plus,
  ShieldCheck,
  Video,
  Timer,
  DollarSign,
  User,
  Award
} from 'lucide-react';
import { cn } from "@ui/lib";
import { <PERSON> } from '../../../types/doctor';
import { getCompleteImageUrl } from '@lib/image-utils';
import { useRouter } from 'next/navigation';
import { BookingModal } from './booking-modal';

// Interface de props
interface DoctorProfileProps {
  doctor: Doctor;
}

// Componente principal
export function DoctorProfile({ doctor }: DoctorProfileProps) {
  console.log("[DOCTOR_PROFILE] Rendering doctor profile", doctor);

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [currentWeek, setCurrentWeek] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const router = useRouter();

  // Generate available time slots for the selected date
  const getAvailableSlots = (date: Date) => {
    console.log("[DOCTOR_PROFILE] Getting available slots for date:", date);
    console.log("[DOCTOR_PROFILE] Doctor schedules:", doctor.doctorSchedules);

    try {
      const dayOfWeek = date.getDay() + 1; // Adjust to match week_day (1-7)
      console.log("[DOCTOR_PROFILE] Day of week:", dayOfWeek);

      if (!doctor.doctorSchedules || doctor.doctorSchedules.length === 0) {
        console.log("[DOCTOR_PROFILE] No doctor schedules available");
        return [];
      }

      const daySchedule = doctor.doctorSchedules.find(
        (schedule) => {
          const scheduleDay = schedule.week_day || schedule.weekDay;
          const isEnabled = schedule.is_enabled || schedule.isEnabled;
          console.log("[DOCTOR_PROFILE] Checking schedule:", {
            scheduleDay,
            dayOfWeek,
            isEnabled,
            match: scheduleDay === dayOfWeek && isEnabled
          });
          return scheduleDay === dayOfWeek && isEnabled;
        }
      );

      console.log("[DOCTOR_PROFILE] Found day schedule:", daySchedule);

      if (!daySchedule) {
        console.log("[DOCTOR_PROFILE] No schedule found for this day");
        return [];
      }

      const slots: Date[] = [];
      const startTimeStr = daySchedule.start_time || daySchedule.startTime || "08:00";
      const endTimeStr = daySchedule.end_time || daySchedule.endTime || "18:00";

      console.log("[DOCTOR_PROFILE] Schedule times:", { startTimeStr, endTimeStr });

      const [startHour, startMinute] = startTimeStr.split(":").map(Number);
      const [endHour, endMinute] = endTimeStr.split(":").map(Number);

      const startDate = new Date(date);
      startDate.setHours(startHour, startMinute, 0, 0);

      const endDate = new Date(date);
      endDate.setHours(endHour, endMinute, 0, 0);

      console.log("[DOCTOR_PROFILE] Start and end times:", {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      let currentSlot = new Date(startDate);
      const slotDuration = doctor.consultationDuration || 30;

      console.log("[DOCTOR_PROFILE] Schedule blocks:", doctor.scheduleBlocks);

      while (isBefore(currentSlot, endDate)) {
        // Check if slot is in a break
        let isInBreak = false;

        if (doctor.scheduleBlocks && doctor.scheduleBlocks.length > 0) {
          isInBreak = doctor.scheduleBlocks.some((block) => {
            try {
              const breakStartStr = block.start_time || block.startTime;
              const breakEndStr = block.end_time || block.endTime;

              if (!breakStartStr || !breakEndStr) {
                console.log("[DOCTOR_PROFILE] Invalid block times:", block);
                return false;
              }

              const breakStart = new Date(breakStartStr);
              const breakEnd = new Date(breakEndStr);

              const slotInBreak = !isBefore(currentSlot, breakStart) && isBefore(currentSlot, breakEnd);

              if (slotInBreak) {
                console.log("[DOCTOR_PROFILE] Slot in break:", {
                  slot: currentSlot.toISOString(),
                  breakStart: breakStart.toISOString(),
                  breakEnd: breakEnd.toISOString()
                });
              }

              return slotInBreak;
            } catch (error) {
              console.error("[DOCTOR_PROFILE] Error checking break:", error);
              return false;
            }
          });
        }

        // Only include the slot if it's not in a break and in the future
        if (!isInBreak && isAfter(currentSlot, new Date())) {
          slots.push(new Date(currentSlot));
        }

        // Move to next slot based on consultation duration
        currentSlot = new Date(currentSlot.getTime() + slotDuration * 60 * 1000);
      }

      console.log("[DOCTOR_PROFILE] Generated slots:", slots.map(s => s.toISOString()));
      return slots;
    } catch (error) {
      console.error("[DOCTOR_PROFILE] Error generating slots:", error);
      return [];
    }
  };

  const availableSlots = getAvailableSlots(selectedDate);

  // Format time slots for display
  const formattedTimeSlots = availableSlots.map(slot =>
    format(slot, 'HH:mm')
  );

  // Function to handle day click in calendar
  const handleDaySelect = (date: Date) => {
    setSelectedDate(date);
    setSelectedSlot(null); // Reset selected slot when changing day
    console.log("[DOCTOR_PROFILE] Selected date:", date);
  };

  // Function to check if a day is disabled in the calendar
  const isDayDisabled = (date: Date) => {
    // Don't allow past dates
    if (isBefore(date, new Date()) && date.getDate() !== new Date().getDate()) {
      return true;
    }

    // Don't allow days more than 2 months in the future
    if (isBefore(addDays(new Date(), 60), date)) {
      return true;
    }

    // Only allow days that have schedules
    const dayOfWeek = date.getDay() + 1; // 1-7 (Sunday-Saturday)
    return !doctor.doctorSchedules?.some(
      (schedule) => {
        const scheduleDay = schedule.week_day || schedule.weekDay;
        const isEnabled = schedule.is_enabled || schedule.isEnabled;
        return scheduleDay === dayOfWeek && isEnabled;
      }
    );
  };

  // Function to proceed to checkout
  const proceedToCheckout = () => {
    if (!selectedSlot) return;

    setIsLoading(true);
    // Format time from "HH:mm" to use in URL
    const time = selectedSlot;
    const isoDate = selectedDate.toISOString();

    // Redirect to checkout with the new URL format
    router.push(`/checkout/${doctor.id}?date=${isoDate}&time=${time}`);
  };

  // Generate week days for the weekly calendar view
  const generateWeekDays = () => {
    const today = new Date();
    const days: Date[] = [];

    for (let i = 0; i < 7; i++) {
      const date = addDays(today, i + (currentWeek * 7));
      days.push(date);
    }

    return days;
  };

  const weekDays = generateWeekDays();

  // Get day name abbreviation
  const getDayName = (date: Date) => {
    return format(date, 'EEE', { locale: pt }).substring(0, 3);
  };

  // Navigate to previous week
  const previousWeek = () => {
    setCurrentWeek(currentWeek - 1);
  };

  // Navigate to next week
  const nextWeek = () => {
    setCurrentWeek(currentWeek + 1);
  };

  // Format week range for display
  const weekRange = `${format(weekDays[0], 'd MMM', { locale: pt })} - ${format(weekDays[6], 'd MMM', { locale: pt })}`;

  // Check if continue button should be enabled
  const canContinue = selectedSlot !== null;

  // Get the bucket name - still useful for console log maybe, but not passed down
  const avatarBucket = process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME;
  if (process.env.NODE_ENV === 'development') {
    console.log("[DoctorProfile] Avatar Bucket:", avatarBucket); // Keep for debugging info
  }
  console.log("[DoctorProfile] Doctor Avatar Path:", doctor.user?.avatarUrl);
  if (!avatarBucket) {
    // Just log error, SignedAvatar will handle missing env var
    console.error("Avatar bucket name environment variable is not set!");
  }

  // Check if doctor has ratings to show
  const hasRatings = doctor.rating && doctor.rating > 0 && doctor.totalRatings && doctor.totalRatings > 0;

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl pt-24">
      {/* Mobile Hero Section - Primeira dobra */}
      <div className="lg:hidden mb-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-primary/5 to-primary/10">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-4">
              {doctor.user?.avatarUrl ? (
                <img
                  src={`https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/${doctor.user.avatarUrl}`}
                  alt={`Dr. ${doctor.user?.name || "Médico"}`}
                  className="w-20 h-20 flex-shrink-0 rounded-full object-cover border-4 border-white shadow-md"
                />
              ) : (
                <div className="w-20 h-20 flex-shrink-0 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center text-2xl font-bold text-white border-4 border-white shadow-md">
                  {(doctor.user?.name || "M").charAt(0)}
                </div>
              )}
              <div className="flex-1">
                <h1 className="text-xl font-bold text-gray-900">{doctor.user?.name || "Médico"}</h1>
                <p className="text-primary font-medium text-sm">{doctor.specialties?.[0]?.name || "Clínico Geral"}</p>
                <div className="flex items-center mt-1 text-sm text-gray-600">
                  <MapPin size={14} className="mr-1" />
                  <span>São Paulo, SP</span>
                </div>
              </div>
            </div>

            {/* Quick Info Cards */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="text-center p-3 bg-white rounded-lg border">
                <Video className="w-5 h-5 text-primary mx-auto mb-1" />
                <p className="text-xs text-gray-600">Online</p>
                <p className="text-sm font-semibold text-gray-900">Consulta</p>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <Timer className="w-5 h-5 text-primary mx-auto mb-1" />
                <p className="text-xs text-gray-600">Duração</p>
                <p className="text-sm font-semibold text-gray-900">{doctor.consultationDuration}min</p>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <DollarSign className="w-5 h-5 text-primary mx-auto mb-1" />
                <p className="text-xs text-gray-600">Valor</p>
                <p className="text-sm font-semibold text-gray-900">R$ {doctor.consultationPrice}</p>
              </div>
            </div>

            {/* CTA Button */}
            <Button
              className="w-full bg-primary hover:bg-primary/90 text-white py-3 rounded-lg text-lg font-semibold shadow-lg"
              onClick={() => document.getElementById('booking-section')?.scrollIntoView({ behavior: 'smooth' })}
            >
              <CalendarIcon className="w-5 h-5 mr-2" />
              Agendar Consulta
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
        {/* Coluna da esquerda - Perfil do médico */}
        <div className="lg:col-span-5 space-y-6">
          {/* Desktop Profile Card */}
          <div className="hidden lg:block">
            <Card className="overflow-hidden border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  {doctor.user?.avatarUrl ? (
                    <img
                      src={`https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/${doctor.user.avatarUrl}`}
                      alt={`Dr. ${doctor.user?.name || "Médico"}`}
                      className="w-28 h-28 flex-shrink-0 rounded-full object-cover border-4 border-white shadow-md"
                    />
                  ) : (
                    <div className="w-28 h-28 flex-shrink-0 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center text-2xl font-bold text-white border-4 border-white shadow-md">
                      {(doctor.user?.name || "M").charAt(0)}
                    </div>
                  )}
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900">{doctor.user?.name || "Médico"}</h2>
                    <p className="text-primary font-medium text-lg">{doctor.specialties?.[0]?.name || "Clínico Geral"}</p>
                    <div className="flex items-center mt-2 text-sm text-gray-600">
                      <MapPin size={16} className="mr-2" />
                      <span>São Paulo, SP</span>
                    </div>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 font-mono">CRM {doctor.crm}/{doctor.crmState}</p>
                    </div>
                  </div>
                </div>

                {/* Ratings - Só mostrar se houver dados */}
                {hasRatings ? (
                  <div className="mt-6 bg-gradient-to-r from-primary/5 to-primary/10 p-4 rounded-lg border border-primary/20">
                    <div className="flex items-center">
                      <div className="text-3xl font-bold text-primary">{doctor.rating?.toFixed(1)}</div>
                      <div className="ml-3">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={18}
                              fill={i < Math.round(doctor.rating || 0) ? "currentColor" : "none"}
                              className={i < Math.round(doctor.rating || 0) ? "text-yellow-400" : "text-gray-300"}
                            />
                          ))}
                        </div>
                        <p className="text-sm text-gray-600">
                          {doctor.totalRatings} avaliações
                        </p>
                      </div>
                    </div>

                    {doctor.evaluations && doctor.evaluations.length > 0 ? (
                      <div className="mt-4 border-t border-primary/20 pt-4">
                        <p className="text-sm italic text-gray-700">
                          "{doctor.evaluations[0].comment || "Ótimo médico, muito atencioso e profissional. Recomendo!"}"
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Paciente • {format(new Date(doctor.evaluations[0].createdAt || new Date()), 'dd/MM/yyyy')}
                        </p>
                      </div>
                    ): null}
                  </div>
                ): null}

                {/* About Section */}
                <div className="mt-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <User className="w-5 h-5 text-primary" />
                    <h3 className="font-semibold text-lg text-gray-900">Sobre o Médico</h3>
                  </div>

                  <div>
                    {(() => {
                      const biography = doctor.biography || `Dr. ${doctor.user?.name} é médico CRM ${doctor.crm}/${doctor.crmState}, especialista em ${doctor.specialties?.[0]?.name || "Clínico Geral"}. Com vasta experiência em atendimento de urgência e emergência, está pronto para atender suas necessidades de saúde através da nossa plataforma digital.`;
                      const shouldTruncate = biography.length > 200;
                      const displayText = shouldTruncate && !isExpanded ? biography.slice(0, 200) + '...' : biography;

                      return (
                        <>
                          <p className="text-gray-700 text-sm leading-relaxed">
                            {displayText}
                          </p>
                          {shouldTruncate && (
                            <button
                              onClick={() => setIsExpanded(!isExpanded)}
                              className="text-primary text-sm font-medium hover:text-primary/80 mt-2"
                            >
                              {isExpanded ? 'Ver menos' : 'Ver mais'}
                            </button>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Highlights Card */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-sky-50 order-last lg:order-none">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <Award className="w-5 h-5" />
                Destaques Profissionais
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start">
                <ShieldCheck size={20} className="text-blue-600 mr-3 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-blue-800">Credenciais Verificadas</h4>
                  <p className="text-sm text-blue-700 leading-relaxed">
                    Registro profissional CRM ativo com validação completa de documentos e certificações médicas
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <Award size={20} className="text-blue-600 mr-3 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-blue-800">Selo de Qualidade ZapVida</h4>
                  <p className="text-sm text-blue-700 leading-relaxed">
                    Profissional aprovado em nossa rigorosa verificação de credenciais
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coluna da direita - Agendamento */}
        <div className="lg:col-span-7" id="booking-section">
          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl text-gray-900">Agendar Consulta Online</CardTitle>
              <p className="text-gray-600 text-base">
                O ZapVida conecta você diretamente com o médico para agendar sua consulta
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Detalhes do agendamento */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <span className="font-medium text-blue-900">Consulta Médica</span>
                  <Badge className="bg-blue-100 text-blue-800 border-blue-200 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Disponível
                  </Badge>
                </div>
                <p className="text-sm text-blue-700">
                  Consulta disponível na rede ZapVida
                </p>
              </div>

              {/* Calendário Semanal */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CalendarIcon size={20} className="text-primary" />
                    <h3 className="font-semibold text-lg text-gray-900">Selecione a Data</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="icon" className="h-9 w-9" onClick={previousWeek}>
                      <ChevronLeft size={16} />
                    </Button>
                    <Button variant="outline" size="icon" className="h-9 w-9" onClick={nextWeek}>
                      <ChevronRight size={16} />
                    </Button>
                  </div>
                </div>

                <div className="text-center text-sm text-gray-600 mb-3">
                  {weekRange}
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {weekDays.map((day, index) => (
                    <div key={index} className="text-center">
                      <p className="text-xs text-gray-500 mb-2 font-medium">{getDayName(day)}</p>
                      <Button
                        variant={format(selectedDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd') ? "default" : "outline"}
                        className={cn(
                          "w-full h-16 py-2 px-0 flex flex-col items-center justify-center",
                          isDayDisabled(day) ? "opacity-50 cursor-not-allowed bg-gray-100" : "",
                          format(selectedDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd') ? "bg-primary text-white shadow-lg scale-105" : "hover:bg-primary/5"
                        )}
                        disabled={isDayDisabled(day)}
                        onClick={() => handleDaySelect(day)}
                      >
                        <span className={cn(
                          "text-lg font-bold",
                          isToday(day) && format(selectedDate, 'yyyy-MM-dd') !== format(day, 'yyyy-MM-dd') ? "text-primary" : ""
                        )}>
                          {format(day, 'd')}
                        </span>
                        {isToday(day) && (
                          <span className="text-xs text-white font-medium">Hoje</span>
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Horários Disponíveis */}
              {availableSlots.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Clock size={20} className="text-primary" />
                    <h3 className="font-semibold text-lg text-gray-900">Horários Disponíveis</h3>
                  </div>
                  <div className="grid grid-cols-3 sm:grid-cols-4 gap-3">
                    {formattedTimeSlots.map((time, index) => (
                      <Button
                        key={index}
                        variant={selectedSlot === time ? "default" : "outline"}
                        className={cn(
                          "h-12 text-base font-medium transition-all duration-200",
                          selectedSlot === time
                            ? "bg-primary text-white shadow-lg scale-105"
                            : "text-gray-700 hover:bg-primary/5 hover:border-primary/30"
                        )}
                        onClick={() => setSelectedSlot(time)}
                      >
                        {time}
                      </Button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                  <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 font-medium">Nenhum horário disponível</p>
                  <p className="text-sm text-gray-400 mt-1">Tente selecionar outra data</p>
                </div>
              )}

              {/* Botão de Continuar */}
              <div className="pt-4">
                <Button
                  className={cn(
                    "w-full py-4 text-lg font-semibold rounded-lg transition-all duration-200",
                    canContinue
                      ? "bg-primary hover:bg-primary/90 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                  )}
                  disabled={!canContinue}
                  onClick={() => setShowConfirmation(true)}
                >
                  {canContinue ? (
                    <>
                      <CalendarIcon className="w-5 h-5 mr-2" />
                      Continuar
                    </>
                  ) : (
                    "Selecione um horário"
                  )}
                </Button>

                {canContinue && (
                  <p className="text-xs text-center text-gray-500 mt-3">
                    Ao clicar em continuar, você será direcionado para o checkout
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal de Confirmação */}
      <BookingModal
        doctor={doctor}
        selectedDate={selectedDate}
        selectedTime={selectedSlot || ""}
        isOpen={showConfirmation}
        setIsOpen={setShowConfirmation}
        onConfirm={proceedToCheckout}
        isLoading={isLoading}
      />
    </div>
  );
}
