"use client";

import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  BadgeCheck,
  CalendarClock,
  Clock,
  Filter,
  Search,
  Star,
  Timer,
  Users,
} from "lucide-react";
import { useForm } from "react-hook-form";


import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";

import { UserAvatar } from "@marketing/shared/components/UserAvatar";
import { DoctorFilters, doctorFiltersSchema } from "../../../actions/doctors/types";
import { formatCurrency } from "../../../lib/utils";
import { cn } from "@ui/lib";
import { Link } from "@i18n/routing";

// Helper function to format price
// const formatPrice = (price: number | string) => {
//   return new Intl.NumberFormat('pt-BR', {
//     style: 'currency',
//     currency: 'BRL'
//   }).format(Number(price));
// };

interface DoctorsListPageProps {
  initialDoctors: any[];
  specialties: any[];
  onlineCount: number;
  total: number;
}

export default function DoctorsListPage({
  initialDoctors,
  specialties,
  onlineCount,
  total,
}: DoctorsListPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSearching, setIsSearching] = React.useState(false);

  // Form com validação Zod
  const form = useForm<DoctorFilters>({
    resolver: zodResolver(doctorFiltersSchema),
    defaultValues: {
      query: searchParams.get("query") || "",
      specialtyId: searchParams.get("specialty") || "",
      onlineOnly: searchParams.get("online") === "true",
      page: 1,
    },
  });

  // Função para atualizar a URL com os filtros
  const updateSearch = (values: DoctorFilters) => {
    setIsSearching(true);
    const params = new URLSearchParams();

    if (values.query) params.set("query", values.query);
    if (values.specialtyId) params.set("specialty", values.specialtyId);
    if (values.onlineOnly) params.set("online", "true");

    router.push(`/doctors?${params.toString()}`);
    setIsSearching(false);
  };

  const onSubmit = (data: DoctorFilters) => {
    updateSearch(data);
  };

  const goToDoctorProfile = (doctorId: string) => {
    router.push(`/doctors/${doctorId}`);
  };

  return (
    <div className="min-h-screen bg-background pt-24">
      {/* Hero Section com Filtros */}
      <section className="border-b bg-muted/30 py-8 pb-12">
        <div className="container mx-auto px-4 md:px-9">
          <div className="mb-6 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Encontre seu Médico</h1>
              <p className="mt-2 text-muted-foreground">
                {total} médicos disponíveis, {onlineCount} online agora
              </p>
            </div>
          </div>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-3 md:grid-cols-12">
              {/* Busca por nome */}
              <div className="relative md:col-span-6">
                <Search className="absolute left-3 top-3 size-4 text-muted-foreground" />
                <Input
                  {...form.register("query")}
                  placeholder="Buscar por nome ou CRM..."
                  className="pl-9 bg-white"
                />
              </div>

              {/* Filtro de Especialidade */}
              <div className="md:col-span-4 bg-white rounded-md">
                <Select
                  value={form.watch("specialtyId")}
                  onValueChange={(value) => form.setValue("specialtyId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Especialidade" />
                  </SelectTrigger>
                  <SelectContent className="bg-white">
                    {specialties.map((specialty) => (
                      <SelectItem key={specialty.id} value={specialty.id}>
                        {specialty.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>



              {/* Botão de Buscar */}
              <div className="md:col-span-2">
                <Button
                  type="submit"
                  className="w-full gap-2"
                  disabled={isSearching}
                  variant="secondary"
                >
                  <Search className="size-4" />
                  Buscar
                </Button>
              </div>
            </div>
          </form>
        </div>
      </section>

      {/* Lista de Médicos */}
      <section className="container mx-auto py-8">
        {initialDoctors.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {initialDoctors.map((doctor) => (
            <Link    key={doctor.id} href={`/doctors/${doctor.id}`} prefetch={false}>
            <Card

                className="cursor-pointer"
                // onClick={() => goToDoctorProfile(doctor.id)}
              >
                <CardContent className="p-4 pt-6">
                  <div className="flex gap-4 ">
                    {/* Avatar do Médico */}
                    <UserAvatar user={doctor.user} className="size-20" />

                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{doctor.user.name}</h3>
                        <Badge

                          className={cn( "rounded-full", doctor.onlineStatus === "ONLINE" && "bg-success/10")}
                        >
                          {doctor.onlineStatus === "ONLINE"
                            ? "Online"
                            : "Offline"}
                        </Badge>
                      </div>

                      <div className="mt-1 flex flex-wrap gap-1">
                        {doctor.specialties.map((specialty) => (
                          <Badge
                            key={specialty.id}
                            className="text-xs"
                          >
                            {specialty.name}
                          </Badge>
                        ))}
                      </div>


{
  doctor?.rating ? (
    <div className="mt-2 flex items-center gap-2">
                        <Star className="size-4 fill-primary text-primary" />
                        <span className="text-sm">
                          {doctor.rating ? doctor.rating.toFixed(1) : ' '}
                        </span>
                      </div>
  ) : null
}

                    </div>
                  </div>

                  {/* Informações Adicionais */}
                  <div className="mt-4 grid grid-cols-2 gap-2 border-t pt-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Timer className="size-4 text-muted-foreground" />
                      <span>{doctor.consultationDuration}min</span>
                    </div>

                    {/* <div className="flex items-center gap-2">
                      <Users className="size-4 text-muted-foreground" />
                      <span>{doctor.totalRatings} pacientes</span>
                    </div> */}
                  </div>

                  <div className="mt-4 flex items-center justify-between border-t pt-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Consulta por
                      </p>
                      <p className="text-lg font-semibold">
                        {formatCurrency(doctor.consultationPrice)}
                      </p>
                    </div>

                    {doctor.onlineStatus === "ONLINE" ? (
                      <Button onClick={() => goToDoctorProfile(doctor.id)}>
                        Agendar Agora
                      </Button>
                    ) : (
                      <Button
                        onClick={() => goToDoctorProfile(doctor.id)}
                        variant="outline"
                      >
                        <CalendarClock className="mr-2 size-4" />
                        Ver Agenda
                      </Button>
                    )}
                  </div>
                </CardContent>

             </Card>      </Link>
            ))}
          </div>
        ) : (
          <div className="flex min-h-[300px] flex-col items-center justify-center">
            <h3 className="text-xl font-semibold">Nenhum médico encontrado</h3>
            <p className="text-muted-foreground">
              Tente ajustar seus filtros de busca
            </p>
          </div>
        )}
      </section>
    </div>
  );
}
