'use client';

import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Clock, Calendar, Timer, DollarSign, User, CheckCircle } from 'lucide-react';

import { formatCurrency } from '../../../lib/utils';
import { Button } from '@ui/components/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@ui/components/dialog';
import { UserAvatar } from '@marketing/shared/components/UserAvatar';
import { Badge } from '@ui/components/badge';

interface BookingModalProps {
	doctor: any;
	selectedDate: Date;
	selectedTime: string;
	isOpen: boolean;
	setIsOpen: (open: boolean) => void;
	onConfirm: () => void;
	isLoading: boolean;
}

export function BookingModal({
	doctor,
	selectedDate,
	selectedTime,
	isOpen,
	setIsOpen,
	onConfirm,
	isLoading,
}: BookingModalProps) {
	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogContent className="max-w-md">
				<DialogHeader className="text-center">
					<DialogTitle className="text-xl text-gray-900">Confirmar Agendamento</DialogTitle>
					<DialogDescription className="text-gray-600">
						Você está agendando uma consulta com:
					</DialogDescription>
				</DialogHeader>

				<div className='space-y-6'>
					{/* Doctor Info */}
					<div className='flex items-center gap-4 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20'>
						<UserAvatar user={doctor.user} className='size-16' />
						<div className="flex-1">
							<p className='font-semibold text-lg text-gray-900'>Dr. {doctor.user.name}</p>
							<p className='text-sm text-gray-600'>
								{doctor.specialties?.map((s: any) => s.name).join(', ')}
							</p>
							<div className='w-fit'>
								<Badge className="mt-2 bg-primary/20 text-primary border-primary/30 flex items-center">
								<CheckCircle className="w-3 h-3 mr-1" />
								Disponível
							</Badge>
							</div>
						</div>
					</div>

					{/* Appointment Details */}
					<div className='space-y-4'>
						<h4 className="font-semibold text-gray-900 flex items-center gap-2">
							<Calendar className="w-5 h-5 text-primary" />
							Detalhes da Consulta
						</h4>

						<div className='grid grid-cols-2 gap-4'>
							<div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
								<Calendar className="w-5 h-5 text-blue-600 mx-auto mb-1" />
								<p className="text-xs text-blue-600">Data</p>
								<p className="font-semibold text-blue-900">
									{format(selectedDate, 'dd MMM yyyy', { locale: ptBR })}
								</p>
							</div>

							<div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
								<Clock className="w-5 h-5 text-green-600 mx-auto mb-1" />
								<p className="text-xs text-green-600">Horário</p>
								<p className="font-semibold text-green-900">{selectedTime}</p>
							</div>

							<div className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
								<Timer className="w-5 h-5 text-purple-600 mx-auto mb-1" />
								<p className="text-xs text-purple-600">Duração</p>
								<p className="font-semibold text-purple-900">
									{doctor.consultationDuration} min
								</p>
							</div>

							<div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
								<DollarSign className="w-5 h-5 text-orange-600 mx-auto mb-1" />
								<p className="text-xs text-orange-600">Valor</p>
								<p className="font-semibold text-orange-900">
									{formatCurrency(doctor.consultationPrice)}
								</p>
							</div>
						</div>
					</div>

					{/* Important Information */}
					<div className='rounded-lg bg-amber-50 border border-amber-200 p-4'>
						<h4 className='mb-3 font-semibold text-amber-800 flex items-center gap-2'>
							<Clock className="w-4 h-4" />
							Informações Importantes
						</h4>
						<ul className='space-y-2 text-sm text-amber-700'>
							<li className="flex items-start gap-2">
								<span className="w-1.5 h-1.5 bg-amber-500 rounded-full mt-2 flex-shrink-0"></span>
								Entre na sala virtual 5 minutos antes do horário
							</li>
							<li className="flex items-start gap-2">
								<span className="w-1.5 h-1.5 bg-amber-500 rounded-full mt-2 flex-shrink-0"></span>
								Tenha uma conexão estável de internet
							</li>
							<li className="flex items-start gap-2">
								<span className="w-1.5 h-1.5 bg-amber-500 rounded-full mt-2 flex-shrink-0"></span>
								Escolha um local silencioso e bem iluminado
							</li>
						</ul>
					</div>
				</div>

				<DialogFooter className='flex gap-3 pt-4'>
					<Button
						variant='outline'
						onClick={() => setIsOpen(false)}
						disabled={isLoading}
						className="flex-1"
					>
						Cancelar
					</Button>
					<Button
						onClick={onConfirm}
						disabled={isLoading}
						className="flex-1 bg-primary hover:bg-primary/90 shadow-lg"
					>
						{isLoading ? (
							<>
								<Clock className="mr-2 size-4 animate-spin" />
								Agendando...
							</>
						) : (
							'Confirmar'
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
