import { Link } from "@i18n/routing";
import { Logo } from "@shared/components/Logo";
import { FaInstagram, FaFacebook, FaWhatsapp, FaPhone, FaArrowUp } from "react-icons/fa";

export function FooterNew() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#001A2C] py-12 text-white relative">
      <div className="container mx-auto px-4">
        {/* Top section with logo and description */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
          <div>
            <img src="/images/logo-zapvida.png" alt="Logo ZapVida"  />
            {/* <Logo withLabel={true} /> */}
            <p className="mt-4 text-sm max-w-md">
              ZapVida é uma plataforma de atendimento médico online e oferece cuidado integral para quem necessita em qualquer circunstância e momento, podendo ser atendido em qualquer horário e local.
            </p>
          </div>

          {/* Contact information */}
          <div className="lg:col-span-1 md:ml-12">
            <h3 className="text-[#71CBE8] font-semibold mb-4">Suporte por e-mail</h3>
            <p className="mb-2"><EMAIL></p>
            <p className="mb-4">(47) 99770-8518</p>

            {/* Social media icons */}
            <div className="flex space-x-3">
              <a href="https://instagram.com/zapvida" aria-label="Instagram" className="bg-[#71CBE8] rounded-full p-2 flex items-center justify-center hover:bg-opacity-80 transition-all">
                <FaInstagram className="text-[#001A2C]" />
              </a>
              <a href="https://facebook.com/zapvida" aria-label="Facebook" className="bg-[#71CBE8] rounded-full p-2 flex items-center justify-center hover:bg-opacity-80 transition-all">
                <FaFacebook className="text-[#001A2C]" />
              </a>
              <a href="https://wa.me/5547997708518" aria-label="WhatsApp" className="bg-[#71CBE8] rounded-full p-2 flex items-center justify-center hover:bg-opacity-80 transition-all">
                <FaWhatsapp className="text-[#001A2C]" />
              </a>
            </div>
          </div>

          {/* Emergency contact */}
          <div className="bg-[#71CBE8]/10 rounded-lg p-6 text-center flex flex-col items-center">
            <div className="bg-[#5AE14A] rounded-full w-16 h-16 flex items-center justify-center mb-3">
              <FaWhatsapp className="text-white text-4xl" />
            </div>
            <h3 className="text-white font-bold text-xl mb-2">Emergência?</h3>
            <p className="text-white font-semibold">(47) 99770-8518</p>
          </div>
        </div>

        {/* Navigation links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 py-8 border-t border-b border-gray-700">
          <div>
            <h3 className="text-[#71CBE8] font-semibold mb-4">Institucional</h3>
            <ul className="space-y-2">
              <li><Link href="/sobre" className="hover:text-[#71CBE8] transition-colors">Sobre nós</Link></li>
              <li><Link href="/como-funciona" className="hover:text-[#71CBE8] transition-colors">Como funciona</Link></li>
              <li><Link href="/especialidades" className="hover:text-[#71CBE8] transition-colors">Especialidades</Link></li>
              <li><Link href="/blog" className="hover:text-[#71CBE8] transition-colors">Blog</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-[#71CBE8] font-semibold mb-4">Para Pacientes</h3>
            <ul className="space-y-2">
              <li><Link href="/consultas" className="hover:text-[#71CBE8] transition-colors">Agendar Consulta</Link></li>
              <li><Link href="/planos" className="hover:text-[#71CBE8] transition-colors">Planos e Preços</Link></li>
              <li><Link href="/faq" className="hover:text-[#71CBE8] transition-colors">Perguntas Frequentes</Link></li>
              <li><Link href="/contato" className="hover:text-[#71CBE8] transition-colors">Contato</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-[#71CBE8] font-semibold mb-4">Para Médicos</h3>
            <ul className="space-y-2">
              <li><Link href="/medicos/cadastro" className="hover:text-[#71CBE8] transition-colors">Seja um Médico Parceiro</Link></li>
              <li><Link href="/medicos/beneficios" className="hover:text-[#71CBE8] transition-colors">Benefícios</Link></li>
              <li><Link href="/medicos/como-funciona" className="hover:text-[#71CBE8] transition-colors">Como Funciona</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-[#71CBE8] font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><Link href="/legal/terms" className="hover:text-[#71CBE8] transition-colors">Termos de Uso</Link></li>
              <li><Link href="/legal/privacy" className="hover:text-[#71CBE8] transition-colors">Política de Privacidade</Link></li>
              <li><Link href="/legal/cookies" className="hover:text-[#71CBE8] transition-colors">Política de Cookies</Link></li>
              <li><Link href="/legal/lgpd" className="hover:text-[#71CBE8] transition-colors">LGPD</Link></li>
            </ul>
          </div>
        </div>

        {/* Compliance notice */}
        {/* <div className="mt-8 p-4 bg-[#002A40] rounded-lg text-sm">
          <p className="mb-2"><strong>Aviso Importante:</strong> A ZapVida é uma plataforma de telemedicina que conecta pacientes a profissionais de saúde qualificados.</p>
          <p className="mb-2">Os serviços oferecidos através da ZapVida não substituem o atendimento presencial em casos de emergência. Em caso de emergência médica, procure imediatamente o serviço de emergência mais próximo ou ligue para 192 (SAMU).</p>
          <p>A ZapVida segue as diretrizes do Conselho Federal de Medicina (CFM) e está em conformidade com a Resolução CFM Nº 2.314/2022, que regulamenta a telemedicina no Brasil.</p>
        </div> */}

        {/* Copyright and back to top */}
        <div className="flex flex-col md:flex-row justify-between items-center mt-8">
          <p className="text-sm opacity-70">
            Copyright © ZapVida {currentYear}. Todos os direitos reservados.
          </p>
          <a
            href="#top"
            className="mt-4 md:mt-0 bg-[#71CBE8]/30 text-[#001A2C] px-4 py-2 rounded flex items-center hover:bg-opacity-80 transition-all"
          >
            SUBIR <FaArrowUp className="ml-2" />
          </a>
        </div>
      </div>
    </footer>
  );
}
