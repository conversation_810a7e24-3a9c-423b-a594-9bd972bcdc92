import { User } from "@prisma/client"
import { AvatarProps } from "@radix-ui/react-avatar"
import { useMemo } from "react"

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar"
import { UserIcon } from "lucide-react"

interface UserAvatarProps extends AvatarProps {
  user: Pick<User, "avatarUrl" | "name">
}

export function UserAvatar({ user, ...props }: UserAvatarProps) {
  // Usando a mesma lógica exata do componente que funciona
  const avatarSrc = useMemo(() => {
    if (!user.avatarUrl) return undefined;

    const supabasePrefix = "https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/";

    // Check if avatarUrl already contains the prefix
    if (user.avatarUrl.startsWith("http")) {
      return user.avatarUrl;
    }

    // Apply the working Supabase URL pattern
    return `${supabasePrefix}${user.avatarUrl}`;
  }, [user.avatarUrl]);

  return (
    <Avatar {...props}>
      {avatarSrc && (
        <AvatarImage alt={user.name || "User avatar"} src={avatarSrc} referrerPolicy="no-referrer" />
      )}
      <AvatarFallback className="bg-primary/10 text-primary">
        <span className="sr-only">{user.name}</span>
        <UserIcon className="size-8" />
      </AvatarFallback>
    </Avatar>
  )
}
