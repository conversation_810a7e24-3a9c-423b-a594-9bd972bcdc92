'use client';

import { config } from '@config';
import { Link, usePathname } from '@i18n/routing';
import { useUser } from '@saas/auth/hooks/use-user';
import { UserAvatar } from '@shared/components/UserAvatar';
import { useRouter } from '@shared/hooks/router';
import { apiClient } from '@shared/lib/api-client';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuPortal,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';
import { useToast } from '@ui/hooks/use-toast';
import {
	EllipsisVertical,
	HardDriveIcon,
	LogOutIcon,
	MoonIcon,
	SettingsIcon,
	SunIcon,
	UserRoundXIcon,
} from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';

const { locales } = config.i18n;

export function UserMenu() {
	const router = useRouter();
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const currentLocale = useLocale();
	const t = useTranslations();
	const { user, logout } = useUser();
	const { toast } = useToast();
	const [locale, setLocale] = useState<string>(currentLocale);
	const { setTheme: setCurrentTheme, theme: currentTheme } = useTheme();
	const [theme, setTheme] = useState<string>(currentTheme ?? 'system');

	const unimpersonateMutation = apiClient.admin.unimpersonate.useMutation();

	const colorModeOptions = [
		{
			value: 'system',
			label: 'System',
			icon: HardDriveIcon,
		},
		{
			value: 'light',
			label: 'Light',
			icon: SunIcon,
		},
		{
			value: 'dark',
			label: 'Dark',
			icon: MoonIcon,
		},
	];

	if (!user) {
		return null;
	}

	const { name, email, avatarUrl, role } = user;

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				<div className='flex  items-center gap-3 justify-between cursor-pointer'>
					<div className='flex  items-center gap-3'>
						<button
							type='button'
							className='rounded-full outline-none focus-visible:ring-2 focus-visible:ring-primary'
							aria-label='User menu'
						>
							<UserAvatar name={name ?? ''} avatarUrl={avatarUrl} />
						</button>

						<div>
							{name}
							<span className='block font-normal text-xs opacity-70'>
								{role ? t(('dashboard.roles.' + role) as any) : role}
							</span>
						</div>
					</div>

					<div>
						<EllipsisVertical className='mr-2 size-4' />
					</div>
				</div>
			</DropdownMenuTrigger>

			<DropdownMenuContent align='end'>
				<DropdownMenuLabel>
					{name}
					<span className='block font-normal text-xs opacity-70'>{email}</span>
				</DropdownMenuLabel>

				<DropdownMenuSeparator />

				{/* Color mode selection */}
				{/* <DropdownMenuSub>
					<DropdownMenuSubTrigger>
						<SunIcon className="mr-2 size-4" />
					Mudar cor
					</DropdownMenuSubTrigger>
					<DropdownMenuPortal>
						<DropdownMenuSubContent>
							<DropdownMenuRadioGroup
								value={theme}
								onValueChange={(value) => {
									setTheme(value);
									setCurrentTheme(value);
								}}
							>
								{colorModeOptions.map((option) => (
									<DropdownMenuRadioItem
										key={option.value}
										value={option.value}
									>
										<option.icon className="mr-2 size-4 opacity-50" />
										{option.label}
									</DropdownMenuRadioItem>
								))}
							</DropdownMenuRadioGroup>
						</DropdownMenuSubContent>
					</DropdownMenuPortal>
				</DropdownMenuSub>

				<DropdownMenuSeparator /> */}

				<DropdownMenuItem asChild>
					<Link href='/app/settings/account/general'>
						<SettingsIcon className='mr-2 size-4' />
						{t('dashboard.userMenu.accountSettings')}
					</Link>
				</DropdownMenuItem>

				{user.impersonatedBy && (
					<DropdownMenuItem
						onClick={async () => {
							const { dismiss } = toast({
								variant: 'loading',
								title: t('admin.users.impersonation.unimpersonating'),
							});
							await unimpersonateMutation.mutateAsync();
							dismiss();
							window.location.reload();
						}}
					>
						<UserRoundXIcon className='mr-2 size-4' />
						{t('dashboard.userMenu.unimpersonate')}
					</DropdownMenuItem>
				)}

				<DropdownMenuItem onClick={logout}>
					<LogOutIcon className='mr-2 size-4' />
					{t('dashboard.userMenu.logout')}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
