'use client';

import { usePathname } from "next/navigation";
import { FaWhatsapp } from "react-icons/fa";

export default function WhatsAppWidget() {
  const pathName = usePathname();

  if (pathName.includes("/patient")) {
    return null;
  }

  return (
    <div className="fixed bottom-6 right-6 flex flex-col items-center z-50">
      {/* Botão redondo com ícone */}
      <button
        onClick={() =>
          window.open(
            "https://api.whatsapp.com/send/?phone=554797708518&text=Gostaria+de+iniciar+o+atendimento",
            "_blank"
          )
        }
        className="w-14 h-14 flex items-center justify-center rounded-full bg-[#25D366] text-white shadow-lg hover:bg-green-500 transition-all duration-200"
        aria-label="Chat no WhatsApp"
      >
        <FaWhatsapp size={28} />
      </button>

      {/* Texto separado abaixo */}
      <span className="mt-2 text-[14px] font-semibold text-white bg-[#25D366] px-3 py-1 rounded-md shadow-md text-center">
        Iniciar consulta<br />pelo WhatsApp já
      </span>
    </div>
  );
}