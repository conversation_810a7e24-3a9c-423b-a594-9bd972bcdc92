'use client';

import Image from 'next/image';
import Link from 'next/link';
import { ArrowRight, CheckCircle2 } from 'lucide-react';
import { Button } from '@ui/components/button';
import { CallToActionButton } from '@shared/components/CallToActionButton';
import { ON_DUTY_DOCTORS } from 'api/constants/on-duty';

export function FarmaciaHero({ whatsappLink }: { whatsappLink: string }) {
	return (
		<div className='relative overflow-hidden bg-secondary pb-12 pt-24 md:pb-20 lg:pb-28 lg:pt-11'>
			<div className='relative mx-auto flex max-w-7xl flex-col items-center gap-4 px-4 sm:px-6 lg:px-8'>
				{/* Background Effects */}
				<div className='absolute inset-0 overflow-hidden'>
					<div className='absolute left-1/2 top-0 -z-10 h-[1000px] w-[1000px] -translate-x-1/2 rounded-full bg-[#00A3E0]/20 blur-3xl' />
				</div>

				{/* Content Grid */}
				<div className='relative grid w-full gap-8 pt-0 lg:grid-cols-2 lg:gap-12 lg:pt-16'>
					{/* Left Column */}
					<div className='flex flex-col justify-center'>
						<div className='flex flex-col gap-6'>
							{/* Badge */}
							<div className='w-fit rounded-full bg-white/10 px-4 py-1 text-sm text-white backdrop-blur-sm'>
								Parceria ZapVida
							</div>

							{/* Main Text */}
							<h1 className='font-urban text-4xl font-bold tracking-tight text-white sm:text-4xl md:text-5xl'>
								Ofereça consultas médicas rápidas em sua Farmácia
							</h1>

							<p className='max-w-xl text-lg text-white/80'>
								Sem custos para a farmácia e com diversas vantagens para seus
								clientes.
							</p>

							{/* Features List */}
							<div className='flex flex-col gap-3'>
								<div className='flex items-center gap-2 text-sm text-white/80'>
									<CheckCircle2 className='h-5 w-5 text-green-500' />
									<span>Aumente o fluxo de clientes na sua farmácia</span>
								</div>
								<div className='flex items-center gap-2 text-sm text-white/80'>
									<CheckCircle2 className='h-5 w-5 text-green-500' />
									<span>Diferencie-se da concorrência com telemedicina</span>
								</div>
								<div className='flex items-center gap-2 text-sm text-white/80'>
									<CheckCircle2 className='h-5 w-5 text-green-500' />
									<span>Atendimento garantido em até 20 minutos</span>
								</div>

								<div className='flex items-center gap-2 text-sm text-white/80'>
									<CheckCircle2 className='h-5 w-5 text-green-500' />
									<span>Fácil implementação com QR code</span>
								</div>
							</div>

							{/* CTA Card */}
							<div className='w-full rounded-xl bg-white/10 p-10 backdrop-blur-sm'>
								<p className='mb-1 text-sm font-medium text-white/80'>
									Consulta para seus clientes por apenas
								</p>
								<div className='mb-3 text-4xl font-bold text-white'>R$ 69</div>

								<CallToActionButton
									className='w-full md:w-auto'
									href={whatsappLink}
									target='_blank'
								>
									FALE COM NOSSO FARMACÊUTICO
								</CallToActionButton>
							</div>
						</div>
					</div>

					{/* Right Column - Images */}
					<div className='relative hidden max-h-[400px] w-full lg:block'>
						{/* Main Image */}
						<div className='relative aspect-[3/4] overflow-hidden rounded-xl   flex items-center justify-center'>
							{/* Replace with actual image when available */}
							<Image
								src='/images/farmacia-hero.png'
								alt='Parceria Farmácia ZapVida'
								fill
								className='object-cover'
								priority
							/>
						</div>

						{/* Partner Status */}
						<div className='absolute right-8 top-6 flex items-center gap-2 rounded-full bg-white px-4 py-2 shadow-lg'>
							<div className='h-2.5 w-2.5 animate-pulse rounded-full bg-green-500' />
							<span className='text-sm font-medium'>Parceria Farmácia</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
