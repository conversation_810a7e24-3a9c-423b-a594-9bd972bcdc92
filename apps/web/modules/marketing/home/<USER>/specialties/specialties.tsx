"use client";

import React from "react";
import Link from "next/link";
import { ChevronRight, Search, StethoscopeIcon } from "lucide-react";

import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { Separator } from "@ui/components/separator";

import { mockSpecialties, Specialty } from "./specialties-mock-data";

// Componente de Card de Especialidade
const SpecialtyCard = ({ specialty }: { specialty: Specialty }) => {
  return (
    <Link href={`/doctors?specialty=${specialty.slug}`}>
      <Card className="h-full cursor-pointer border-l-4 border-l-primary transition-all hover:shadow-md">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <h3 className="text-lg font-medium text-primary">
                {specialty.title}
              </h3>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </div>

            <p className="line-clamp-2 text-sm text-muted-foreground">
              {specialty.description}
            </p>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

// Componente Principal
export default function SpecialtiesPage() {
  const [searchTerm, setSearchTerm] = React.useState("");

  const filteredSpecialties = React.useMemo(() => {
    if (!searchTerm) return mockSpecialties;

    return mockSpecialties
      .map((group) => ({
        letter: group.letter,
        items: group.items.filter(
          (specialty) =>
            specialty.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            specialty.description
              .toLowerCase()
              .includes(searchTerm.toLowerCase()),
        ),
      }))
      .filter((group) => group.items.length > 0);
  }, [searchTerm]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4">
          <div className="py-8">
            <div className="my-6 flex items-center gap-3">
              <h1 className="text-3xl font-bold">Especialidades Médicas</h1>
            </div>

            <div className="max-w-2xl">
              <p className="mb-6 text-lg text-muted-foreground">
                Encontre o especialista ideal para seu atendimento entre nossas
                diversas áreas médicas disponíveis
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo Principal */}
      <div className="container mx-auto px-4 py-12">
        <div className="space-y-12">
          {filteredSpecialties.map((group) => (
            <div key={group.letter}>
              {/* Grid de Cards */}
              <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {group.items.map((specialty) => (
                  <SpecialtyCard key={specialty.id} specialty={specialty} />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
