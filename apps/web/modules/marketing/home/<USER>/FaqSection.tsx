import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@ui/components/accordion";
import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";

export function FaqSection({ className }: { className?: string }) {
	const t = useTranslations();

	const items = [
		{
			question: "O que é Telemedicina e como funciona?",
			answer:
				"A Telemedicina é o uso de tecnologias de comunicação para prestar serviços de saúde à distância. No ZapVida, oferecemos consultas online com médicos qualificados, através de chat, em consultas do tipo assincronas, ou seja, conversas com tempo e resposta de chat. Você pode realizar sua consulta atraves de nosso site.",
		},
		{
			question: "Quais são os benefícios da Telemedicina?",
			answer: [
				"<p><strong>Praticidade:</strong> Você consulta um médico de onde estiver, sem precisar se deslocar ou enfrentar filas.</p>",
				"<p><strong>Agilidade:</strong> Realizamos sua consulta rapidamente e você recebe o atendimento no conforto de casa.</p>",
				"<p><strong>Acessibilidade:</strong> Consultas com preços acessíveis e o plano ZapVida Sempre com 2 consultas por mês por R$ 49 que cabe no seu bolso.</p>",
				"<p><strong>Segurança:</strong> Nossas plataformas são seguras e protegem seus dados de acordo com a LGPD.</p>",
				"<p><strong>Comodidade:</strong> Você recebe a receita médica eletronicamente e pode comprar os medicamentos online com desconto.</p>",
			].join(""),
		},
		{
			question: "Como funciona a consulta assíncrona por chat e audio na telemedicina?",
			answer: [
				"<p>A consulta assíncrona por chat e audio na telemedicina oferece uma maneira flexível e conveniente de se conectar com um profissional de saúde, utilizando ferramentas de comunicação textuais e de voz para realizar consultas remotas.</p>",
				"<h4 class='font-semibold text-base mt-4 mb-2'>Como funciona:</h4>",
				"<p><strong>– Comunicação:</strong> A comunicação durante a consulta se dá por meio de mensagens de texto e/ou chamadas de áudio, permitindo que o paciente descreva seus sintomas, faça perguntas e receba orientações médicas.</p>",
				"<p><strong>– Recursos adicionais:</strong> Fotos, vídeos e resultados de exames podem ser compartilhados durante a consulta, complementando o diagnóstico e o tratamento.</p>",
				"<p><strong>– Prescrições e atestados:</strong> O profissional pode emitir receitas médicas e atestados de forma eletrônica, otimizando o processo e agilizando o acesso à medicação e ao descanso necessário.</p>",
				"<h4 class='font-semibold text-base mt-4 mb-2'>Vantagens:</h4>",
				"<p><strong>– Acessibilidade:</strong> Facilita o acesso à saúde para pessoas com dificuldade de locomoção, que vivem em áreas remotas ou com horários indisponíveis para consultas presenciais.</p>",
				"<p><strong>– Praticidade:</strong> Permite realizar consultas de forma rápida e descomplicada, sem necessidade de deslocamentos ou longas esperas em salas de espera.</p>",
				"<p><strong>– Eficiência:</strong> Otimiza o tempo de profissionais e pacientes, possibilitando a realização de um maior número de consultas em um mesmo período.</p>",
				"<p><strong>– Redução de custos:</strong> Diminui os custos com deslocamentos e outros gastos relacionados à consulta presencial.</p>",
				"<p><strong>– Segurança:</strong> Plataformas seguras garantem a privacidade e confidencialidade das informações do paciente.</p>",
				"<h4 class='font-semibold text-base mt-4 mb-2'>Aplicações:</h4>",
				"<p><strong>– Consultas de rotina:</strong> Acompanhamento de doenças crônicas, check-ups preventivos, avaliação de sintomas leves, renovação de receitas, entre outras.</p>",
				"<p><strong>– Orientações médicas:</strong> Esclarecimento de dúvidas, acompanhamento pós-operatório, monitoramento de tratamentos, fornecimento de informações sobre saúde e bem-estar.</p>",
				"<p><strong>– Diagnóstico de doenças:</strong> Triagem de pacientes, avaliação inicial de sintomas, solicitação de exames complementares, encaminhamento para consultas especializadas.</p>",
				"<p><strong>– Atendimento psicológico:</strong> Terapia online, apoio emocional, manejo de ansiedade e stress, orientação para lidar com desafios da vida cotidiana.</p>",
				"<h4 class='font-semibold text-base mt-4 mb-2'>Importante:</h4>",
				"<p><strong>–</strong> A consulta assíncrona por chat e audio na telemedicina não substitui totalmente as consultas presenciais, especialmente em casos que necessitem de exame físico detalhado ou procedimentos médicos complexos.</p>",
				"<p><strong>–</strong> É fundamental escolher plataformas confiáveis e seguras, que garantam a proteção dos dados do paciente e a qualidade da consulta.</p>",
				"<p><strong>–</strong> Ao realizar uma consulta assíncrona, seja claro e objetivo ao descrever seus sintomas e forneça todas as informações relevantes para que o profissional possa realizar um diagnóstico preciso e prescrever o tratamento adequado.</p>",
				"<p><strong>–</strong> A consulta assíncrona por chat e audio na telemedicina se apresenta como uma ferramenta promissora para democratizar o acesso à saúde, oferecer atendimento de qualidade e promover a saúde da população de forma inovadora e eficiente.</p>",
			].join(""),
		},
		{
			question: "Como funciona a consulta por chat?",
			answer: [
				"<p>Digite suas mensagens para o médico e ele responderá em tempo real.</p>",
				"<p>Você também pode enviar fotos e exames pelo chat.</p>",
				"<p>Recebe sua prescriçao medica via chat, alem de documentos pertinentes, tais como: atestado medico, laudos, encaminhamentos e pedidos de check up medico.</p>",
			].join(""),
		},
		{
			question: "Como recebo a receita médica?",
			answer: [
				"<p>Após a consulta, o médico enviará a receita médica eletronicamente para seu chat, e-mail ou app.</p>",
				"<p>Você poderá imprimir a receita ou apresentá-la em formato digital na farmácia.</p>",
				"<p>Todas as farmácias aceitam a receita médica eletrônica diretamente no app.</p>",
			].join(""),
		},
		{
			question: "Como funciona a emissão de nota fiscal?",
			answer: [
				"<p>A nota fiscal da sua consulta é emitida automaticamente e enviada para o seu e-mail.</p>",
				"<p>Você também pode acessar a nota fiscal no seu perfil.</p>",
			].join(""),
		},
		{
			question: "Segurança e LGPD",
			answer:
				"<p>No ZapVida, levamos a sério a segurança dos seus dados. Todas as nossas plataformas são seguras e protegem seus dados de acordo com a Lei Geral de Proteção de Dados Pessoais (LGPD).</p>",
		},
	];

	if (!items) {
		return null;
	}

	return (
		<section className={cn("bg-primary/5 py-16 lg:py-24", className)} id="faq">
			<div className="container max-w-3xl">

				<div className="mb-12 text-center">
				<h2 className="mb-4 text-3xl font-bold">{t("faq.title")}</h2>
				<p className="text-muted-foreground">
				{t("faq.description")}
				</p>
				</div>

				<Accordion type="single" collapsible className="flex flex-col gap-3">
					{items.map((item, i) => (
						<AccordionItem
							key={i}
							value={`faq-item-${i}`}
							className="rounded-xl border bg-card px-6 py-4"
						>
							<AccordionTrigger className="py-2 text-lg text-left">
								{item.question}
							</AccordionTrigger>
							<AccordionContent className="pt-2 pb-1 space-y-4">
								<div dangerouslySetInnerHTML={{ __html: item.answer }} className="[&>p]:mb-3" />
							</AccordionContent>
						</AccordionItem>
					))}
				</Accordion>
			</div>
		</section>
	);
}
