import { Link } from "@i18n/routing";
import { Button } from "@ui/components/button";
import { ArrowRightIcon } from "lucide-react";
import Image from "next/image";
import { CallToActionButton } from "@shared/components/CallToActionButton";

export function Hero() {
	return (
		<div className="container pt-16 md:pt-24  pb-16 md:pb-0">
			<div className="flex flex-col items-center md:flex-row md:items-start md:justify-between">
				<div className="text-center md:max-w-xl md:text-left">
					<div className="mb-6 mt-14 flex justify-center md:justify-start">
						<div className="flex flex-wrap items-center rounded-full border border-ring p-px px-4 py-1 font-normal text-ring text-sm">
							<span className="flex items-center gap-2 rounded-full font-black text-ring">
								<span className="size-2 rounded-full bg-ring" />
								Novidade:
							</span>
							<span className="ml-1 block font-medium">
								Atendimento 24 horas
							</span>
						</div>
					</div>

					<h1 className="text-balance font-bold text-3xl md:text-4xl lg:text-5xl">
						Atendimento médico imediato sem filas e sem sair de casa!
					</h1>

					<p className="mx-auto mt-6 md:mx-0 text-balance text-foreground/60 text-lg md:text-xl">
						Seu médico disponível em minutos. Sem complicações, 100% online e seguro.
					</p>

					<div className="mt-8 flex flex-col items-center justify-center gap-3 sm:flex-row md:justify-start">
						<Button size="lg" variant="outline" asChild className="min-w-32 w-full md:px-11 md:w-auto rounded-3xl md:mr-3">
							<Link href="/doctors">
								Agendar
							</Link>
						</Button>
						<CallToActionButton className="w-full md:w-auto" href="/plantao">
							SEJA ATENDIDO AGORA!
						</CallToActionButton>
					</div>
				</div>

				{/* Imagem - visível apenas em telas médias ou maiores */}
				<div className="hidden md:block">
					<Image
						src="/images/home/<USER>"
						alt="Atendimento Médico 24 Horas"
						width={650}
						height={970}
						className="max-h-[520px] w-auto"
					/>
				</div>
			</div>
		</div>
	);
}
