import Link from "next/link";
import { MoveRightIcon, ScanFaceIcon, Stethoscope } from "lucide-react";
import { But<PERSON> } from "@ui/components/button";



export const CtaFooter = () => {
  return (
    <section className="bg-gradient-to-br from-ring to-[#71CBE8] py-20 text-primary-foreground">
      <div className="mx-auto max-w-4xl px-4 text-center">
        <Stethoscope className="mx-auto mb-6 h-16 w-16  " />
        <h2 className="mb-4 text-3xl font-bold">
          Cuide da sua saúde com praticidade
        </h2>
        <p className="mb-8 text-lg text-primary-foreground/90">
          Atendimento médico sem complicações, quando e onde precisar.
        </p>
        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
          <Link href="/doctors" passHref>
            <Button size="lg"   className="text-white  bg-primary hover:bg-primary/90" >
              Ver todos os médicos <MoveRightIcon className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};
