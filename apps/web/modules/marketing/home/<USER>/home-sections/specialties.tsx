"use client";

import { useRouter } from "next/navigation";
import { EarIcon } from "lucide-react";
import {
  FaBaby,
  FaBrain,
  FaEye,
  FaFemale,
  FaHeart,
  FaLungs,
  FaPills,
  FaSmile,
  FaStethoscope,
  FaTooth,
  FaUserMd,
} from "react-icons/fa";
import { FloatingCTA } from "@shared/components/FloatingCTA";
import { CallToActionButton } from "@shared/components/CallToActionButton";



export function SpecialtiesSection() {
  const router = useRouter();

  const specialties = [
    {
      name: "Clínico Geral",
      icon: <FaStethoscope className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Pediatria",
      icon: <FaBaby className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Psiquiatria",
      icon: <FaBrain className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Cardiologia",
      icon: <FaHeart className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Ginecologia",
      icon: <FaFemale className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Oftalmologia",
      icon: <FaEye className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Odontologia",
      icon: <FaTooth className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Dermatologia",
      icon: <FaSmile className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Endocrinologia",
      icon: <FaPills className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Otorrinolaringologia",
      icon: <EarIcon className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Pneumologia",
      icon: <FaLungs className="mb-4 size-10 text-primary" />,
    },
    {
      name: "Medicina Geral",
      icon: <FaUserMd className="mb-4 size-10 text-primary" />,
    },
  ];

  return (
    <section className="py-16 container">

        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold">Especialidades</h2>
          <p className="text-muted-foreground">
            Encontre o profissional médico certo para você.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3 lg:grid-cols-6">
          {specialties.map((specialty, index) => (
            <div
              key={index}
              onClick={() => router.push(`/doctors`)}
              className="flex cursor-pointer flex-col items-center rounded-lg border p-6 transition-colors hover:border-ring/50"
            >
              {specialty.icon}
              <h3 className="font-medium">{specialty.name}</h3>
            </div>
          ))}
        </div>



    </section>
  );
}
