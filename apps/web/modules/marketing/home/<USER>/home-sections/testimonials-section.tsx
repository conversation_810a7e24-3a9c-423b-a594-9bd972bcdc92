import { Testimonial } from "@ui/components/ui/testimonial-card"
import { testimonialsMock } from "./testimonials-data"






function TestimonialsSection() {
  return (
    <div className="container py-10">

<div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold">Depoimentos</h2>
          <p className="text-muted-foreground">
          Mais de 30.000 pacientes atendidos
          </p>
        </div>


      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {testimonialsMock.map((testimonial) => (
          <Testimonial key={testimonial.name} {...testimonial} />
        ))}
      </div>
    </div>
  )
}

export { TestimonialsSection }
