"use client";

import {
  <PERSON><PERSON>he<PERSON>,
  MessageSquareQuote,
  <PERSON>Check,
  ShieldPlus,
} from "lucide-react";

export function BenefitsSection() {
  const benefits = [
    {
      title: "Prescrição Online Fácil",
      description: "Receba receitas médicas digitais de forma rápida e segura.",
      icon: (
        <MessageSquareQuote className="text-cyan-300 size-6" />
      ),
    },
    {
      title: "Especialistas de Confiança",
      description:
        "Médicos qualificados para atender você com precisão e segurança.",
      icon: <FileCheck className="text-cyan-300 size-6" />,
    },
    {
      title: "Atendimento 24h",
      description:
        "Converse com médicos experientes a qualquer hora do dia ou da noite.",
      icon: <ShieldPlus className="text-cyan-300 size-6" />,
    },
    {
      title: "Proteção e Sigilo Absoluto",
      description: "Atendimento 100% seguro, seguindo normas da LGPD.",
      icon: <ShieldCheck className="text-cyan-300 size-6" />,
    },
  ];

  return (
    <section className="bg-primary py-16">
      <div className="container">
        <div className="flex flex-col md:flex-row gap-12">
          {/* Left side - Title and subtitle */}
          <div className="md:w-1/3 flex flex-col justify-center">
            <div className="mb-6">
              <h2 className="text-3xl font-bold text-white mb-4">Como funciona ZapVida?</h2>
              <div className="text-cyan-300 text-2xl font-bold">
                Simples, rápido e seguro, Consulte já!
              </div>
            </div>

            {/* Mobile image - only visible on mobile */}
            <div className="mt-8  ">
              <img
                src="/images/home/<USER>"
                alt="Redes Sociais"
                className="w-full h-auto md:-ml-5"
              />
            </div>
          </div>

          {/* Right side - Numbered benefits */}
          <div className="md:w-2/3">
            <div className="grid gap-6">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="flex gap-4 bg-primary-dark/20 rounded-lg p-5 border border-ring/30"
                >
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-cyan-300/20 text-white">
                      <span className="font-bold">{index + 1}</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex-shrink-0">
                        {benefit.icon}
                      </div>
                      <h3 className="font-semibold text-white">{benefit.title}</h3>
                    </div>
                    <p className="text-white/80">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
