"use client";

import { CallToActionButton } from "@shared/components/CallToActionButton";
import {
  Calendar,
  ChevronRight,
  CreditCard,
  MessageCircleCode
} from "lucide-react";

export function HowItWorksSection() {
  const steps = [
    {
      number: 1,
      title: "Escolha Plantão ou Agende",
      description: "Atendimento imediato ou consulta agendada – você escolhe o que precisa.",
      icon: <Calendar className="h-6 w-6 text-ring" />,
      details: [
        "Busque por especialidade",
        "Compare perfis",
        "Agende sua consulta"
      ]
    },
    {
      number: 2,
      title: "Confirmação instantânea",
      description: "Após pagamento, você e o médico recebem a confirmação no WhatsApp e e-mail, e são automaticamente conectados para iniciar a consulta.",
      icon: <CreditCard className="h-6 w-6 text-ring" />,
      details: [
        "Escolha como quer pagar",
        "Cartão de crédito ou PIX",
        "Receba o link da consulta"
      ]
    },
    {
      number: 3,
      title: "In<PERSON>cio da consulta",
      description: "Seu médico já está pronto para atender, elucidar seu diagnóstico e encaminhar os documentos médicos pertinentes.",
      icon: <MessageCircleCode className="h-6 w-6 text-ring" />,
      details: [
        "Atendimento 100% online",
        "Receba prescrições digitais",
        "Médicos Verificados"
      ]
    }
  ];

  return (
    <section id="como-funciona" className="py-20 bg-slate-50">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Consulta em 3 passos simples</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Atendimento médico rápido, seguro e sem complicações
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {steps.map((step, index) => (
            <div key={index} className="mb-12 last:mb-0">
              <div className="flex flex-col md:flex-row gap-8 items-start">
                {/* Step Number */}
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center w-14 h-14 rounded-full bg-primary/10 text-primary font-bold text-xl">
                    {step.number}
                  </div>
                </div>

                {/* Step Content */}
                <div className="flex-1">
                  <div className="bg-white p-6 rounded-xl shadow-sm border border-slate-100">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 rounded-full bg-primary/10 text-ring">
                        {step.icon}
                      </div>
                      <h3 className="text-xl font-semibold">{step.title}</h3>
                    </div>

                    <p className="text-muted-foreground mb-5">
                      {step.description}
                    </p>

                    <ul className="space-y-2">
                      {step.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-center text-sm text-muted-foreground">
                          <ChevronRight className="mr-2 h-4 w-4 text-primary" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Connector line between steps (except for the last one) */}
              {index < steps.length - 1 && (
                <div className="hidden md:block ml-7 h-12 w-px bg-slate-200 my-1"></div>
              )}
            </div>
          ))}
        </div>
      </div>


      <div className="mt-12 text-center">
       <CallToActionButton href="/plantao">
         SEJA ATENDIDO AGORA!
        </CallToActionButton>
       </div>
    </section>
  );
}
