'use client';

import { But<PERSON> } from '@ui/components/button';
import { <PERSON>, <PERSON>Check, BadgeCheck, Smartphone } from 'lucide-react';
import { Link } from '@i18n/routing';
import { CallToActionButton } from '@shared/components/CallToActionButton';

export function SubscriptionHighlight() {
  return (
    <section className="relative py-16 md:py-24 bg-primary">
      <div className="container">
        <div className="grid gap-10 md:grid-cols-2 items-center">
          <div className="text-white">
            <h2 className="text-3xl md:text-4xl font-bold leading-tight">Assine o ZapVida Sempre</h2>
            <p className="mt-3 text-white/85 text-lg">
              Cuide da sua saúde com praticidade: 2 consultas médicas por mês, atendimento online e receitas digitais válidas.
            </p>

            <div className="mt-6 space-y-3">
              {[
                '2 consultas por mês',
                'Atendimento por vídeo, áudio ou chat',
                'Acesso a especialistas qualificados',
                'Receitas e atestados digitais válidos',
              ].map((feature) => (
                <div key={feature} className="flex items-center gap-3 text-white/90">
                  <Check className="h-5 w-5 text-green-300" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>

            <div className="mt-8 flex flex-col sm:flex-row gap-3 sm:items-center">
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white/10">
                <Link href="/assinaturas">Conhecer benefícios</Link>
              </Button>
               <Button asChild size="lg" className="bg-white text-primary hover:bg-gray-100">
                <Link href="/pay/assinatura?plan=zapvida-sempre">Assinar por R$ 49/mês</Link>
              </Button>

            </div>

            <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex items-center gap-3">
                <div className="h-9 w-9 rounded-full bg-white/15 flex items-center justify-center">
                  <ShieldCheck className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-semibold">Pagamento seguro</p>
                  <p className="text-xs text-white/70">Criptografia e antifraude</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-9 w-9 rounded-full bg-white/15 flex items-center justify-center">
                  <BadgeCheck className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-semibold">Sem fidelidade</p>
                  <p className="text-xs text-white/70">Cancele quando quiser</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-9 w-9 rounded-full bg-white/15 flex items-center justify-center">
                  <Smartphone className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-semibold">Acesso imediato</p>
                  <p className="text-xs text-white/70">Use do celular ou computador</p>
                </div>
              </div>
            </div>
          </div>

            <div className="rounded-2xl bg-white/10 backdrop-blur-sm p-6 md:p-8 text-white">
            <div className="mb-4 flex items-baseline gap-2">
              <span className="text-4xl font-bold">R$ 49</span>
              <span className="opacity-90">/mês</span>
            </div>
            <p className="text-white/85">ZapVida Sempre — acesso garantido a cuidados de saúde todos os meses.</p>
            <ul className="mt-6 space-y-2 text-white/90">
              <li className="flex items-center gap-2"><Check className="h-4 w-4 text-green-300" /> Consultas de clínica geral</li>
              <li className="flex items-center gap-2"><Check className="h-4 w-4 text-green-300" /> Renovação de receitas</li>
              <li className="flex items-center gap-2"><Check className="h-4 w-4 text-green-300" /> Atestados digitais</li>
              <li className="flex items-center gap-2"><Check className="h-4 w-4 text-green-300" /> Suporte 24/7</li>
            </ul>
            <div className="mt-8">
              <CallToActionButton className="w-full" href="/pay/assinatura?plan=zapvida-sempre">
                Assinar por R$ 49/mês
						  </CallToActionButton>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}


