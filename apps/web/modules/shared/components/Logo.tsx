import { cn } from "@ui/lib";

export function Logo({
	withLabel = true,
	className,
	isCompact = false,
}: {
	className?: string;
	withLabel?: boolean;
	isCompact?: boolean;
}) {
	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			{withLabel && !isCompact && (
					<svg
					xmlns="http://www.w3.org/2000/svg"
					width="139"
					height="40"
					fill="none"
					viewBox="0 0 139 40"
				  >
					<path
					  fill="#184F70"
					  d="M36.633 18.05c.138 5.928-2.982 11.88-7.942 15.158-4.48 2.96-9.207 2.915-10.11 2.891a17.6 17.6 0 0 1-6.087-1.263c-.974.87-2.37 1.07-3.51.505-1.03-.51-1.7-1.568-1.737-2.74L4.25 35.26q.013-3.331.028-6.662C2.93 26.826.482 23.027.532 18.049.619 9.601 7.889 0 18.583 0c10.695 0 17.845 9.276 18.05 18.05"
					></path>
					<path
					  fill="#fff"
					  d="M18.544 34.46a15 15 0 0 1-5.623-1.093.727.727 0 0 1-.403-.949.727.727 0 0 1 .95-.403 13.6 13.6 0 0 0 5.173.988h.005c8.243 0 14.95-6.706 14.95-14.948s-6.706-14.95-14.95-14.95S3.697 9.808 3.697 18.052c0 2.232.623 4.283.657 4.398a12.9 12.9 0 0 0 2.458 4.32l.179.209-.024 2.69 4.475-4.532a.728.728 0 1 1 1.037 1.024l-7.003 7.094.05-5.746a14.3 14.3 0 0 1-2.557-4.606l-.004-.012c-.029-.093-.727-2.334-.727-4.84 0-4.381 1.707-8.502 4.806-11.601a16.3 16.3 0 0 1 11.602-4.806c2.2 0 4.34.437 6.364 1.299a16.5 16.5 0 0 1 5.217 3.529 16.5 16.5 0 0 1 3.528 5.216 16.15 16.15 0 0 1 1.3 6.366 16.1 16.1 0 0 1-1.3 6.363 16.5 16.5 0 0 1-3.529 5.217 16.5 16.5 0 0 1-5.216 3.529 16.1 16.1 0 0 1-6.362 1.298h-.106z"
					></path>
					<path
					  fill="#fff"
					  d="M18.674 28.775c-1.147 0-6.53-.253-8.965-4.862-2.264-4.288-.02-8.589.243-9.065l1.276.705a9.5 9.5 0 0 0-.896 2.6c-.356 1.856-.131 3.564.667 5.077 2.168 4.103 7.226 4.098 7.794 4.084 1.191-.029 5.237-.404 7.256-3.98 2.102-3.723.08-7.622-.007-7.786l1.289-.684c.116.218.706 1.389.986 3.063.369 2.198.022 4.315-.998 6.122-1.069 1.891-2.72 3.251-4.91 4.04a11.6 11.6 0 0 1-3.58.679l-.157.002z"
					></path>
					<path
					  fill="#fff"
					  d="M10.334 17.02a1.82 1.82 0 1 0 0-3.641 1.82 1.82 0 0 0 0 3.641M26.599 16.68a1.82 1.82 0 1 0 0-3.64 1.82 1.82 0 0 0 0 3.64M10.88 34.051a2.323 2.323 0 0 1-2.32-2.32c0-1.28 1.04-2.322 2.32-2.322s2.32 1.042 2.32 2.321-1.04 2.321-2.32 2.321m0-3.785c-.807 0-1.462.657-1.462 1.463 0 .805.657 1.462 1.462 1.462s1.462-.657 1.462-1.462c0-.806-.657-1.463-1.462-1.463"
					></path>
					<path
					  fill="#fff"
					  d="M10.88 32.826a1.096 1.096 0 1 0 0-2.191 1.096 1.096 0 0 0 0 2.191"
					></path>
					<path
					  fill="#194F70"
					  d="M112.764 39.403q-.71.595-1.855.595c-.763 0-1.343-.092-1.722-.274q-.133-.057-.165-.118-.033-.062.005-.214l.283-.728q.105-.274.369-.18.501.2 1.107.199.454 0 .785-.171.33-.17.331-.568a.55.55 0 0 0-.194-.403q-.195-.185-.63-.44a3.2 3.2 0 0 1-.918-.734q-.34-.412-.341-1.036 0-1.088.643-1.647t1.836-.558q.975 0 1.486.238.133.055.166.117t-.014.203l-.274.729q-.095.245-.369.17a3 3 0 0 0-.966-.141q-.936 0-.937.672 0 .246.195.44.194.196.582.43.643.39.974.777.33.389.331.985 0 1.06-.71 1.655zM117.998 33.224q.294 0 .312.264l.55 6.113q.027.302-.256.302h-1.012q-.266 0-.265-.246l-.085-1.051h-1.684l-.446 1.051q-.113.246-.331.246h-1.06q-.133-.001-.184-.086t.005-.218l2.707-6.113q.112-.265.378-.264h1.372zm-.758 4.078-.142-2.573h-.029l-1.059 2.573zM119.701 38.314q0-.266.056-.596l.758-4.23q.038-.265.293-.264h.994q.273 0 .237.264l-.719 4.06a3 3 0 0 0-.047.426q0 .654.662.654.446 0 .675-.256.233-.255.337-.814l.709-4.068q.038-.266.294-.265h1.003q.267 0 .227.265l-.747 4.23q-.207 1.193-.833 1.736-.624.544-1.798.544-2.101 0-2.101-1.684zm3.208-6.473q.113-.18.294-.179h1.078q.076 0 .109.048a.13.13 0 0 1 .019.11.2.2 0 0 1-.072.108l-.851.748a.5.5 0 0 1-.35.133h-.568q-.228 0-.115-.198l.454-.766zM125.293 39.905q-.283-.001-.227-.265l1.089-6.15q.038-.266.302-.265h1.609q1.231 0 1.841.516.61.514.611 1.69 0 .444-.104 1.096-.312 1.856-1.102 2.616-.791.762-2.248.761h-1.771m2.186-5.318-.691 3.964h.416q.692 0 1.074-.426t.59-1.6q.094-.614.094-.899 0-.585-.259-.814-.26-.227-.82-.227h-.406zM132.646 37.142l-.256 1.428h2.082q.274 0 .237.264l-.141.804q-.056.266-.302.265h-3.331q-.294 0-.238-.265l1.089-6.15q.038-.265.294-.264h3.293q.274 0 .237.264l-.141.804q-.038.265-.302.264h-2.063l-.227 1.306h1.93q.265 0 .227.264l-.133.758q-.047.255-.294.256h-1.959z"
					></path>
					<path
					  fill="#71CBE8"
					  d="M42.218 26.923q.113-.565.621-1.242l8.898-11.384H45.55q-.819 0-.678-.82l.452-2.513q.113-.791.876-.792h10.621q.82 0 .706.792l-.423 2.401q-.114.593-.62 1.215l-8.871 11.44h6.638q.847 0 .677.763l-.452 2.543q-.113.79-.875.79h-11.13q-.847 0-.677-.79l.423-2.401zM68.433 29.38q-.114.735-.79.734h-2.288q-.679 0-.734-.62l.056-.989q-1.525 1.807-3.956 1.807-1.78 0-2.824-.947t-1.044-2.641q0-1.469.495-2.543.493-1.073 1.285-1.724.791-.65 2.02-1.044 1.23-.396 2.43-.538 1.201-.14 2.783-.198l.142-.819q.198-.96-.268-1.397-.465-.439-1.708-.439-1.498 0-3.587.65-.82.226-.82-.508l.03-1.922q0-.65.677-.875 2.148-.82 4.66-.82 5.284 0 5.284 3.815 0 .705-.113 1.3l-1.723 9.719zm-2.995-6.242q-2.148.084-3.276.75-1.13.662-1.13 2.16 0 1.272 1.357 1.271 1.185 0 1.878-.734.692-.736.89-1.948l.283-1.497zM77.474 16.444a5 5 0 0 1 1.68-1.384 4.7 4.7 0 0 1 2.218-.538q4.464 0 4.464 5.086 0 1.243-.255 2.88-.705 4.096-2.274 6.003t-4.168 1.907q-1.242 0-2.302-.48-1.059-.48-1.37-1.242l-.677 3.814q-.114.734-.849.733h-2.796q-.764.001-.65-.733l2.994-16.922q.141-.762.848-.763h2.487q.734 0 .734.622l-.086 1.017zm-1.159 8.078q0 2.345 1.836 2.345 1.188 0 1.922-1.004.734-1.002 1.159-3.404.225-1.44.225-2.232 0-1.157-.423-1.667-.425-.51-1.328-.509-1.215 0-1.992 1.045t-1.2 3.361q-.198 1.159-.199 2.062zM90.581 30.116q-.82 0-.904-.848L88.18 11.075q-.056-.904.763-.904h3.135q.762 0 .82.763l.395 10.254.027 4.21h.086l1.525-4.21 4.04-10.254q.253-.764.987-.763h3.277q.395 0 .551.254t-.013.65l-7.91 18.193q-.366.848-1.13.848zM106.767 29.38q-.168.735-.819.734h-2.824q-.762 0-.65-.733l2.457-13.814q.141-.763.819-.763h2.825q.734 0 .621.763zM118.435 28.505q-.622.848-1.681 1.37a5 5 0 0 1-2.246.523q-4.464-.001-4.464-5 0-1.357.283-2.939.707-4.096 2.302-6.017 1.597-1.92 4.195-1.921 1.215 0 2.259.494 1.044.495 1.357 1.229l.961-5.34q.112-.734.819-.733h2.797q.819 0 .65.733l-3.249 18.475q-.113.735-.82.734h-2.513q-.734 0-.734-.622l.085-.988zm1.186-8.107q0-2.345-1.866-2.345-1.213 0-1.921 1.003-.707 1.004-1.13 3.405-.254 1.384-.254 2.26 0 2.145 1.751 2.146 1.214 0 1.991-1.03.776-1.032 1.202-3.376.225-1.3.225-2.062zM136.628 29.38q-.114.735-.791.734h-2.288q-.677 0-.734-.62l.057-.989q-1.526 1.807-3.956 1.807-1.78 0-2.824-.947-1.046-.947-1.046-2.641 0-1.469.494-2.543.495-1.073 1.285-1.724.79-.65 2.021-1.044 1.23-.396 2.43-.538 1.201-.14 2.783-.198l.142-.819q.197-.96-.268-1.397-.467-.439-1.708-.439-1.499 0-3.587.65-.819.226-.819-.508l.029-1.922q0-.65.677-.875 2.148-.82 4.661-.82 5.283 0 5.283 3.815 0 .705-.113 1.3l-1.723 9.719zm-2.995-6.242q-2.148.084-3.277.75-1.129.662-1.13 2.16 0 1.272 1.357 1.271 1.185 0 1.879-.734.692-.736.889-1.948l.283-1.497zM109.298 11.64h-.81l.16-.858c.024-.128-.063-.232-.195-.232h-1.182a.3.3 0 0 0-.282.232l-.16.858h-.778a.3.3 0 0 0-.282.232l-.229 1.215a.19.19 0 0 0 .195.232h.778l-.116.62c-.024.128.063.232.195.232h1.182a.3.3 0 0 0 .282-.232l.116-.62h.81a.3.3 0 0 0 .282-.232l.229-1.215c.024-.128-.063-.232-.195-.232"
					></path>
				  </svg>
			)}
		</span>
	);
}
