'use client';

import { cn } from '@ui/lib';
import Link from 'next/link';
import { MessageCircle } from 'lucide-react';
import { ButtonHTMLAttributes, forwardRef } from 'react';

interface CallToActionButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	href?: string;
	className?: string;
	icon?: boolean;
	target?: string;
}

export const CallToActionButton = forwardRef<
	HTMLButtonElement,
	CallToActionButtonProps
>(
	(
		{ children, href, className, target = null, icon = true, ...props },
		ref
	) => {
		const buttonContent = (
			<>
				{icon && <MessageCircle className='mr-2 h-5 w-5' />}
				{children}
			</>
		);

		const buttonClasses = cn(
			'group relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-cyan-400 to-ring px-6 py-3 font-medium text-white shadow-lg transition-all duration-300',
			'hover:shadow-[0_0_25px_5px_rgba(59,130,246,0.5)]',
			'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
			'before:absolute before:inset-0 before:h-full before:w-full before:rounded-full before:bg-gradient-to-r before:from-cyan-500 before:to-blue-600 before:opacity-0 before:transition-opacity before:duration-300 group-hover:before:opacity-100',
			'after:absolute after:left-0 after:top-0 after:h-full after:w-full after:rounded-full after:bg-gradient-to-r after:from-cyan-400 after:to-ring after:opacity-0 after:transition-opacity after:duration-300 group-hover:after:opacity-100',
			'pulse-animation',
			className
		);

		const pulseAnimation = `
      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
        70% {
          box-shadow: 0 0 0 15px rgba(59, 130, 246, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
        }
      }

      .pulse-animation {
        animation: pulse 3s infinite;
      }
    `;

		if (href) {
			return (
				<>
					<style jsx global>
						{pulseAnimation}
					</style>
					<Link
						href={href}
						target={target || '_self'}
						className={cn(buttonClasses)}
					>
						<span className='relative z-10 flex items-center'>
							{buttonContent}
						</span>
					</Link>
				</>
			);
		}

		return (
			<>
				<style jsx global>
					{pulseAnimation}
				</style>
				<button ref={ref} className={cn(buttonClasses)} {...props}>
					<span className='relative z-10 flex items-center'>
						{buttonContent}
					</span>
				</button>
			</>
		);
	}
);

CallToActionButton.displayName = 'CallToActionButton';
