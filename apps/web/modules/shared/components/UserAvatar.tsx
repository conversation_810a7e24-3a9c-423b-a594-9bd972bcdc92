import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { forwardRef, useMemo } from "react";
import { UserIcon } from "lucide-react";

export const UserAvatar = forwardRef<
	HTMLSpanElement,
	{
		name: string;
		avatarUrl?: string | null;
		className?: string;
	}
>(({ name, avatarUrl, className }, ref) => {
	// Create the Supabase URL if avatarUrl exists
	const avatarSrc = useMemo(() => {
		if (!avatarUrl) return undefined;

		const supabasePrefix = "https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/";
		
		// Check if avatarUrl already contains the prefix
		if (avatarUrl.startsWith("http")) {
			return avatarUrl;
		}
		
		// Apply the working Supabase URL pattern
		return `${supabasePrefix}${avatarUrl}`;
	}, [avatarUrl]);

	return (
		<Avatar ref={ref} className={className}>
			{avatarSrc && <AvatarImage src={avatarSrc} alt={name || "User avatar"} />}
			<AvatarFallback className="bg-primary/10 text-primary">
				<UserIcon className="size-6" />
			</AvatarFallback>
		</Avatar>
	);
});

UserAvatar.displayName = "UserAvatar";
