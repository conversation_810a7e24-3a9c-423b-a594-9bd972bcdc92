// src/lib/supabase/admin.ts
import { createClient } from "@supabase/supabase-js";

export const createAdminClient = () => {
	const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
	const supabaseServiceRole = process.env.SUPABASE_SERVICE_ROLE_KEY;

	console.log("Criando cliente admin do Supabase");

	if (!supabaseUrl) {
		console.error("Missing Supabase URL in environment variables");
		throw new Error("Missing Supabase URL");
	}

	if (!supabaseServiceRole) {
		console.error("Missing Supabase service role key in environment variables");
		throw new Error("Missing Supabase service role key");
	}

	try {
		return createClient(supabaseUrl, supabaseServiceRole, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
			// Adicionar configuração explícita para o Realtime
			realtime: {
				params: {
					eventsPerSecond: 10
				}
			},
		});
	} catch (error) {
		console.error("Error creating Supabase admin client:", error);
		throw new Error("Failed to create Supabase admin client");
	}
};
