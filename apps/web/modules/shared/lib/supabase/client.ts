// shared/lib/supabase/client.ts
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// shared/lib/supabase/client.ts
export function createClient() {
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
  }

  // Tente obter o token de autenticação atual (se disponível)
  const token = localStorage.getItem('auth_token') || null; // Adapte para o nome correto do seu token

  return createSupabaseClient(supabaseUrl, supabaseAnonKey, {
    realtime: {
      timeout: 30000,
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      headers: token ? {
        Authorization: `Bearer ${token}`
      } : {},
    },
    auth: {
      persistSession: false,
    },
  });
}
