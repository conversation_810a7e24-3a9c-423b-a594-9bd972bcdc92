// shared/lib/supabase/realtime-client.ts
import { createClient } from '@supabase/supabase-js';

// Remove singleton pattern to allow fresh clients for each chat
export function createRealtimeClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase credentials are missing');
  }

  // Create a fresh client instance each time to avoid channel conflicts
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: false,
      detectSessionInUrl: false,
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      },
      // Improved configurations for better connection stability
      config: {
        presence: {
          key: 'chat'
        },
        broadcast: {
          self: false  // Não receber eventos que você mesmo enviou
        }
      },
      // Add heartbeat and timeout configurations
      heartbeatIntervalMs: 30000,
      reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 10000),
      timeout: 10000
    },
    db: {
      schema: 'public'
    }
  });
}

// Função para limpar um cliente específico (útil em logout ou troca de usuário)
export function clearRealtimeClient(client: any) {
  if (client) {
    try {
      client.removeAllChannels();
    } catch (error) {
      console.error('Error clearing realtime client:', error);
    }
  }
}

// Função para monitorar se o canal está funcionando corretamente
export function monitorRealtimeConnection(channelName: string, callback: (isConnected: boolean) => void) {
  const client = createRealtimeClient();
  const channel = client.channel(channelName);

  let heartbeatInterval: NodeJS.Timeout;

  // Monitorar o canal
  channel.on('system', { event: '*' }, (payload) => {
    console.log(`System event on channel ${channelName}:`, payload);

    // Verificar se está conectado
    if (payload.event === 'connected') {
      callback(true);
    } else if (payload.event === 'disconnected') {
      callback(false);
    }
  });

  // Inscrever no canal
  channel.subscribe((status) => {
    console.log(`Channel ${channelName} status:`, status);
    callback(status === 'SUBSCRIBED');

    // Iniciar heartbeat se inscrito
    if (status === 'SUBSCRIBED') {
      heartbeatInterval = setInterval(() => {
        console.log(`Sending heartbeat to channel ${channelName}`);
        // Poderia implementar algum tipo de ping aqui
      }, 30000); // A cada 30 segundos
    } else {
      clearInterval(heartbeatInterval);
    }
  });

  // Retorna uma função para limpar
  return () => {
    clearInterval(heartbeatInterval);
    client.removeChannel(channel);
  };
}
