// utils/file-validations.ts
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const validateFile = (file: File) => {
	// Verificar tamanho
	if (file.size > MAX_FILE_SIZE) {
		throw new Error("Arquivo muito grande. Máximo de 5MB permitido.");
	}

	// Verificar tipo
	const allowedTypes = [
		"image/jpeg",
		"image/png",
		"image/gif",
		"application/pdf",
		"audio/mpeg",
		"audio/wav",
		"audio/webm",
		"video/mp4",
	];

	if (!allowedTypes.includes(file.type)) {
		throw new Error("Tipo de arquivo não permitido.");
	}

	return true;
};
