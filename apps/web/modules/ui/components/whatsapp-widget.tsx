"use client";

import { usePathname } from "next/navigation";
import { FaWhatsapp } from "react-icons/fa";

export default function WhatsAppWidget() {
  const pathName = usePathname();

  if (
    pathName.includes("/patient") ||
    pathName.includes("/dashboard") ||
    pathName.includes("/admin") ||
    pathName.includes("/doctor") ||
    pathName.includes("/hospital")
  ) {
    return null;
  }

  return (
    <button
      onClick={() =>
        window.open(
          "https://api.whatsapp.com/send/?phone=5547988023325&text=Olá,+quero+fazer+um+agendamento.&type=phone_number&app_absent=0",
          "_blank",
        )
      }
      className="fixed bottom-9 right-9 flex size-14 items-center justify-center rounded-full bg-[#25D366] text-white shadow-lg transition-transform hover:scale-110 hover:shadow-xl"
      aria-label="Chat on WhatsApp"
    >
      <FaWhatsapp size={32} />
    </button>
  );
}
