import { cn } from '@ui/lib';

interface DashboardHeaderProps {
	heading: string;
	text?: string;
	children?: React.ReactNode;
	className?: string;
}

export function DashboardHeader({
	heading,
	text,
	children,
	className,
}: DashboardHeaderProps) {
	return (
		<div
			className={cn(
				'flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-0 px-4 py-2 sm:py-4',
				className
			)}
		>
			<div className='grid gap-1 w-full sm:w-auto'>
				<h1 className='font-heading font-semibold text-xl sm:text-2xl'>
					{heading}
				</h1>
				{text && (
					<p className='text-sm sm:text-base text-muted-foreground'>{text}</p>
				)}
			</div>
			{children && <div className='w-full sm:w-auto'>{children}</div>}
		</div>
	);
}
