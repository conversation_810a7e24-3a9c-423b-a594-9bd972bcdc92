'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';


import { Button } from '@ui/components/button';
import { Calendar } from '@ui/components/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@ui/components/popover';
import { useTheme } from 'next-themes';

export default function DateRangePicker({ className }: { className?: string }) {
	const [date, setDate] = React.useState<any | null>(null);
	const { theme: mode } = useTheme();

	return (
		<div className={cn('grid gap-2', className)}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						className={cn(' font-normal', {
							'  bg-background hover:bg-background hover:ring-background text-default-600':
								mode !== 'dark',
						})}
					>
						<CalendarIcon className='ltr:mr-2 rtl:ml-2 h-4 w-4' />
						{date?.from ? (
							date.to ? (
								<>
									{format(date.from, 'LLL dd, y')} -{' '}
									{format(date.to, 'LLL dd, y')}
								</>
							) : (
								format(date.from, 'LLL dd, y')
							)
						) : (
							<span>Escolha uma data</span>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent className='w-auto p-0' align='end'>
					<Calendar
						initialFocus
						mode='range'
						defaultMonth={date?.from}
						selected={date}
						onSelect={setDate}
						numberOfMonths={2}
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
}
