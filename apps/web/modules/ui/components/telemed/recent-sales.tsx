"use client";

import { Avatar, AvatarFallback } from "@ui/components/avatar";

interface RecentSalesProps {
	data?: Array<{
		name: string;
		appointments: number;
		revenue: number;
	}>;
}

export function RecentSales({ data = [] }: RecentSalesProps) {
	return (
		<div className="space-y-8">
			{data?.map((doctor, index) => (
				<div key={index} className="flex items-center">
					<Avatar className="h-9 w-9">
						<AvatarFallback>{doctor.name.charAt(0)}</AvatarFallback>
					</Avatar>
					<div className="ml-4 space-y-1">
						<p className="text-sm font-medium leading-none">{doctor.name}</p>
						<p className="text-sm text-muted-foreground">
							{doctor.appointments} consultas
						</p>
					</div>
					<div className="ml-auto font-medium">
						R$ {doctor.revenue.toLocaleString("pt-BR")}
					</div>
				</div>
			))}
		</div>
	);
}
