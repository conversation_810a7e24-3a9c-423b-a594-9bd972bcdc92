"use client";

import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface AppointmentChartProps {
  doctorId: string;
}

export function AppointmentChart({ doctorId }: AppointmentChartProps) {
  // This would be replaced with real data from an API call
  const data = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    datasets: [
      {
        label: "Consultas Realizadas",
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: "rgba(37, 99, 235, 0.5)",
      },
      {
        label: "Consultas Canceladas",
        data: [28, 48, 40, 19, 86, 27],
        backgroundColor: "rgba(239, 68, 68, 0.5)",
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
    },
  };

  return <Bar options={options} data={data} />;
}