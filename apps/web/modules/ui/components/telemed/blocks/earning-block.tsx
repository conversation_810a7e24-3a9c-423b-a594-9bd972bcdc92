"use client";

import { useTheme } from "next-themes";
import dynamic from "next/dynamic";

import { Card, CardContent } from "@ui/components/card";
import { cn } from "@ui/lib";

const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface EarningBlockProps {
	title?: string;
	className?: string;
	colors?: string[];
	series?: number[];
	labels?: string[];
	height?: number;
	chartType?: "donut" | "pie" | "radialBar";
	total?: number | string;
	percentage?: string;
}

const EarningBlock = ({
	title = "Earnings",
	total = "$0",
	percentage = "+08%",
	series = [70, 30],
	chartType = "donut",
	height = 200,
	labels = ["Success", "Return"],
	colors = ["#ffbf99", "#5cffff"],
	className = "",
}: EarningBlockProps) => {
	const { theme: mode } = useTheme();
	const options: any = {
		labels: labels,
		dataLabels: {
			enabled: false,
		},
		tooltip: {
			theme: mode === "dark" ? "dark" : "light",
		},
		colors: [...colors],
		legend: {
			position: "bottom",
			fontSize: "12px",
			fontFamily: "Outfit",
			fontWeight: 400,
			labels: {
				colors: mode === "dark" ? "#cbd5e1" : "#0f172a",
			},
		},

		plotOptions: {
			pie: {
				donut: {
					size: "70%",
					labels: {
						show: true,
						name: {
							show: false,
							fontSize: "14px",
							fontWeight: "bold",
							fontFamily: "Inter",
						},
						value: {
							show: true,
							fontSize: "16px",
							fontFamily: "Outfit",
							color: mode === "dark" ? "#cbd5e1" : "#0f172a",
							formatter(val: string) {
								return `${Number.parseInt(val)}%`;
							},
						},
						total: {
							show: true,
							fontSize: "10px",
							label: "",
							formatter() {
								return "70";
							},
						},
					},
				},
			},
		},
	};

	return (
		<Card className={cn("", className)}>
			<CardContent className="px-4 py-3">
				<div className="flex flex-col items-center gap-2 md:flex-row">
					<div className="flex-1">
						<div className="text-default-600 mb-1.5 text-sm">{title}</div>
						<div className="text-default-900 mb-1.5 text-lg font-medium">
							{total}
						</div>
						<div className="text-default-600 whitespace-nowrap text-xs font-normal">
							<span className="me-1 text-primary">{percentage}</span>
							Statistics graph desc
						</div>
					</div>
					<div className="flex-none">
						<Chart
							options={options}
							series={series}
							type={chartType}
							height={height}
							width={"100%"}
						/>
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

export default EarningBlock;
