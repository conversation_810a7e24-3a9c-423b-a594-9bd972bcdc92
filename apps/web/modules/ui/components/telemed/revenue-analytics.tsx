"use client";

import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	Cell,
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	<PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";

interface RevenueAnalyticsProps {
	bySpecialty?: Array<{
		specialty: string;
		revenue: number;
	}>;
	byRegion?: Array<{
		region: string;
		revenue: number;
	}>;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

export function RevenueAnalytics({
	bySpecialty = [],
	byRegion = [],
}: RevenueAnalyticsProps) {
	return (
		<div className="grid gap-4">
			<Card>
				<CardHeader>
					<CardTitle>Faturamento por Região</CardTitle>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={300}>
						<BarChart data={byRegion}>
							<XAxis dataKey="region" />
							<YAxis
								tickFormatter={(value) => `R$ ${value.toLocaleString("pt-BR")}`}
							/>
							<Tooltip
								formatter={(value: number) =>
									`R$ ${value.toLocaleString("pt-BR")}`
								}
							/>
							<Bar
								dataKey="revenue"
								fill="#8884d8"
								radius={[4, 4, 0, 0]}
								className="fill-primary"
							/>
						</BarChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Faturamento por Especialidade</CardTitle>
				</CardHeader>
				<CardContent>
					<ResponsiveContainer width="100%" height={300}>
						<PieChart>
							<Pie
								data={bySpecialty}
								dataKey="revenue"
								nameKey="specialty"
								cx="50%"
								cy="50%"
								outerRadius={80}
								label={(entry) =>
									`${entry.specialty}: R$ ${entry.revenue.toLocaleString("pt-BR")}`
								}
							>
								{bySpecialty.map((entry, index) => (
									<Cell
										key={`cell-${index}`}
										fill={COLORS[index % COLORS.length]}
									/>
								))}
							</Pie>
							<Tooltip
								formatter={(value: number) =>
									`R$ ${value.toLocaleString("pt-BR")}`
								}
							/>
						</PieChart>
					</ResponsiveContainer>
				</CardContent>
			</Card>
		</div>
	);
}
