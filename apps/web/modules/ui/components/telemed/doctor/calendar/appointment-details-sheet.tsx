"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { AppointmentStatus, ConsultType } from "@prisma/client";
import { AppointmentDetails } from "../../../../../../app/[locale]/(app)/app/appointments/components/appointment-details";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { CalendarClock, MessageSquare, PhoneIcon, Video, VideoIcon } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface AppointmentDetailsSheetProps {
	isOpen: boolean;
	onClose: () => void;
	appointment: {
		id: string;
		title: string;
		start: Date;
		end: Date;
		status: string;
		consultType: string;
		appointment?: any; // Dados completos da consulta
	} | null;
}

export function AppointmentDetailsSheet({
	isOpen,
	onClose,
	appointment,
}: AppointmentDetailsSheetProps) {
	if (!appointment) return null;

	const router = useRouter();

	// Log quando o componente é aberto
	useEffect(() => {
		if (isOpen && appointment) {
			console.log("AppointmentDetailsSheet aberto com:", appointment);
		}
	}, [isOpen, appointment]);

	// Extrair dados completos da consulta
	const fullAppointment = appointment.appointment;

	console.log("AppointmentDetailsSheet - appointment:", appointment);
	console.log("AppointmentDetailsSheet - fullAppointment:", fullAppointment);

	// Verificar se temos dados completos suficientes para usar o AppointmentDetails
	const hasCompleteData = fullAppointment &&
		fullAppointment.doctor &&
		fullAppointment.doctor.user &&
		fullAppointment.patient &&
		fullAppointment.patient.user;

	console.log("AppointmentDetailsSheet - hasCompleteData:", hasCompleteData);

	// Se temos dados completos, usar o AppointmentDetails
	if (hasCompleteData) {
		try {
			const appointmentData = {
				id: appointment.id,
				status: (fullAppointment.status || appointment.status) as AppointmentStatus,
				scheduledAt: appointment.start,
				symptoms: fullAppointment.symptoms || null,
				consultType: "CHAT" as ConsultType, // Definindo como CHAT por padrão
				amount: fullAppointment.amount || null,
				hospitalId: fullAppointment.hospitalId || null,
				doctor: fullAppointment.doctor,
				patient: fullAppointment.patient,
				hospital: fullAppointment.hospital || null,
				duration: fullAppointment.duration || 30
			};

			return (
				<Sheet open={isOpen} onOpenChange={onClose}>
					<SheetContent className="w-full overflow-y-auto bg-white sm:max-w-xl">
						<SheetHeader>
							<SheetTitle>Detalhes da Consulta</SheetTitle>
						</SheetHeader>
						<div className="mt-2 overflow-auto pr-2">
							<AppointmentDetails
								appointment={appointmentData}
								hideBackButton={true}
							/>
						</div>
					</SheetContent>
				</Sheet>
			);
		} catch (error) {
			console.error("Erro ao renderizar AppointmentDetails completo:", error);
			// Se ocorrer algum erro, cair para a versão simplificada
		}
	}

	// Se não temos dados completos, mostrar uma versão simplificada
	const statusConfig = {
		SCHEDULED: { label: "Agendada", color: "bg-blue-100 text-blue-800" },
		IN_PROGRESS: { label: "Em Andamento", color: "bg-yellow-100 text-yellow-800" },
		COMPLETED: { label: "Finalizada", color: "bg-green-100 text-green-800" },
		CANCELED: { label: "Cancelada", color: "bg-red-100 text-red-800" },
		NO_SHOW: { label: "Não Compareceu", color: "bg-gray-100 text-gray-800" },
	};

	// Extrair o nome do paciente do título (formato típico: "Nome do Paciente - TIPO")
	const patientName = appointment.title.split(" - ")[0] || "Paciente";

	const handleViewDetails = () => {
		router.push(`/app/appointments/${appointment.id}`);
		onClose();
	};

	return (
		<Sheet open={isOpen} onOpenChange={onClose}>
			<SheetContent className="w-full overflow-y-auto bg-white sm:max-w-xl">
				<SheetHeader>
					<SheetTitle>Detalhes da Consulta</SheetTitle>
				</SheetHeader>
				<div className="mt-6 space-y-6 pb-8">
					{/* Informações Gerais */}
					<Card>
						<CardHeader>
							<CardTitle>Informações Gerais</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="flex items-center justify-between">
								<div>
									<Badge className={statusConfig[appointment.status as keyof typeof statusConfig]?.color || "bg-gray-100 text-gray-800"}>
										{statusConfig[appointment.status as keyof typeof statusConfig]?.label || "Status Desconhecido"}
									</Badge>
								</div>
							</div>

							<div className="grid gap-4">
								<div className="flex items-center gap-2">
									<CalendarClock className="h-5 w-5 text-muted-foreground" />
									<div>
										<p className="text-muted-foreground text-sm">Data e Hora</p>
										<p className="font-medium">
											{format(appointment.start, "PPP 'às' HH:mm", { locale: ptBR })}
										</p>
									</div>
								</div>

								<div>
									<p className="text-sm text-muted-foreground">Paciente</p>
									<p className="font-medium">{patientName}</p>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Botões de ação */}
					<div className="flex flex-col gap-3">
						<Button
							variant="default"
							className="w-full"
							onClick={() => router.push(`/app/zapchat?appointment=${appointment.id}`)}
						>
							<MessageSquare className="mr-2 h-4 w-4" />
							Acessar Consulta
						</Button>

						<Button
							variant="outline"
							className="w-full"
							onClick={handleViewDetails}
						>
							Ver Detalhes Completos
						</Button>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
