"use client";

import ptBrLocale from "@fullcalendar/core/locales/pt-br";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import { useRouter } from "next/navigation";
import { useCallback, useState, useEffect } from "react";
import { AppointmentDetailsSheet } from "./appointment-details-sheet";

interface Event {
	id: string;
	title: string;
	start: Date;
	end: Date;
	status: string;
	consultType: string;
	appointment?: any; // Dados completos da consulta
}

interface DoctorCalendarProps {
	events: Event[];
}

export function DoctorCalendar({ events }: DoctorCalendarProps) {
	const router = useRouter();
	const [selectedAppointment, setSelectedAppointment] = useState<Event | null>(
		null,
	);

	// Log events para debug
	useEffect(() => {
		console.log("DoctorCalendar - events:", events);

		// Verificar a estrutura detalhada do primeiro evento (se existir)
		if (events && events.length > 0) {
			console.log("Primeiro evento - estrutura detalhada:", JSON.stringify(events[0], null, 2));
			console.log("Primeiro evento - appointment:", events[0].appointment);
		}
	}, [events]);

	const handleEventClick = useCallback((info: any) => {
		console.log("Event clicked - info:", info);
		console.log("Event clicked - event object:", info.event);
		console.log("Event clicked - event ID:", info.event.id);
		console.log("Event clicked - extendedProps:", info.event.extendedProps);

		// Buscar o evento original no array de eventos para obter os dados completos
		const eventData = events.find(event => event.id === info.event.id);
		console.log("Found event data:", eventData);

		// Se não encontrou os dados completos, usar apenas as informações básicas
		if (!eventData) {
			console.log("No matching event found, creating basic event");

			// Criar um evento básico a partir das informações disponíveis
			const event = {
				id: info.event.id,
				title: info.event.title,
				start: info.event.start,
				end: info.event.end || new Date(new Date(info.event.start).getTime() + 30 * 60000), // Adicionar 30 minutos se não tiver end
				status: info.event.extendedProps?.status || "SCHEDULED",
				consultType: info.event.extendedProps?.consultType || "VIDEO",
			};

			console.log("Created basic event:", event);
			setSelectedAppointment(event);
			return;
		}

		// Usar os dados completos do evento
		console.log("Using complete event data:", eventData);

		// Garantir que as datas sejam objetos Date
		const processedEvent = {
			...eventData,
			start: eventData.start instanceof Date ? eventData.start : new Date(eventData.start),
			end: eventData.end instanceof Date ? eventData.end : new Date(eventData.end),
		};

		console.log("Processed event with Date objects:", processedEvent);
		setSelectedAppointment(processedEvent);
	}, [events]);

	// Log quando o selectedAppointment muda
	useEffect(() => {
		console.log("Selected appointment changed:", selectedAppointment);
	}, [selectedAppointment]);

	return (
		<div className="p-4">
			<FullCalendar
				plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
				initialView="timeGridWeek"
				locale={ptBrLocale}
				events={events}
				headerToolbar={{
					left: "prev,next today",
					center: "title",
					right: "dayGridMonth,timeGridWeek,timeGridDay",
				}}
				buttonText={{
					today: "Hoje",
					month: "Mês",
					week: "Semana",
					day: "Dia",
				}}
				slotMinTime="06:00:00"
				slotMaxTime="22:00:00"
				allDaySlot={false}
				eventClick={handleEventClick}
			/>
			<AppointmentDetailsSheet
				isOpen={!!selectedAppointment}
				onClose={() => setSelectedAppointment(null)}
				appointment={selectedAppointment}
			/>
		</div>
	);
}
