"use client";



import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "./button";
import { useRouter } from "@i18n/routing";

interface PaginationButtonProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  searchParams?: Record<string, string>;
}

export function PaginationButton({
  currentPage,
  totalPages,
  baseUrl,
  searchParams: additionalParams
}: PaginationButtonProps) {
  const router = useRouter();
  const urlSearchParams = new URLSearchParams(additionalParams);

  const createQueryString = (page: number) => {
    const params = new URLSearchParams(urlSearchParams);
    params.set("page", page.toString());

    // Add any additional search parameters
    if (additionalParams) {
      Object.entries(additionalParams).forEach(([key, value]) => {
        params.set(key, value);
      });
    }

    return params.toString();
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        disabled={currentPage <= 1}
        onClick={() => {
          router.push(`${baseUrl}?${createQueryString(currentPage - 1)}`);
        }}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      <span className="text-sm">
        Página {currentPage} de {totalPages}
      </span>
      <Button
        variant="outline"
        size="sm"
        disabled={currentPage >= totalPages}
        onClick={() => {
          router.push(`${baseUrl}?${createQueryString(currentPage + 1)}`);
        }}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
