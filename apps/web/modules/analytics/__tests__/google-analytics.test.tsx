/**
 * Testes para verificar a implementação do Google Analytics
 */

import { render } from '@testing-library/react';
import { AnalyticsScriptGoogle, useAnalytics } from '../provider/google';

// Mock do Next.js Script component
jest.mock('next/script', () => {
  return function MockScript({ children, dangerouslySetInnerHTML, ...props }: any) {
    if (dangerouslySetInnerHTML) {
      return <script {...props} dangerouslySetInnerHTML={dangerouslySetInnerHTML} />;
    }
    return <script {...props}>{children}</script>;
  };
});

// Mock das variáveis de ambiente
const mockEnv = {
  NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: 'G-EDZQW4LKVR',
  NEXT_PUBLIC_GOOGLE_ADS_ID: 'AW-347192758'
};

describe('Google Analytics Implementation', () => {
  beforeEach(() => {
    // Mock das variáveis de ambiente
    Object.assign(process.env, mockEnv);
    
    // Limpar mocks do window
    delete (window as any).gtag;
    delete (window as any).dataLayer;
  });

  describe('AnalyticsScriptGoogle Component', () => {
    it('should render Google Analytics scripts with correct IDs', () => {
      const { container } = render(<AnalyticsScriptGoogle />);
      
      const scripts = container.querySelectorAll('script');
      expect(scripts).toHaveLength(2);
      
      // Verificar script do gtag.js
      const gtagScript = scripts[0];
      expect(gtagScript.src).toContain('https://www.googletagmanager.com/gtag/js?id=G-EDZQW4LKVR');
      
      // Verificar script de configuração
      const configScript = scripts[1];
      expect(configScript.innerHTML).toContain('G-EDZQW4LKVR');
      expect(configScript.innerHTML).toContain('AW-347192758');
      expect(configScript.innerHTML).toContain('window.dataLayer = window.dataLayer || []');
      expect(configScript.innerHTML).toContain('function gtag(){dataLayer.push(arguments);}');
    });

    it('should configure both Google Analytics and Google Ads', () => {
      const { container } = render(<AnalyticsScriptGoogle />);
      
      const configScript = container.querySelector('script[id="google-analytics"]');
      expect(configScript?.innerHTML).toContain("gtag('config', 'G-EDZQW4LKVR')");
      expect(configScript?.innerHTML).toContain("gtag('config', 'AW-347192758')");
    });
  });

  describe('useAnalytics Hook', () => {
    it('should provide tracking functions', () => {
      const TestComponent = () => {
        const { trackEvent, trackConversion, trackPurchase } = useAnalytics();
        
        expect(typeof trackEvent).toBe('function');
        expect(typeof trackConversion).toBe('function');
        expect(typeof trackPurchase).toBe('function');
        
        return null;
      };
      
      render(<TestComponent />);
    });

    it('should handle missing gtag gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      const TestComponent = () => {
        const { trackEvent } = useAnalytics();
        trackEvent('test_event');
        return null;
      };
      
      render(<TestComponent />);
      
      expect(consoleSpy).toHaveBeenCalledWith('Google Analytics não está disponível');
      consoleSpy.mockRestore();
    });

    it('should track events when gtag is available', () => {
      const mockGtag = jest.fn();
      (window as any).gtag = mockGtag;
      
      const TestComponent = () => {
        const { trackEvent } = useAnalytics();
        trackEvent('test_event', { value: 100 });
        return null;
      };
      
      render(<TestComponent />);
      
      expect(mockGtag).toHaveBeenCalledWith('event', 'test_event', { value: 100 });
    });

    it('should track conversions with correct format', () => {
      const mockGtag = jest.fn();
      (window as any).gtag = mockGtag;
      
      const TestComponent = () => {
        const { trackConversion } = useAnalytics();
        trackConversion({ value: 80, currency: 'BRL', transaction_id: 'TEST123' });
        return null;
      };
      
      render(<TestComponent />);
      
      expect(mockGtag).toHaveBeenCalledWith('event', 'conversion', {
        send_to: 'AW-347192758/rQMbCIHgyLkaELb7xqUB',
        currency: 'BRL',
        value: 80,
        transaction_id: 'TEST123'
      });
    });

    it('should track purchases correctly', () => {
      const mockGtag = jest.fn();
      (window as any).gtag = mockGtag;
      
      const TestComponent = () => {
        const { trackPurchase } = useAnalytics();
        trackPurchase('ORDER123', 150, 'BRL');
        return null;
      };
      
      render(<TestComponent />);
      
      expect(mockGtag).toHaveBeenCalledWith('event', 'conversion', {
        send_to: 'AW-347192758/rQMbCIHgyLkaELb7xqUB',
        currency: 'BRL',
        value: 150,
        transaction_id: 'ORDER123'
      });
    });
  });

  describe('Environment Variables', () => {
    it('should use correct Google Analytics ID from environment', () => {
      expect(process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID).toBe('G-EDZQW4LKVR');
    });

    it('should use correct Google Ads ID from environment', () => {
      expect(process.env.NEXT_PUBLIC_GOOGLE_ADS_ID).toBe('AW-347192758');
    });
  });
});
