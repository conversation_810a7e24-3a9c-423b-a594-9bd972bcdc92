'use client';

import { useEffect } from 'react';
import { useAnalytics, ConversionData } from '../provider/google';

interface ConversionTrackerProps {
  conversionData: ConversionData;
}

/**
 * Componente para rastrear conversões automaticamente
 * Coloque este componente na página onde deseja rastrear a conversão
 */
export function ConversionTracker({ conversionData }: ConversionTrackerProps) {
  const { trackConversion } = useAnalytics();

  useEffect(() => {
    // Rastrear a conversão quando o componente for montado
    trackConversion(conversionData);
  }, [conversionData, trackConversion]);

  // Este componente não renderiza nada visível
  return null;
}

/**
 * Componente para rastrear compras automaticamente
 * Coloque este componente na página de confirmação de compra
 */
export function PurchaseTracker({ 
  transactionId, 
  value, 
  currency = 'BRL' 
}: { 
  transactionId: string; 
  value: number; 
  currency?: string;
}) {
  const { trackPurchase } = useAnalytics();

  useEffect(() => {
    // Rastrear a compra quando o componente for montado
    trackPurchase(transactionId, value, currency);
  }, [transactionId, value, currency, trackPurchase]);

  // Este componente não renderiza nada visível
  return null;
}
