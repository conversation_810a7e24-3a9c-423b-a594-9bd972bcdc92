'use client';
import { useEffect } from 'react';

interface PurchaseTrackerProps {
  transactionId: string;
  value: number;
  currency?: string;
}

export function PurchaseTracker100({ transactionId, value, currency = 'BRL' }: PurchaseTrackerProps) {
  useEffect(() => {
    const sendConversion = () => {
      if (typeof window !== 'undefined' && typeof (window as any).gtag === 'function') {
        (window as any).gtag('event', 'conversion', {
          send_to: 'AW-347192758/toCICMz_hdAaELb7xqUB',
          value,
          currency,
          transaction_id: transactionId,
        });
        console.log('✅ Conversão de R$100 enviada', { transactionId, value });
      } else {
        console.warn('⚠️ gtag não pronto, tentando novamente...');
        setTimeout(sendConversion, 500);
      }
    };
    sendConversion();
  }, [transactionId, value, currency]);

  return null;
}