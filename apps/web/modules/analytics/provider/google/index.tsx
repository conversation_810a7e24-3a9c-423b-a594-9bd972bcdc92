'use client';

const googleAnalyticsId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID as string;
const googleAdsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID || 'AW-347192758';

export function AnalyticsScriptGoogle() {
	// Não renderizar se não tiver o ID configurado
	if (!googleAnalyticsId) {
		console.warn('NEXT_PUBLIC_GOOGLE_ANALYTICS_ID não está configurado');
		return null;
	}

	return (
		<>
			{/* Google tag (gtag.js) - Exatamente como fornecido pelo Google */}
			<script
				async
				src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`}
			/>
			<script
				dangerouslySetInnerHTML={{
					__html: `
						window.dataLayer = window.dataLayer || [];
						function gtag(){dataLayer.push(arguments);}
						gtag('js', new Date());
						gtag('config', '${googleAnalyticsId}');
						${googleAdsId ? `gtag('config', '${googleAdsId}');` : ''}
					`,
				}}
			/>
		</>
	);
}

// Interface para dados de conversão
export interface ConversionData {
	value?: number;
	currency?: string;
	transaction_id?: string;
	item_name?: string;
	item_category?: string;
	[key: string]: any; // Permite propriedades adicionais
}

// Hook para usar analytics
export function useAnalytics() {
	// Função para rastrear eventos genéricos
	const trackEvent = (event: string, data?: Record<string, unknown>) => {
		if (
			typeof window === 'undefined' ||
			typeof (window as any).gtag !== 'function'
		) {
			console.warn('Google Analytics não está disponível');
			return;
		}

		(window as any).gtag('event', event, data);
	};

	// Função específica para rastrear conversões do Google Ads
	const trackConversion = (data: ConversionData = {}) => {
		if (
			typeof window === 'undefined' ||
			typeof (window as any).gtag !== 'function'
		) {
			console.warn('Google Analytics não está disponível');
			return;
		}

		const googleAdsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID || 'AW-347192758';
		const conversionLabel = '/rQMbCIHgyLkaELb7xqUB'; // Você pode tornar isso configurável se necessário

		// Configuração padrão para a conversão
		const conversionData = {
			send_to: `${googleAdsId}${conversionLabel}`,
			currency: 'BRL',
			...data,
		};

		// Rastrear a conversão
		(window as any).gtag('event', 'conversion', conversionData);
		console.log('Conversão rastreada:', conversionData);
	};

	// Função específica para rastrear compras concluídas
	const trackPurchase = (
		transactionId: string,
		value: number,
		currency: string = 'BRL'
	) => {
		trackConversion({
			value,
			currency,
			transaction_id: transactionId,
		});
	};

	return {
		trackEvent,
		trackConversion,
		trackPurchase,
	};
}
