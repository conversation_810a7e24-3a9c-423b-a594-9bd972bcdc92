'use client';

import React from 'react';

export function AnalyticsScript() {
	// return your script here
	return null;
}

export function useAnalytics() {
	const trackEvent = (event: string, data: Record<string, unknown>) => {
		// call your analytics service to track a custom event here
		console.info('tracking event', event, data);
	};

	return {
		trackEvent,
	};
}

// Componente para rastrear compras/conversões
export function PurchaseTracker({
	transactionId,
	value,
}: {
	transactionId: string;
	value: number;
}) {
	const { trackEvent } = useAnalytics();

	// Efeito para rastrear a compra quando o componente montar
	React.useEffect(() => {
		trackEvent('purchase_complete', {
			transaction_id: transactionId,
			value: value,
			currency: 'BRL',
		});

		console.info('Purchase tracked:', { transactionId, value });
	}, [transactionId, value, trackEvent]);

	// Retorna null - é um componente invisível só para tracking
	return null;
}
