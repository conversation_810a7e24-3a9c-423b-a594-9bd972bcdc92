'use client';

import { PurchaseTracker } from '../components/ConversionTracker';

interface PaymentConfirmationProps {
  orderId: string;
  amount: number;
}

/**
 * Exemplo de página de confirmação de pagamento
 * Este é apenas um exemplo de como usar o componente PurchaseTracker
 */
export function PaymentConfirmationExample({ orderId, amount }: PaymentConfirmationProps) {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Pagamento Confirmado!</h1>
      
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <p className="text-green-700 mb-2">
          Seu pagamento foi processado com sucesso.
        </p>
        <p className="text-sm text-gray-600">
          Número do pedido: <span className="font-medium">{orderId}</span>
        </p>
        <p className="text-sm text-gray-600">
          Valor: <span className="font-medium">R$ {amount.toFixed(2)}</span>
        </p>
      </div>
      
      <p className="text-gray-600 mb-4">
        Você receberá um e-mail de confirmação com os detalhes da sua compra.
      </p>
      
      <div className="mt-8">
        <a 
          href="/app/appointments" 
          className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90"
        >
          Ver minhas consultas
        </a>
      </div>
      
      {/* Componente invisível que rastreia a conversão */}
      <PurchaseTracker 
        transactionId={orderId}
        value={amount}
      />
    </div>
  );
}
