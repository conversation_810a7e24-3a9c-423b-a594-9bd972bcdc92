'use client';

import { useCallback } from 'react';
import { useAnalytics, ConversionData } from '../provider/google';

/**
 * Hook para rastrear conversões do Google Ads
 * 
 * Exemplo de uso:
 * 
 * const { trackPurchase } = useConversion();
 * 
 * // Em um evento de clique ou após uma compra
 * trackPurchase('ORDER123', 80.0);
 */
export function useConversion() {
  const { trackConversion, trackPurchase } = useAnalytics();
  
  /**
   * Rastreia uma conversão do lado do cliente
   */
  const trackClientConversion = useCallback((data: ConversionData) => {
    trackConversion(data);
  }, [trackConversion]);
  
  /**
   * Rastreia uma conversão do lado do servidor via API
   */
  const trackServerConversion = useCallback(async (data: ConversionData) => {
    try {
      const response = await fetch('/api/analytics/conversion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Falha ao rastrear conversão');
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro ao rastrear conversão:', error);
      return { success: false, error };
    }
  }, []);
  
  return {
    trackConversion: trackClientConversion,
    trackServerConversion,
    trackPurchase,
  };
}
