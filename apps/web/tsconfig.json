{"extends": "tsconfig/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "paths": {"@": ["./"], "@config": ["../../config"], "@analytics": ["./modules/analytics"], "@marketing/*": ["./modules/marketing/*"], "@saas/*": ["./modules/saas/*"], "@ui/*": ["./modules/ui/*"], "@i18n": ["./modules/i18n"], "@i18n/*": ["./modules/i18n/*"], "@shared/*": ["./modules/shared/*"], "content-collections": ["./.content-collections/generated"], "@lib/*": ["./lib/*"]}, "skipLibCheck": true, "noImplicitAny": false}, "include": ["**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", ".next/types/**/*.ts", "../../config.ts"], "exclude": ["node_modules"]}