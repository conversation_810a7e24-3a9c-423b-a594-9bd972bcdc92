// Definição do tipo Doctor para a aplicação web
export interface Doctor {
  id: string;
  userId?: string; // Campo opcional para compatibilidade
  user: {
    id?: string; // Opcional para compatibilidade
    name: string;
    email?: string;
    emailVerified?: boolean;
    phone?: string;
    avatarUrl?: string | null;
  } | null;
  crm: string;
  crmState: string;
  biography?: string | null;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  consultationPrice: number;
  consultationDuration: number;
  returnPeriod?: number;
  rating?: number;
  totalRatings?: number;
  onlineStatus?: 'ONLINE' | 'OFFLINE';
  documentStatus?: 'APPROVED' | 'PENDING' | 'REJECTED';
  consultTypes?: ('VIDEO' | 'AUDIO' | 'CHAT')[];
  memedToken?: string | null;
  returnEnabled?: boolean;
  asaasId?: string | null;
  specialties: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
  doctorSchedules: Array<{
    id: string;
    week_day?: number;
    weekDay?: number;
    start_time?: string;
    startTime?: string;
    end_time?: string;
    endTime?: string;
    is_enabled?: boolean;
    isEnabled?: boolean;
  }>;
  scheduleBlocks?: Array<{
    id: string;
    start_time?: string;
    startTime?: string;
    end_time?: string;
    endTime?: string;
    description?: string;
  }>;
  timeSlots?: Array<{
    id: string;
    startTime: string;
    endTime: string;
    isAvailable: boolean;
  }>;
  evaluations?: Array<{
    id: string;
    rating: number;
    comment?: string;
    createdAt: string;
  }>;
}
