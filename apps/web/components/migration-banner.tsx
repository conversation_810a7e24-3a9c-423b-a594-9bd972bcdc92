"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@ui/components/button";
import { X, ArrowR<PERSON>, Sparkles } from "lucide-react";

export function MigrationBanner() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 relative">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            <span className="font-semibold">Novidade!</span>
          </div>
          <span className="text-sm lg:text-base">
            Experimente o novo ZapChat V2 com interface melhorada e recursos avançados
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Link href="/app/zapchat">
            <Button
              variant="secondary"
              size="sm"
              className="bg-white text-blue-600 hover:bg-gray-100 font-medium"
            >
              Experimentar V2
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="text-white hover:bg-white/20 p-1"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
