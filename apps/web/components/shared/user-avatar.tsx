"use client";

import { AvatarProps } from "@radix-ui/react-avatar";
import { User } from "lucide-react";
import { useMemo } from "react";

import { cn } from "../../modules/ui/lib";
import { Avatar, AvatarFallback, AvatarImage } from "../../modules/ui/components/avatar";

interface UserAvatarProps extends AvatarProps {
  user: {
    name?: string | null;
    avatarUrl?: string | null;
    image?: string | null;
  };
}

export function UserAvatar({ user, className, ...props }: UserAvatarProps) {
  // Prioriza avatarUrl sobre image, para compatibilidade com diferentes fontes de dados
  const rawAvatarUrl = user.avatarUrl || user.image;

  // Formata a URL do avatar usando o mesmo padrão do UserAvatar.tsx que funciona
  const avatarUrl = useMemo(() => {
    if (!rawAvatarUrl) return undefined;

    const supabasePrefix = "https://moupvfqlulvqbzwajkif.supabase.co/storage/v1/object/public/avatars/";

    // Check if avatarUrl already contains the prefix


    // Apply the working Supabase URL pattern
    return `${supabasePrefix}${rawAvatarUrl}`;
  }, [rawAvatarUrl]);

  // Pega as iniciais do nome para o fallback
  const initials = user.name
    ? user.name
        .split(" ")
        .map((n) => n[0])
        .slice(0, 2)
        .join("")
        .toUpperCase()
    : "";

  return (
    <Avatar className={cn("bg-primary/10", className)} {...props}>
      {avatarUrl ? (
        <AvatarImage
          src={avatarUrl}
          alt={user.name || "Avatar do usuário"}
          referrerPolicy="no-referrer"
        />
      ) : null}
      <AvatarFallback className="bg-primary/10 text-primary">
        {initials || <User className="size-4" />}
      </AvatarFallback>
    </Avatar>
  );
}
