"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
// Keep this import if needed by other parts, or remove if avatar construction is done here
// import { getCompleteImageUrl } from "../../lib/image-utils";
import { useMemo } from 'react'; // Import useMemo

interface SignedAvatarProps {
  imagePath: string | null;
  name: string;
  // Bucket prop no longer needed if we get it from env var here
  // bucket: string;
  className?: string;
  fallback?: string;
}

/**
 * A component that displays an avatar from a PUBLIC storage bucket.
 */
export function SignedAvatar({ imagePath, name, className, fallback }: SignedAvatarProps) {

  const imageUrl = useMemo(() => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const bucketName = process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME;

    if (!imagePath || !supabaseUrl || !bucketName) {
      return "";
    }

    // Base public URL structure for Supabase storage
    const storagePublicUrlBase = `${supabaseUrl}/storage/v1/object/public`;

    // Clean the path: remove leading slash if present
    const cleanedPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

    // Construct the final URL
    const finalUrl = `${storagePublicUrlBase}/${bucketName}/${cleanedPath}`;

    // Optional: console.log for debugging the constructed URL
    // console.log("[SignedAvatar] Constructed Public URL:", finalUrl);

    return finalUrl;

  }, [imagePath]); // Recalculate only when imagePath changes

  // Get initials for fallback (keep this logic)
  const initials = name
    ? name
        .split(' ')
        .map(n => n[0])
        .slice(0, 2)
        .join('')
        .toUpperCase()
    : fallback || "?";

  // Remove useEffect and related state (isLoading, error, setImageUrl)
  /*
  useEffect(() => {
    // ... removed fetchSignedUrl logic ...
  }, [imagePath, bucket]);
  */

  return (
    <Avatar className={className}>
      {/* Directly use the calculated imageUrl */}
      {imageUrl && <AvatarImage src={imageUrl} alt={name || "Avatar"} />}
      {/* Display fallback if no imageUrl */}
      {!imageUrl && <AvatarFallback>{initials}</AvatarFallback>}
    </Avatar>
  );
}
