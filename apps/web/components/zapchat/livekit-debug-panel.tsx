"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Alert, AlertDescription } from "@ui/components/alert";
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  Wifi,
  WifiOff,
  Info
} from "lucide-react";

interface LiveKitConfig {
  apiKey: string;
  serverUrl: string;
  hasApiSecret: boolean;
}

export function LiveKitDebugPanel() {
  const [config, setConfig] = useState<LiveKitConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
    details?: any;
  } | null>(null);

  const checkConfiguration = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/livekit/config-check');
      const data = await response.json();

      if (response.ok) {
        setConfig(data);
        setTestResult({
          success: true,
          message: 'Configuração do LiveKit verificada com sucesso',
          details: data
        });
      } else {
        setTestResult({
          success: false,
          message: data.error || 'Erro ao verificar configuração',
          details: data
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Erro de conexão ao verificar configuração',
        details: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/livekit/test-connection');
      const data = await response.json();

      if (response.ok) {
        setTestResult({
          success: true,
          message: 'Conexão com LiveKit testada com sucesso',
          details: data
        });
      } else {
        setTestResult({
          success: false,
          message: data.error || 'Erro ao testar conexão',
          details: data
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Erro de conexão ao testar LiveKit',
        details: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkConfiguration();
  }, []);

  if (!config) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            LiveKit Debug Panel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2">Carregando configuração...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          LiveKit Debug Panel
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status da Configuração */}
        <div className="space-y-3">
          <h3 className="font-medium text-sm text-gray-700">Status da Configuração</h3>

          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">API Key</span>
              <Badge variant={config.apiKey ? "default" : "destructive"}>
                {config.apiKey ? "Configurada" : "Não configurada"}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">Server URL</span>
              <Badge variant={config.serverUrl ? "default" : "destructive"}>
                {config.serverUrl ? "Configurada" : "Não configurada"}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">API Secret</span>
              <Badge variant={config.hasApiSecret ? "default" : "destructive"}>
                {config.hasApiSecret ? "Configurada" : "Não configurada"}
              </Badge>
            </div>
          </div>
        </div>

        {/* Ações */}
        <div className="flex gap-2">
          <Button
            onClick={checkConfiguration}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Verificar Config
          </Button>

          <Button
            onClick={testConnection}
            disabled={isLoading || !config.apiKey || !config.serverUrl}
            size="sm"
          >
            <Wifi className="h-4 w-4 mr-2" />
            Testar Conexão
          </Button>
        </div>

        {/* Resultado dos Testes */}
        {testResult && (
          <Alert variant={testResult.success ? "default" : "destructive"}>
            {testResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <AlertDescription>
              {testResult.message}
              {testResult.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm font-medium">
                    Detalhes técnicos
                  </summary>
                  <pre className="mt-2 text-xs bg-black/10 p-2 rounded overflow-auto">
                    {JSON.stringify(testResult.details, null, 2)}
                  </pre>
                </details>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Informações de Ajuda */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Problemas comuns:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• Verifique se as variáveis de ambiente estão configuradas no arquivo .env.local</li>
              <li>• Certifique-se de que NEXT_PUBLIC_LIVEKIT_URL começa com "wss://"</li>
              <li>• Confirme se as credenciais do LiveKit estão corretas</li>
              <li>• Reinicie o servidor após alterar as variáveis de ambiente</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
