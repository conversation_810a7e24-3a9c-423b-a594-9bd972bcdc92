"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Paperclip,
  Phone,
  Video,
  ArrowLeft,
  MoreVertical,
  Heart,
  FileText,
  Camera,
  Image as ImageIcon,
  UserCheck,
  Star,
  CheckCheck,
  Wifi,
  WifiOff,
  AlertCircle,
  Shield,
  Clock
} from "lucide-react";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@ui/components/dropdown-menu";
import { useUser } from "@saas/auth/hooks/use-user";

// Tipos
import { ChatMessage, ConnectionStatus, TypingStatus } from "../../lib/services/supabase-realtime.service";

interface PatientMobileInterfaceProps {
  consultation: {
    id: string;
    doctor_name?: string;
    doctor_avatar?: string;
    doctor_specialty?: string;
    doctor_rating?: number;
    is_on_duty?: boolean;
    estimated_wait_time?: number;
  };

  userId: string;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  typingUsers: TypingStatus[];

  onSendMessage: (content: string) => Promise<void>;
  onSendAudio: (audioBlob: Blob) => Promise<void>;
  onSendFile: (file: File) => Promise<void>;
  onStartTyping: () => void;
  onStopTyping: () => void;

  onBack?: () => void;
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onRateDoctor?: () => void;
  onEndConsultation?: () => void;
}

export function PatientMobileInterface({
  consultation,
  userId,
  messages,
  isLoading,
  error,
  connectionStatus,
  isConnected,
  typingUsers,
  onSendMessage,
  onSendAudio,
  onSendFile,
  onStartTyping,
  onStopTyping,
  onBack,
  onVideoCall,
  onAudioCall,
  onRateDoctor,
  onEndConsultation,
}: PatientMobileInterfaceProps) {
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get user for custom background
  const { user } = useUser();

  // Auto-scroll para nova mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Configurar MediaRecorder
  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);
            try {
              await onSendAudio(audioBlob);
            } catch (error) {
              toast.error("Erro ao enviar áudio");
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
        });
    }
  }, [onSendAudio, audioChunks]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const content = inputValue.trim();
    setInputValue("");
    onStopTyping();

    try {
      await onSendMessage(content);
    } catch (error) {
      setInputValue(content);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim()) {
      onStartTyping();
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      onStopTyping();
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
      toast.info("Gravando áudio...");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const sendQuickMessage = async (message: string) => {
    try {
      await onSendMessage(message);
      setShowQuickActions(false);
    } catch (error) {
      toast.error("Erro ao enviar mensagem");
    }
  };

  const formatMessageTime = (createdAt: string) => {
    const messageDate = new Date(createdAt);
    return format(messageDate, "HH:mm", { locale: ptBR });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "w-3 h-3",
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 relative">
      {/* Header paciente */}
      <div className="bg-green-600 text-white px-4 py-3 flex items-center justify-between shadow-md relative z-10">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {onBack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}

          <Avatar className="w-10 h-10 ring-2 ring-white/30">
            <AvatarImage src={consultation.doctor_avatar} alt={consultation.doctor_name} />
            <AvatarFallback className="bg-green-500 text-white font-medium">
              <UserCheck className="w-5 h-5" />
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h1 className="font-medium truncate text-white">
              Dr. {consultation.doctor_name || 'Médico'}
            </h1>
            <div className="flex items-center gap-2">
              <Badge
                variant={consultation.is_on_duty ? "secondary" : "outline"}
                className="text-xs bg-green-700 text-white border-green-500"
              >
                {consultation.is_on_duty ? "Plantão" : "Consulta"}
              </Badge>

              {consultation.doctor_rating && (
                <div className="flex items-center gap-1">
                  {renderStars(consultation.doctor_rating)}
                </div>
              )}

              <div className="flex items-center gap-1">
                {isConnected ? (
                  <Wifi className="w-3 h-3" />
                ) : (
                  <WifiOff className="w-3 h-3 text-red-300" />
                )}
                <span className="text-xs">
                  {isConnected ? 'online' : 'offline'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Ações do header paciente */}
        <div className="flex items-center gap-1">
          {onVideoCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onVideoCall}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <Video className="w-5 h-5" />
            </Button>
          )}

          {onAudioCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onAudioCall}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <Phone className="w-5 h-5" />
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-green-700 p-2 h-auto"
              >
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <FileText className="w-4 h-4 mr-2" />
                Receitas
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Camera className="w-4 h-4 mr-2" />
                Exames
              </DropdownMenuItem>
              {onRateDoctor && (
                <DropdownMenuItem onClick={onRateDoctor}>
                  <Star className="w-4 h-4 mr-2" />
                  Avaliar Médico
                </DropdownMenuItem>
              )}
              {onEndConsultation && (
                <DropdownMenuItem onClick={onEndConsultation} className="text-red-600">
                  <Clock className="w-4 h-4 mr-2" />
                  Finalizar Consulta
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Info da especialidade */}
      {consultation.doctor_specialty && (
        <div className="bg-green-50 border-b border-green-200 px-4 py-2 flex items-center gap-2">
          <Shield className="w-4 h-4 text-green-600" />
          <span className="text-sm text-green-800 font-medium">
            {consultation.doctor_specialty}
          </span>
          {consultation.estimated_wait_time && (
            <Badge variant="outline" className="ml-auto text-xs">
              ~{consultation.estimated_wait_time}min
            </Badge>
          )}
        </div>
      )}

      {/* Wallpaper paciente */}
      <div
        className="absolute inset-0 z-0 opacity-5"
        style={{
          backgroundImage: user?.chatBackgroundUrl
            ? `url(${user.chatBackgroundUrl})`
            : 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Cpath d="M30 30c0-8.3-6.7-15-15-15s-15 6.7-15 15 6.7 15 15 15 15-6.7 15-15zm15 0c0-8.3-6.7-15-15-15s-15 6.7-15 15 6.7 15 15 15 15-6.7 15-15z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        }}
      />

      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto px-4 py-2 space-y-3 relative z-1">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-xs mx-auto">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <Heart className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Conversa com seu médico
              </h3>
              <p className="text-gray-500 text-sm">
                Descreva seus sintomas e tire suas dúvidas
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwnMessage = message.senderId === userId;
              const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;

              return (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-2 max-w-[85%]",
                    isOwnMessage ? "ml-auto flex-row-reverse" : "mr-auto"
                  )}
                >
                  {!isOwnMessage && (
                    <div className="w-8 h-8 flex-shrink-0">
                      {showAvatar ? (
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={consultation.doctor_avatar} alt={consultation.doctor_name} />
                          <AvatarFallback className="bg-green-100 text-green-600 text-xs">
                            <UserCheck className="w-4 h-4" />
                          </AvatarFallback>
                        </Avatar>
                      ) : null}
                    </div>
                  )}

                  <div
                    className={cn(
                      "relative max-w-full px-3 py-2 rounded-lg shadow-sm",
                      isOwnMessage
                        ? "bg-green-500 text-white rounded-br-none"
                        : "bg-white text-gray-900 rounded-bl-none border"
                    )}
                  >
                    <p className="text-sm leading-relaxed break-words">
                      {message.content}
                    </p>

                    <div className={cn(
                      "flex items-center justify-end gap-1 mt-1",
                      isOwnMessage ? "text-green-100" : "text-gray-500"
                    )}>
                      <span className="text-xs">
                        {formatMessageTime(message.createdAt)}
                      </span>
                      {isOwnMessage && <CheckCheck className="w-3 h-3" />}
                    </div>

                    {/* Seta da bolha */}
                    <div
                      className={cn(
                        "absolute bottom-0 w-0 h-0",
                        isOwnMessage
                          ? "right-0 translate-x-1 border-l-8 border-l-green-500 border-b-8 border-b-transparent"
                          : "left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent"
                      )}
                    />
                  </div>
                </div>
              );
            })}

            {/* Indicador de digitação */}
            {typingUsers.length > 0 && (
              <div className="flex gap-2 max-w-[85%] mr-auto">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={consultation.doctor_avatar} alt={consultation.doctor_name} />
                  <AvatarFallback className="bg-green-100 text-green-600 text-xs">
                    <UserCheck className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>

                <div className="bg-white rounded-lg rounded-bl-none px-3 py-2 shadow-sm border relative">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span className="text-xs text-gray-500">Dr. {consultation.doctor_name} está digitando...</span>
                  </div>
                  <div className="absolute bottom-0 left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent" />
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Ações rápidas do paciente */}
      {showQuickActions && (
        <div className="bg-white border-t border-gray-200 p-3 grid grid-cols-2 gap-2 relative z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Estou sentindo dor...")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Sinto dor</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Tenho febre...")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Febre</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Preciso de uma receita...")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Receita</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Quando devo retornar?")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Retorno</span>
          </Button>
        </div>
      )}

      {/* Footer paciente */}
      <div className="bg-white border-t border-gray-200 px-3 py-2 relative z-10">
        {error && (
          <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-600 flex-1">{error}</span>
          </div>
        )}

        <div className="flex items-end gap-2">
          {/* Botão de ações rápidas */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowQuickActions(!showQuickActions)}
            className={cn(
              "p-2 h-12",
              showQuickActions && "bg-green-100 border-green-300"
            )}
          >
            <Heart className="w-5 h-5" />
          </Button>

          {/* Input principal */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Descreva seus sintomas..."
              className="rounded-full border-gray-300 pr-16 py-2 min-h-[44px]"
              disabled={!isConnected}
            />

            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) onSendFile(file);
                }}
                accept="image/*,.pdf,.doc,.docx"
              />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                className="p-1 h-auto text-gray-500 hover:text-gray-700"
                disabled={!isConnected}
              >
                <Paperclip className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Botão de envio/áudio */}
          {inputValue.trim() ? (
            <Button
              onClick={handleSendMessage}
              className="rounded-full w-12 h-12 bg-green-600 hover:bg-green-700 p-0"
              disabled={!isConnected}
            >
              <Send className="w-5 h-5" />
            </Button>
          ) : (
            <Button
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              onMouseLeave={stopRecording}
              className={cn(
                "rounded-full w-12 h-12 p-0 transition-all",
                isRecording
                  ? "bg-red-600 hover:bg-red-700 animate-pulse"
                  : "bg-green-600 hover:bg-green-700"
              )}
              disabled={!isConnected || !mediaRecorder}
            >
              <Mic className="w-5 h-5" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
