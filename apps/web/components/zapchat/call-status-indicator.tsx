"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Off, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Wifi, WifiOff, <PERSON><PERSON><PERSON><PERSON>gle, Phone } from "lucide-react";

interface CallStatusIndicatorProps {
  isVisible: boolean;
  participantName: string;
  callType: 'video' | 'audio';
  status: 'idle' | 'connecting' | 'connected' | 'disconnected' | 'error';
  localAudioEnabled: boolean;
  localVideoEnabled: boolean;
  participantCount: number;
  onToggleAudio?: () => void;
  onToggleVideo?: () => void;
  onEndCall?: () => void;
  connectionQuality?: 'excellent' | 'good' | 'poor';
}

export function CallStatusIndicator({
  isVisible,
  participantName,
  callType,
  status,
  localAudioEnabled,
  localVideoEnabled,
  participantCount,
  onToggleAudio,
  onToggleVideo,
  onEndCall,
  connectionQuality = 'good'
}: CallStatusIndicatorProps) {
  const [showControls, setShowControls] = useState(false);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (status === 'connected') {
      interval = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);
    } else {
      setDuration(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [status]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'idle':
      case 'disconnected':
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return `Conectado • ${formatDuration(duration)}`;
      case 'connecting':
        return 'Conectando...';
      case 'error':
        return 'Erro de conexão';
      case 'idle':
      case 'disconnected':
      default:
        return 'Desconectado';
    }
  };

  const getConnectionIcon = () => {
    switch (connectionQuality) {
      case 'excellent':
        return <Wifi className="w-3 h-3 text-green-600" />;
      case 'poor':
        return <WifiOff className="w-3 h-3 text-red-600" />;
      default:
        return <Wifi className="w-3 h-3 text-yellow-600" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
      <div
        className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[280px] cursor-pointer"
        onClick={() => setShowControls(!showControls)}
      >
        {/* Main Status */}
        <div className="flex items-center gap-3">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}>
            {status === 'connecting' && (
              <div className="w-3 h-3 rounded-full bg-current animate-pulse" />
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              {callType === 'video' ? (
                <Video className="w-4 h-4 text-green-600" />
              ) : (
                <Phone className="w-4 h-4 text-blue-600" />
              )}
              <span className="text-sm font-medium text-gray-900 truncate">
                {participantName}
              </span>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500">
                {getStatusText()}
              </span>
              {participantCount > 1 && (
                <div className="flex items-center gap-1">
                  <Users className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-500">{participantCount}</span>
                </div>
              )}
              {getConnectionIcon()}
            </div>
          </div>

          {/* Quick Status Icons */}
          <div className="flex items-center gap-1">
            {!localAudioEnabled && (
              <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                <MicOff className="w-3 h-3 text-red-600" />
              </div>
            )}
            {callType === 'video' && !localVideoEnabled && (
              <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                <VideoOff className="w-3 h-3 text-red-600" />
              </div>
            )}
            {status === 'error' && (
              <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-3 h-3 text-red-600" />
              </div>
            )}
          </div>
        </div>

        {/* Expanded Controls */}
        {showControls && status === 'connected' && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-center gap-2">
              {/* Mute/Unmute */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleAudio?.();
                }}
                className={`p-2 rounded-full transition-colors ${
                  localAudioEnabled
                    ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    : 'bg-red-500 hover:bg-red-600 text-white'
                }`}
                title={localAudioEnabled ? 'Silenciar' : 'Ativar áudio'}
              >
                {localAudioEnabled ? (
                  <Mic className="w-4 h-4" />
                ) : (
                  <MicOff className="w-4 h-4" />
                )}
              </button>

              {/* Video Toggle (only for video calls) */}
              {callType === 'video' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggleVideo?.();
                  }}
                  className={`p-2 rounded-full transition-colors ${
                    localVideoEnabled
                      ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                      : 'bg-red-500 hover:bg-red-600 text-white'
                  }`}
                  title={localVideoEnabled ? 'Desligar câmera' : 'Ligar câmera'}
                >
                  {localVideoEnabled ? (
                    <Video className="w-4 h-4" />
                  ) : (
                    <VideoOff className="w-4 h-4" />
                  )}
                </button>
              )}

              {/* End Call */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEndCall?.();
                }}
                className="p-2 bg-red-500 hover:bg-red-600 rounded-full text-white transition-colors"
                title="Encerrar chamada"
              >
                <Phone className="w-4 h-4 transform rotate-180" />
              </button>
            </div>

            {/* Connection Quality */}
            <div className="mt-2 text-center">
              <span className={`text-xs ${
                connectionQuality === 'excellent' ? 'text-green-600' :
                connectionQuality === 'poor' ? 'text-red-600' :
                'text-yellow-600'
              }`}>
                Conexão: {
                  connectionQuality === 'excellent' ? 'Excelente' :
                  connectionQuality === 'poor' ? 'Instável' :
                  'Boa'
                }
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
