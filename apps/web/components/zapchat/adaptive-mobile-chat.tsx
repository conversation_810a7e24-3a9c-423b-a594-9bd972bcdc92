"use client";

import { useMobileChat } from "../../lib/hooks/use-mobile-chat";
import { DoctorMobileInterface } from "./doctor-mobile-interface";
import { PatientMobileInterface } from "./patient-mobile-interface";
import { MobileChatFullSkeleton } from "../skeletons/mobile-chat-skeleton";

interface AdaptiveMobileChatProps {
  consultation: {
    id: string;
    doctor_name?: string;
    doctor_avatar?: string;
    doctor_specialty?: string;
    doctor_rating?: number;
    patient_name?: string;
    patient_avatar?: string;
    patient_age?: number;
    is_on_duty?: boolean;
    urgency_level?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    medical_condition?: string;
    estimated_wait_time?: number;
  };

  user: {
    id: string;
    name: string;
    role: 'DOCTOR' | 'PATIENT';
  };

  // Callbacks opcionais
  onBack?: () => void;
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onOpenMedicalRecord?: () => void;
  onPrescription?: () => void;
  onRateDoctor?: () => void;
  onEndConsultation?: () => void;
}

export function AdaptiveMobileChat({
  consultation,
  user,
  onBack,
  onVideoCall,
  onAudioCall,
  onOpenMedicalRecord,
  onPrescription,
  onRateDoctor,
  onEndConsultation,
}: AdaptiveMobileChatProps) {
  // Hook otimizado para mobile
  const chat = useMobileChat({
    appointmentId: consultation.id,
    userId: user.id,
    userName: user.name,
    userRole: user.role,
    autoConnect: true,
    enableTypingIndicators: true,
  });

  // Loading inicial
  if (chat.isLoading && chat.messages.length === 0) {
    return <MobileChatFullSkeleton />;
  }

  // Interface específica para médicos
  if (user.role === 'DOCTOR') {
    return (
      <DoctorMobileInterface
        consultation={consultation}
        userId={user.id}
        messages={chat.messages}
        isLoading={chat.isLoading}
        error={chat.error}
        connectionStatus={chat.connectionStatus}
        isConnected={chat.isConnected}
        typingUsers={chat.typingUsers}
        onSendMessage={chat.sendMessage}
        onSendAudio={chat.sendAudio}
        onSendFile={chat.sendFile}
        onStartTyping={chat.startTyping}
        onStopTyping={chat.stopTyping}
        onBack={onBack}
        onVideoCall={onVideoCall}
        onAudioCall={onAudioCall}
        onOpenMedicalRecord={onOpenMedicalRecord}
        onPrescription={onPrescription}
        onEndConsultation={onEndConsultation}
      />
    );
  }

  // Interface específica para pacientes
  return (
    <PatientMobileInterface
      consultation={consultation}
      userId={user.id}
      messages={chat.messages}
      isLoading={chat.isLoading}
      error={chat.error}
      connectionStatus={chat.connectionStatus}
      isConnected={chat.isConnected}
      typingUsers={chat.typingUsers}
      onSendMessage={chat.sendMessage}
      onSendAudio={chat.sendAudio}
      onSendFile={chat.sendFile}
      onStartTyping={chat.startTyping}
      onStopTyping={chat.stopTyping}
      onBack={onBack}
      onVideoCall={onVideoCall}
      onAudioCall={onAudioCall}
      onRateDoctor={onRateDoctor}
      onEndConsultation={onEndConsultation}
    />
  );
}
