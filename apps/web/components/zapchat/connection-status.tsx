"use client";

import { Badge } from "@ui/components/badge";
import { Wifi, WifiOff, RefreshCw } from "lucide-react";

type ConnectionStatus = "connected" | "connecting" | "disconnected" | "reconnecting" | "error";

interface ConnectionStatusProps {
  status: ConnectionStatus;
  className?: string;
}

export function ConnectionStatus({ status, className = "" }: ConnectionStatusProps) {
  const getStatusConfig = () => {
    switch (status) {
      case "connected":
        return {
          icon: <Wifi className="w-3 h-3 lg:w-4 lg:h-4" />,
          label: "Conectado",
          badgeStatus: "success" as const,
          className: "text-green-600 bg-green-50 border-green-200"
        };
      case "connecting":
        return {
          icon: <RefreshCw className="w-3 h-3 lg:w-4 lg:h-4 animate-spin" />,
          label: "Conectando...",
          badgeStatus: "warning" as const,
          className: "text-yellow-600 bg-yellow-50 border-yellow-200"
        };
      case "reconnecting":
        return {
          icon: <RefreshCw className="w-3 h-3 lg:w-4 lg:h-4 animate-spin" />,
          label: "Reconectando...",
          badgeStatus: "warning" as const,
          className: "text-orange-600 bg-orange-50 border-orange-200"
        };
      case "error":
        return {
          icon: <WifiOff className="w-3 h-3 lg:w-4 lg:h-4" />,
          label: "Erro",
          badgeStatus: "error" as const,
          className: "text-red-600 bg-red-50 border-red-200"
        };
      case "disconnected":
      default:
        return {
          icon: <WifiOff className="w-3 h-3 lg:w-4 lg:h-4" />,
          label: "Desconectado",
          badgeStatus: "error" as const,
          className: "text-gray-600 bg-gray-50 border-gray-200"
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-1 lg:py-2 rounded-lg border ${config.className} ${className}`}>
      {config.icon}
      <span className="text-xs lg:text-sm font-medium hidden sm:inline">{config.label}</span>
    </div>
  );
}

export function SimpleConnectionStatus({ status }: { status: ConnectionStatus }) {
  const getIndicatorColor = () => {
    switch (status) {
      case "connected":
        return "bg-green-500";
      case "connecting":
      case "reconnecting":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      case "disconnected":
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "connected":
        return "Online";
      case "connecting":
        return "Conectando...";
      case "reconnecting":
        return "Reconectando...";
      case "error":
        return "Erro";
      case "disconnected":
      default:
        return "Offline";
    }
  };

  return (
    <div className="flex items-center gap-1 lg:gap-2">
      <div className={`w-2 h-2 rounded-full ${getIndicatorColor()}`} />
      <span className="text-xs lg:text-sm text-gray-600 hidden sm:inline">{getStatusText()}</span>
    </div>
  );
}