"use client";

import { useState } from "react";
import { Video, Phone, X, AlertCircle, Loader2 } from "lucide-react";

interface CallConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (type: 'video' | 'audio') => Promise<void>;
  participantName: string;
  callType: 'video' | 'audio';
}

export function CallConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  participantName,
  callType
}: CallConfirmationDialogProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      await onConfirm(callType);
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erro ao iniciar chamada');
    } finally {
      setIsConnecting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Dialog */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              {callType === 'video' ? 'Chamada de Vídeo' : 'Chamada de Áudio'}
            </h3>
            <button
              onClick={onClose}
              disabled={isConnecting}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                callType === 'video' ? 'bg-green-100' : 'bg-blue-100'
              }`}>
                {callType === 'video' ? (
                  <Video className={`w-6 h-6 ${callType === 'video' ? 'text-green-600' : 'text-blue-600'}`} />
                ) : (
                  <Phone className="w-6 h-6 text-blue-600" />
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  Ligar para {participantName}
                </p>
                <p className="text-sm text-gray-500">
                  {callType === 'video'
                    ? 'Iniciar uma chamada de vídeo'
                    : 'Iniciar uma chamada de áudio'
                  }
                </p>
              </div>
            </div>

            {/* Permissions Notice */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex gap-2">
                <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">Permissões necessárias</p>
                  <p className="mt-1">
                    {callType === 'video'
                      ? 'Será solicitado acesso à sua câmera e microfone.'
                      : 'Será solicitado acesso ao seu microfone.'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex gap-2">
                  <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-red-800">
                    <p className="font-medium">Erro</p>
                    <p className="mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Call Quality Settings */}
            <div className="space-y-3 mb-6">
              <h4 className="text-sm font-medium text-gray-900">Configurações da chamada</h4>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Qualidade HD</p>
                    <p className="text-xs text-gray-500">
                      {callType === 'video' ? '720p, 30fps' : 'Áudio de alta qualidade'}
                    </p>
                  </div>
                </div>
                <div className="text-xs text-green-600 font-medium">Ativo</div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Cancelamento de ruído</p>
                    <p className="text-xs text-gray-500">Reduz ruídos de fundo</p>
                  </div>
                </div>
                <div className="text-xs text-blue-600 font-medium">Ativo</div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 p-4 bg-gray-50 border-t border-gray-200">
            <button
              onClick={onClose}
              disabled={isConnecting}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleConfirm}
              disabled={isConnecting}
              className={`flex-1 px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${
                callType === 'video'
                  ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
              }`}
            >
              {isConnecting ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Conectando...
                </div>
              ) : (
                `Iniciar ${callType === 'video' ? 'Vídeo' : 'Áudio'}`
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
