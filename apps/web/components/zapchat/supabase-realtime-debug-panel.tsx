"use client";

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Settings,
  Database,
  Activity,
  Zap,
  X,
  Play,
  TestTube
} from 'lucide-react';
import { createClient } from '@supabase/supabase-js';
import { getSupabaseConfig } from '../../lib/config/supabase.config';
import { SupabaseTestUtil, quickSupabaseTest } from '../../lib/utils/supabase-test';

interface TestResult {
  success: boolean;
  error?: string;
  data?: any;
}

interface DebugInfo {
  supabaseUrl: string;
  supabaseKey: string;
  isConfigured: boolean;
  connectionStatus: 'unknown' | 'connecting' | 'connected' | 'error';
  lastError?: string;
  channels: Array<{
    name: string;
    status: string;
    subscriptionCount: number;
    lastEvent?: string;
  }>;
  realtimeEnabled: boolean;
  databaseStatus: 'unknown' | 'connected' | 'error';
  testResults: Array<{
    test: string;
    status: 'pending' | 'success' | 'error';
    message: string;
    timestamp: Date;
  }>;
}

export function SupabaseRealtimeDebugPanel() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    supabaseUrl: '',
    supabaseKey: '',
    isConfigured: false,
    connectionStatus: 'unknown',
    channels: [],
    realtimeEnabled: false,
    databaseStatus: 'unknown',
    testResults: []
  });

  const [isVisible, setIsVisible] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testAppointmentId, setTestAppointmentId] = useState('test-appointment-123');
  const [testUserId, setTestUserId] = useState('test-user-456');

  const supabaseClient = useRef<any>(null);
  const testChannel = useRef<any>(null);

  // Carregar informações de debug
  useEffect(() => {
    loadDebugInfo();
  }, []);

  const loadDebugInfo = () => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    setDebugInfo(prev => ({
      ...prev,
      supabaseUrl,
      supabaseKey: supabaseKey ? `${supabaseKey.substring(0, 20)}...` : '',
      isConfigured: !!(supabaseUrl && supabaseKey),
    }));
  };

    // Testar conexão básica do Supabase
  const testBasicConnection = async () => {
    addTestResult('Conexão Básica', 'pending', 'Testando conexão básica...');

    try {
      const result = await SupabaseTestUtil.checkEnvironment();
      if (!result) {
        throw new Error('Variáveis de ambiente não configuradas');
      }

      const tester = new SupabaseTestUtil();
      const testResult = await tester.testBasicConnection() as TestResult;

      if (testResult.success) {
        addTestResult('Conexão Básica', 'success', 'Conexão básica estabelecida com sucesso');
        setDebugInfo(prev => ({ ...prev, databaseStatus: 'connected' }));
      } else {
        throw new Error(testResult.error || 'Falha na conexão');
      }

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erro desconhecido';
      addTestResult('Conexão Básica', 'error', `Falha na conexão: ${message}`);
      setDebugInfo(prev => ({ ...prev, databaseStatus: 'error' }));
    }
  };

    // Testar Realtime
  const testRealtime = async () => {
    addTestResult('Realtime', 'pending', 'Testando conexão Realtime...');

    try {
      const tester = new SupabaseTestUtil();
      const testResult = await tester.testRealtime() as TestResult;

      if (testResult.success) {
        addTestResult('Realtime', 'success', 'Canal Realtime conectado com sucesso');
        setDebugInfo(prev => ({ ...prev, realtimeEnabled: true }));
      } else {
        throw new Error(testResult.error || 'Falha no Realtime');
      }

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erro desconhecido';
      addTestResult('Realtime', 'error', `Falha no Realtime: ${message}`);
      setDebugInfo(prev => ({ ...prev, realtimeEnabled: false }));
    }
  };

    // Testar canal específico
  const testSpecificChannel = async () => {
    addTestResult('Canal Específico', 'pending', `Testando canal para appointment: ${testAppointmentId}`);

    try {
      const tester = new SupabaseTestUtil();
      const testResult = await tester.testMessagesChannel(testAppointmentId) as TestResult;

      if (testResult.success) {
        addTestResult('Canal Específico', 'success', 'Canal de mensagens conectado com sucesso');
      } else {
        throw new Error(testResult.error || 'Falha no canal');
      }

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erro desconhecido';
      addTestResult('Canal Específico', 'error', `Falha no canal: ${message}`);
    }
  };

    // Executar todos os testes
  const runAllTests = async () => {
    setIsTesting(true);
    setDebugInfo(prev => ({ ...prev, testResults: [] }));

    try {
      // Usar o utilitário de teste integrado
      const result = await quickSupabaseTest(testAppointmentId) as any;

      if (result.success || result.allPassed) {
        addTestResult('Teste Completo', 'success', 'Todos os testes passaram com sucesso!');
        setDebugInfo(prev => ({
          ...prev,
          databaseStatus: 'connected',
          realtimeEnabled: true,
          connectionStatus: 'connected'
        }));
      } else {
        addTestResult('Teste Completo', 'error', `Alguns testes falharam: ${result.error || 'Erro desconhecido'}`);
      }

    } catch (error) {
      console.error('Erro durante testes:', error);
      addTestResult('Teste Completo', 'error', `Erro durante execução: ${error}`);
    } finally {
      setIsTesting(false);
    }
  };

  // Adicionar resultado de teste
  const addTestResult = (test: string, status: 'pending' | 'success' | 'error', message: string) => {
    setDebugInfo(prev => ({
      ...prev,
      testResults: [
        ...prev.testResults,
        {
          test,
          status,
          message,
          timestamp: new Date()
        }
      ]
    }));
  };

  // Limpar resultados
  const clearResults = () => {
    setDebugInfo(prev => ({ ...prev, testResults: [] }));
  };

    // Verificar configuração do banco
  const checkDatabaseConfig = async () => {
    addTestResult('Configuração DB', 'pending', 'Verificando configuração do banco...');

    try {
      // Verificar se a tabela messages existe
      const config = getSupabaseConfig();
      const client = createClient(
        config.url,
        config.anonKey
      );

      // Verificar se a tabela messages existe
      const { data: tables, error } = await client
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'messages');

      if (error) {
        throw error;
      }

      if (tables && tables.length > 0) {
        addTestResult('Configuração DB', 'success', 'Tabela messages encontrada');

        // Verificar se realtime está habilitado testando um canal
        addTestResult('Realtime DB', 'pending', 'Verificando se realtime está habilitado...');

        const testResult = await new SupabaseTestUtil().testRealtime() as TestResult;
        if (testResult.success) {
          addTestResult('Realtime DB', 'success', 'Realtime está funcionando no banco');
        } else {
          addTestResult('Realtime DB', 'error', 'Realtime não está habilitado no banco');
        }

      } else {
        addTestResult('Configuração DB', 'error', 'Tabela messages não encontrada');
      }

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Erro desconhecido';
      addTestResult('Configuração DB', 'error', `Erro na verificação: ${message}`);
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className="fixed bottom-4 right-4 z-50"
      >
        <TestTube className="w-4 h-4 mr-2" />
        Debug Supabase
      </Button>
    );
  }

  return (
    <div className="fixed inset-4 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="bg-gray-50 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5 text-blue-600" />
              Debug Panel - Supabase Realtime
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Configuração */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Configuração</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Supabase URL</Label>
                  <Input
                    value={debugInfo.supabaseUrl}
                    readOnly
                    className="font-mono text-sm"
                  />
                </div>
                <div>
                  <Label>Supabase Key</Label>
                  <Input
                    value={debugInfo.supabaseKey}
                    readOnly
                    className="font-mono text-sm"
                  />
                </div>
              </div>
              <div className="mt-2">
                <Badge className={debugInfo.isConfigured ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                  {debugInfo.isConfigured ? "Configurado" : "Não Configurado"}
                </Badge>
              </div>
            </div>

            {/* Status da Conexão */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Status da Conexão</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 border rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    {debugInfo.databaseStatus === 'connected' ? (
                      <CheckCircle className="w-6 h-6 text-green-500" />
                    ) : debugInfo.databaseStatus === 'error' ? (
                      <AlertCircle className="w-6 h-6 text-red-500" />
                    ) : (
                      <Clock className="w-6 h-6 text-yellow-500" />
                    )}
                  </div>
                  <div className="text-sm font-medium">Database</div>
                  <Badge className={debugInfo.databaseStatus === 'connected' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {debugInfo.databaseStatus === 'connected' ? 'Conectado' :
                     debugInfo.databaseStatus === 'error' ? 'Erro' : 'Desconhecido'}
                  </Badge>
                </div>

                <div className="text-center p-3 border rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    {debugInfo.realtimeEnabled ? (
                      <Zap className="w-6 h-6 text-green-500" />
                    ) : (
                      <WifiOff className="w-6 h-6 text-red-500" />
                    )}
                  </div>
                  <div className="text-sm font-medium">Realtime</div>
                  <Badge className={debugInfo.realtimeEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {debugInfo.realtimeEnabled ? 'Habilitado' : 'Desabilitado'}
                  </Badge>
                </div>

                <div className="text-center p-3 border rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    {debugInfo.connectionStatus === 'connected' ? (
                      <Wifi className="w-6 h-6 text-green-500" />
                    ) : debugInfo.connectionStatus === 'error' ? (
                      <AlertCircle className="w-6 h-6 text-red-500" />
                    ) : (
                      <Clock className="w-6 h-6 text-yellow-500" />
                    )}
                  </div>
                  <div className="text-sm font-medium">Conexão</div>
                  <Badge className={debugInfo.connectionStatus === 'connected' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {debugInfo.connectionStatus === 'connected' ? 'Conectado' :
                     debugInfo.connectionStatus === 'error' ? 'Erro' : 'Desconhecido'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Testes */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Testes de Conexão</h3>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <Label>Appointment ID de Teste</Label>
                  <Input
                    value={testAppointmentId}
                    onChange={(e) => setTestAppointmentId(e.target.value)}
                    placeholder="ID da consulta para teste"
                  />
                </div>
                <div>
                  <Label>User ID de Teste</Label>
                  <Input
                    value={testUserId}
                    onChange={(e) => setTestUserId(e.target.value)}
                    placeholder="ID do usuário para teste"
                  />
                </div>
              </div>

              <div className="flex gap-2 mb-4">
                <Button
                  onClick={runAllTests}
                  disabled={isTesting}
                  className="flex items-center gap-2"
                >
                  {isTesting ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  {isTesting ? 'Executando...' : 'Executar Todos os Testes'}
                </Button>

                <Button
                  onClick={testBasicConnection}
                  variant="outline"
                  disabled={isTesting}
                >
                  Testar Conexão Básica
                </Button>

                <Button
                  onClick={testRealtime}
                  variant="outline"
                  disabled={isTesting}
                >
                  Testar Realtime
                </Button>

                <Button
                  onClick={testSpecificChannel}
                  variant="outline"
                  disabled={isTesting}
                >
                  Testar Canal
                </Button>

                <Button
                  onClick={checkDatabaseConfig}
                  variant="outline"
                  disabled={isTesting}
                >
                  Verificar DB
                </Button>

                                <Button
                  onClick={clearResults}
                  variant="outline"
                >
                  Limpar
                </Button>

                <Button
                  onClick={() => {
                    // Expor utilitários no console para debug
                    (window as any).SupabaseTestUtil = SupabaseTestUtil;
                    (window as any).quickSupabaseTest = quickSupabaseTest;
                    console.log('🧪 Utilitários de teste expostos no console:');
                    console.log('  - SupabaseTestUtil');
                    console.log('  - quickSupabaseTest()');
                    addTestResult('Console Debug', 'success', 'Utilitários expostos no console do navegador');
                  }}
                  variant="outline"
                >
                  Console Debug
                </Button>
              </div>
            </div>

            {/* Resultados dos Testes */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Resultados dos Testes</h3>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {debugInfo.testResults.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    Nenhum teste executado ainda
                  </div>
                ) : (
                  debugInfo.testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        result.status === 'success' ? 'bg-green-50 border-green-200' :
                        result.status === 'error' ? 'bg-red-50 border-red-200' :
                        'bg-yellow-50 border-yellow-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {result.status === 'success' ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : result.status === 'error' ? (
                            <AlertCircle className="w-4 h-4 text-red-600" />
                          ) : (
                            <Clock className="w-4 h-4 text-yellow-600" />
                          )}
                          <span className="font-medium">{result.test}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {result.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm mt-1 text-gray-700">{result.message}</p>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Informações de Debug */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Informações de Debug</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Navegador Online:</strong> {navigator?.onLine ? 'Sim' : 'Não'}
                  </div>
                  <div>
                    <strong>User Agent:</strong> {navigator?.userAgent?.substring(0, 50)}...
                  </div>
                  <div>
                    <strong>Timestamp:</strong> {new Date().toLocaleString()}
                  </div>
                  <div>
                    <strong>Testes Executados:</strong> {debugInfo.testResults.length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
