"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Paperclip,
  Phone,
  Video,
  MoreVertical,
  FileText,
  FileAudio,
  FileImage,
  Download,
  Play,
  Pause,
  Volume2,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { VideoAudioModal } from "./video-audio-modal";

// Interfaces para o sistema robusto
export interface ChatMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  senderId: string;
  appointmentId: string;
  createdAt: string;
  metadata?: Record<string, any>;
  senderRole?: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  reconnectAttempts: number;
  lastError?: string;
  isOnline: boolean;
  mode: 'realtime' | 'polling' | 'hybrid';
}

interface ChatInterfaceProps {
  consultation: any;
  userId: string;
  onEndConsultation: (consultationId: string) => void;
  // Novos props para o sistema robusto
  messages?: ChatMessage[];
  isLoading?: boolean;
  error?: string | null;
  onSendMessage?: (content: string) => Promise<void>;
  onSendAudio?: (audioBlob: Blob) => Promise<void>;
  onSendFile?: (file: File) => Promise<void>;
  isTyping?: boolean;
  connectionStatus?: ConnectionStatus;
  // Props para chamadas de vídeo/áudio
  onStartVideoCall?: () => void;
  onStartAudioCall?: () => void;
}

export function ChatInterface({
  consultation,
  userId,
  onEndConsultation,
  messages = [],
  isLoading = false,
  error = null,
  onSendMessage,
  onSendAudio,
  onSendFile,
  isTyping = false,
  connectionStatus,
  onStartVideoCall,
  onStartAudioCall
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [isAttaching, setIsAttaching] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [isPlayingAudio, setIsPlayingAudio] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());
  const [showConnectionStatus, setShowConnectionStatus] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [showAudioModal, setShowAudioModal] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Scroll para baixo quando novas mensagens chegarem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Auto-focus no input
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Configurar MediaRecorder para gravação de áudio
  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);

            if (onSendAudio) {
              try {
                await onSendAudio(audioBlob);
              } catch (error) {
                console.error("Erro ao enviar áudio:", error);
              }
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
        });
    }
  }, [onSendAudio]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !onSendMessage) return;

    const content = inputValue.trim();
    setInputValue("");

    try {
      await onSendMessage(content);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      // Restaurar mensagem em caso de erro
      setInputValue(content);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onSendFile) {
      try {
        await onSendFile(file);
      } catch (error) {
        console.error("Erro ao enviar arquivo:", error);
      }
    }
    // Limpar input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileClick = (fileUrl: string, fileName: string) => {
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = fileName;
    link.target = "_blank";
    link.click();
  };

  const playAudio = (audioUrl: string, messageId: string) => {
    const existingAudio = audioElements.get(messageId);

    if (existingAudio) {
      if (isPlayingAudio === messageId) {
        existingAudio.pause();
        setIsPlayingAudio(null);
      } else {
        // Parar áudio anterior se estiver tocando
        if (isPlayingAudio) {
          const previousAudio = audioElements.get(isPlayingAudio);
          if (previousAudio) {
            previousAudio.pause();
            previousAudio.currentTime = 0;
          }
        }

        existingAudio.play();
        setIsPlayingAudio(messageId);
      }
    } else {
      const audio = new Audio(audioUrl);
      audio.onended = () => setIsPlayingAudio(null);
      audio.onerror = () => setIsPlayingAudio(null);

      setAudioElements(prev => new Map(prev).set(messageId, audio));
      audio.play();
      setIsPlayingAudio(messageId);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (['mp3', 'wav', 'ogg', 'm4a'].includes(extension || '')) {
      return <FileAudio className="w-4 h-4" />;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return <FileImage className="w-4 h-4" />;
    } else {
      return <FileText className="w-4 h-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getConnectionStatusIcon = () => {
    if (!connectionStatus) return null;

    switch (connectionStatus.status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />;
    }
  };

  const getConnectionStatusText = () => {
    if (!connectionStatus) return 'Status desconhecido';

    const statusText = {
      'connected': 'Conectado',
      'connecting': 'Conectando...',
      'disconnected': 'Desconectado',
      'error': 'Erro de conexão',
      'reconnecting': 'Reconectando...'
    }[connectionStatus.status];

    const modeText = connectionStatus.mode === 'polling' ? ' (Polling)' : '';
    return `${statusText}${modeText}`;
  };

  const getConnectionStatusColor = () => {
    if (!connectionStatus) return 'text-gray-500';

    switch (connectionStatus.status) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <CardHeader className="pb-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={consultation.doctor_avatar} alt={consultation.doctor_name} />
              <AvatarFallback>
                {consultation.doctor_name?.charAt(0) || "D"}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{consultation.doctor_name}</CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant={consultation.is_on_duty ? "default" : "secondary"}>
                  {consultation.is_on_duty ? "Plantão" : "Consulta"}
                </Badge>
                {/* Status da conexão */}
                {connectionStatus && (
                  <div
                    className="flex items-center gap-1 cursor-pointer"
                    onClick={() => setShowConnectionStatus(!showConnectionStatus)}
                  >
                    {getConnectionStatusIcon()}
                    <span className={`text-xs ${getConnectionStatusColor()}`}>
                      {getConnectionStatusText()}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowAudioModal(true);
                onStartAudioCall?.();
              }}
              title="Iniciar chamada de áudio"
            >
              <Phone className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowVideoModal(true);
                onStartVideoCall?.();
              }}
              title="Iniciar chamada de vídeo"
            >
              <Video className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEndConsultation(consultation.id)}
            >
              Finalizar
            </Button>
          </div>
        </div>

        {/* Detalhes do status da conexão */}
        {showConnectionStatus && connectionStatus && (
          <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Status:</span>
                <Badge variant={connectionStatus.status === 'connected' ? 'default' : 'destructive'}>
                  {connectionStatus.status}
                </Badge>
              </div>
              <div>
                <span className="font-medium">Modo:</span>
                <Badge variant="outline">{connectionStatus.mode}</Badge>
              </div>
              {connectionStatus.lastConnected && (
                <div>
                  <span className="font-medium">Última conexão:</span>
                  <span className="text-gray-600">
                    {formatDistanceToNow(new Date(connectionStatus.lastConnected), {
                      addSuffix: true,
                      locale: ptBR
                    })}
                  </span>
                </div>
              )}
              {connectionStatus.lastError && (
                <div className="col-span-2">
                  <span className="font-medium text-red-600">Último erro:</span>
                  <span className="text-red-600 text-xs ml-2">{connectionStatus.lastError}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardHeader>

      {/* Mensagens */}
      <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-gray-600">Carregando mensagens...</p>
            </div>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>Nenhuma mensagem ainda</p>
            <p className="text-sm">Inicie a conversa enviando uma mensagem</p>
          </div>
        ) : (
          messages.map((message) => {
            const isOwnMessage = message.senderId === userId;
            const isAudio = message.type === 'AUDIO';
            const isFile = message.type === 'FILE';
            const isSystem = message.type === 'SYSTEM';

            if (isSystem) {
              return (
                <div key={message.id} className="flex justify-center">
                  <Badge variant="secondary" className="text-xs">
                    {message.content}
                  </Badge>
                </div>
              );
            }

            return (
              <div
                key={message.id}
                className={`flex gap-3 ${isOwnMessage ? "flex-row-reverse" : ""}`}
              >
                <Avatar className="w-8 h-8">
                  <AvatarImage
                    src={isOwnMessage ? undefined : consultation.doctor_avatar}
                    alt={isOwnMessage ? "Você" : consultation.doctor_name}
                  />
                  <AvatarFallback className="text-xs">
                    {isOwnMessage ? "V" : consultation.doctor_name?.charAt(0) || "D"}
                  </AvatarFallback>
                </Avatar>

                <div className={`flex flex-col max-w-[70%] ${isOwnMessage ? "items-end" : "items-start"}`}>
                  <div
                    className={`rounded-lg px-3 py-2 ${
                      isOwnMessage
                        ? "bg-blue-500 text-white"
                        : "bg-gray-100 text-gray-900"
                    }`}
                  >
                    {isAudio ? (
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-auto text-inherit hover:bg-white/20"
                          onClick={() => playAudio(message.file_url || '', message.id)}
                        >
                          {isPlayingAudio === message.id ? (
                            <Pause className="w-4 h-4" />
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                        </Button>
                        <span className="text-sm">Mensagem de áudio</span>
                        {message.file_size && (
                          <span className="text-xs opacity-75">
                            ({formatFileSize(message.file_size)})
                          </span>
                        )}
                      </div>
                    ) : isFile ? (
                      <div className="flex items-center gap-2">
                        {getFileIcon(message.file_name || '')}
                        <span className="text-sm">{message.content}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-auto text-inherit hover:bg-white/20"
                          onClick={() => handleFileClick(message.file_url || '', message.file_name || '')}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <p className="text-sm">{message.content}</p>
                    )}
                  </div>
                  <span className="text-xs text-gray-500 mt-1">
                    {formatDistanceToNow(new Date(message.createdAt), {
                      addSuffix: true,
                      locale: ptBR
                    })}
                  </span>
                </div>
              </div>
            );
          })
        )}

        {/* Indicador de digitação */}
        {isTyping && (
          <div className="flex gap-3">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="text-xs">
                {consultation.doctor_name?.charAt(0) || "D"}
              </AvatarFallback>
            </Avatar>
            <div className="bg-gray-100 rounded-lg px-3 py-2">
              <div className="flex items-center gap-1">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-500 ml-2">Digitando...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </CardContent>

      {/* Input e controles */}
      <div className="p-4 border-t">
        {/* Indicador de erro */}
        {error && (
          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-600">{error}</span>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-red-600 hover:bg-red-100"
              onClick={() => {/* Limpar erro */}}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}

        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Digite sua mensagem..."
            className="flex-1"
            disabled={!onSendMessage}
          />

          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            onChange={handleFileSelect}
            accept="image/*,audio/*,.pdf,.doc,.docx,.txt"
          />

          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={!onSendFile}
            className="p-2"
          >
            <Paperclip className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onMouseDown={startRecording}
            onMouseUp={stopRecording}
            onMouseLeave={stopRecording}
            disabled={!onSendAudio || !mediaRecorder}
            className={`p-2 ${isRecording ? 'bg-red-500 text-white hover:bg-red-600' : ''}`}
          >
            <Mic className="w-4 h-4" />
          </Button>

          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || !onSendMessage}
            className="p-2"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Modal de chamada de vídeo */}
      {showVideoModal && (
        <VideoAudioModal
          isOpen={showVideoModal}
          onClose={() => setShowVideoModal(false)}
          appointmentId={consultation.id}
          userRole="DOCTOR" // Assumindo que é sempre médico no zapchat
          mode="video"
        />
      )}

      {/* Modal de chamada de áudio */}
      {showAudioModal && (
        <VideoAudioModal
          isOpen={showAudioModal}
          onClose={() => setShowAudioModal(false)}
          appointmentId={consultation.id}
          userRole="DOCTOR" // Assumindo que é sempre médico no zapchat
          mode="audio"
        />
      )}
    </div>
  );
}
