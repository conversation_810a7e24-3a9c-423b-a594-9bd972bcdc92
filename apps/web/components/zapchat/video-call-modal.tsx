"use client";

import { useState, useRef, useEffect } from "react";
import {
  X,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  MonitorOff,
  Phone,
  PhoneOff,
  Volume2,
  VolumeX
} from "lucide-react";
import { getVideoCallService, VideoCallStatus, VideoCallParticipant } from "@lib/services/video-call.service";

interface VideoCallModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId: string;
  userId: string;
  participantName: string;
}

export function VideoCallModal({
  isOpen,
  onClose,
  appointmentId,
  userId,
  participantName
}: VideoCallModalProps) {
  const [status, setStatus] = useState<VideoCallStatus>({
    status: 'idle',
    participants: [],
    localAudioEnabled: true,
    localVideoEnabled: true
  });
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const videoCallService = getVideoCallService();

  useEffect(() => {
    if (!isOpen) return;

    const unsubscribe = videoCallService.onStatusChange((newStatus) => {
      setStatus(newStatus);

      // Configurar streams de vídeo
      if (newStatus.participants.length > 0) {
        setupVideoStreams(newStatus.participants);
      }
    });

    return unsubscribe;
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      startCall();
    } else {
      endCall();
    }

    return () => {
      if (status.status === 'connected') {
        endCall();
      }
    };
  }, [isOpen]);

  const setupVideoStreams = (participants: VideoCallParticipant[]) => {
    // Configurar vídeo local
    const localParticipant = participants.find(p => p.isLocal);
    if (localParticipant && localVideoRef.current) {
      // O vídeo local será configurado pelo LiveKit automaticamente
    }

    // Configurar vídeo remoto
    const remoteParticipant = participants.find(p => !p.isLocal);
    if (remoteParticipant?.videoTrack && remoteVideoRef.current) {
      remoteParticipant.videoTrack.attach(remoteVideoRef.current);
    }
  };

  const startCall = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      // Criar sala e obter token
      const { roomName, token } = await videoCallService.createRoom(appointmentId, userId);

      // Entrar na sala
      await videoCallService.joinRoom(roomName, token, {
        audio: true,
        video: true
      });

    } catch (error) {
      console.error('Erro ao iniciar chamada:', error);
      setError(error instanceof Error ? error.message : 'Erro ao iniciar chamada');
    } finally {
      setIsConnecting(false);
    }
  };

  const endCall = async () => {
    try {
      await videoCallService.leaveRoom();
      onClose();
    } catch (error) {
      console.error('Erro ao finalizar chamada:', error);
    }
  };

  const toggleAudio = async () => {
    try {
      await videoCallService.toggleAudio();
    } catch (error) {
      console.error('Erro ao alternar áudio:', error);
    }
  };

  const toggleVideo = async () => {
    try {
      await videoCallService.toggleVideo();
    } catch (error) {
      console.error('Erro ao alternar vídeo:', error);
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        await videoCallService.stopScreenShare();
        setIsScreenSharing(false);
      } else {
        await videoCallService.shareScreen();
        setIsScreenSharing(true);
      }
    } catch (error) {
      console.error('Erro ao alternar compartilhamento de tela:', error);
    }
  };

  if (!isOpen) return null;

  const remoteParticipant = status.participants.find(p => !p.isLocal);
  const isConnected = status.status === 'connected';

  return (
    <div className="fixed inset-0 z-50 bg-black zapchat-video-modal">
      {/* Header da Chamada */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/80 to-transparent p-4">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {participantName.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="font-medium">{participantName}</h2>
              <p className="text-sm text-gray-300">
                {isConnecting ? 'Conectando...' :
                 isConnected ? 'Conectado' :
                 status.status === 'error' ? 'Erro de conexão' : 'Desconectado'}
              </p>
            </div>
          </div>

          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white/20 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Área Principal de Vídeo */}
      <div className="relative w-full h-full">
        {/* Vídeo Remoto (Principal) */}
        <div className="w-full h-full bg-gray-900 flex items-center justify-center">
          {remoteParticipant?.videoTrack ? (
            <video
              ref={remoteVideoRef}
              autoPlay
              playsInline
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-center text-white">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
                <span className="text-3xl">
                  {participantName.charAt(0).toUpperCase()}
                </span>
              </div>
              <p className="text-lg font-medium">{participantName}</p>
              <p className="text-sm text-gray-400">
                {isConnecting ? 'Conectando...' : 'Sem vídeo'}
              </p>
            </div>
          )}
        </div>

        {/* Vídeo Local (Picture-in-Picture) */}
        <div className="absolute top-20 right-4 w-32 h-24 lg:w-40 lg:h-30 bg-gray-800 rounded-lg overflow-hidden border-2 border-white/20">
          {status.localVideoEnabled ? (
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-700 flex items-center justify-center">
              <VideoOff className="w-6 h-6 text-gray-400" />
            </div>
          )}
        </div>

        {/* Indicadores de Status */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
          {isConnecting && (
            <div className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm">
              Conectando...
            </div>
          )}
          {error && (
            <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm">
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Controles da Chamada */}
      <div className="absolute bottom-0 left-0 right-0 zapchat-video-controls p-6">
        <div className="flex items-center justify-center gap-4">
          {/* Mute/Unmute */}
          <button
            onClick={toggleAudio}
            disabled={!isConnected}
            className={`p-4 rounded-full transition-colors ${
              status.localAudioEnabled
                ? 'bg-gray-700 hover:bg-gray-600 text-white'
                : 'bg-red-500 hover:bg-red-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {status.localAudioEnabled ? (
              <Mic className="w-6 h-6" />
            ) : (
              <MicOff className="w-6 h-6" />
            )}
          </button>

          {/* Video On/Off */}
          <button
            onClick={toggleVideo}
            disabled={!isConnected}
            className={`p-4 rounded-full transition-colors ${
              status.localVideoEnabled
                ? 'bg-gray-700 hover:bg-gray-600 text-white'
                : 'bg-red-500 hover:bg-red-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {status.localVideoEnabled ? (
              <Video className="w-6 h-6" />
            ) : (
              <VideoOff className="w-6 h-6" />
            )}
          </button>

          {/* Compartilhar Tela */}
          <button
            onClick={toggleScreenShare}
            disabled={!isConnected}
            className={`p-4 rounded-full transition-colors ${
              isScreenSharing
                ? 'bg-blue-500 hover:bg-blue-600 text-white'
                : 'bg-gray-700 hover:bg-gray-600 text-white'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isScreenSharing ? (
              <MonitorOff className="w-6 h-6" />
            ) : (
              <Monitor className="w-6 h-6" />
            )}
          </button>

          {/* Finalizar Chamada */}
          <button
            onClick={endCall}
            className="p-4 bg-red-500 hover:bg-red-600 rounded-full text-white transition-colors"
          >
            <PhoneOff className="w-6 h-6" />
          </button>
        </div>

        {/* Info dos Participantes */}
        <div className="mt-4 text-center text-white text-sm">
          {status.participants.length > 1 ? (
            <p>{status.participants.length} participantes na chamada</p>
          ) : (
            <p>Aguardando outros participantes...</p>
          )}
        </div>
      </div>
    </div>
  );
}
