"use client";

import { useState } from "react";
import { ArrowLeft, MoreVertical, Phone, Video, PhoneOff } from "lucide-react";
import { ConnectionStatus } from "@lib/services/chat.service";
import { Consultation } from "@lib/services/consultation.service";

interface MobileLayoutProps {
  consultation: Consultation | null;
  connectionStatus: ConnectionStatus;
  onBack: () => void;
  onEndConsultation: (consultationId: string) => void;
  onStartVideoCall?: () => void;
  onStartVoiceCall?: () => void;
  children: React.ReactNode;
}

export function MobileLayout({
  consultation,
  connectionStatus,
  onBack,
  onEndConsultation,
  onStartVideoCall,
  onStartVoiceCall,
  children
}: MobileLayoutProps) {
  const [showOptions, setShowOptions] = useState(false);

  if (!consultation) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <span className="text-2xl">💬</span>
          </div>
          <p className="text-gray-500 text-sm">Selecione uma consulta</p>
        </div>
      </div>
    );
  }

  const getConnectionColor = () => {
    switch (connectionStatus.status) {
      case "connected":
        return "bg-green-500";
      case "connecting":
      case "reconnecting":
        return "bg-yellow-500";
      default:
        return "bg-red-500";
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus.status) {
      case "connected":
        return "Online";
      case "connecting":
        return "Conectando...";
      case "reconnecting":
        return "Reconectando...";
      default:
        return "Offline";
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header Mobile Compacto */}
      <header className="h-14 bg-white border-b border-gray-200 flex items-center px-3 relative">
        <button
          onClick={onBack}
          className="p-2 mr-2 hover:bg-gray-100 rounded-lg transition-colors"
          aria-label="Voltar"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>

        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Avatar */}
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white font-semibold text-xs">
              {consultation.patient_name?.charAt(0)?.toUpperCase() || "P"}
            </span>
          </div>

          {/* Info */}
          <div className="flex-1 min-w-0">
            <h1 className="text-sm font-medium truncate text-gray-900">
              {consultation.patient_name}
            </h1>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getConnectionColor()}`} />
              <span className="text-xs text-gray-500">
                {getConnectionText()}
              </span>
              {consultation.is_on_duty && (
                <span className="ml-1 px-1.5 py-0.5 bg-red-100 text-red-700 text-xs rounded font-medium">
                  Plantão
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-1">
          {onStartVoiceCall && (
            <button
              onClick={onStartVoiceCall}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Chamada de voz"
            >
              <Phone className="w-4 h-4" />
            </button>
          )}

          {onStartVideoCall && (
            <button
              onClick={onStartVideoCall}
              className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
              aria-label="Chamada de vídeo"
            >
              <Video className="w-4 h-4" />
            </button>
          )}

          <button
            onClick={() => setShowOptions(!showOptions)}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Mais opções"
          >
            <MoreVertical className="w-4 h-4" />
          </button>
        </div>

        {/* Options Menu */}
        {showOptions && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 bg-black bg-opacity-25 z-40"
              onClick={() => setShowOptions(false)}
            />

            {/* Menu */}
            <div className="absolute right-3 top-12 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50 min-w-[160px]">
              <button
                onClick={() => {
                  setShowOptions(false);
                  onEndConsultation(consultation.id);
                }}
                className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 hover:bg-red-50 transition-colors text-sm"
              >
                <PhoneOff className="w-4 h-4" />
                Finalizar Consulta
              </button>
            </div>
          </>
        )}
      </header>

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
}
