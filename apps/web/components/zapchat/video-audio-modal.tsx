"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@ui/components/dialog";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Settings,
  X,
  Loader2,
  AlertCircle,
  RefreshCw,
  Signal,
  Wifi,
  WifiOff
} from "lucide-react";
import { toast } from "sonner";
import { LiveKitRoom, VideoConference, useLocalParticipant } from "@livekit/components-react";
import { Room, Track, RoomEvent } from "livekit-client";
import "@livekit/components-styles";
import { LiveKitDebugPanel } from "./livekit-debug-panel";

interface VideoAudioModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId: string;
  userRole: "DOCTOR" | "PATIENT";
  mode: "video" | "audio";
  otherParticipantName: string;
  onError?: (error: Error) => void;
}

interface LiveKitTokenResponse {
  token: string;
  serverUrl: string;
  roomName: string;
  identity: string;
}

export function VideoAudioModal({
  isOpen,
  onClose,
  appointmentId,
  userRole,
  mode,
  otherParticipantName,
  onError
}: VideoAudioModalProps) {
  const [token, setToken] = useState<string | null>(null);
  const [serverUrl, setServerUrl] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<Error | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [callDuration, setCallDuration] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor'>('good');

  const maxReconnectAttempts = 3;
  const callStartTimeRef = useRef<Date | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Função para obter token do LiveKit
  const getLiveKitToken = useCallback(async (): Promise<LiveKitTokenResponse> => {
    try {
      const response = await fetch('/api/livekit/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId,
          userRole,
          mode
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.token || !data.serverUrl) {
        throw new Error('Token ou ServerUrl não recebidos');
      }

      // Validar se a URL é válida
      try {
        new URL(data.serverUrl);
      } catch {
        throw new Error('URL do servidor LiveKit inválida');
      }

      return data;
    } catch (error) {
      console.error('Erro ao obter token LiveKit:', error);
      throw new Error(error instanceof Error ? error.message : 'Não foi possível obter token de conexão');
    }
  }, [appointmentId, userRole, mode]);

  // Conectar à sala
  const connectToRoom = useCallback(async (isReconnect = false) => {
    if (isConnecting) return;

    try {
      setIsConnecting(true);
      setConnectionError(null);

      // Timeout de conexão
      connectionTimeoutRef.current = setTimeout(() => {
        setIsConnecting(false);
        setConnectionError(new Error("Tempo limite de conexão excedido"));
        onError?.(new Error("Connection timeout"));
      }, 15000);

      console.log(`${isReconnect ? 'Reconectando' : 'Conectando'} à sala ${mode}...`);

      const { token: newToken, serverUrl: newServerUrl } = await getLiveKitToken();

      setToken(newToken);
      setServerUrl(newServerUrl);

      if (isReconnect) {
        toast.success(`Reconectado à chamada de ${mode}`);
        setReconnectAttempts(0);
      } else {
        toast.success(`Conectado à chamada de ${mode}`);
        callStartTimeRef.current = new Date();

        // Iniciar contador de duração
        durationIntervalRef.current = setInterval(() => {
          if (callStartTimeRef.current) {
            const now = new Date();
            const duration = Math.floor((now.getTime() - callStartTimeRef.current.getTime()) / 1000);
            setCallDuration(duration);
          }
        }, 1000);
      }

    } catch (error) {
      console.error("Erro ao conectar:", error);
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido ao conectar";
      setConnectionError(new Error(errorMessage));
      onError?.(error as Error);
    } finally {
      setIsConnecting(false);
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
        connectionTimeoutRef.current = null;
      }
    }
  }, [isConnecting, getLiveKitToken, mode, onError]);

  // Tentar reconectar
  const handleReconnect = useCallback(() => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      toast.error("Número máximo de tentativas excedido");
      return;
    }

    setReconnectAttempts(prev => prev + 1);
    connectToRoom(true);
  }, [connectToRoom, reconnectAttempts]);

  // Encerrar chamada
  const handleEndCall = useCallback(() => {
    setToken(null);
    setServerUrl(null);
    setIsConnected(false);

    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }

    callStartTimeRef.current = null;
    setCallDuration(0);

    toast.info("Chamada encerrada");
    onClose();
  }, [onClose]);

  // Conectar quando o modal abrir
  useEffect(() => {
    if (isOpen && !token && !isConnecting && !connectionError) {
      connectToRoom();
    }
  }, [isOpen, token, isConnecting, connectionError, connectToRoom]);

  // Cleanup quando fechar
  useEffect(() => {
    if (!isOpen) {
      setToken(null);
      setServerUrl(null);
      setIsConnected(false);
      setConnectionError(null);
      setReconnectAttempts(0);
      setCallDuration(0);

      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
        durationIntervalRef.current = null;
      }

      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
        connectionTimeoutRef.current = null;
      }

      callStartTimeRef.current = null;
    }
  }, [isOpen]);

  // Formatar duração da chamada
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Componente de controles customizados
  const CallControls = () => {
    const { localParticipant } = useLocalParticipant();
    const [isMuted, setIsMuted] = useState(false);
    const [isVideoOff, setIsVideoOff] = useState(mode === "audio");

    const toggleMute = useCallback(async () => {
      if (localParticipant) {
        try {
          const enabled = localParticipant.isMicrophoneEnabled;
          await localParticipant.setMicrophoneEnabled(!enabled);
          setIsMuted(enabled);
          toast.success(enabled ? "Microfone desativado" : "Microfone ativado");
        } catch (error) {
          toast.error("Erro ao alterar microfone");
        }
      }
    }, [localParticipant]);

    const toggleVideo = useCallback(async () => {
      if (localParticipant && mode === "video") {
        try {
          const enabled = localParticipant.isCameraEnabled;
          await localParticipant.setCameraEnabled(!enabled);
          setIsVideoOff(enabled);
          toast.success(enabled ? "Câmera desativada" : "Câmera ativada");
        } catch (error) {
          toast.error("Erro ao alterar câmera");
        }
      }
    }, [localParticipant, mode]);

    return (
      <div className="flex items-center justify-center gap-4 p-6 bg-black/60 backdrop-blur-md border-t border-white/20">
        <Button
          size="lg"
          variant={isMuted ? "destructive" : "secondary"}
          onClick={toggleMute}
          className="rounded-full h-14 w-14 hover:scale-105 transition-transform"
          title={isMuted ? "Ativar microfone" : "Desativar microfone"}
        >
          {isMuted ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
        </Button>

        {mode === "video" && (
          <Button
            size="lg"
            variant={isVideoOff ? "destructive" : "secondary"}
            onClick={toggleVideo}
            className="rounded-full h-14 w-14 hover:scale-105 transition-transform"
            title={isVideoOff ? "Ativar câmera" : "Desativar câmera"}
          >
            {isVideoOff ? <VideoOff className="h-6 w-6" /> : <Video className="h-6 w-6" />}
          </Button>
        )}

        <Button
          size="lg"
          variant="destructive"
          onClick={handleEndCall}
          className="rounded-full h-16 w-16 hover:scale-105 transition-transform bg-red-600 hover:bg-red-700"
          title="Encerrar chamada"
        >
          <PhoneOff className="h-7 w-7" />
        </Button>
      </div>
    );
  };

  // Componente de status da conexão
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      <div className="flex items-center gap-1">
        <Signal className="h-3 w-3" />
        <span className="text-xs">Conexão</span>
      </div>
      <Badge variant={connectionQuality === 'excellent' ? 'default' : connectionQuality === 'good' ? 'secondary' : 'destructive'}>
        {connectionQuality === 'excellent' ? 'Excelente' : connectionQuality === 'good' ? 'Boa' : 'Ruim'}
      </Badge>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleEndCall()}>
      <DialogContent className="max-w-5xl h-[90vh] p-0 overflow-hidden bg-gray-900">
        <DialogHeader className="p-4 border-b border-gray-700 bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {mode === "video" ? (
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Video className="h-5 w-5 text-blue-400" />
                  </div>
                ) : (
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Phone className="h-5 w-5 text-green-400" />
                  </div>
                )}
                <div>
                  <DialogTitle className="text-lg font-semibold text-white">
                    {mode === "video" ? "Videochamada" : "Chamada de áudio"} - {otherParticipantName}
                  </DialogTitle>
                  <div className="flex items-center gap-3 mt-1">
                    {isConnected && callDuration > 0 && (
                      <div className="flex items-center gap-2 text-sm text-gray-300">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        <span className="font-mono">{formatDuration(callDuration)}</span>
                      </div>
                    )}
                    {isConnected && <ConnectionStatus />}
                  </div>
                </div>
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleEndCall}
              className="text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 relative bg-gray-900">
          {connectionError ? (
            <div className="h-full overflow-y-auto p-6">
              <Card className="mb-6 bg-gray-800 border-gray-700">
                <CardContent className="flex flex-col items-center justify-center p-8">
                  <div className="p-4 bg-red-500/20 rounded-full mb-6">
                    <AlertCircle className="h-12 w-12 text-red-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-white">Erro de conexão</h3>
                  <Alert variant="destructive" className="mb-6 max-w-md bg-red-500/20 border-red-500/30">
                    <AlertDescription className="text-red-200">{connectionError.message}</AlertDescription>
                  </Alert>
                  <div className="flex gap-3">
                    <Button
                      onClick={handleReconnect}
                      disabled={reconnectAttempts >= maxReconnectAttempts || isConnecting}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isConnecting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Conectando...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Tentar novamente ({reconnectAttempts}/{maxReconnectAttempts})
                        </>
                      )}
                    </Button>
                    <Button variant="outline" onClick={handleEndCall} className="border-gray-600 text-gray-300 hover:bg-gray-700">
                      Encerrar chamada
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Painel de Debug para problemas de configuração - Só em desenvolvimento */}
              {/* {process.env.NODE_ENV === 'development' && (connectionError.message.includes('URL') || connectionError.message.includes('configuração')) ? (
                <LiveKitDebugPanel />
              ) : null} */}
            </div>
          ) : isConnecting || !token || !serverUrl ? (
            <Card className="m-6 h-[calc(100%-3rem)] bg-gray-800 border-gray-700">
              <CardContent className="flex h-full items-center justify-center">
                <div className="text-center text-white">
                  <div className="p-4 bg-blue-500/20 rounded-full mb-6 mx-auto w-20 h-20 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">
                    {isConnecting ? `Conectando à chamada de ${mode}...` : "Preparando conexão..."}
                  </h3>
                  <p className="text-gray-400">
                    {isConnecting ? "Aguarde enquanto estabelecemos a conexão" : "Configurando ambiente de chamada"}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="h-full relative">
              <LiveKitRoom
                token={token}
                serverUrl={serverUrl}
                connect={true}
                video={mode === "video"}
                audio={true}
                className="h-full"
                style={{
                  "--lk-bg": "transparent",
                  "--lk-control-bg": "var(--background)",
                  "--lk-control-border": "var(--border)",
                  "--lk-text-color": "var(--foreground)",
                  "--lk-video-bg": "var(--muted)",
                } as React.CSSProperties}
                options={{
                  adaptiveStream: true,
                  dynacast: true,
                  publishDefaults: {
                    simulcast: mode === "video",
                    videoEncoding: mode === "video" ? undefined : false,
                  },
                }}
                onConnected={(room: Room) => {
                  console.log("Conectado à sala LiveKit");
                  setIsConnected(true);
                  setConnectionError(null);
                  setReconnectAttempts(0);

                  // Monitorar qualidade da conexão
                  room.on(RoomEvent.ConnectionQualityChanged, (quality, participant) => {
                    if (participant.identity === room.localParticipant.identity) {
                      if (quality >= 0.8) setConnectionQuality('excellent');
                      else if (quality >= 0.5) setConnectionQuality('good');
                      else setConnectionQuality('poor');
                    }
                  });
                }}
                onDisconnected={(reason) => {
                  console.log("Desconectado da sala LiveKit:", reason);
                  setIsConnected(false);

                  // Só mostrar erro se não foi desconexão intencional
                  if (reason && reason !== "CLIENT_INITIATED") {
                    toast.error("Desconectado da chamada. Tentando reconectar...");
                    if (reconnectAttempts < maxReconnectAttempts) {
                      setTimeout(() => handleReconnect(), 2000);
                    }
                  }
                }}
                onError={(error) => {
                  console.error("Erro LiveKit:", error);
                  setConnectionError(error);
                  onError?.(error);
                }}
              >
                <div className="flex h-full flex-col">
                  <div className="flex-1">
                    {mode === "video" ? (
                      <VideoConference />
                    ) : (
                      <div className="flex h-full items-center justify-center bg-gradient-to-br from-green-900 via-green-800 to-green-700">
                        <div className="text-center text-white">
                          <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 backdrop-blur-sm">
                            <Phone className="h-16 w-16 text-white/80" />
                          </div>
                          <h3 className="text-2xl font-semibold mb-3">Chamada de áudio</h3>
                          <p className="text-white/90 text-lg mb-4">{otherParticipantName}</p>
                          {callDuration > 0 && (
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 inline-block">
                              <p className="text-2xl font-mono font-bold">{formatDuration(callDuration)}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Controles customizados na parte inferior */}
                  <div className="absolute bottom-0 left-0 right-0">
                    <CallControls />
                  </div>
                </div>
              </LiveKitRoom>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
