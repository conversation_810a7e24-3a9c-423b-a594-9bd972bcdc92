"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Paperclip,
  Phone,
  Video,
  ArrowLeft,
  MoreVertical,
  Stethoscope,
  FileText,
  Prescription,
  Clock,
  AlertTriangle,
  Camera,
  Image as ImageIcon,
  User,
  Activity,
  CheckCheck,
  Wifi,
  WifiOff
} from "lucide-react";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@ui/components/dropdown-menu";
import { useUser } from "@saas/auth/hooks/use-user";

// Tipos
import { Chat<PERSON>essage, ConnectionStatus, TypingStatus } from "../../lib/services/supabase-realtime.service";

interface DoctorMobileInterfaceProps {
  consultation: {
    id: string;
    patient_name?: string;
    patient_avatar?: string;
    patient_age?: number;
    is_on_duty?: boolean;
    urgency_level?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    medical_condition?: string;
  };

  userId: string;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  connectionStatus: ConnectionStatus;
  isConnected: boolean;
  typingUsers: TypingStatus[];

  onSendMessage: (content: string) => Promise<void>;
  onSendAudio: (audioBlob: Blob) => Promise<void>;
  onSendFile: (file: File) => Promise<void>;
  onStartTyping: () => void;
  onStopTyping: () => void;

  onBack?: () => void;
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onOpenMedicalRecord?: () => void;
  onPrescription?: () => void;
  onEndConsultation?: () => void;
}

export function DoctorMobileInterface({
  consultation,
  userId,
  messages,
  isLoading,
  error,
  connectionStatus,
  isConnected,
  typingUsers,
  onSendMessage,
  onSendAudio,
  onSendFile,
  onStartTyping,
  onStopTyping,
  onBack,
  onVideoCall,
  onAudioCall,
  onOpenMedicalRecord,
  onPrescription,
  onEndConsultation,
}: DoctorMobileInterfaceProps) {
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get user for custom background
  const { user } = useUser();

  // Auto-scroll para nova mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Configurar MediaRecorder
  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);
            try {
              await onSendAudio(audioBlob);
            } catch (error) {
              toast.error("Erro ao enviar áudio");
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
        });
    }
  }, [onSendAudio, audioChunks]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const content = inputValue.trim();
    setInputValue("");
    onStopTyping();

    try {
      await onSendMessage(content);
    } catch (error) {
      setInputValue(content);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim()) {
      onStartTyping();
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      onStopTyping();
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
      toast.info("Gravando áudio...");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const sendQuickMessage = async (message: string) => {
    try {
      await onSendMessage(message);
      setShowQuickActions(false);
    } catch (error) {
      toast.error("Erro ao enviar mensagem");
    }
  };

  const formatMessageTime = (createdAt: string) => {
    const messageDate = new Date(createdAt);
    return format(messageDate, "HH:mm", { locale: ptBR });
  };

  const getUrgencyColor = () => {
    switch (consultation.urgency_level) {
      case 'CRITICAL':
        return 'bg-red-600';
      case 'HIGH':
        return 'bg-orange-600';
      case 'MEDIUM':
        return 'bg-yellow-600';
      case 'LOW':
        return 'bg-green-600';
      default:
        return 'bg-blue-600';
    }
  };

  const getUrgencyIcon = () => {
    switch (consultation.urgency_level) {
      case 'CRITICAL':
      case 'HIGH':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Stethoscope className="w-4 h-4" />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 relative">
      {/* Header médico */}
      <div className={cn("text-white px-4 py-3 flex items-center justify-between shadow-md relative z-10", getUrgencyColor())}>
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {onBack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-white hover:bg-black/20 p-2 h-auto"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}

          <Avatar className="w-10 h-10 ring-2 ring-white/30">
            <AvatarImage src={consultation.patient_avatar} alt={consultation.patient_name} />
            <AvatarFallback className="bg-white/20 text-white font-medium">
              <User className="w-5 h-5" />
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h1 className="font-medium truncate text-white">
              {consultation.patient_name || 'Paciente'}
            </h1>
            <div className="flex items-center gap-2">
              <Badge
                variant={consultation.is_on_duty ? "secondary" : "outline"}
                className="text-xs bg-white/20 text-white border-white/30"
              >
                {consultation.is_on_duty ? "Plantão" : "Consulta"}
                {consultation.patient_age && ` • ${consultation.patient_age}a`}
              </Badge>

              <div className="flex items-center gap-1">
                {isConnected ? (
                  <Wifi className="w-3 h-3" />
                ) : (
                  <WifiOff className="w-3 h-3 text-red-300" />
                )}
                <span className="text-xs">
                  {isConnected ? 'online' : 'offline'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Ações do header médico */}
        <div className="flex items-center gap-1">
          {onVideoCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onVideoCall}
              className="text-white hover:bg-black/20 p-2 h-auto"
            >
              <Video className="w-5 h-5" />
            </Button>
          )}

          {onAudioCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onAudioCall}
              className="text-white hover:bg-black/20 p-2 h-auto"
            >
              <Phone className="w-5 h-5" />
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-black/20 p-2 h-auto"
              >
                <MoreVertical className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onOpenMedicalRecord && (
                <DropdownMenuItem onClick={onOpenMedicalRecord}>
                  <FileText className="w-4 h-4 mr-2" />
                  Prontuário
                </DropdownMenuItem>
              )}
              {onPrescription && (
                <DropdownMenuItem onClick={onPrescription}>
                  <Prescription className="w-4 h-4 mr-2" />
                  Receita
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Activity className="w-4 h-4 mr-2" />
                Exames
              </DropdownMenuItem>
              {onEndConsultation && (
                <DropdownMenuItem onClick={onEndConsultation} className="text-red-600">
                  <Clock className="w-4 h-4 mr-2" />
                  Finalizar Consulta
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Info da condição médica */}
      {consultation.medical_condition && (
        <div className="bg-blue-50 border-b border-blue-200 px-4 py-2 flex items-center gap-2">
          {getUrgencyIcon()}
          <span className="text-sm text-blue-800 font-medium">
            {consultation.medical_condition}
          </span>
          <Badge variant="outline" className="ml-auto text-xs">
            {consultation.urgency_level || 'NORMAL'}
          </Badge>
        </div>
      )}

      {/* Wallpaper médico */}
      <div
        className="absolute inset-0 z-0 opacity-5"
        style={{
          backgroundImage: user?.chatBackgroundUrl
            ? `url(${user.chatBackgroundUrl})`
            : 'url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Cpath d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/%3E%3C/g%3E%3C/svg%3E")',
        }}
      />

      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto px-4 py-2 space-y-3 relative z-1">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-xs mx-auto">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <Stethoscope className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Início da Consulta
              </h3>
              <p className="text-gray-500 text-sm">
                Comece conversando com o paciente
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwnMessage = message.senderId === userId;
              const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;

              return (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-2 max-w-[85%]",
                    isOwnMessage ? "ml-auto flex-row-reverse" : "mr-auto"
                  )}
                >
                  {!isOwnMessage && (
                    <div className="w-8 h-8 flex-shrink-0">
                      {showAvatar ? (
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={consultation.patient_avatar} alt={consultation.patient_name} />
                          <AvatarFallback className="bg-gray-300 text-gray-600 text-xs">
                            <User className="w-4 h-4" />
                          </AvatarFallback>
                        </Avatar>
                      ) : null}
                    </div>
                  )}

                  <div
                    className={cn(
                      "relative max-w-full px-3 py-2 rounded-lg shadow-sm",
                      isOwnMessage
                        ? "bg-blue-600 text-white rounded-br-none"
                        : "bg-white text-gray-900 rounded-bl-none border"
                    )}
                  >
                    <p className="text-sm leading-relaxed break-words">
                      {message.content}
                    </p>

                    <div className={cn(
                      "flex items-center justify-end gap-1 mt-1",
                      isOwnMessage ? "text-blue-100" : "text-gray-500"
                    )}>
                      <span className="text-xs">
                        {formatMessageTime(message.createdAt)}
                      </span>
                      {isOwnMessage && <CheckCheck className="w-3 h-3" />}
                    </div>

                    {/* Seta da bolha */}
                    <div
                      className={cn(
                        "absolute bottom-0 w-0 h-0",
                        isOwnMessage
                          ? "right-0 translate-x-1 border-l-8 border-l-blue-600 border-b-8 border-b-transparent"
                          : "left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent"
                      )}
                    />
                  </div>
                </div>
              );
            })}

            {/* Indicador de digitação */}
            {typingUsers.length > 0 && (
              <div className="flex gap-2 max-w-[85%] mr-auto">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={consultation.patient_avatar} alt={consultation.patient_name} />
                  <AvatarFallback className="bg-gray-300 text-gray-600 text-xs">
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>

                <div className="bg-white rounded-lg rounded-bl-none px-3 py-2 shadow-sm border relative">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent" />
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Ações rápidas médicas */}
      {showQuickActions && (
        <div className="bg-white border-t border-gray-200 p-3 grid grid-cols-2 gap-2 relative z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Pode me contar mais sobre os sintomas?")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Sintomas</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Há quanto tempo está sentindo isso?")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Duração</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Já tomou algum medicamento?")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Medicação</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendQuickMessage("Vou prescrever um tratamento.")}
            className="text-left justify-start h-auto p-2"
          >
            <span className="text-xs">Tratamento</span>
          </Button>
        </div>
      )}

      {/* Footer médico */}
      <div className="bg-white border-t border-gray-200 px-3 py-2 relative z-10">
        {error && (
          <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-600 flex-1">{error}</span>
          </div>
        )}

        <div className="flex items-end gap-2">
          {/* Botão de ações rápidas */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowQuickActions(!showQuickActions)}
            className={cn(
              "p-2 h-12",
              showQuickActions && "bg-blue-100 border-blue-300"
            )}
          >
            <Stethoscope className="w-5 h-5" />
          </Button>

          {/* Input principal */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Mensagem médica..."
              className="rounded-full border-gray-300 pr-16 py-2 min-h-[44px]"
              disabled={!isConnected}
            />

            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) onSendFile(file);
                }}
                accept="image/*,.pdf,.doc,.docx"
              />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                className="p-1 h-auto text-gray-500 hover:text-gray-700"
                disabled={!isConnected}
              >
                <Paperclip className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Botão de envio/áudio */}
          {inputValue.trim() ? (
            <Button
              onClick={handleSendMessage}
              className="rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 p-0"
              disabled={!isConnected}
            >
              <Send className="w-5 h-5" />
            </Button>
          ) : (
            <Button
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              onMouseLeave={stopRecording}
              className={cn(
                "rounded-full w-12 h-12 p-0 transition-all",
                isRecording
                  ? "bg-red-600 hover:bg-red-700 animate-pulse"
                  : "bg-blue-600 hover:bg-blue-700"
              )}
              disabled={!isConnected || !mediaRecorder}
            >
              <Mic className="w-5 h-5" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
