"use client";

import { AdaptiveMobileChat } from "./adaptive-mobile-chat";
import { useUser } from "../../lib/hooks/use-user"; // Assumindo que existe
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface ExampleUsageProps {
  consultationId: string;
}

export function ExampleUsage({ consultationId }: ExampleUsageProps) {
  const { user } = useUser();
  const router = useRouter();

  // Dados de exemplo da consulta
  const consultation = {
    id: consultationId,

    // Dados do médico
    doctor_name: "<PERSON>",
    doctor_avatar: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=100&h=100&fit=crop&crop=face",
    doctor_specialty: "Clínica Geral",
    doctor_rating: 4.8,

    // Dados do paciente
    patient_name: "<PERSON>",
    patient_avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    patient_age: 35,

    // Configurações da consulta
    is_on_duty: false,
    urgency_level: "MEDIUM" as const,
    medical_condition: "Dor de cabeça persistente",
    estimated_wait_time: 5,
  };

  // Handler para voltar
  const handleBack = () => {
    router.back();
  };

  // Handler para chamada de vídeo
  const handleVideoCall = () => {
    toast.info("Iniciando chamada de vídeo...");
    // Implementar lógica de chamada de vídeo
  };

  // Handler para chamada de áudio
  const handleAudioCall = () => {
    toast.info("Iniciando chamada de áudio...");
    // Implementar lógica de chamada de áudio
  };

  // Handler para abrir prontuário (apenas médicos)
  const handleOpenMedicalRecord = () => {
    router.push(`/medical-records/${consultation.id}`);
  };

  // Handler para prescrição (apenas médicos)
  const handlePrescription = () => {
    router.push(`/prescriptions/new?consultation=${consultation.id}`);
  };

  // Handler para avaliar médico (apenas pacientes)
  const handleRateDoctor = () => {
    router.push(`/rate-doctor/${consultation.id}`);
  };

  // Handler para finalizar consulta
  const handleEndConsultation = () => {
    if (confirm("Tem certeza que deseja finalizar a consulta?")) {
      toast.success("Consulta finalizada com sucesso!");
      router.push(user?.role === "DOCTOR" ? "/doctor/dashboard" : "/patient/dashboard");
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p>Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <AdaptiveMobileChat
      consultation={consultation}
      user={{
        id: user.id,
        name: user.name,
        role: user.role,
      }}
      onBack={handleBack}
      onVideoCall={handleVideoCall}
      onAudioCall={handleAudioCall}
      onOpenMedicalRecord={user.role === "DOCTOR" ? handleOpenMedicalRecord : undefined}
      onPrescription={user.role === "DOCTOR" ? handlePrescription : undefined}
      onRateDoctor={user.role === "PATIENT" ? handleRateDoctor : undefined}
      onEndConsultation={handleEndConsultation}
    />
  );
}

// Exemplo de página que usa o componente
export default function ChatPage({ params }: { params: { id: string } }) {
  return <ExampleUsage consultationId={params.id} />;
}
