"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Refresh<PERSON>w, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { getClientEnvVars } from "../../lib/config/client-config";
import { checkClientEnvVars } from "@lib/config/client-env";

export function EnvDebugPanel() {
  const [envVars, setEnvVars] = useState<Record<string, string | undefined>>({});
  const [isLoading, setIsLoading] = useState(true);

      const checkEnvVars = () => {
    const clientStatus = checkClientEnvVars();
    const clientVars = getClientEnvVars();

    const vars = {
      NEXT_PUBLIC_SUPABASE_URL: clientVars.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: clientVars.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      NODE_ENV: clientVars.NODE_ENV || process.env.NODE_ENV,
      CLIENT_STATUS: clientStatus.isValid ? 'Válido' : 'Inválido',
    };

    setEnvVars(vars);
    setIsLoading(false);
  };

  useEffect(() => {
    checkEnvVars();
  }, []);

  const getStatusIcon = (value: string | undefined) => {
    if (!value) return <XCircle className="h-4 w-4 text-red-500" />;
    if (value.startsWith('http') || value.length > 20) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = (value: string | undefined) => {
    if (!value) return "Não configurada";
    if (value.startsWith('http') || value.length > 20) return "Configurada";
    return "Parcialmente configurada";
  };

  const getStatusVariant = (value: string | undefined) => {
    if (!value) return "destructive";
    if (value.startsWith('http') || value.length > 20) return "default";
    return "secondary";
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Debug das Variáveis de Ambiente
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status:</span>
          <Badge variant={isLoading ? "secondary" : "default"}>
            {isLoading ? "Verificando..." : "Verificado"}
          </Badge>
        </div>

        <div className="space-y-3">
          {Object.entries(envVars).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(value)}
                <span className="text-sm font-medium">{key}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={getStatusVariant(value) as any}>
                  {getStatusText(value)}
                </Badge>
                {value && (
                  <span className="text-xs text-gray-500 max-w-xs truncate">
                    {value.length > 50 ? `${value.substring(0, 50)}...` : value}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="pt-4 border-t">
          <Button onClick={checkEnvVars} className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Verificar Novamente
          </Button>
        </div>

        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
          <p><strong>Nota:</strong> As variáveis NEXT_PUBLIC_* são expostas ao cliente.</p>
          <p>Se estiverem vazias, verifique se o arquivo .env está na raiz do projeto.</p>
        </div>
      </CardContent>
    </Card>
  );
}
