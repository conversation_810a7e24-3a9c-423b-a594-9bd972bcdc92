"use client";

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, AlertCircle, RefreshCw, CheckCircle } from 'lucide-react';
import { cn } from '@ui/lib';
import { getRealtimeService, RealtimeConnectionStatus } from '@lib/services/supabase-realtime.service';

interface ConnectionStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export function ConnectionStatusIndicator({
  className,
  showDetails = false
}: ConnectionStatusIndicatorProps) {
  const [status, setStatus] = useState<RealtimeConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true
  });

  useEffect(() => {
    const realtimeService = getRealtimeService();
    const unsubscribe = realtimeService.onStatusChange(setStatus);

    return unsubscribe;
  }, []);

  const getStatusIcon = () => {
    switch (status.status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'connecting':
      case 'reconnecting':
        return <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'disconnected':
        return status.isOnline ?
          <Wifi className="w-4 h-4 text-gray-400" /> :
          <WifiOff className="w-4 h-4 text-red-400" />;
      default:
        return <Wifi className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (status.status) {
      case 'connected':
        return 'Conectado';
      case 'connecting':
        return 'Conectando...';
      case 'reconnecting':
        return `Reconectando... (${status.reconnectAttempts})`;
      case 'error':
        return 'Erro de conexão';
      case 'disconnected':
        return status.isOnline ? 'Desconectado' : 'Offline';
      default:
        return 'Desconhecido';
    }
  };

  const getStatusColor = () => {
    switch (status.status) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'connecting':
      case 'reconnecting':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'disconnected':
        return status.isOnline ?
          'text-gray-600 bg-gray-50 border-gray-200' :
          'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleReconnect = () => {
    if (status.status === 'error' || status.status === 'disconnected') {
      const realtimeService = getRealtimeService();
      realtimeService.reconnect();
    }
  };

  const isReconnectable = status.status === 'error' ||
    (status.status === 'disconnected' && status.isOnline);

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-2">
        {getStatusIcon()}

        {showDetails && (
          <div className="flex flex-col">
            <span className={cn(
              "text-xs font-medium px-2 py-1 rounded border",
              getStatusColor()
            )}>
              {getStatusText()}
            </span>

            {status.lastError && (
              <span className="text-xs text-red-500 mt-1 max-w-xs truncate">
                {status.lastError}
              </span>
            )}

            {status.lastConnected && (
              <span className="text-xs text-gray-500 mt-1">
                Última conexão: {new Date(status.lastConnected).toLocaleTimeString()}
              </span>
            )}
          </div>
        )}
      </div>

      {isReconnectable && (
        <button
          onClick={handleReconnect}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="Tentar reconectar"
        >
          <RefreshCw className="w-3 h-3 text-gray-500" />
        </button>
      )}
    </div>
  );
}
