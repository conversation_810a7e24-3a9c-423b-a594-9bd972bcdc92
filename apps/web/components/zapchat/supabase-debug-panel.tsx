"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import {
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Settings,
  Database,
  Activity,
  Stethoscope,
  FileText
} from 'lucide-react';
import { getRealtimeService, RealtimeConnectionStatus } from '@lib/services/supabase-realtime.service';
import { validateSupabaseConnection } from '@lib/config/supabase.config';
import { quickRealtimeDiagnostic, generateRealtimeReport, SupabaseRealtimeFixer } from '@lib/services/supabase-realtime-fix';
import { ConnectionStatusIndicator } from './connection-status-indicator';

interface DebugInfo {
  timestamp: string;
  type: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  message: string;
  details?: any;
}

interface DiagnosticResult {
  success: boolean;
  issues: string[];
  recommendations: string[];
  canConnect: boolean;
  realtimeEnabled: boolean;
  tablesConfigured: string[];
}

export function SupabaseDebugPanel() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<RealtimeConnectionStatus>({
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: true
  });
  const [debugLogs, setDebugLogs] = useState<DebugInfo[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isRunningDiagnostic, setIsRunningDiagnostic] = useState(false);
  const [isAttemptingFix, setIsAttemptingFix] = useState(false);
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [configInfo, setConfigInfo] = useState<any>(null);

  useEffect(() => {
    const realtimeService = getRealtimeService();
    const unsubscribe = realtimeService.onStatusChange(setConnectionStatus);

    // Adicionar logs de debug
    const addDebugLog = (type: DebugInfo['type'], message: string, details?: any) => {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type,
        message,
        details
      }, ...prev.slice(0, 49)]); // Manter apenas os últimos 50 logs
    };

    // Interceptar logs do console para debug
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = (...args) => {
      originalLog(...args);
      if (args[0]?.includes?.('[Supabase]')) {
        addDebugLog('INFO', args.join(' '));
      }
    };

    console.warn = (...args) => {
      originalWarn(...args);
      if (args[0]?.includes?.('[Supabase]')) {
        addDebugLog('WARNING', args.join(' '));
      }
    };

    console.error = (...args) => {
      originalError(...args);
      if (args[0]?.includes?.('[Supabase]')) {
        addDebugLog('ERROR', args.join(' '));
      }
    };

    return () => {
      unsubscribe();
      console.log = originalLog;
      console.warn = originalWarn;
      console.error = originalError;
    };
  }, []);

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const isValid = await validateSupabaseConnection();
      if (isValid) {
        setDebugLogs(prev => [{
          timestamp: new Date().toLocaleTimeString(),
          type: 'SUCCESS',
          message: 'Conexão com Supabase testada com sucesso'
        }, ...prev.slice(0, 49)]);
      } else {
        setDebugLogs(prev => [{
          timestamp: new Date().toLocaleTimeString(),
          type: 'ERROR',
          message: 'Falha na conexão com Supabase'
        }, ...prev.slice(0, 49)]);
      }
    } catch (error) {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'ERROR',
        message: `Erro ao testar conexão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }, ...prev.slice(0, 49)]);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const runDiagnostic = async () => {
    setIsRunningDiagnostic(true);
    try {
      const result = await quickRealtimeDiagnostic();
      setDiagnosticResult(result);

      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: result.success ? 'SUCCESS' : 'ERROR',
        message: `Diagnóstico executado: ${result.success ? 'TUDO OK' : `${result.issues.length} problemas encontrados`}`,
        details: result
      }, ...prev.slice(0, 49)]);
    } catch (error) {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'ERROR',
        message: `Erro ao executar diagnóstico: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }, ...prev.slice(0, 49)]);
    } finally {
      setIsRunningDiagnostic(false);
    }
  };

  const attemptAutoFix = async () => {
    setIsAttemptingFix(true);
    try {
      const fixer = new SupabaseRealtimeFixer();
      const result = await fixer.attemptAutoFix();

      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: result.success ? 'SUCCESS' : 'WARNING',
        message: `Correção automática: ${result.success ? 'Bem-sucedida' : 'Não foi possível'}`,
        details: result.actions
      }, ...prev.slice(0, 49)]);

      // Se a correção foi bem-sucedida, executar diagnóstico novamente
      if (result.success) {
        setTimeout(() => {
          runDiagnostic();
        }, 2000);
      }
    } catch (error) {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'ERROR',
        message: `Erro durante correção automática: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }, ...prev.slice(0, 49)]);
    } finally {
      setIsAttemptingFix(false);
    }
  };

  const generateReport = async () => {
    try {
      const report = await generateRealtimeReport();

      // Criar arquivo para download
      const blob = new Blob([report], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `supabase-realtime-diagnostic-${new Date().toISOString().split('T')[0]}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'SUCCESS',
        message: 'Relatório de diagnóstico gerado e baixado'
      }, ...prev.slice(0, 49)]);
    } catch (error) {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'ERROR',
        message: `Erro ao gerar relatório: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }, ...prev.slice(0, 49)]);
    }
  };

  const forceReconnect = () => {
    try {
      const realtimeService = getRealtimeService();
      realtimeService.reconnect();
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'INFO',
        message: 'Reconexão forçada iniciada'
      }, ...prev.slice(0, 49)]);
    } catch (error) {
      setDebugLogs(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        type: 'ERROR',
        message: `Erro ao forçar reconexão: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      }, ...prev.slice(0, 49)]);
    }
  };

  const clearLogs = () => {
    setDebugLogs([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'connecting': return 'bg-yellow-100 text-yellow-800';
      case 'reconnecting': return 'bg-orange-100 text-orange-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'disconnected': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLogTypeColor = (type: DebugInfo['type']) => {
    switch (type) {
      case 'SUCCESS': return 'bg-green-100 text-green-800';
      case 'WARNING': return 'bg-yellow-100 text-yellow-800';
      case 'ERROR': return 'bg-red-100 text-red-800';
      case 'INFO': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Database className="w-4 h-4" />
          Debug do Supabase Realtime
        </CardTitle>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Settings className="w-4 h-4 mr-2" />
            {isExpanded ? 'Recolher' : 'Expandir'}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status da Conexão */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2 p-3 border rounded-lg">
            <Activity className="w-4 h-4" />
            <div>
              <p className="text-sm font-medium">Status</p>
              <Badge className={getStatusColor(connectionStatus.status)}>
                {connectionStatus.status}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 border rounded-lg">
            <Wifi className="w-4 h-4" />
            <div>
              <p className="text-sm font-medium">Online</p>
              <Badge variant={connectionStatus.isOnline ? "default" : "destructive"}>
                {connectionStatus.isOnline ? 'Sim' : 'Não'}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 border rounded-lg">
            <RefreshCw className="w-4 h-4" />
            <div>
              <p className="text-sm font-medium">Tentativas</p>
              <Badge variant="outline">
                {connectionStatus.reconnectAttempts}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-2 p-3 border rounded-lg">
            <Stethoscope className="w-4 h-4" />
            <div>
              <p className="text-sm font-medium">Modo Fallback</p>
              <Badge variant={connectionStatus.fallbackMode ? "secondary" : "outline"}>
                {connectionStatus.fallbackMode ? 'Ativo' : 'Inativo'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Indicador de Status */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <span className="text-sm font-medium">Status da Conexão:</span>
          <ConnectionStatusIndicator showDetails={true} />
        </div>

        {/* Ações */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            onClick={testConnection}
            disabled={isTestingConnection}
            variant="outline"
            size="sm"
          >
            {isTestingConnection ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            Testar Conexão
          </Button>

          <Button
            onClick={runDiagnostic}
            disabled={isRunningDiagnostic}
            variant="outline"
            size="sm"
          >
            {isRunningDiagnostic ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Stethoscope className="w-4 h-4 mr-2" />
            )}
            Executar Diagnóstico
          </Button>

          <Button
            onClick={attemptAutoFix}
            disabled={isAttemptingFix}
            variant="outline"
            size="sm"
          >
            {isAttemptingFix ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Settings className="w-4 h-4 mr-2" />
            )}
            Tentar Correção
          </Button>

          <Button
            onClick={forceReconnect}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Forçar Reconexão
          </Button>

          <Button
            onClick={generateReport}
            variant="outline"
            size="sm"
          >
            <FileText className="w-4 h-4 mr-2" />
            Gerar Relatório
          </Button>

          <Button
            onClick={clearLogs}
            variant="outline"
            size="sm"
          >
            Limpar Logs
          </Button>
        </div>

        {/* Resultado do Diagnóstico */}
        {diagnosticResult && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Resultado do Diagnóstico</h4>
            <div className="p-3 border rounded-lg bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                <div>
                  <span className="font-medium">Status Geral:</span>
                  <Badge className={diagnosticResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {diagnosticResult.success ? '✅ FUNCIONANDO' : '❌ PROBLEMAS'}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Conexão:</span>
                  <Badge variant={diagnosticResult.canConnect ? "default" : "destructive"}>
                    {diagnosticResult.canConnect ? '✅ OK' : '❌ FALHOU'}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Realtime:</span>
                  <Badge variant={diagnosticResult.realtimeEnabled ? "default" : "destructive"}>
                    {diagnosticResult.realtimeEnabled ? '✅ OK' : '❌ FALHOU'}
                  </Badge>
                </div>
              </div>

              {diagnosticResult.issues.length > 0 && (
                <div className="mb-3">
                  <span className="font-medium text-red-600">Problemas:</span>
                  <ul className="text-sm text-red-600 mt-1">
                    {diagnosticResult.issues.map((issue, index) => (
                      <li key={index}>• {issue}</li>
                    ))}
                  </ul>
                </div>
              )}

              {diagnosticResult.recommendations.length > 0 && (
                <div>
                  <span className="font-medium text-blue-600">Recomendações:</span>
                  <ul className="text-sm text-blue-600 mt-1">
                    {diagnosticResult.recommendations.map((rec, index) => (
                      <li key={index}>• {rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Logs de Debug */}
        {isExpanded && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Logs de Debug</h4>
            <div className="max-h-64 overflow-y-auto space-y-1">
              {debugLogs.map((log, index) => (
                <div
                  key={index}
                  className="flex items-start gap-2 p-2 text-xs border rounded bg-gray-50"
                >
                  <Badge className={getLogTypeColor(log.type)} variant="secondary">
                    {log.type}
                  </Badge>
                  <span className="text-gray-500">{log.timestamp}</span>
                  <span className="flex-1">{log.message}</span>
                  {log.details && (
                    <details className="text-xs text-gray-600">
                      <summary>Detalhes</summary>
                      <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Informações de Configuração */}
        {isExpanded && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Configuração</h4>
            <div className="p-3 border rounded-lg bg-gray-50">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="font-medium">URL:</span>
                  <span className="ml-2 text-gray-600">
                    {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓ Configurada' : '✗ Não configurada'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Chave Anônima:</span>
                  <span className="ml-2 text-gray-600">
                    {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Configurada' : '✗ Não configurada'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Chave Service Role:</span>
                  <span className="ml-2 text-gray-600">
                    {process.env.SUPABASE_SERVICE_ROLE_KEY ? '✓ Configurada' : '✗ Não configurada'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Ambiente:</span>
                  <span className="ml-2 text-gray-600">
                    {process.env.NODE_ENV || 'development'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
