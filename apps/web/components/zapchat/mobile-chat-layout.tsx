"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import {
  Send,
  Mic,
  Paperclip,
  Phone,
  Video,
  ArrowLeft,
  MoreVertical,
  Smile,
  Plus,
  Camera,
  MicIcon,
  FileText,
  CheckCheck,
  Check,
  Clock,
  AlertCircle,
  Wifi,
  WifiOff
} from "lucide-react";
import { cn } from "@ui/lib";
import { formatDistanceToNow, format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";

// Tipos
import { ChatMessage, ConnectionStatus, TypingStatus } from "../../lib/services/supabase-realtime.service";

interface MobileChatLayoutProps {
  // Dados da consulta
  consultation: {
    id: string;
    doctor_name?: string;
    doctor_avatar?: string;
    patient_name?: string;
    patient_avatar?: string;
    is_on_duty?: boolean;
  };

  // Estado do usuário
  userId: string;
  userRole: 'DOCTOR' | 'PATIENT';

  // Dados do chat
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Estado da conexão
  connectionStatus: ConnectionStatus;
  isConnected: boolean;

  // Estado de digitação
  typingUsers: TypingStatus[];

  // Funções do chat
  onSendMessage: (content: string) => Promise<void>;
  onSendAudio: (audioBlob: Blob) => Promise<void>;
  onSendFile: (file: File) => Promise<void>;
  onStartTyping: () => void;
  onStopTyping: () => void;

  // Navegação
  onBack?: () => void;
  onVideoCall?: () => void;
  onAudioCall?: () => void;
  onEndConsultation?: () => void;
}

export function MobileChatLayout({
  consultation,
  userId,
  userRole,
  messages,
  isLoading,
  error,
  connectionStatus,
  isConnected,
  typingUsers,
  onSendMessage,
  onSendAudio,
  onSendFile,
  onStartTyping,
  onStopTyping,
  onBack,
  onVideoCall,
  onAudioCall,
  onEndConsultation,
}: MobileChatLayoutProps) {
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Dados do destinatário baseado no role
  const recipientName = userRole === 'DOCTOR' ? consultation.patient_name : consultation.doctor_name;
  const recipientAvatar = userRole === 'DOCTOR' ? consultation.patient_avatar : consultation.doctor_avatar;
  const recipientInitial = recipientName?.charAt(0) || (userRole === 'DOCTOR' ? 'P' : 'D');

  // Auto-scroll para nova mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Configurar MediaRecorder
  useEffect(() => {
    if (typeof window !== "undefined" && navigator.mediaDevices) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setAudioChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
            setAudioChunks([]);

            try {
              await onSendAudio(audioBlob);
            } catch (error) {
              console.error("Erro ao enviar áudio:", error);
              toast.error("Erro ao enviar áudio");
            }
          };
        })
        .catch((error) => {
          console.error("Erro ao acessar microfone:", error);
        });
    }
  }, [onSendAudio, audioChunks]);

  // Funções de envio
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const content = inputValue.trim();
    setInputValue("");
    onStopTyping();

    try {
      await onSendMessage(content);
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error);
      setInputValue(content);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Gerenciar indicador de digitação
    if (value.trim()) {
      onStartTyping();
    }

    // Limpar timeout anterior
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Parar digitação após 1 segundo de inatividade
    typingTimeoutRef.current = setTimeout(() => {
      onStopTyping();
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Funções de áudio
  const startRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
      toast.info("Gravando áudio...");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  // Função para formatar hora
  const formatMessageTime = (createdAt: string) => {
    const messageDate = new Date(createdAt);
    const now = new Date();
    const isToday = messageDate.toDateString() === now.toDateString();

    if (isToday) {
      return format(messageDate, "HH:mm", { locale: ptBR });
    } else {
      return format(messageDate, "dd/MM HH:mm", { locale: ptBR });
    }
  };

  const getMessageStatus = (message: ChatMessage) => {
    const isOwnMessage = message.senderId === userId;
    if (!isOwnMessage) return null;

    // Simular status de entrega/leitura
    return <CheckCheck className="w-3 h-3 text-blue-500" />;
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 relative">
      {/* Header estilo WhatsApp */}
      <div className="bg-green-600 text-white px-4 py-3 flex items-center justify-between shadow-md relative z-10">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {onBack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
          )}

          <Avatar className="w-10 h-10 ring-2 ring-white/30">
            <AvatarImage src={recipientAvatar} alt={recipientName} />
            <AvatarFallback className="bg-green-500 text-white font-medium">
              {recipientInitial}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h1 className="font-medium truncate text-white">
              {recipientName || (userRole === 'DOCTOR' ? 'Paciente' : 'Médico')}
            </h1>
            <div className="flex items-center gap-2">
              <Badge
                variant={consultation.is_on_duty ? "default" : "secondary"}
                className="text-xs bg-green-700 text-white border-green-500"
              >
                {consultation.is_on_duty ? "Plantão" : "Consulta"}
              </Badge>

              {/* Status de conexão compacto */}
              <div className="flex items-center gap-1">
                {isConnected ? (
                  <Wifi className="w-3 h-3" />
                ) : (
                  <WifiOff className="w-3 h-3 text-red-300" />
                )}
                <span className="text-xs">
                  {isConnected ? 'online' : 'offline'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Ações do header */}
        <div className="flex items-center gap-1">
          {onVideoCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onVideoCall}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <Video className="w-5 h-5" />
            </Button>
          )}

          {onAudioCall && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onAudioCall}
              className="text-white hover:bg-green-700 p-2 h-auto"
            >
              <Phone className="w-5 h-5" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-green-700 p-2 h-auto"
          >
            <MoreVertical className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Wallpaper background */}
      <div
        className="absolute inset-0 z-0 opacity-10"
        style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Cpath d="m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        }}
      />

      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto px-4 py-2 space-y-3 relative z-1">
        {isLoading && messages.length === 0 ? (
          <MobileChatSkeleton />
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-xs mx-auto">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <Send className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Inicie a conversa
              </h3>
              <p className="text-gray-500 text-sm">
                Envie uma mensagem para começar o atendimento
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwnMessage = message.senderId === userId;
              const showAvatar = index === 0 || messages[index - 1]?.senderId !== message.senderId;

              return (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-2 max-w-[85%]",
                    isOwnMessage ? "ml-auto flex-row-reverse" : "mr-auto"
                  )}
                >
                  {/* Avatar apenas na primeira mensagem do grupo */}
                  {!isOwnMessage && (
                    <div className="w-8 h-8 flex-shrink-0">
                      {showAvatar ? (
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={recipientAvatar} alt={recipientName} />
                          <AvatarFallback className="bg-gray-300 text-gray-600 text-xs">
                            {recipientInitial}
                          </AvatarFallback>
                        </Avatar>
                      ) : null}
                    </div>
                  )}

                  {/* Mensagem */}
                  <div
                    className={cn(
                      "relative max-w-full px-3 py-2 rounded-lg shadow-sm",
                      isOwnMessage
                        ? "bg-green-500 text-white rounded-br-none"
                        : "bg-white text-gray-900 rounded-bl-none border"
                    )}
                  >
                    {/* Conteúdo da mensagem */}
                    {message.type === 'AUDIO' ? (
                      <div className="flex items-center gap-2 min-w-[150px]">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "p-1 h-auto rounded-full",
                            isOwnMessage
                              ? "text-white hover:bg-green-600"
                              : "text-green-600 hover:bg-green-50"
                          )}
                        >
                          <MicIcon className="w-4 h-4" />
                        </Button>
                        <div className="flex-1">
                          <div className="h-2 bg-white/30 rounded-full">
                            <div className="h-2 bg-white/60 rounded-full w-2/3"></div>
                          </div>
                        </div>
                        <span className="text-xs opacity-75">0:15</span>
                      </div>
                    ) : message.type === 'FILE' ? (
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        <span className="text-sm">{message.content}</span>
                      </div>
                    ) : (
                      <p className="text-sm leading-relaxed break-words">
                        {message.content}
                      </p>
                    )}

                    {/* Info da mensagem */}
                    <div className={cn(
                      "flex items-center justify-end gap-1 mt-1",
                      isOwnMessage ? "text-green-100" : "text-gray-500"
                    )}>
                      <span className="text-xs">
                        {formatMessageTime(message.createdAt)}
                      </span>
                      {getMessageStatus(message)}
                    </div>

                    {/* Seta da bolha */}
                    <div
                      className={cn(
                        "absolute bottom-0 w-0 h-0",
                        isOwnMessage
                          ? "right-0 translate-x-1 border-l-8 border-l-green-500 border-b-8 border-b-transparent"
                          : "left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent"
                      )}
                    />
                  </div>
                </div>
              );
            })}

            {/* Indicador de digitação */}
            {typingUsers.length > 0 && (
              <div className="flex gap-2 max-w-[85%] mr-auto">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={recipientAvatar} alt={recipientName} />
                  <AvatarFallback className="bg-gray-300 text-gray-600 text-xs">
                    {recipientInitial}
                  </AvatarFallback>
                </Avatar>

                <div className="bg-white rounded-lg rounded-bl-none px-3 py-2 shadow-sm border relative">
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: '0.1s' }}
                      />
                      <div
                        className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                        style={{ animationDelay: '0.2s' }}
                      />
                    </div>
                  </div>

                  {/* Seta da bolha */}
                  <div className="absolute bottom-0 left-0 -translate-x-1 border-r-8 border-r-white border-b-8 border-b-transparent" />
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Footer fixo com input */}
      <div className="bg-white border-t border-gray-200 px-3 py-2 relative z-10">
        {/* Indicador de erro */}
        {error && (
          <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-600 flex-1">{error}</span>
          </div>
        )}

        <div className="flex items-end gap-2">
          {/* Input principal */}
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Mensagem"
              className="rounded-full border-gray-300 pr-20 py-2 min-h-[44px]"
              disabled={!isConnected}
            />

            {/* Botões internos do input */}
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
                className="p-1 h-auto text-gray-500 hover:text-gray-700"
                disabled={!isConnected}
              >
                <Paperclip className="w-4 h-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto text-gray-500 hover:text-gray-700"
                disabled={!isConnected}
              >
                <Smile className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Botão de envio/áudio */}
          {inputValue.trim() ? (
            <Button
              onClick={handleSendMessage}
              className="rounded-full w-12 h-12 bg-green-600 hover:bg-green-700 p-0"
              disabled={!isConnected}
            >
              <Send className="w-5 h-5" />
            </Button>
          ) : (
            <Button
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              onMouseLeave={stopRecording}
              className={cn(
                "rounded-full w-12 h-12 p-0 transition-all",
                isRecording
                  ? "bg-red-600 hover:bg-red-700 animate-pulse"
                  : "bg-green-600 hover:bg-green-700"
              )}
              disabled={!isConnected || !mediaRecorder}
            >
              <Mic className="w-5 h-5" />
            </Button>
          )}
        </div>

        {/* Menu de anexos */}
        {showAttachmentMenu && (
          <div className="absolute bottom-full right-3 mb-2 bg-white rounded-lg shadow-lg border p-2 grid grid-cols-3 gap-2 min-w-[180px]">
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onSendFile(file);
                  setShowAttachmentMenu(false);
                }
              }}
              accept="image/*,audio/*,.pdf,.doc,.docx,.txt"
            />

            <Button
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="flex flex-col gap-1 h-auto p-3"
            >
              <FileText className="w-6 h-6 text-blue-600" />
              <span className="text-xs">Arquivo</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="flex flex-col gap-1 h-auto p-3"
            >
              <Camera className="w-6 h-6 text-green-600" />
              <span className="text-xs">Câmera</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="flex flex-col gap-1 h-auto p-3"
            >
              <Plus className="w-6 h-6 text-purple-600" />
              <span className="text-xs">Mais</span>
            </Button>
          </div>
        )}

        {/* Overlay para fechar menu */}
        {showAttachmentMenu && (
          <div
            className="fixed inset-0 z-0"
            onClick={() => setShowAttachmentMenu(false)}
          />
        )}
      </div>
    </div>
  );
}

// Skeleton para carregamento
function MobileChatSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(6)].map((_, i) => (
        <div
          key={i}
          className={cn(
            "flex gap-2 max-w-[80%]",
            i % 2 === 0 ? "mr-auto" : "ml-auto flex-row-reverse"
          )}
        >
          {i % 2 === 0 && (
            <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
          )}
          <div
            className={cn(
              "px-3 py-2 rounded-lg space-y-2",
              i % 2 === 0
                ? "bg-gray-200 animate-pulse rounded-bl-none"
                : "bg-green-200 animate-pulse rounded-br-none"
            )}
          >
            <div className="h-4 bg-gray-300 rounded animate-pulse" />
            <div className="h-3 bg-gray-300 rounded w-2/3 animate-pulse" />
          </div>
        </div>
      ))}
    </div>
  );
}
