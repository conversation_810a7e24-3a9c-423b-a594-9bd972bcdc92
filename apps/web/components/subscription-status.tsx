"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import { CalendarIcon, CheckCircle2Icon, ClockIcon } from 'lucide-react';

interface SubscriptionStatusProps {
  refreshTrigger?: number;
}

interface ConsultationSummary {
  hasSubscription: boolean;
  consultationsIncluded: number;
  consultationsUsed: number;
  remainingConsultations: number;
  planName?: string;
  usageHistory: Array<{
    id: string;
    usedAt: string;
    type: string;
    appointment: {
      id: string;
      scheduledAt: string;
      status: string;
      doctor: {
        user: { name: string | null };
      };
    };
  }>;
}

export function SubscriptionStatus({ refreshTrigger }: SubscriptionStatusProps) {
  const [summary, setSummary] = useState<ConsultationSummary | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchSummary = async () => {
    try {
      const response = await fetch('/api/appointments/subscription');
      if (response.ok) {
        const data = await response.json();
        setSummary(data);
      }
    } catch (error) {
      console.error('Erro ao buscar resumo de consultas:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSummary();
  }, [refreshTrigger]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Carregando...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!summary?.hasSubscription) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-800">
            <CalendarIcon className="h-5 w-5" />
            Nenhuma Assinatura Ativa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-yellow-700 text-sm">
            Você não possui uma assinatura ativa. Assine o plano ZapVida Sempre para agendar consultas mensais.
          </p>
        </CardContent>
      </Card>
    );
  }

  const usagePercentage = (summary.consultationsUsed / summary.consultationsIncluded) * 100;
  const isLimitReached = summary.remainingConsultations === 0;

  return (
    <div className="space-y-4">
      {/* Status da Assinatura */}
      <Card className={isLimitReached ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            {summary.planName || 'Plano Ativo'}
            <Badge variant={isLimitReached ? "destructive" : "default"}>
              {isLimitReached ? "Limite Atingido" : "Ativo"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Consultas utilizadas este mês</span>
              <span className="font-medium">
                {summary.consultationsUsed} de {summary.consultationsIncluded}
              </span>
            </div>
            <Progress
              value={usagePercentage}
              className={`h-2 ${isLimitReached ? '[&>div]:bg-red-500' : '[&>div]:bg-green-500'}`}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle2Icon className="h-4 w-4 text-green-500" />
              <span>Consultas restantes: <strong>{summary.remainingConsultations}</strong></span>
            </div>
            <div className="flex items-center gap-2">
              <ClockIcon className="h-4 w-4 text-blue-500" />
              <span>Reset: todo dia 1°</span>
            </div>
          </div>

          {isLimitReached && (
            <div className="bg-red-100 border border-red-200 rounded-lg p-3">
              <p className="text-red-800 text-sm">
                <strong>Limite atingido!</strong> Você esgotou suas 2 consultas mensais.
                Para atendimentos adicionais, você pode agendar uma consulta avulsa ou usar o plantão médico.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Histórico de Consultas */}
      {summary.usageHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Consultas Este Mês</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {summary.usageHistory.map((usage) => (
                <div key={usage.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-sm">
                      Dr. {usage.appointment.doctor.user.name || 'Médico'}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(usage.appointment.scheduledAt).toLocaleDateString('pt-BR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="text-xs">
                      {usage.type === 'SUBSCRIPTION' ? 'Assinatura' : usage.type}
                    </Badge>
                    <div className="text-xs text-gray-500 mt-1">
                      {usage.appointment.status === 'COMPLETED' ? 'Realizada' :
                       usage.appointment.status === 'SCHEDULED' ? 'Agendada' :
                       usage.appointment.status === 'CANCELED' ? 'Cancelada' : usage.appointment.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
