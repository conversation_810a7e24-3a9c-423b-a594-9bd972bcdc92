import { <PERSON>, CardContent, CardHeader } from '@ui/components/card';
import { Skeleton } from '@ui/components/skeleton';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Search } from 'lucide-react';

export function DoctorsListSkeleton() {
  return (
    <div className="min-h-screen bg-background pt-24">
      {/* Hero Section com Filtros */}
      <section className="border-b bg-muted/30 py-8 pb-12">
        <div className="container mx-auto px-4 md:px-9">
          <div className="mb-6 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Encontre seu Médico</h1>
              <p className="mt-2 text-muted-foreground">
                <Skeleton className="h-5 w-64 inline-block" />
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="grid gap-3 md:grid-cols-12">
              {/* Busca por nome */}
              <div className="relative md:col-span-6">
                <Search className="absolute left-3 top-3 size-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome ou CRM..."
                  className="pl-9 bg-white"
                  disabled
                />
              </div>

              {/* Filtro de Especialidade */}
              <div className="md:col-span-4 bg-white rounded-md">
                <Select disabled>
                  <SelectTrigger>
                    <SelectValue placeholder="Especialidade" />
                  </SelectTrigger>
                </Select>
              </div>

              {/* Botão de Buscar */}
              <div className="md:col-span-2">
                <Button
                  className="w-full gap-2"
                  disabled
                  variant="secondary"
                >
                  <Search className="size-4" />
                  Buscar
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Lista de Médicos */}
      <section className="py-8">
        <div className="container mx-auto px-4 md:px-9">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-16 w-16 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                      <div className="flex items-center gap-1">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}

export function DoctorCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <Skeleton className="h-16 w-16 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24" />
            <div className="flex items-center gap-1">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-6 w-16" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-10 w-full" />
      </CardContent>
    </Card>
  );
}