import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { Skeleton } from '@ui/components/skeleton';
import { User, Award, ShieldCheck, CheckCircle, Calendar as CalendarIcon, Clock } from 'lucide-react';

export function DoctorProfileSkeleton() {
  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl pt-24">
      {/* Mobile Hero Section Skeleton */}
      <div className="lg:hidden mb-6">
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <Skeleton className="w-20 h-20 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-28" />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-3 mb-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="text-center p-3 bg-gray-50 rounded-lg">
                  <Skeleton className="w-5 h-5 mx-auto mb-1" />
                  <Skeleton className="h-3 w-12 mx-auto mb-1" />
                  <Skeleton className="h-4 w-16 mx-auto" />
                </div>
              ))}
            </div>

            <Skeleton className="h-12 w-full rounded-lg" />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
        {/* Left Column - Doctor Profile Skeleton */}
        <div className="lg:col-span-5 space-y-6">
          {/* Desktop Profile Card Skeleton */}
          <div className="hidden lg:block">
            <Card className="overflow-hidden border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Skeleton className="w-28 h-28 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-8 w-48" />
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-4 w-36" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>

                {/* Ratings Skeleton */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Skeleton className="h-12 w-12" />
                    <div className="ml-3 space-y-2">
                      <div className="flex gap-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Skeleton key={i} className="w-4 h-4" />
                        ))}
                      </div>
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>

                {/* About Section Skeleton */}
                <div className="mt-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <User className="w-5 h-5 text-primary" />
                    <h3 className="font-semibold text-lg text-gray-900">Sobre o Médico</h3>
                  </div>

                  <div>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                    <Skeleton className="h-4 w-16 mt-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>

        {/* Right Column - Booking Skeleton */}
        <div className="lg:col-span-7" id="booking-section">
          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl text-gray-900">Agendar Consulta Online</CardTitle>
              <p className="text-gray-600 text-base">
                O ZapVida conecta você diretamente com o médico para agendar sua consulta
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Booking Details Skeleton */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <span className="font-medium text-blue-900">Consulta Médica</span>
                  <Badge className="bg-blue-100 text-blue-800 border-blue-200 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Disponível
                  </Badge>
                </div>
                <Skeleton className="h-4 w-48" />
              </div>

              {/* Calendar Section Skeleton */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="w-5 h-5 text-primary" />
                    <h3 className="font-semibold text-lg text-gray-900">Escolha uma Data</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-9 w-9" />
                    <Skeleton className="h-9 w-9" />
                  </div>
                </div>

                <Skeleton className="h-4 w-32 mx-auto" />

                <div className="grid grid-cols-7 gap-2">
                  {Array.from({ length: 7 }).map((_, i) => (
                    <div key={i} className="text-center">
                      <Skeleton className="h-3 w-8 mx-auto mb-2" />
                      <Skeleton className="h-16 w-full" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Time Slots Skeleton */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-primary" />
                  <h3 className="font-semibold text-lg text-gray-900">Horários Disponíveis</h3>
                </div>
                <div className="grid grid-cols-3 sm:grid-cols-4 gap-3">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <Skeleton key={i} className="h-12" />
                  ))}
                </div>
              </div>

              {/* Continue Button Skeleton */}
              <div className="pt-4">
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
