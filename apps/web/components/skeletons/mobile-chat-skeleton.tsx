"use client";

import { cn } from "@ui/lib";

interface MobileChatSkeletonProps {
  messageCount?: number;
}

export function MobileChatSkeleton({ messageCount = 8 }: MobileChatSkeletonProps) {
  return (
    <div className="space-y-4 p-4">
      {[...Array(messageCount)].map((_, i) => {
        const isOwnMessage = Math.random() > 0.5;
        const hasAvatar = !isOwnMessage && (i === 0 || Math.random() > 0.7);

        return (
          <div
            key={i}
            className={cn(
              "flex gap-2 max-w-[85%] animate-pulse",
              isOwnMessage ? "ml-auto flex-row-reverse" : "mr-auto"
            )}
          >
            {/* Avatar skeleton */}
            {!isOwnMessage && (
              <div className="w-8 h-8 flex-shrink-0">
                {hasAvatar && (
                  <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
                )}
              </div>
            )}

            {/* Message bubble skeleton */}
            <div
              className={cn(
                "relative max-w-full px-3 py-2 rounded-lg",
                isOwnMessage
                  ? "bg-green-200 rounded-br-none"
                  : "bg-gray-200 rounded-bl-none"
              )}
            >
              {/* Content skeleton */}
              <div className="space-y-2">
                <div
                  className="h-4 bg-gray-300 rounded animate-pulse"
                  style={{ width: `${Math.random() * 40 + 60}%` }}
                />
                {Math.random() > 0.6 && (
                  <div
                    className="h-4 bg-gray-300 rounded animate-pulse"
                    style={{ width: `${Math.random() * 60 + 40}%` }}
                  />
                )}
              </div>

              {/* Time skeleton */}
              <div className="flex items-center justify-end gap-1 mt-2">
                <div className="h-3 w-12 bg-gray-300 rounded animate-pulse" />
                {isOwnMessage && (
                  <div className="h-3 w-3 bg-gray-300 rounded animate-pulse" />
                )}
              </div>

              {/* Bubble tail */}
              <div
                className={cn(
                  "absolute bottom-0 w-0 h-0",
                  isOwnMessage
                    ? "right-0 translate-x-1 border-l-8 border-l-green-200 border-b-8 border-b-transparent"
                    : "left-0 -translate-x-1 border-r-8 border-r-gray-200 border-b-8 border-b-transparent"
                )}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
}

export function MobileChatHeaderSkeleton() {
  return (
    <div className="bg-green-600 px-4 py-3 flex items-center justify-between">
      <div className="flex items-center gap-3 flex-1">
        <div className="w-6 h-6 bg-green-700 rounded animate-pulse" />
        <div className="w-10 h-10 bg-green-700 rounded-full animate-pulse" />
        <div className="flex-1 space-y-1">
          <div className="h-4 bg-green-700 rounded w-32 animate-pulse" />
          <div className="h-3 bg-green-700 rounded w-16 animate-pulse" />
        </div>
      </div>
      <div className="flex items-center gap-1">
        <div className="w-8 h-8 bg-green-700 rounded animate-pulse" />
        <div className="w-8 h-8 bg-green-700 rounded animate-pulse" />
        <div className="w-8 h-8 bg-green-700 rounded animate-pulse" />
      </div>
    </div>
  );
}

export function MobileChatInputSkeleton() {
  return (
    <div className="bg-white border-t border-gray-200 px-3 py-2">
      <div className="flex items-center gap-2">
        <div className="flex-1 h-11 bg-gray-200 rounded-full animate-pulse" />
        <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
      </div>
    </div>
  );
}

export function MobileChatFullSkeleton() {
  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <MobileChatHeaderSkeleton />
      <div className="flex-1 overflow-hidden">
        <MobileChatSkeleton />
      </div>
      <MobileChatInputSkeleton />
    </div>
  );
}

// Skeleton para lista de conversas
export function ConversationListSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="space-y-1">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="flex items-center gap-3 p-3 animate-pulse">
          <div className="w-12 h-12 bg-gray-200 rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <div className="h-4 bg-gray-200 rounded w-24" />
              <div className="h-3 bg-gray-200 rounded w-12" />
            </div>
            <div className="h-3 bg-gray-200 rounded w-40" />
          </div>
          <div className="w-6 h-6 bg-gray-200 rounded-full" />
        </div>
      ))}
    </div>
  );
}

// Skeleton para card de consulta
export function ConsultationCardSkeleton() {
  return (
    <div className="bg-white rounded-lg border p-4 space-y-3 animate-pulse">
      <div className="flex items-center gap-3">
        <div className="w-12 h-12 bg-gray-200 rounded-full" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded w-32" />
          <div className="h-3 bg-gray-200 rounded w-24" />
        </div>
        <div className="w-16 h-6 bg-gray-200 rounded" />
      </div>

      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded w-full" />
        <div className="h-3 bg-gray-200 rounded w-3/4" />
      </div>

      <div className="flex items-center justify-between pt-2">
        <div className="h-3 bg-gray-200 rounded w-20" />
        <div className="flex gap-2">
          <div className="w-8 h-8 bg-gray-200 rounded" />
          <div className="w-8 h-8 bg-gray-200 rounded" />
        </div>
      </div>
    </div>
  );
}

// Skeleton para conexão
export function ConnectionStatusSkeleton() {
  return (
    <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg animate-pulse">
      <div className="w-4 h-4 bg-yellow-300 rounded-full" />
      <div className="h-3 bg-yellow-300 rounded w-24" />
    </div>
  );
}
