{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NODE_ENV", "PORT", "DATABASE_URL", "NEXT_PUBLIC_SITE_URL", "VERCEL_URL", "LEMONSQUEEZY_WEBHOOK_SECRET", "LEMONSQUEEZY_API_KEY", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "CHARGEBEE_API_KEY", "CHARGEBEE_SITE", "MAIL_HOST", "MAIL_PORT", "MAIL_USER", "MAIL_PASS", "GITHUB_CLIENT_ID", "GITHUB_CLIENT_SECRET", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "S3_ENDPOINT", "S3_ACCESS_KEY_ID", "S3_SECRET_ACCESS_KEY", "PLUNK_API_KEY", "RESEND_API_KEY", "POSTMARK_SERVER_TOKEN", "NEXT_PUBLIC_AVATARS_BUCKET_NAME", "CARD_ENCRYPTION_KEY", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "ASAAS_API_KEY", "ASAAS_API_URL", "ASAAS_WEBHOOK_TOKEN", "EVOLUTION_API_KEY", "EVOLUTION_INSTANCE", "EVOLUTION_URL", "EVOLUTION_API_URL", "EVOLUTION_WEBHOOK_URL", "EVOLUTION_GROUP_PLANTAO_ID", "WHATSAPP_DOCTORS_GROUP_ID", "WHATSAPP_ADMIN_GROUP_ID", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", "NEXT_PUBLIC_LIVEKIT_URL", "OPENAI_API_KEY", "NEXT_PUBLIC_S3_ENDPOINT", "NEXT_PUBLIC_UPLOADS_BUCKET_NAME", "NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME", "NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET", "NEXT_PUBLIC_ZAPCHAT_V2_ENABLED", "NEXT_PUBLIC_ZAPCHAT_V2_BETA"], "tasks": {"build": {"dependsOn": ["^db:generate", "^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "type-check": {}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "dev": {"cache": false, "dependsOn": ["^db:generate"], "persistent": true}, "export": {"outputs": ["out/**"]}, "lint": {}}}