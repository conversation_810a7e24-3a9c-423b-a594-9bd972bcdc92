{"name": "zapvida-app", "private": true, "workspaces": ["apps/*", "packages/*", "tooling/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "create-groups": "node scripts/create-whatsapp-groups.js", "test-plantao": "node scripts/test-plantao-whatsapp.js", "test-whatsapp": "npm run test-plantao", "validate-groups": "npx tsx scripts/validate-whatsapp-groups.ts", "check:whatsapp": "node scripts/check-whatsapp-config.js"}, "devDependencies": {"tsconfig": "workspace:*", "prettier": "^3.3.3", "turbo": "^2.1.3"}, "packageManager": "pnpm@9.9.0"}