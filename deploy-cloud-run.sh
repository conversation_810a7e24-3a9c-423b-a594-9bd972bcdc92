#!/bin/bash

# Script de Deploy Automatizado para Google Cloud Run - ZapVida
# Uso: ./deploy-cloud-run.sh [PROJECT_ID] [SERVICE_NAME] [REGION]

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%d-%m %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se o gcloud CLI está instalado
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        error "Google Cloud CLI (gcloud) não está instalado!"
        echo "Instale em: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    log "Google Cloud CLI encontrado"
}

# Verificar se o Docker está instalado
check_docker() {
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado!"
        echo "Instale em: https://docs.docker.com/get-docker/"
        exit 1
    fi
    log "Docker encontrado"
}

# Verificar se o arquivo .env.yaml existe
check_env_file() {
    if [ ! -f ".env.yaml" ]; then
        error "Arquivo .env.yaml não encontrado!"
        echo "Copie o arquivo cloud-run-env.yaml.example para .env.yaml e configure as variáveis"
        exit 1
    fi
    log "Arquivo .env.yaml encontrado"
}

# Configurar projeto e região
setup_project() {
    local project_id=$1
    local region=${2:-"us-central1"}

    if [ -z "$project_id" ]; then
        error "PROJECT_ID é obrigatório!"
        echo "Uso: $0 PROJECT_ID [SERVICE_NAME] [REGION]"
        exit 1
    fi

    log "Configurando projeto: $project_id"
    gcloud config set project "$project_id"

    log "Configurando região: $region"
    gcloud config set run/region "$region"

    PROJECT_ID=$project_id
    REGION=$region
}

# Fazer login no Google Cloud
login_gcloud() {
    log "Fazendo login no Google Cloud..."
    gcloud auth login --no-launch-browser

    log "Configurando Docker para usar gcloud como credencial"
    gcloud auth configure-docker
}

# Build da imagem Docker
build_image() {
    local service_name=$1
    local image_tag="gcr.io/$PROJECT_ID/$service_name:latest"

    log "Build da imagem Docker: $image_tag"

    # Usar Dockerfile.cloudrun se existir, senão usar Dockerfile padrão
    if [ -f "Dockerfile.cloudrun" ]; then
        log "Usando Dockerfile.cloudrun otimizado para Cloud Run"
        docker build -f Dockerfile.cloudrun -t "$image_tag" .
    else
        log "Usando Dockerfile padrão"
        docker build -t "$image_tag" .
    fi

    log "Build concluído com sucesso!"
}

# Push da imagem para Google Container Registry
push_image() {
    local service_name=$1
    local image_tag="gcr.io/$PROJECT_ID/$service_name:latest"

    log "Push da imagem para Google Container Registry..."
    docker push "$image_tag"
    log "Push concluído com sucesso!"
}

# Deploy no Cloud Run
deploy_service() {
    local service_name=$1

    log "Fazendo deploy do serviço: $service_name"

    # Verificar se o serviço já existe
    if gcloud run services describe "$service_name" --region="$REGION" &>/dev/null; then
        log "Serviço existente encontrado. Atualizando..."
        gcloud run services update "$service_name" \
            --image="gcr.io/$PROJECT_ID/$service_name:latest" \
            --region="$REGION" \
            --env-vars-file=".env.yaml"
    else
        log "Criando novo serviço..."
        gcloud run deploy "$service_name" \
            --image="gcr.io/$PROJECT_ID/$service_name:latest" \
            --platform="managed" \
            --region="$REGION" \
            --allow-unauthenticated \
            --port="8080" \
            --env-vars-file=".env.yaml"
    fi

    log "Deploy concluído com sucesso!"
}

# Verificar status do serviço
check_service_status() {
    local service_name=$1

    log "Verificando status do serviço..."

    # Obter URL do serviço
    local service_url=$(gcloud run services describe "$service_name" \
        --region="$REGION" \
        --format="value(status.url)")

    log "Serviço disponível em: $service_url"

    # Verificar se está respondendo
    log "Testando conectividade..."
    if curl -s "$service_url" > /dev/null; then
        log "✅ Serviço respondendo corretamente!"
    else
        warn "⚠️  Serviço pode não estar respondendo ainda. Aguarde alguns minutos."
    fi
}

# Mostrar logs do serviço
show_logs() {
    local service_name=$1

    log "Últimos logs do serviço:"
    gcloud run services logs read "$service_name" \
        --region="$REGION" \
        --limit=20
}

# Função principal
main() {
    local project_id=$1
    local service_name=${2:-"zapvida"}
    local region=${3:-"us-central1"}

    echo "🚀 ZapVida - Deploy Automatizado para Google Cloud Run"
    echo "=================================================="

    # Verificações iniciais
    check_gcloud
    check_docker
    check_env_file

    # Configurar projeto
    setup_project "$project_id" "$region"

    # Fazer login se necessário
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        login_gcloud
    fi

    # Processo de deploy
    build_image "$service_name"
    push_image "$service_name"
    deploy_service "$service_name"

    # Verificações finais
    check_service_status "$service_name"

    echo ""
    log "🎉 Deploy concluído com sucesso!"
    echo ""
    echo "📋 Próximos passos:"
    echo "1. Verifique se o serviço está funcionando"
    echo "2. Configure um domínio personalizado se necessário"
    echo "3. Configure alertas de monitoramento"
    echo "4. Verifique os logs para identificar possíveis problemas"
    echo ""
    echo "🔍 Comandos úteis:"
    echo "  - Ver logs: gcloud run services logs read $service_name --region=$REGION"
    echo "  - Ver status: gcloud run services describe $service_name --region=$REGION"
    echo "  - Abrir console: gcloud run services list --region=$REGION"
}

# Executar script
if [ $# -eq 0 ]; then
    error "PROJECT_ID é obrigatório!"
    echo "Uso: $0 PROJECT_ID [SERVICE_NAME] [REGION]"
    echo ""
    echo "Exemplo:"
    echo "  $0 meu-projeto-id"
    echo "  $0 meu-projeto-id zapvida-app us-central1"
    exit 1
fi

main "$@"
